package com.zte.mcrm.activity.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 10307200
 * @since 2023-12-26 下午3:22
 **/
@Getter
@Setter
@Component
@ConfigurationProperties(
        prefix = "exhibition.url.config",
        ignoreUnknownFields = true
)
public class ExhibitionUrlConfig {

    /**
     * 展会详情
     */
    private String detail;

    private String defaultRedirectUrl;

    /**
     * 获取展会详情跳转url。
     *
     * @param exhibitionRowId 展会id
     * @return url
     */
    public String fetchDetailUrl(String exhibitionRowId) {
        return detail + "?id=" + exhibitionRowId;
    }

    /**
     * 获取展会详情下页签跳转url。
     *
     * @param exhibitionRowId 展会id
     * @return url
     */
    public String fetchDetailCommunicationUrl(String exhibitionRowId, String tab) {
        return detail + "?id=" + exhibitionRowId + "&name=" + tab;
    }

}
