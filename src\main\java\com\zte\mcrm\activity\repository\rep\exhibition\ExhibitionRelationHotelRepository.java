package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午2:15
 **/
public interface ExhibitionRelationHotelRepository {

    /***
     * <p>
     * 插入一条记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:57
     * @param record 展会关联酒店记录
     * @return int
     */
    int insertSelective(ExhibitionRelationHotelDO record);

    /***
     * <p>
     * 更新一条记录的指定字段
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:57
     * @param record 展会关联酒店记录
     * @return int
     */
    int updateByPrimaryKeySelective(ExhibitionRelationHotelDO record);

    /***
     * <p>
     * 根据主键Id列表 获取 展会关联酒店记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:58
     * @param hotelRowId 展会关联酒店记录主键Id
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO>
     */
    List<ExhibitionRelationHotelDO> queryAllByHotelRowIds(List<String> hotelRowId);
    
    /***
     * <p>
     * 根据展会Id 获取 对应的关联酒店集合
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午3:02
     * @param exhibitionRowIds 展会Id
     * @return java.util.Map<java.lang.String,java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO>>
     */
    Map<String, List<ExhibitionRelationHotelDO>> getRelationHotelListByExhibitionRowIds(Set<String> exhibitionRowIds);
    
    /***
     * <p>
     * 批量插入数据
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午3:03
     * @param records 展会关联酒店记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationHotelDO> records);
    
    /***
     * <p>
     * 批量更新数据
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午3:03
     * @param records 展会关联酒店记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationHotelDO> records);
    
    /***
     * <p>
     * 根据展会Id 删除 对应的关联酒店记录
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午3:04
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id列表
     * @return int
     */
    int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);

    /***
     * <p>
     * 根据主键Id 删除 对应的关联酒店记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午3:05
     * @param operator 操作人
     * @param rowIds 关联酒店记录主键Id列表
     * @return int
     */
    int deleteByRowIds(String operator, List<String> rowIds);

}
