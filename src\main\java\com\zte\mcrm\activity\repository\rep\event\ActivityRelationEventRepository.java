package com.zte.mcrm.activity.repository.rep.event;

import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;

import java.util.List;


/**
 * 客户活动关联事件 服务接口类
 * <AUTHOR>
 * @date 2023/06/29  
 */
public interface ActivityRelationEventRepository {
    /**
     * 根据ID查询
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2023/06/29
     */
	ActivityRelationEventDO get(String rowId);

    /**
     * 查询列表
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2023/06/29
     */
	List<ActivityRelationEventDO> getList(ActivityRelationEventDO entity);

    /**
     * 删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2023/06/29
     */
	int delete(String rowId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2023/06/29
     */
	int insert(ActivityRelationEventDO entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2023/06/29
     */
	int update(ActivityRelationEventDO entity);

}