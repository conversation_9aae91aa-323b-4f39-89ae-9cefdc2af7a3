package com.zte.mcrm.activity.common.util;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;

import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class ServiceDataUtils extends ServiceResultUtil {
    /** 成功（用于快速终止业务执行，比如：无需处理、重复处理等） */
    public static final String SUCCESS_TERMINATION = "T0000";
    /** 成功 */
    public static final String SUCCESS_CODE = "0000";
    /** 系统异常 */
    public static final String SERVERERROR_CODE = "0001";
    /** 登录校验失败 */
    public static final String AUTHFAILED_CODE = "0002";
    /** 无权操作 */
    public static final String PERMISSIONDENIED_CODE = "0003";
    /** 参数校验错误 **/
    public static final String VALIDATIONERROR_CODE="0004";
    /** 业务校验失败 */
    public static final String BUSINESSERROR_CODE = "0005";

    /**
     * 是否成功
     *
     * @param serviceData
     * @return true-成，false-失败
     */
    public static boolean isSuccess(ServiceData<?> serviceData) {
        RetCode retCode = serviceData == null ? null : serviceData.getCode();

        return retCode != null && SUCCESS_CODE.equals(retCode.getCode());
    }

    /**
     * 构建ServiceData
     *
     * @param code
     * @param errMsg 错误提示信息
     * @param <T>
     * @return
     */
    public static <T> ServiceData<T> buildServiceData(String code, String errMsg) {
        ServiceData<T> serviceData = new ServiceData<>();
        RetCode r = new RetCode();
        r.setCode(code);
        r.setMsg(errMsg);
        serviceData.setCode(r);
        return serviceData;
    }

    /**
     * 根据源ServiceData构建新的ServiceData
     * @param oriData  源ServiceData对象
     * @param boConvert bo转换器
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> ServiceData<T> buildServiceData(ServiceData<F> oriData, Function<F, T> boConvert) {
        ServiceData<T> serviceData = new ServiceData<>();
        serviceData.setCode(oriData.getCode());
        if (oriData.getBo() != null) {
            serviceData.setBo(boConvert.apply(oriData.getBo()));
        }
        return serviceData;
    }

    /**
     * 将结果解析为ServiceData
     *
     * @param json
     * @param convert 类型转换器，如：json -> JSON.parseObject(json, new TypeReference<ServiceData<Test>>() {})
     * @param <T>
     * @return
     */
    public static <T> ServiceData<T> parseServiceData(String json, Function<String, ServiceData<T>> convert) {
        ServiceData<T> s;
        try {
            s = convert.apply(json);
        } catch (Exception e) {
            s = new ServiceData<>();
            RetCode c = new RetCode();
            c.setCode(SERVERERROR_CODE);
            c.setMsg(json);
            s.setCode(c);
        }

        return s;
    }

}
