package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationExportParam;
import com.zte.mcrm.activity.web.controller.schedule.vo.LastScheduleOrchestrationVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationVO;
import org.apache.http.entity.mime.content.ByteArrayBody;

import java.io.IOException;

/**
 * 展会资源编排查询服务
 *
 * <AUTHOR>
 */
public interface ScheduleOrchestrationQueryAppService {

    /**
     * 查询可编排数据列表
     *
     * @param request
     * @return
     */
    BizResult<ScheduleOrchestrationVO> scheduleOrchestrationEditList(BizRequest<ScheduleOrchestrationExportParam> request);

    /**
     * 最新发布资源编排列表
     *
     * @param request
     * @return
     */
    BizResult<LastScheduleOrchestrationVO> lastPublishVersion(BizRequest<ScheduleOrchestrationExportParam> request);

    /**
     * 导出资源编排列表
     *
     * @param req
     * @return
     */
    ByteArrayBody exportScheduleOrchestrationEditList(BizRequest<ScheduleOrchestrationExportParam> req) throws IOException;
}
