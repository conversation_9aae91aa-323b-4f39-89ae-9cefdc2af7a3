package com.zte.mcrm.activity.repository.model.plancto;

/**
 * <AUTHOR>
 * @date 2024年12月09日15:41
 */
/* Started by AICoder, pid:7e1b9m3053lda5c1454b095451df7645adc5c9ba */

import lombok.Data;

import java.util.Date;

@Data
public class CtoPlanProductFinishDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * CTO拓展计划ID
     */
    private String ctoPlanInfoId;

    /**
     * CTO产品线维度明细ID
     */
    private String ctoPlanDetailProductId;

    /**
     * 员工编号
     */
    private String employeeNo;

    /**
     * PeopleRoleLabelEnum
     */
    private String employeeType;

    /**
     * 产品线RAN，CCN，BN，FM，SN-数能
     */
    private String productCode;

    /**
     * 营销事业部
     */
    private String orgDivision;

    /**
     * 客户Account编码明细json数组
     */
    private String accountCode;

    /**
     * 客户Account名称明细json数组
     */
    private String accountName;

    /**
     * 客户目标总数
     */
    private Integer accountTarget;

    /**
     * 客户目标达成数
     */
    private Integer accountFinish;

    /**
     * 客户目标达成数拓展活动ID
     */
    private String accountActivityId;

    /**
     * 拓展活动目标总数
     */
    private Integer activityTarget;

    /**
     * 拓展活动目标达成数
     */
    private Integer activityFinish;

    /**
     * 客户目标达成数拓展活动ID
     */
    private String allActivityId;

    /**
     * 执行状态。BooleanEnum
     */
    private String exeStatus;

    /**
     * CTO周期开始时间
     */
    private Date scopeStart;

    /**
     * CTO周期结束时间
     */
    private Date scopeEnd;

    /**
     * 待执行时间
     */
    private Date exeWaitTime;

    /**
     * 执行完毕时间
     */
    private Date exeFinishTime;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;
    /**
     * 序号
     */
    private Integer seq;


}

/* Ended by AICoder, pid:7e1b9m3053lda5c1454b095451df7645adc5c9ba */
