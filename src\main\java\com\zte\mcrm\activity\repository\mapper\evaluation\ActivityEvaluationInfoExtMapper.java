package com.zte.mcrm.activity.repository.mapper.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.activity.service.resource.vo.ActivityScoreCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityEvaluationInfoExtMapper extends ActivityEvaluationInfoMapper {
    List<ActivityEvaluationInfoDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);

    /**
     * 查询活动参与人评价分数及总次数
     *
     * @param codeList              参与人编号
     * @param activityStatusList    活动状态
     * @return {@link List< ActivityScoreCountVO>}
     * <AUTHOR>
     * @date 2023/5/22 下午3:14
     */
    List<ActivityScoreCountVO> selectParticipantsScoreCount(@Param("codeList") List<String> codeList,
                                                            @Param("activityStatusList") List<String> activityStatusList);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list")List<ActivityEvaluationInfoDO> list);
}