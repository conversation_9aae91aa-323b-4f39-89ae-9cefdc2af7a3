package com.zte.mcrm.activity.service.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.AiConstant;
import com.zte.mcrm.activity.common.constant.DictConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.ai.ApiTypeEnum;
import com.zte.mcrm.activity.common.enums.ai.MessageTypeEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.service.ai.MarketAgentStrategy;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.BiddingInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/10/31 14:43
 */
@Service
public class MarketAgentBiddingInfoStrategy implements MarketAgentStrategy {

    @Autowired
    private DictService dictService;

    @Override
    public boolean support(String apiType) {
        return ApiTypeEnum.QUERY_BIDDING_INFO.isMe(apiType);
    }

    /**
     * 处理流程
     * @param req
     * @return
     */
    @Override
    public List<IgptRespVO> processBusiness(AiApplicationRespVO req) {
        List<IgptRespVO> igptRespVOList = Lists.newArrayList();
        if(StringUtils.isBlank(req.getApiData())){
            return igptRespVOList;
        }
        JSONObject jsonObject = JSONObject.parseObject(req.getApiData());
        if(null == jsonObject.getJSONArray(AiConstant.ROWS)){
            return igptRespVOList;
        }
        List<BiddingInfoVO> biddingInfoVOList = jsonObject.getJSONArray(AiConstant.ROWS).toJavaList(BiddingInfoVO.class);
        String num = dictService.getDictValueByTypeAndKey(DictConstant.MARKET_AGENT, DictConstant.BIDDING_INFO_NUM);

        for (int i = 0; i < Math.min(biddingInfoVOList.size(), Integer.parseInt(num)); i++) {
            BiddingInfoVO biddingInfoVO = biddingInfoVOList.get(i);
            packIgptRespVOList(biddingInfoVO, req.getChatUuid(), igptRespVOList);
        }
        return igptRespVOList;
    }

    /**
     * 打包参数
     * @param biddingInfoVO
     * @param chatUuid
     * @param igptRespVOList
     */
    private void packIgptRespVOList(BiddingInfoVO biddingInfoVO, String chatUuid, List<IgptRespVO> igptRespVOList){
        IgptRespVO igptRespTextVO = new IgptRespVO();
        igptRespTextVO.setChatUuid(chatUuid);
        igptRespTextVO.setStatus(NumberConstant.ZERO);
        JSONObject object2 = new JSONObject();
        object2.put(AiConstant.CONTENT, biddingInfoVO.getType() + ":\n");
        object2.put(AiConstant.TYPE, MessageTypeEnum.TEXT.getType());
        igptRespTextVO.setResult(object2.toJSONString());
        igptRespVOList.add(igptRespTextVO);

        IgptRespVO igptRespLinkVO = new IgptRespVO();
        igptRespLinkVO.setChatUuid(chatUuid);
        igptRespLinkVO.setStatus(NumberConstant.ZERO);
        JSONObject object = new JSONObject();
        object.put(AiConstant.CONTENT,biddingInfoVO.getLink());
        object.put(AiConstant.TYPE, MessageTypeEnum.LINK.getType());
        object.put(AiConstant.NAME, biddingInfoVO.getTitle() + "\n");
        igptRespLinkVO.setResult(object.toJSONString());
        igptRespVOList.add(igptRespLinkVO);
    }
}
