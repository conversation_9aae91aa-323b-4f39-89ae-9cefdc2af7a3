package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityDepartmentParam;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ActivityBaseInfoVO;

/**
 * 活动校验服务类
 *
 * <AUTHOR> 10333830
 * @since  2023-08-31 21:14
 **/
public interface ActivityCheckService {

    /***
     * <p>
     * 校验用户是否拥有变更权限
     *
     * </p>
     * <AUTHOR>
     * @since  2023/11/16 下午7:59
     * @param request
     * @return boolean
     */
    boolean checkActivityChangePermissionAndStatusIsValid(BizRequest<ActivityBaseInfoVO> request);

    /**
     * 校验讲师方案信息
     * @param request   请求
     * @return com.zte.mcrm.activity.common.model.BizResult<java.lang.String>
     * <AUTHOR>
     * date: 2024/1/18 21:12
     */
    BizResult<String> checkLectureSolution(BizRequest<String> request);

    /**
     * 校验活动提交数据
     * @param baseInfoVO    活动基本信息
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/31 21:23
     */
    boolean checkActivitySubmitData(ActivityBaseInfoVO baseInfoVO);

    /**
     * 校验是否有谈参编辑权限
     * @param request
     * @return boolean
     * <AUTHOR>
     * date: 2024/1/24 17:11
     */
    BizResult<Boolean> hasSolutionEditAuth(BizRequest<String> request);


    /**
     * 获取活动业务归属类型学
     * @param request
     * @return
     * <AUTHOR>
     * @date 2024/5/28
     */
    BizResult<String> queryActivityDepartmentType(BizRequest<ActivityDepartmentParam> request);
}
