package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 慢任务线程池，由于AI交互等操作耗时太长，为防止共用线程池影响其他业务，单独定义一个线程池
 */
public class SlowTaskExecutor extends BaseTaskExecutor {

    /**
     * 核心线程数
     */
    private static final int CORE_POOL_SIZE = 5;

    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 10;

    /**
     * 最大队列size
     */
    private static final int QUEUE_CAPACITY = 5000;

    /**
     * 存活时间
     */
    private static final int KEEP_ALIVE_TIME = 60;

    public SlowTaskExecutor() {
        super("SlowTask", new ThreadPoolExecutor.AbortPolicy());
        initParam(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, new LinkedBlockingQueue<>(QUEUE_CAPACITY));
    }
}
