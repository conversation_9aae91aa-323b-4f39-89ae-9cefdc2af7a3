package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;

public interface ActivitySummaryIssueMapper {
    /**
     * all field insert
     */
    int insert(ActivitySummaryIssueDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivitySummaryIssueDO record);

    /**
     * query by primary key
     */
    ActivitySummaryIssueDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivitySummaryIssueDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivitySummaryIssueDO record);
}