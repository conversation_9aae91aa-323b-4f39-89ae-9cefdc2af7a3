package com.zte.mcrm.activity.application.cto.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.activity.application.cto.CtoPlanActivityDataService;
import com.zte.mcrm.activity.application.cto.CtoPlanReportApIndicatorService;
import com.zte.mcrm.activity.application.cto.CtoPlanReportService;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanSheetDataHelper;
import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.CtoPlanPublishDTO;
import com.zte.mcrm.activity.application.model.CtoReportApIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportItemIndicatorVO;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.cache.client.AreaDataCacheClient;
import com.zte.mcrm.activity.common.cache.model.AreaDataModel;
import com.zte.mcrm.activity.common.config.CloudDiskConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.CtoPlanConstants;
import com.zte.mcrm.activity.common.constant.DateConstants;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.AreaTypeEnum;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.CtoPlanProductTypeEnum;
import com.zte.mcrm.activity.common.enums.SaleDivisionEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.forwardmessage.ForwardMessageComponent;
import com.zte.mcrm.activity.integration.mdm.LocalAreaQueryService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.integration.zteKmCloudUdmCloudDisk.CloudDiskDownloadService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.resource.ResourceCtoPersonDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanExeReportRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanOrgFinishRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import com.zte.mcrm.activity.repository.rep.resource.ResourceCtoPersonRepository;
import com.zte.mcrm.activity.service.activitylist.ActivityInfoListQueryService;
import com.zte.mcrm.activity.service.activitylist.param.ActivityDataSourceQuery;
import com.zte.mcrm.activity.service.converter.CtoPlanDomainConverter;
import com.zte.mcrm.activity.service.file.UploadService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanDataParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanOrgDetailParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanOrgParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanProductParam;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanOrgDetailVO;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanOrgFinishVO;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanProductFinishVO;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.common.util.DateUtil;
import com.zte.mcrm.custcomm.common.RetCode;
import com.zte.mcrm.custcomm.common.constant.CustCommConstants;
import com.zte.mcrm.customermgr.util.LocalMessageUtils;
import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zte.mcrm.activity.common.constant.CtoPlanConstants.*;
import static com.zte.mcrm.activity.common.constant.I18Constants.CTO_PLAN_PUBLISH_FAILURE;
import static com.zte.mcrm.activity.common.constant.I18Constants.TIPS_COMMON_BUSINESS_ERROR;
import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;

/**
 * <AUTHOR>
 * @date 2024年12月09日19:17
 */
@Slf4j
@Service
public class CtoPlanActivityDataServiceImpl implements CtoPlanActivityDataService {

    @Autowired
    private CtoPlanOrgFinishRepository orgFinishRepository;

    @Autowired
    private CtoPlanProductFinishRepository productFinishRepository;

    @Autowired
    private CtoPlanExeReportRepository exeReportRepository;

    @Autowired
    private ResourceCtoPersonRepository resourceCtoRepository;

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private LocalAreaQueryService localAreaQueryService;

    @Autowired
    private CtoPlanReportService ctoPlanReportService;
    @Autowired
    private LocaleMessageSourceBean lmsb;
    @Autowired
    private ForwardMessageComponent forwardMessageComponent;

    @Autowired
    private UploadService uploadService;
    @Autowired
    private CloudDiskDownloadService cloudDiskDownloadService;
    @Autowired
    private CloudDiskConfig cloudDiskConfig;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private CtoPlanReportApIndicatorService ctoPlanReportApIndicatorService;

    String templateFile = "templet/CTO_Report.xlsx";

    @Autowired
    private HrmOrgInfoSearchService hrmOrgInfoSearchService;

    @Autowired
    private ActivityInfoListQueryService activityInfoListQueryService;

    @Autowired
    private AreaDataCacheClient areaDataCacheClient;

    /**
     * 活动数据-查询事业部维度
     * @param req
     * @return
     */
    @Override
    public LowCodePageRow<CtoPlanOrgFinishVO> listOrgFinish(BizRequest<PageQuery<String>> req) {
        String planInfoId = req.getParam().getParam();
        if (StringUtils.isBlank(planInfoId)) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        PageRows<CtoPlanOrgFinishDO> orgFinishRows = orgFinishRepository.pageOrgFinish(req.getParam());
        List<CtoPlanOrgFinishDO> orgFinishDOList = orgFinishRows.getRows();
        if (CollectionUtils.isEmpty(orgFinishDOList)) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        List<CtoPlanOrgFinishVO> orgFinishVos = CtoPlanDomainConverter.INSTANCE.toOrgVO(orgFinishDOList);
        Set<String> localCodes = orgFinishVos.stream().map(CtoPlanOrgFinishVO::getLocalCode).collect(Collectors.toSet());
        Set<String> orgIdList = orgFinishVos.stream().map(CtoPlanOrgFinishVO::getOrgId).collect(Collectors.toSet());
        /* Started by AICoder, pid:u7f86f8304p652a14539087350ecd00d74e37ac9 */
        Set<String> resultLocalCodeSets = localCodes.stream()
                .flatMap(entry -> {
                    if (StringUtils.isNotBlank(entry)) {
                        List<String> localCode = JSON.parseArray(entry, String.class);
                        return localCode.stream();
                    }
                    return null;
                }).collect(Collectors.toSet());
        /* Ended by AICoder, pid:u7f86f8304p652a14539087350ecd00d74e37ac9 */
        Map<String, MdmAreaDTO> mdmAreaDTOMap = localAreaQueryService.queryCountryLocal(MsaRpcRequestUtil.createWithCurrentUser(resultLocalCodeSets)).getBo();
        // 获取组织名称
        Map<String, OrgInfoVO> orgMap = hrmOrgInfoSearchService.getOrgInfoByOrgIds(new ArrayList<>(orgIdList));
        orgFinishVos.forEach(e -> {
            e.setOrgDivisionName(SaleDivisionEnum.nameByCode(e.getOrgDivision(), req.fetchLanguage()));
            String localCodeArray = e.getLocalCode();
            /* Started by AICoder, pid:kacb08ea29551e3146670845d035150aa70300e2 */
            if (StringUtils.isNotBlank(localCodeArray)) {
                List<String> localCodeList = JSON.parseArray(localCodeArray, String.class);
                List<String> localNames = localCodeList.stream().map(code -> mdmAreaDTOMap.getOrDefault(code, new MdmAreaDTO()).getDesc3()).collect(Collectors.toList());
                e.setCountryName(String.join(",", localNames));
                String accountNames = e.getAccountName();
                if (StringUtils.isNotBlank(accountNames)) {
                    List<String> accountNameList = JSON.parseArray(accountNames, String.class);
                    String accountNameLocalCodes = IntStream.range(0, Math.min(localNames.size(), accountNameList.size()))
                            .mapToObj(i -> localNames.get(i) + "-" + accountNameList.get(i))
                            .collect(Collectors.joining(";"));
                    e.setAccountName(accountNameLocalCodes);
                }
            }
            /* Ended by AICoder, pid:kacb08ea29551e3146670845d035150aa70300e2 */
            e.buildFinishDesc();
            e.buildOrgName(orgMap.get(e.getOrgId()));
        });
        return PageRowsUtil.buildLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize(), (int) orgFinishRows.getTotal(), orgFinishVos);
    }

    /**
     * 活动数据-查询产品维度
     * @param req
     * @return
     */
    @Override
    public LowCodePageRow<CtoPlanProductFinishVO> listProductFinish(BizRequest<PageQuery<String>> req) {
        String planInfoId = req.getParam().getParam();
        if (StringUtils.isBlank(planInfoId)) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        PageRows<CtoPlanProductFinishDO> productFinishRows = productFinishRepository.pageProductFinish(req.getParam());
        List<CtoPlanProductFinishDO> productFinishDOList = productFinishRows.getRows();
        if (CollectionUtils.isEmpty(productFinishDOList)) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        List<CtoPlanProductFinishVO> productFinishVOS = CtoPlanDomainConverter.INSTANCE.toProductVO(productFinishDOList);
        Set<String> shortNos = productFinishVOS.stream().map(CtoPlanProductFinishVO::getEmployeeNo).collect(Collectors.toSet());
        Map<String, PersonInfoDTO> empInfosByShortNo = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithBizReq(req, e -> shortNos)).getBo();
        productFinishVOS.forEach(e -> {
            e.setOrgDivisionName(SaleDivisionEnum.nameByCode(e.getOrgDivision(), req.fetchLanguage()));
            e.setProductTypeName(CtoPlanProductTypeEnum.nameByCode(e.getProductCode(), req.fetchLanguage()));
            e.setEmployeeTypeName(PeopleRoleLabelEnum.getDescByCode(e.getEmployeeType()));
            PersonInfoDTO employee = empInfosByShortNo.getOrDefault(e.getEmployeeNo(), new PersonInfoDTO());
            e.setEmployeeName(employee.getEmpName());
            e.setPosition(employee.getEmpPostNameMain());
            e.buildFinishDesc();
        });
        return PageRowsUtil.buildLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize(), (int) productFinishRows.getTotal(), productFinishVOS);
    }


    /* Started by AICoder, pid:x183ck0954l2aae147a80b26904c903aeaf4bcd6 */
    @Override
    public ByteArrayOutputStream generatePlanDetailFileData(BizRequest<CtoPlanExportParam> req) {
        ClassPathResource classPathResource = new ClassPathResource(templateFile);
        CtoPlanExportParam ctoPlanExportParam = req.getParam();
        String planId = ctoPlanExportParam.getPlanId();
        Date analysisDate = ctoPlanExportParam.getAnalysisDate();

        // 获取各sheet页的数据
        // 规划详表覆盖数据
        List<CtoReportIndicatorVO> ctoReportIndicatorVOS = ctoPlanReportService.generatePlanDetailReport(planId, false);
        // 核心领导覆盖数据
        List<CtoReportIndicatorVO> ctoLeaderReportIndicatorVOS = ctoPlanReportService.generatePlanDetailReport(planId, true);
        // AP覆盖数
        List<CtoReportApIndicatorVO> ctoReportApIndicatorVOS = ctoPlanReportApIndicatorService.ctoPlanApIndicatorSheetData(planId);
        // 名单盘活数据
        List<CtoReportItemIndicatorVO> ctoReportItemIndicatorVOS = ctoPlanReportService.generateActivationReport(planId, analysisDate);

        //将各sheet的数据放入参数传递到后续步骤
        ctoPlanExportParam.setReportSavant(JSON.toJSONString(ctoReportIndicatorVOS));
        ctoPlanExportParam.setReportLeader(JSON.toJSONString(ctoLeaderReportIndicatorVOS));
        ctoPlanExportParam.setReportActivity(JSON.toJSONString(ctoReportApIndicatorVOS));
        ctoPlanExportParam.setReportDetail(JSON.toJSONString(ctoReportItemIndicatorVOS));

        // 写入数据到模板并导出流
        try (InputStream inputStream = classPathResource.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            CtoPlanSheetDataHelper sheetDataHelper = new CtoPlanSheetDataHelper();
            // 填充第1、2个sheet
            sheetDataHelper.fillSheet(ctoReportIndicatorVOS, workbook, 0);
            sheetDataHelper.fillSheet(ctoLeaderReportIndicatorVOS, workbook, 1);
            // 填充第3个Sheet
            sheetDataHelper.fillSheet3(ctoReportApIndicatorVOS, workbook);
            // 填充第4个Sheet
            sheetDataHelper.fillSheet4(ctoReportItemIndicatorVOS, workbook);

            // 写入更新后的Workbook到新的文件
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream;
        } catch (IOException e) {
            log.error("Export CTO Report Error", e);
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "Export CTO Report Error");
        }
    }

    /* Ended by AICoder, pid:x183ck0954l2aae147a80b26904c903aeaf4bcd6 */

    @Override
    public Boolean publishPlan(BizRequest<CtoPlanDataParam> req) {
        // 取入参的数据
        CtoPlanDataParam param = req.getParam();
        String planInfoId = param.getCtoPlanInfoId();
        if (StringUtils.isBlank(planInfoId)) {
            return Boolean.FALSE;
        }
        param.setEmpNo(req.getEmpNo());
        // 初始化三张表(事业部、产品、执行计划)的数据
        CtoPlanPublishDTO ctoPlanPublishDTO = initPublishData(param);
        // 组装事业部和产品的数据
        generateOrgAndProductData(ctoPlanPublishDTO);
        // 准备插入三张表的数据
        prepareInsertData(ctoPlanPublishDTO);
        return Boolean.TRUE;
    }

    /**
     * 组装事业部和产品的数据
     * @param ctoPlanPublishDTO
     */
    private void generateOrgAndProductData(CtoPlanPublishDTO ctoPlanPublishDTO) {
        // 组装事业部的数据
        generateOrgFinishData(ctoPlanPublishDTO);
        // 组装产品的数据
        generateProductFinishData(ctoPlanPublishDTO);
    }

    // 初始化发布的数据
    CtoPlanPublishDTO initPublishData(CtoPlanDataParam param) {
        CtoPlanPublishDTO ctoPlanPublishDTO = new CtoPlanPublishDTO();
        // 定义日期格式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析日期字符串
        LocalDate startDate = LocalDate.parse(param.getScopeStart(), dateFormatter);
        LocalDate endDate = LocalDate.parse(param.getScopeEnd(), dateFormatter);

        // 转换为 LocalDateTime 并设置时间部分
        LocalDateTime startDateTime = LocalDateTime.of(startDate,
                LocalTime.of(NumberConstant.ZERO, NumberConstant.ZERO, NumberConstant.ZERO));
        LocalDateTime endDateTime = LocalDateTime.of(endDate,
                LocalTime.of(DateConstants.DATE_HOUR, DateConstants.DATE_MINUTES, DateConstants.DATE_SECOND));
        List<CtoPlanProductParam> productList = param.getProductList();
        List<CtoPlanOrgParam> orgList = param.getOrgList();
        List<CtoPlanProductFinishDO> productFinishDOList = CtoPlanDomainConverter.INSTANCE.paramsToProductDO(productList);
        if (CollectionUtils.isEmpty(productFinishDOList)) {
            productFinishDOList = new ArrayList<>();
        }
        List<CtoPlanOrgFinishDO> orgFinishDOList = CtoPlanDomainConverter.INSTANCE.paramsToOrgDO(orgList);
        ctoPlanPublishDTO.setCtoPlanInfoId(param.getCtoPlanInfoId());
        ctoPlanPublishDTO.setEmpNo(param.getEmpNo());
        ctoPlanPublishDTO.setPublishReceiver(param.getPublishReceiver());
        ctoPlanPublishDTO.setScopeStart(DateUtil.localDateTimeToDate(startDateTime));
        ctoPlanPublishDTO.setScopeEnd(DateUtil.localDateTimeToDate(endDateTime));
        ctoPlanPublishDTO.setOrgFinishDOList(orgFinishDOList);
        ctoPlanPublishDTO.setProductFinishDOList(productFinishDOList);
        List<CtoPlanExeReportDO> ctoPlanExeReportDOS = new ArrayList<>();
        int size = exeReportRepository.getByCtoPlanInfoId(param.getCtoPlanInfoId()).size();
        if (size == NumberConstant.ZERO) {
            ctoPlanExeReportDOS = generateExeReportData(startDateTime, endDateTime, ctoPlanPublishDTO);
        }
        ctoPlanPublishDTO.setExeWaitTime(DateUtil.getTomorrowZero());
        ctoPlanPublishDTO.setExeReportDOList(ctoPlanExeReportDOS);
        return ctoPlanPublishDTO;
    }

    /**
     * 准备插入三张表的数据
     * @param ctoPlanPublishDTO
     */
    void prepareInsertData(CtoPlanPublishDTO ctoPlanPublishDTO) {
        transactionTemplate.execute(st ->{
            try {
                // 处理事业部数据
                dealWithOrgFinish(ctoPlanPublishDTO);
                // 处理产品数据
                dealWithProductFinish(ctoPlanPublishDTO);
                // 处理执行报表的数据
                dealWithExeReport(ctoPlanPublishDTO);
            } catch (Exception e) {
                st.setRollbackOnly();
                log.error("CtoPlanActivityDataServiceImpl.publishPlan: " + CTO_PLAN_PUBLISH_FAILURE, e);
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage(CTO_PLAN_PUBLISH_FAILURE));
            }
            return true;
        });
    }

    void dealWithExeReport(CtoPlanPublishDTO ctoPlanPublishDTO) {
        // 修改执行报表中的接收人
        if (CollectionUtils.isNotEmpty(ctoPlanPublishDTO.getPublishReceiver())) {
            String receivers = JSON.toJSONString(ctoPlanPublishDTO.getPublishReceiver());
            List<CtoPlanExeReportDO> waitUpdateReceiverList = exeReportRepository.listExeReportWait(ctoPlanPublishDTO.getCtoPlanInfoId());
            if (CollectionUtils.isNotEmpty(waitUpdateReceiverList)) {
                List<CtoPlanExeReportDO> waitUpdateDatas = waitUpdateReceiverList.stream().peek(e -> e.setReceiver(receivers)).collect(Collectors.toList());
                exeReportRepository.batchUpdate(waitUpdateDatas);
            }
        }
        // 处理执行周期的数据
        exeReportRepository.batchInsert(ctoPlanPublishDTO.getExeReportDOList());
    }

    void dealWithProductFinish(CtoPlanPublishDTO ctoPlanPublishDTO) {
        List<CtoPlanProductFinishDO> productFinishDOList = ctoPlanPublishDTO.getProductFinishDOList();
        List<CtoPlanProductFinishDO> baseProductFinishDOs = productFinishRepository.listProductFinish(ctoPlanPublishDTO.getCtoPlanInfoId());
        // 如果没有此计划的数据，直接新增
        if (CollectionUtils.isEmpty(baseProductFinishDOs)) {
            productFinishRepository.batchInsert(productFinishDOList);
            return;
        }
        // 分离数据,核心领导和非核心领导,以入参里的数据去分离
        // 使用 partitioningBy 方法根据 type 字段进行分区
        Map<Boolean, List<CtoPlanProductFinishDO>> partitionedMap = productFinishDOList.stream()
                .collect(Collectors.partitioningBy(obj -> PeopleRoleLabelEnum.LEADER.isMe(obj.getEmployeeType())));

        // 获取核心领导类型的数据,以产品主键ID为维度
        List<CtoPlanProductFinishDO> leaderList = partitionedMap.get(Boolean.TRUE);
        Map<String, CtoPlanProductFinishDO> leaderMap = leaderList.stream()
                .collect(Collectors.toMap(CtoPlanProductFinishDO::getCtoPlanDetailProductId, obj -> obj, (v1, v2) -> v1));
        Map<String, CtoPlanProductFinishDO> baseLeaderMap = baseProductFinishDOs.stream()
                .collect(Collectors.toMap(CtoPlanProductFinishDO::getCtoPlanDetailProductId, obj -> obj, (v1, v2) -> v1));
        List<CtoPlanProductFinishDO> insertLeaderList = leaderList.stream().filter(e -> !baseLeaderMap.containsKey(e.getCtoPlanDetailProductId())).collect(Collectors.toList());
        // 插入核心领导的数据
        productFinishRepository.batchInsert(insertLeaderList);

        // 获取非核心领导类型的数据,以员工为维度
        List<CtoPlanProductFinishDO> savantList = partitionedMap.get(Boolean.FALSE);
        Map<String, CtoPlanProductFinishDO> savantMap = savantList.stream()
                .collect(Collectors.toMap(e -> empTypePath(e.getEmployeeNo(),e.getProductCode(), e.getOrgDivision()), obj -> obj, (v1, v2) -> v1));
        /* Started by AICoder, pid:w965be9a74q5e12143d70be5a0cd0c1223e589ea */
        Map<String, CtoPlanProductFinishDO> baseSavantMap = baseProductFinishDOs.stream()
                .collect(Collectors.toMap(e -> empTypePath(e.getEmployeeNo(),e.getProductCode(), e.getOrgDivision()), obj -> obj));
        List<CtoPlanProductFinishDO> insertSavantList = savantList.stream()
                .filter(e -> !baseSavantMap.containsKey(empTypePath(e.getEmployeeNo(),e.getProductCode(), e.getOrgDivision()))).collect(Collectors.toList());
        // 插入非核心领导的数据
        productFinishRepository.batchInsert(insertSavantList);
        /* Ended by AICoder, pid:w965be9a74q5e12143d70be5a0cd0c1223e589ea */
        String empNo = ctoPlanPublishDTO.getEmpNo();
        baseProductFinishDOs.forEach(e -> {
            CtoPlanProductFinishDO leaderDO = leaderMap.get(e.getCtoPlanDetailProductId());
            if (Objects.nonNull(leaderDO)) {
                setValue(e, leaderDO);
            }
            // 取不到非核心领导的数据，说明就要做删除的操作
            if (Objects.isNull(leaderDO) && PeopleRoleLabelEnum.LEADER.isMe(e.getEmployeeType())) {
                e.setEnabledFlag(BooleanEnum.N.getCode());
                e.setLastUpdatedBy(empNo);
            }
            CtoPlanProductFinishDO savantDO = savantMap.get(empTypePath(e.getEmployeeNo(),e.getProductCode(), e.getOrgDivision()));
            // 取不到非核心领导的数据，说明就要做删除的操作
            if (Objects.isNull(savantDO) && PeopleRoleLabelEnum.SAVANT.isMe(e.getEmployeeType())) {
                e.setEnabledFlag(BooleanEnum.N.getCode());
                e.setLastUpdatedBy(empNo);
            }
        });
        productFinishRepository.batchUpdate(baseProductFinishDOs);
    }

    public void setValue(CtoPlanProductFinishDO sourceDO, CtoPlanProductFinishDO productDO) {
        sourceDO.setCtoPlanDetailProductId(productDO.getCtoPlanDetailProductId());
        sourceDO.setEmployeeNo(productDO.getEmployeeNo());
        sourceDO.setEmployeeType(productDO.getEmployeeType());
        sourceDO.setProductCode(productDO.getProductCode());
        sourceDO.setOrgDivision(productDO.getOrgDivision());
        sourceDO.setAccountTarget(productDO.getAccountTarget());
        sourceDO.setActivityTarget(productDO.getActivityTarget());
        sourceDO.setSeq(productDO.getSeq());
        List<String> baseAccountNames = JSON.parseArray(sourceDO.getAccountName(), String.class);
        if (CollectionUtils.isNotEmpty(baseAccountNames)) {
            List<String> newAccountNames = JSON.parseArray(productDO.getAccountName(), String.class);
            baseAccountNames.removeAll(newAccountNames);
            // 为true说明两个值相等
            boolean isSame = baseAccountNames.isEmpty();
            if (!sourceDO.getOrgDivision().equals(productDO.getOrgDivision()) || !isSame) {
                sourceDO.setAccountFinish(productDO.getAccountFinish());
                sourceDO.setActivityFinish(productDO.getActivityFinish());
            }
        }
        sourceDO.setAccountCode(productDO.getAccountCode());
        sourceDO.setAccountName(productDO.getAccountName());
        sourceDO.setExeStatus(productDO.getExeStatus());
        sourceDO.setScopeStart(productDO.getScopeStart());
        sourceDO.setScopeEnd(productDO.getScopeEnd());
        sourceDO.setExeWaitTime(productDO.getExeWaitTime());
        sourceDO.setCreatedBy(productDO.getCreatedBy());
        sourceDO.setLastUpdatedBy(productDO.getLastUpdatedBy());
    }

    public String empTypePath(String empNo, String productCode, String orgDivision){
        return Joiner.on(CustCommConstants.COMMA_MINUS).useForNull(StringUtils.EMPTY).join(empNo, productCode, orgDivision);
    }

    /**
     * 构建产品维度的数据
     * @param ctoPlanPublishDTO
     */
    private void generateProductFinishData(CtoPlanPublishDTO ctoPlanPublishDTO) {
        List<CtoPlanProductFinishDO> productFinishDOList = ctoPlanPublishDTO.getProductFinishDOList();
        // 查询resource表中的有效数据
        List<ResourceCtoPersonDO> resourceCtoPersonDOS = resourceCtoRepository.listValidResourceCtoPerson();
        generateResourceCto(resourceCtoPersonDOS,productFinishDOList);
        String planInfoId = ctoPlanPublishDTO.getCtoPlanInfoId();
        String empNo = ctoPlanPublishDTO.getEmpNo();
        Date startDateTime = ctoPlanPublishDTO.getScopeStart();
        Date endDateTime = ctoPlanPublishDTO.getScopeEnd();
        Date exeWaitTime = ctoPlanPublishDTO.getExeWaitTime();
        productFinishDOList.forEach(e -> {
            e.setCtoPlanInfoId(planInfoId);
            e.setScopeStart(startDateTime);
            e.setScopeEnd(endDateTime);
            e.setExeWaitTime(exeWaitTime);
            // 从接口入参进来的默认就是领导,不要传参进来
            if (StringUtils.isBlank(e.getEmployeeType())) {
                e.setEmployeeType(PeopleRoleLabelEnum.LEADER.getCode());
            }
            // 对于产品维度，不管是入参的数据还是resource中的数据
            // 只要是变化的，finish都会为0
            e.setActivityFinish(NumberConstant.ZERO);
            e.setAccountFinish(NumberConstant.ZERO);
            e.setExeStatus(BooleanEnum.N.getCode());
            e.setCreatedBy(empNo);
            e.setLastUpdatedBy(empNo);
        });
    }

    private void generateResourceCto(List<ResourceCtoPersonDO> resourceCtoPersonDOS, List<CtoPlanProductFinishDO> productFinishDOList) {
        // 这里为什么要清空数据
        // 当productFinishDOList为空的时候，说明不需要产品的数据了
        // 那resource表的数据也不要同步到产品表中，同时产品表中的数据也要删除
        if (CollectionUtils.isEmpty(productFinishDOList)) {
            resourceCtoPersonDOS = new ArrayList<>();
            return;
        }
        // 使用 HashSet 存储 list2 中对象的 ID，以便快速查找
        HashSet<String> idsToRemove = productFinishDOList.stream()
                .map(CtoPlanProductFinishDO::getEmployeeNo)
                .collect(Collectors.toCollection(HashSet::new));
        // 移除 list1 中所有在 list2 中存在的对象
        resourceCtoPersonDOS.removeIf(obj -> idsToRemove.contains(obj.getEmployeeNo()));
        // 将产品方向的多个值转换成多条数据
        /* Started by AICoder, pid:c3b3ex74a75be3514b320acb60c50f1fd506b0d1 */
        List<ResourceCtoPersonDO> transformedList = resourceCtoPersonDOS.stream()
                .flatMap(personData -> {
                    String proType = personData.getProductDirection();
                    List<String> proTypeSplits = Arrays.asList(proType.split(","));
                    return proTypeSplits.stream().map(s -> {
                        ResourceCtoPersonDO newItem = CtoPlanDomainConverter.INSTANCE.copyDO(personData);
                        newItem.setProductDirection(s);
                        return newItem;
                    });
                }).collect(Collectors.toList());
        transformedList.forEach(e -> {
            // 这里为什么要一个个set
            // resource中的数据有可能在product表中存在
            // 因为是修改,所以必须将值先set进去
            CtoPlanProductFinishDO productFinishDO = new CtoPlanProductFinishDO();
            productFinishDO.setCtoPlanDetailProductId("0");
            productFinishDO.setEmployeeNo(e.getEmployeeNo());
            productFinishDO.setProductCode(e.getProductDirection());
            productFinishDO.setEmployeeType(PeopleRoleLabelEnum.SAVANT.getCode());
            productFinishDO.setOrgDivision(StringUtils.EMPTY);
            productFinishDO.setAccountName(StringUtils.EMPTY);
            productFinishDO.setAccountTarget(NumberConstant.ZERO);
            productFinishDO.setAccountFinish(NumberConstant.ZERO);
            productFinishDO.setActivityTarget(NumberConstant.ZERO);
            productFinishDO.setActivityFinish(NumberConstant.ZERO);
            productFinishDO.setExeStatus(BooleanEnum.N.getCode());
            productFinishDOList.add(productFinishDO);
        });
        /* Ended by AICoder, pid:c3b3ex74a75be3514b320acb60c50f1fd506b0d1 */
    }

    void dealWithOrgFinish(CtoPlanPublishDTO ctoPlanPublishDTO) {
        List<CtoPlanOrgFinishDO> orgFinishDOList = ctoPlanPublishDTO.getOrgFinishDOList();
        if (CollectionUtils.isEmpty(orgFinishDOList)) {
            return;
        }
        List<CtoPlanOrgFinishDO> baseOrgFinishDOs = orgFinishRepository.listOrgFinishByPlanInfoId(ctoPlanPublishDTO.getCtoPlanInfoId());
        // 如果数据库中没有此计划的数据,直接新增
        if (CollectionUtils.isEmpty(baseOrgFinishDOs)) {
            orgFinishRepository.batchInsert(orgFinishDOList);
            return;
        }
        // 解析数据
        /* Started by AICoder, pid:jeeeb3d7f3me5ed1487a0a6f4016e429fcf2b955 */
        // 使用 Set 存储 list1 和 list2 的 ID
        Set<String> newIds = orgFinishDOList.stream()
                .map(CtoPlanOrgFinishDO::getCtoPlanDetailOrgId)
                .collect(Collectors.toSet());
        Set<String> baseIds = baseOrgFinishDOs.stream()
                .map(CtoPlanOrgFinishDO::getCtoPlanDetailOrgId)
                .collect(Collectors.toSet());

        // 找出两个列表中 ID 相同的交集,那就要做修改
        // 应该以数据库中的数据为准去做交集,不然有些数据不做修改的话，就会丢失部分数据库中的数据
        List<CtoPlanOrgFinishDO> intersectionList = baseOrgFinishDOs.stream()
                .filter(obj -> newIds.contains(obj.getCtoPlanDetailOrgId()))
                .collect(Collectors.toList());
        // 解析新的数据和数据库中的数据
        if (CollectionUtils.isNotEmpty(intersectionList)) {
            generateOrgIntersection(intersectionList, orgFinishDOList);
            orgFinishRepository.batchUpdate(intersectionList);
        }

        // 找出 list1 中 ID 没有出现在 list2 的数据,那就要做新增
        List<CtoPlanOrgFinishDO> insertDOs = orgFinishDOList.stream()
                .filter(obj -> !baseIds.contains(obj.getCtoPlanDetailOrgId()))
                .collect(Collectors.toList());
        orgFinishRepository.batchInsert(insertDOs);

        // 找出 list2 中 ID 没有出现在 list1 的数据,那就要做删除
        List<CtoPlanOrgFinishDO> deleteDOs = baseOrgFinishDOs.stream()
                .filter(obj -> !newIds.contains(obj.getCtoPlanDetailOrgId()))
                .collect(Collectors.toList());
        deleteDOs.forEach(e -> {
            e.setLastUpdatedBy(ctoPlanPublishDTO.getEmpNo());
            e.setEnabledFlag(BooleanEnum.N.getCode());
        });
        orgFinishRepository.batchUpdate(deleteDOs);
        /* Ended by AICoder, pid:jeeeb3d7f3me5ed1487a0a6f4016e429fcf2b955 */
    }

    private void generateOrgIntersection(List<CtoPlanOrgFinishDO> intersection, List<CtoPlanOrgFinishDO> orgFinishDOList) {
        Map<String, CtoPlanOrgFinishDO> orgFinishDOMap = orgFinishDOList.stream()
                .collect(Collectors.toMap(CtoPlanOrgFinishDO::getCtoPlanDetailOrgId, obj -> obj, (v1, v2) -> v1));
        intersection.forEach(e -> {
            CtoPlanOrgFinishDO orgFinishDO = orgFinishDOMap.get(e.getCtoPlanDetailOrgId());
            e.setLocalCode(orgFinishDO.getLocalCode());
            e.setRanTarget(orgFinishDO.getRanTarget());
            e.setCcnTarget(orgFinishDO.getCcnTarget());
            e.setBnTarget(orgFinishDO.getBnTarget());
            e.setSnTarget(orgFinishDO.getSnTarget());
            e.setFmTarget(orgFinishDO.getFmTarget());
            e.setOrgId(orgFinishDO.getOrgId());
            e.setSeq(orgFinishDO.getSeq());
            List<String> baseAccountNames = JSON.parseArray(e.getAccountName(), String.class);
            if (CollectionUtils.isNotEmpty(baseAccountNames)) {
                List<String> newAccountNames = JSON.parseArray(orgFinishDO.getAccountName(), String.class);
                baseAccountNames.removeAll(newAccountNames);
                // 为true说明两个值相等
                boolean isSame = baseAccountNames.isEmpty();
                if (!e.getOrgDivision().equals(orgFinishDO.getOrgDivision()) || !isSame) {
                    e.setBnFinish(NumberConstant.ZERO);
                    e.setCcnFinish(NumberConstant.ZERO);
                    e.setRanFinish(NumberConstant.ZERO);
                    e.setFmFinish(NumberConstant.ZERO);
                    e.setSnFinish(NumberConstant.ZERO);
                }
            }
            e.setOrgDivision(orgFinishDO.getOrgDivision());
            e.setAccountName(orgFinishDO.getAccountName());
            e.setAccountCode(orgFinishDO.getAccountCode());
            e.setExeStatus(BooleanEnum.N.getCode());
            e.setExeWaitTime(orgFinishDO.getExeWaitTime());
            e.setLastUpdatedBy(orgFinishDO.getLastUpdatedBy());
        });
    }

    /**
     * 构建事业部维度的数据
     * @param ctoPlanPublishDTO
     */
    private void generateOrgFinishData(CtoPlanPublishDTO ctoPlanPublishDTO) {
        List<CtoPlanOrgFinishDO> orgFinishDOList = ctoPlanPublishDTO.getOrgFinishDOList();
        if (CollectionUtils.isEmpty(orgFinishDOList)) {
            return;
        }
        String planInfoId = ctoPlanPublishDTO.getCtoPlanInfoId();
        String empNo = ctoPlanPublishDTO.getEmpNo();
        Date startDateTime = ctoPlanPublishDTO.getScopeStart();
        Date endDateTime = ctoPlanPublishDTO.getScopeEnd();
        Date exeWaitTime = ctoPlanPublishDTO.getExeWaitTime();
        orgFinishDOList.forEach(e -> {
            e.setCtoPlanInfoId(planInfoId);
            e.setScopeStart(startDateTime);
            e.setScopeEnd(endDateTime);
            e.setExeWaitTime(exeWaitTime);
            setValue(e, empNo);
        });
    }

    public void setValue(CtoPlanOrgFinishDO orgFinishDO, String empNo) {
        orgFinishDO.setRanFinish(NumberConstant.ZERO);
        orgFinishDO.setRanLeaderFinish(NumberConstant.ZERO);
        orgFinishDO.setRanSvantFinish(NumberConstant.ZERO);
        orgFinishDO.setCcnFinish(NumberConstant.ZERO);
        orgFinishDO.setCcnLeaderFinish(NumberConstant.ZERO);
        orgFinishDO.setCcnSvantFinish(NumberConstant.ZERO);
        orgFinishDO.setBnFinish(NumberConstant.ZERO);
        orgFinishDO.setBnLeaderFinish(NumberConstant.ZERO);
        orgFinishDO.setBnSvantFinish(NumberConstant.ZERO);
        orgFinishDO.setFmFinish(NumberConstant.ZERO);
        orgFinishDO.setFmLeaderFinish(NumberConstant.ZERO);
        orgFinishDO.setFmSvantFinish(NumberConstant.ZERO);
        orgFinishDO.setSnFinish(NumberConstant.ZERO);
        orgFinishDO.setSnLeaderFinish(NumberConstant.ZERO);
        orgFinishDO.setSnSvantFinish(NumberConstant.ZERO);
        orgFinishDO.setExeStatus(BooleanEnum.N.getCode());
        orgFinishDO.setCreatedBy(empNo);
        orgFinishDO.setLastUpdatedBy(empNo);
    }

    /* Started by AICoder, pid:m76daq9226u0428140540bdfe09b12277c29884e */

    private List<CtoPlanExeReportDO> generateExeReportData(LocalDateTime startDate,
                                                           LocalDateTime endDate, CtoPlanPublishDTO ctoPlanPublishDTO) {
        String planInfoId = ctoPlanPublishDTO.getCtoPlanInfoId();
        List<String> publishReceiver = ctoPlanPublishDTO.getPublishReceiver();
        String empNo = ctoPlanPublishDTO.getEmpNo();
        List<CtoPlanExeReportDO> results = new ArrayList<>();

        YearMonth currentYearMonth = YearMonth.from(startDate.toLocalDate());
        YearMonth endYearMonth = YearMonth.from(endDate.toLocalDate());

        while (!currentYearMonth.isAfter(endYearMonth)) {
            LocalDate lastDayOfMonth = currentYearMonth.atEndOfMonth();
            LocalDateTime endOfMonth = LocalDateTime.of(lastDayOfMonth,
                    LocalTime.of(DateConstants.DATE_HOUR, DateConstants.DATE_MINUTES, DateConstants.DATE_SECOND));
            // Ensure the last entry does not exceed the original end date
            if (endOfMonth.isAfter(endDate)) {
                endOfMonth = endDate;
            }
            YearMonth nextYearMonth = currentYearMonth.plusMonths(1);
            LocalDate firstDayOfNextMonth = nextYearMonth.atDay(1);
            LocalDateTime startOfNextMonth = LocalDateTime.of(firstDayOfNextMonth, LocalTime.of(0, 0, 0));
            CtoPlanExeReportDO exeReportDO = new CtoPlanExeReportDO();
            exeReportDO.setCtoPlanInfoId(planInfoId);
            exeReportDO.setCtoPlanYear(String.valueOf(endOfMonth.getYear()));
            exeReportDO.setCtoPlanMonth(String.valueOf(endOfMonth.getMonthValue()));
            exeReportDO.setScopeStart(DateUtil.localDateTimeToDate(startDate));
            exeReportDO.setScopeEnd(DateUtil.localDateTimeToDate(endOfMonth));
            exeReportDO.setExeStatus(BooleanEnum.N.getCode());
            exeReportDO.setHasPush(BooleanEnum.N.getCode());
            exeReportDO.setExeWaitTime(DateUtil.localDateTimeToDate(startOfNextMonth));
            if (CollectionUtils.isNotEmpty(publishReceiver)) {
                exeReportDO.setReceiver(JSON.toJSONString(publishReceiver));
            }
            exeReportDO.setCreatedBy(empNo);
            exeReportDO.setLastUpdatedBy(empNo);
            results.add(exeReportDO);
            currentYearMonth = currentYearMonth.plusMonths(NumberConstant.ONE);
        }
        return results;
    }
    /* Ended by AICoder, pid:m76daq9226u0428140540bdfe09b12277c29884e */

    @Override
    public boolean updateCtoPlanExeReport(BizRequest<CtoPlanExportParam> req) {
        CtoPlanExportParam param = req.getParam();
        // 成功返回true
        // 设置回滚
        // 记录异常日志
        // 抛出业务异常
        return Boolean.TRUE.equals(transactionTemplate.execute(st -> {
            try {
                CtoPlanExeReportDO updateDO = new CtoPlanExeReportDO();
                updateDO.setRowId(param.getPlanExeReportRowId());
                updateDO.setFileKey(param.getFileToken());
                updateDO.setHasPush(BooleanEnum.Y.getCode());
                updateDO.setExeStatus(BooleanEnum.Y.getCode());
                updateDO.setExeFinishTime(new Date());
                updateDO.setLastUpdateDate(new Date());
                updateDO.setReportSavant(param.getReportSavant());
                updateDO.setReportLeader(param.getReportLeader());
                updateDO.setReportActivity(param.getReportActivity());
                updateDO.setReportDetail(param.getReportDetail());
                exeReportRepository.updateByPrimaryKeySelective(updateDO);
                return true;  // 成功返回true
            } catch (Exception e) {
                st.setRollbackOnly();  // 设置回滚
                log.error("[updateCtoPlanExeReport] 更新 CtoPlanExeReportDO 失败", e);  // 记录异常日志
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(TIPS_COMMON_BUSINESS_ERROR));  // 抛出业务异常
            }
        }));
    }


    /**
     * 查询规划详情的下钻数据
      * @param req
     * @return
     */
    @Override
    public LowCodePageRow<CtoPlanOrgDetailVO> queryOrgDetail(BizRequest<PageQuery<CtoPlanOrgDetailParam>> req) {
        CtoPlanOrgDetailParam detailParam = req.getParam().getParam();
        boolean isCn = StringUtils.equals(req.getLangId(), LANGUAGE_ZH_CN);
        if (StringUtils.isBlank(detailParam.getRowId())) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        List<CtoPlanOrgDetailVO> detailVOS = new ArrayList<>();
        CtoPlanDetailDTO detailDTO;
        List<String> activityIdList = new ArrayList<>();
        List<String> accountCodes = new ArrayList<>();
        Set<String> relationPeopleCodes = new HashSet<>();
        if (DETAIL_ORG.equals(detailParam.getDataType())) {
            detailDTO = orgFinishRepository.queryOrgDetail(detailParam.getRowId());
            // 将对应产品的活动ID转到activityId，方便后面的组装数据
            if (Objects.isNull(detailDTO)) {
                return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
            }
            String activityIds;
            switch (detailParam.getProductType()) {
                case RAN:
                    activityIds = detailDTO.getRanActivityIds();
                    break;
                case CCN:
                    activityIds = detailDTO.getCcnActivityIds();
                    break;
                case BN:
                    activityIds = detailDTO.getBnActivityIds();
                    break;
                case SN:
                    activityIds = detailDTO.getSnActivityIds();
                    break;
                case FM:
                    activityIds = detailDTO.getFmActivityIds();
                    break;
                default:
                    activityIds = "";
                    break;
            }
            // 拿出活动id
            activityIdList = JSON.parseArray(activityIds,  String.class);
            // 解析accountCode
            accountCodes = JSON.parseArray(detailDTO.getAccountCodes(), String.class);
            // 拿planInfoId查询productFinish表得到employee
            List<CtoPlanProductFinishDO> productFinishDOList = productFinishRepository.listProductFinishByPlanId(detailDTO.getCtoPlanInfoId());
            relationPeopleCodes = productFinishDOList.stream().map(CtoPlanProductFinishDO::getEmployeeNo).collect(Collectors.toSet());
        }
        if (CtoPlanConstants.DETAIL_PRODUCT.equals(detailParam.getDataType())) {
            detailDTO = productFinishRepository.queryProductDetail(detailParam.getRowId());
            if (Objects.isNull(detailDTO)) {
                return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
            }
            activityIdList = JSON.parseArray(detailDTO.getActivityIds(), String.class);
            // 解析accountCode
            accountCodes = JSON.parseArray(detailDTO.getAccountCodes(), String.class);
            // 拿到employee
            relationPeopleCodes.add(detailDTO.getEmployeeNo());
        }
        // 根据活动id查询活动表的数据
        // 【2】打包活动需要的信息
        ActivityDataSourceQuery sourceQuery = new ActivityDataSourceQuery();
        sourceQuery.setActivityRowIdList(activityIdList);
        sourceQuery.setNeedActivityCustomerInfo(true);
        sourceQuery.setNeedActivityZtePeople(true);
        StandardActivityDetailDataSource ds = activityInfoListQueryService.getStandardActivityDetailDataSource(sourceQuery);
        Set<String> accountCodeSet = new HashSet<>(accountCodes);
        // 活动信息列表
        List<ActivityInfoDO> activityInfoList = ds.getActivityInfoList();
        Set<String> finalRelationPeopleCodes = relationPeopleCodes;
        AtomicInteger num = new AtomicInteger(0);
        activityInfoList.forEach(activityInfo -> {
            CtoPlanOrgDetailVO orgDetailVO = new CtoPlanOrgDetailVO();
            // 序号
            orgDetailVO.setSerializeNum(num.incrementAndGet());
            orgDetailVO.setActivityId(activityInfo.getRowId());
            // 活动名称
            orgDetailVO.setActivityName(activityInfo.getActivityTitle());
            orgDetailVO.setActivityType(activityInfo.getActivityType());
            // 活动结束时间
            orgDetailVO.setActivityEndDate(DateUtils.getDateFormatDay(activityInfo.getEndTime()));
            // 客户信息
            List<ActivityCustomerInfoDO> activityCustomerInfoDOS = ds.getCustomerInfoMap().get(activityInfo.getRowId());
            Map<String, String> accountCodeAndLocalMap = this.getAccountCodeLocalName(activityCustomerInfoDOS, isCn);

            // 取出交集的accountCode
            Set<String> accountCodeIntersection = new HashSet<>();
            // product维度不取交集，直接取customerInfo里面的客户
            if (DETAIL_PRODUCT.equals(detailParam.getDataType())) {
                activityCustomerInfoDOS.stream().map(ActivityCustomerInfoDO::getMktCode).forEach(accountCodeIntersection::add);
            }
            // org维度要取交集
            if (DETAIL_ORG.equals(detailParam.getDataType())) {
                for (ActivityCustomerInfoDO obj : activityCustomerInfoDOS) {
                    if (accountCodeSet.contains(obj.getMktCode())) {
                        accountCodeIntersection.add(obj.getMktCode());
                    }
                }
            }
            String customerNames = accountCodeIntersection.stream().map(accountCodeAndLocalMap::get).collect(Collectors.joining(CharacterConstant.PAUSE_MARK));
            // account客户名称
            orgDetailVO.setAccountName(customerNames);
            // 达成数
            orgDetailVO.setFinishNum(accountCodeIntersection.size());
            List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOS =
                    ds.fetchActivityZtePeople(activityInfo.getRowId(), ActivityPeopleTypeEnum.allParticipantsType());
            // 握手领导
            Set<String> ztePeopleCodes = activityRelationZtePeopleDOS.stream()
                    .map(ActivityRelationZtePeopleDO::getPeopleCode)
                    .filter(finalRelationPeopleCodes::contains)
                    .collect(Collectors.toSet());
            Map<String, PersonInfoDTO> empInfosByShortNo = hrmUserCenterSearchService
                    .fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithBizReq(req, e -> ztePeopleCodes)).getBo();
            List<String> employeeCodeName = new ArrayList<>();
            ztePeopleCodes.forEach(e -> {
                PersonInfoDTO employee = empInfosByShortNo.getOrDefault(e, new PersonInfoDTO());
                employeeCodeName.add(employee.personNameAndEmpNo());
            });
            // 握手领导
            String codeNameStr = String.join(CharacterConstant.PAUSE_MARK, employeeCodeName);
            orgDetailVO.setCtoLeader(codeNameStr);
            detailVOS.add(orgDetailVO);
        });
        return PageRowsUtil.buildLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize(), detailVOS.size(), detailVOS);
    }

    /**
     * 根据AccountCode和LocalCode获取国家-客户名称
     *
     * @param activityCustomerInfoDOS
     * @param isCn
     * @return
     */
    Map<String, String> getAccountCodeLocalName(List<ActivityCustomerInfoDO> activityCustomerInfoDOS, boolean isCn) {
        // 获取MDM数据`
        Set<String> localCodes = activityCustomerInfoDOS.stream().map(ActivityCustomerInfoDO::getLocalCode).collect(Collectors.toSet());
        Map<String, AreaDataModel> areaMap = areaDataCacheClient.fetchAllCache(AreaTypeEnum.COUNTRY, localCodes);
        Map<String, String> result = new HashMap<>();
        for (ActivityCustomerInfoDO info : activityCustomerInfoDOS) {
            String mktCode = info.getMktCode();
            String localCode = info.getLocalCode();
            // 通过localCode在areaMap中查找对应的AreaDataModel，并获取其areaNameZh字段
            AreaDataModel areaDataModel = areaMap.getOrDefault(localCode, new AreaDataModel());
            String name = areaDataModel.getAreaNameEn();
            if (isCn){
                name = areaDataModel.getAreaNameZh();
            }
            String accountShowName = name + CharacterConstant.SHORT_BAR_EN + info.getMktName();
            result.put(mktCode, accountShowName);
        }
        return result;
    }

    @Override
    public LowCodePageRow<CtoPlanOrgDetailVO> queryKeyAccount(BizRequest<PageQuery<CtoPlanOrgDetailParam>> req) {
        CtoPlanOrgDetailParam detailParam = req.getParam().getParam();
        if (StringUtils.isBlank(detailParam.getRowId())) {
            return PageRowsUtil.buildEmptyLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize());
        }
        List<CtoPlanOrgDetailVO> detailVOS = new ArrayList<>();
        CtoPlanDetailDTO detailDTO = productFinishRepository.queryProductDetail(detailParam.getRowId());
        List<String> activityIdList = JSON.parseArray(detailDTO.getAccountActivityId(), String.class);
        // 解析accountCode
        List<String> accountCodes = JSON.parseArray(detailDTO.getAccountCodes(), String.class);
        List<String> accountNames = JSON.parseArray(detailDTO.getAccountNames(), String.class);
        /* Started by AICoder, pid:3be18fff56517d514cc00b28705fc80183b3f6de */
        Map<String, String> accountNameMap = IntStream.range(0, Math.min(accountCodes.size(), accountNames.size()))
                .boxed()
                .collect(Collectors.toMap(accountCodes::get, accountNames::get));

        /* Ended by AICoder, pid:3be18fff56517d514cc00b28705fc80183b3f6de */
        // 【2】打包活动需要的信息
        ActivityDataSourceQuery sourceQuery = new ActivityDataSourceQuery();
        sourceQuery.setActivityRowIdList(activityIdList);
        sourceQuery.setNeedActivityCustomerInfo(true);
        StandardActivityDetailDataSource ds = activityInfoListQueryService.getStandardActivityDetailDataSource(sourceQuery);
        Set<String> accountCodeSet = new HashSet<>(accountCodes);
        // 活动信息列表
        List<ActivityInfoDO> activityInfoList = ds.getActivityInfoList();
        AtomicInteger num = new AtomicInteger(0);
        Set<String> accountCodeIntersection = new HashSet<>();
        activityInfoList.forEach(activityInfo -> {
            CtoPlanOrgDetailVO orgDetailVO = new CtoPlanOrgDetailVO();
            // 序号
            orgDetailVO.setSerializeNum(num.incrementAndGet());
            // 活动名称
            orgDetailVO.setActivityName(activityInfo.getActivityTitle());
            // 活动结束时间
            orgDetailVO.setActivityEndDate(DateUtils.getDateFormatDay(activityInfo.getEndTime()));
            // 客户信息
            List<ActivityCustomerInfoDO> activityCustomerInfoDOS = ds.getCustomerInfoMap().get(activityInfo.getRowId());

            // 取出交集的accountCode
            Set<String> accountCodeDetailntersection = new HashSet<>();
            for (ActivityCustomerInfoDO obj : activityCustomerInfoDOS) {
                if (accountCodeSet.contains(obj.getMktCode())) {
                    accountCodeDetailntersection.add(obj.getMktCode());
                    accountCodeIntersection.add(obj.getMktCode());
                }
            }
            String customerNames = accountCodeDetailntersection.stream().map(accountNameMap::get).collect(Collectors.joining(CharacterConstant.PAUSE_MARK));
            // account客户名称
            orgDetailVO.setAccountName(customerNames);
            detailVOS.add(orgDetailVO);
        });
        // 用核心领导去掉打包后的客户
        Set<String> waitCoverageAccountCode = new HashSet<>();
        for (String s : accountCodeSet) {
            if (!accountCodeIntersection.contains(s)) {
                waitCoverageAccountCode.add(s);
            }
        }
        String waitCoverageAccountName = waitCoverageAccountCode.stream().map(accountNameMap::get).collect(Collectors.joining(CharacterConstant.PAUSE_MARK));
        // 待覆盖的客户
        if (!detailVOS.isEmpty()){
            detailVOS.get(NumberConstant.ZERO).setWaitCoverageAccountName(String.join(CharacterConstant.PAUSE_MARK, waitCoverageAccountName));
        }
        return PageRowsUtil.buildLowCodePageRow(req.getParam().getPageNo(), req.getParam().getPageSize(), detailVOS.size(), detailVOS);

    }
}
