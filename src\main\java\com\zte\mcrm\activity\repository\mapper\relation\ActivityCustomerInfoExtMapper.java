package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.relation.param.BaseActivityCustomerQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface ActivityCustomerInfoExtMapper extends ActivityCustomerInfoMapper {
    List<ActivityCustomerInfoDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);

    /**
     * 通用基础拓展活动客户信息查询
     * @param query
     * @return
     */
    List<ActivityCustomerInfoDO> baseActivityCustomerInfoQuery(BaseActivityCustomerQuery query);

    /**
     * 根据活动id查询客户参与信息
     * @param activityRowIds
     * @return
     */
    List<ActivityCustomerInfoDO> queryAllByActivityRowIds(@Param("activityRowIds") List<String> activityRowIds);


    /**
     * 根据orderHeaderId获取信息
     * @param activityRowIds
     * @return
     */
    List<ActivityCustomerInfoDO> getActivityCustomerListByActivityRowId(@Param("activityRowIds") Set<String> activityRowIds);

    /**
     * 查找用户最近创建的活动中使用的客户
     *
     * @param param 查询人工号
     * @return {@link List< ActivityCustomerInfoDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:52
     */
    List<ActivityCustomerInfoDO> selectRecentlyCustomerByUser(ActivityRecentlySearchParam param);


    /**
     * 批量插入
     *
     * @param list 项目列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list")List<ActivityCustomerInfoDO> list);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 查询所有包含无效数据
     * @param activityRowId 活动Id
     * 增加enabled_flag = 'Y'，推送ES不需要包含无效客户
     * @return java.util.List<ActivityCustomerInfoDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityCustomerInfoDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    List<ActivityCustomerInfoDO> getErrorDataList();
    List<ActivityCustomerInfoDO> queryActivityMtkMtoIsnull(@Param("rowId")String rowId);

    List<ActivityCustomerInfoDO> queryActivityLocalIsnull(@Param("startRow") int startRow, @Param("numberOneBatch") int numberOneBatch);
    int updateLeader();

    int batchUpdateByPrimaryKey(List<ActivityCustomerInfoDO> updateList);

    /**
     * 根据 id 批量查询
     *
     * @param ids
     * @return {@link List< ActivityCustomerInfoDO>}
     * <AUTHOR>
     * @date 2024/4/28 下午9:43
     */
    List<ActivityCustomerInfoDO> selectListByIds(@Param("ids") List<String> ids);

    /**
     * 查询 code 不满足条件的数据，客户融合活动迁移异常数据，慎用
     *
     * @param codes
     * @return {@link List< ActivityCustomerInfoDO>}
     * <AUTHOR>
     * @date 2024/4/28 下午10:32
     */
    List<ActivityCustomerInfoDO> selectListByNoInMtoMktCodes(@Param("codes") List<String> codes, int start, int limit);

    /**
     * 根据mktCode查询mktName
     * @param codes
     * @return
     */
    List<ActivityCustomerInfoDO> listMktNameByMktCode(@Param("codes") Collection<String> codes);

    /**
     * 根据客户三级结构编码查询活动id列表
     * @param mtoCode
     * @param mktCode
     * @param customerCode
     * @return
     */
    List<String> getActivityRowIdsByCustomerInfo(@Param("mtoCode") String mtoCode,@Param("mktCode") String mktCode,
                                                 @Param("customerCode") String customerCode);

}
