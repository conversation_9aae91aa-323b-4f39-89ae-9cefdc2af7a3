package com.zte.mcrm.activity.application.exhibition.business;

import org.apache.commons.lang3.StringUtils;

import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDrillDownDTO;

/**
 * 展会分析 -mto比较器
 * <AUTHOR>
 *
 */
public class ExhibitionMtoComparator extends ExhibitionBaseComparator {

    @Override
    public int compare(ExhibitionAnalysisDrillDownDTO o1, ExhibitionAnalysisDrillDownDTO o2) {
        int compare = StringUtils.compare(o1.getZteBusinessCategory(),o2.getZteBusinessCategory());
        return compare!=0?compare:super.compare(o1, o2);
    }
}
