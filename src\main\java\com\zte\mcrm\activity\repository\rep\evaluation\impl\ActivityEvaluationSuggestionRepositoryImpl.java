package com.zte.mcrm.activity.repository.rep.evaluation.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.evaluation.ActivityEvaluationSuggestionExtMapper;
import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationSuggestionDO;
import com.zte.mcrm.activity.repository.rep.evaluation.ActivityEvaluationSuggestionRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
@Service
public class ActivityEvaluationSuggestionRepositoryImpl implements ActivityEvaluationSuggestionRepository {
    @Autowired
    private ActivityEvaluationSuggestionExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityEvaluationSuggestionDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityEvaluationSuggestionDO record) {
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityEvaluationSuggestionDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

}