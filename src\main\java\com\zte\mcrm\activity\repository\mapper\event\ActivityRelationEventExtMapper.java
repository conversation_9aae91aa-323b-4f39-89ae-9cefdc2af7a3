package com.zte.mcrm.activity.repository.mapper.event;

import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 客户活动关联事件 数据访问接口类 
 * <AUTHOR>
 * @date 2023/06/29 
 */
@Mapper
public interface ActivityRelationEventExtMapper extends ActivityRelationEventMapper {

    /**
     * 批量更新
     *
     * @param updateList 待更新列表
     * @return int
     * <AUTHOR>
     * date: 2023/7/1 10:59
     */
    int batchUpdateByPrimaryKey(@Param(value = "updateList")List<ActivityRelationEventDO> updateList);

    /**
     * 按条件查询
     *
     * @param query 按条件查询
     * @return: List<ActivityRelationEventDO>
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    List<ActivityRelationEventDO> getListByQuery(ActivityRelationEventQuery query);

    /**
     * 根据活动Id批次删除
     *
     * @param operator          用户工号
     * @param activityIds 活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:47
     */
    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);
}