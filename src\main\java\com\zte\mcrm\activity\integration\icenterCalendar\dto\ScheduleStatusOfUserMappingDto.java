package com.zte.mcrm.activity.integration.icenterCalendar.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 日程状态映射dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ScheduleStatusOfUserMappingDto {
    // 无注释的其他字段业务暂时用不到，以后谁用谁加即可
    private String createBy;
    private String createTime;
    private String del;
    private String id;
    /**
     * 日程ID
     */
    private String inviteCalendarId;
    /**
     * 参会人员工编号
     */
    private String inviteNo;
    /**
     * 参会人日程状态：0-待处理 1-接受 2-拒绝
     */
    private String inviteStatus;
    private String inviteType;
    private String lastUpdatedBy;
    private String lastUpdatedTime;
    private String recurrenceEndTime;
    private String recurrenceRule;
    private String recurrenceStartTime;
    private String recurrenceType;
    /**
     * 备注
     */
    private String remark;
    private String sendCalendarId;
    private String sendNo;
}
