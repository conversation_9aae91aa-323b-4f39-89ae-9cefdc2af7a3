package com.zte.mcrm.activity.repository.rep.sample.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.sample.SamplePointRelationAttachmentExtMapper;
import com.zte.mcrm.activity.repository.model.sample.SamplePointRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointRelationAttachmentRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 样板点关联的附件信息
 *
 * <AUTHOR>
 */
@Component
public class SamplePointRelationAttachmentRepositoryImpl implements SamplePointRelationAttachmentRepository {
    @Resource
    private SamplePointRelationAttachmentExtMapper samplePointRelationAttachmentExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<SamplePointRelationAttachmentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (SamplePointRelationAttachmentDO infoDO : recordList) {
            if (StringUtils.isBlank(infoDO.getRowId())) {
                infoDO.setRowId(keyIdService.getKeyId());
            }
            samplePointRelationAttachmentExtMapper.insertSelective(infoDO);
        }
        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(SamplePointRelationAttachmentDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return samplePointRelationAttachmentExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteBySamplePointRowId(String samplePointRowId) {
        if (StringUtils.isBlank(samplePointRowId)) {
            return NumberConstant.ZERO;
        }
        return samplePointRelationAttachmentExtMapper.deleteBySamplePointRowId(samplePointRowId);
    }

    @Override
    public Map<String,List<SamplePointRelationAttachmentDO>> queryAttachmentBySamplePointRowId(List<String> samplePointRowIdList) {
        return CollectionUtils.isEmpty(samplePointRowIdList) ? Collections.emptyMap() :
                samplePointRelationAttachmentExtMapper.getAttachmentBySamplePointRowIds(samplePointRowIdList).stream().collect(
                        Collectors.groupingBy(SamplePointRelationAttachmentDO::getSamplePointRowId));
    }
}