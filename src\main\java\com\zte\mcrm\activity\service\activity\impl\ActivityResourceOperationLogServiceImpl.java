package com.zte.mcrm.activity.service.activity.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationBizTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.log.ActivityResourceOperationLogExtMapper;
import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.log.ActivityResourceOperationLogRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.service.activity.ActivityResourceOperationLogService;
import com.zte.mcrm.activity.service.activity.convert.ActivityResourceOperationLogConvert;
import com.zte.mcrm.activity.service.activity.param.ActivityResourceOperationLogParam;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityResourceOperationLogQueryParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityResourceOperationLogVO;
import com.zte.mcrm.adapter.EmployeeAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：2024/11/18 20:58
 * @description ：活动资源操作记录业务实现层
 */
@Service
@Slf4j
public class ActivityResourceOperationLogServiceImpl implements ActivityResourceOperationLogService {

    @Autowired
    private ActivityResourceOperationLogExtMapper extMapper;

    @Autowired
    private ActivityRelationAttachmentRepository attachmentRepository;

    @Autowired
    private ActivityResourceOperationLogRepository operationLogRepository;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    /**
     * 批量保存活动资源操作日志
     *
     * @param  operationLogParams 活动资源操作日志入参列表
     * @return 保存结果
     */
    @Override
    public Integer saveActivityResourceOperationLog(List<ActivityResourceOperationLogParam> operationLogParams) {
        if (CollectionUtils.isEmpty(operationLogParams)) {
            return 0;
        }
        List<ActivityResourceOperationLogDO> operationLogDOList = operationLogParams
                .stream().map(item -> {
                    ActivityResourceOperationLogDO logDO = new ActivityResourceOperationLogDO();
                    logDO.setActivityRowId(item.getActivityId());
                    logDO.setBizType(item.getBizType());
                    logDO.setBizRelatedId(item.getBizRelatedId());
                    logDO.setOperationType(item.getOperationType());
                    logDO.setOperationBefore(item.getOperationBefore());
                    logDO.setOperationBeforeDesc(item.getOperationBeforeDesc());
                    logDO.setOperationAfter(item.getOperationAfter());
                    logDO.setOperationAfterDesc(item.getOperationAfterDesc());
                    return logDO;
                })
                .collect(Collectors.toList());
        return operationLogRepository.batchInsert(operationLogDOList);
    }

    /**
     * 查询活动资源操作记录列表
     *
     * @param bizRequest 查询参数
     * @return 活动资源操作记录列表
     */
    @Override
    /* Started by AICoder, pid:117ebrdfcf5747f14c230a6390db52454d993128 */
    public PageRows<ActivityResourceOperationLogVO> getActivityResourceOperationLogList(
            BizRequest<PageQuery<ActivityResourceOperationLogQueryParam>> bizRequest) {
        PageQuery<ActivityResourceOperationLogQueryParam> pageQuery = bizRequest.getParam();
        if (!pageQuery.validatePage()) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        ActivityResourceOperationLogQueryParam param = pageQuery.getParam();
        // 分页查询数据
        PageInfo<ActivityResourceOperationLogDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
                .doSelectPageInfo(() -> extMapper.selectByBizRelatedId(param.getBizRelatedId()));
        // 如果查询结果为空，返回空的分页对象
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        List<ActivityResourceOperationLogVO> operationLogVOList = this.assembleOperationLogVO(pageInfo.getList());
        // 构建并返回分页结果
        return PageRowsUtil.buildPageRow(
                pageInfo.getPageNum(),
                pageInfo.getPageSize(),
                pageInfo.getTotal(),
                operationLogVOList
        );
    }

    /**
     * 组装谈参操作日志出参
     *
     * @param operationLogDOList 谈参操作日志实体
     * @return 谈参操作日志出参
     */
    private List<ActivityResourceOperationLogVO> assembleOperationLogVO(List<ActivityResourceOperationLogDO> operationLogDOList) {
        // 转换为VO列表
        List<ActivityResourceOperationLogVO> operationLogVOList = ActivityResourceOperationLogConvert.convert2VO(operationLogDOList);
        // 操作人：姓名+工号
        operationLogVOList
                .forEach(operationLogVO ->
                        operationLogVO.setCreatedBy(employeeAdapter.getEmployeeNameNoByShortNo(operationLogVO.getCreatedBy())));
        return operationLogVOList;
    }
    /* Ended by AICoder, pid:117ebrdfcf5747f14c230a6390db52454d993128 */
}
