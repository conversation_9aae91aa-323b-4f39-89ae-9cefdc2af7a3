package com.zte.mcrm.activity.repository.rep.relation.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.relation.ActivityRelationSolutionExtMapper;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationSolutionRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 活动相关的方案信息
 *
 * <AUTHOR>
 */
@Component
public class ActivityRelationSolutionRepositoryImpl implements ActivityRelationSolutionRepository {
    @Resource
    private ActivityRelationSolutionExtMapper activityRelationSolutionExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivityRelationSolutionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityRelationSolutionDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            activityRelationSolutionExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationSolutionDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return activityRelationSolutionExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationSolutionDO> queryAllSolutionForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityRelationSolutionExtMapper.queryAllSolutionForActivity(activityRowId);
    }

    /**
     * 批量插入数据
     *
     * @param list
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchInsert(List<ActivityRelationSolutionDO> list) {
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        list.forEach(this::setDefaultValue);
        activityRelationSolutionExtMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 批量修改数据
     *
     * @param list 列表数据
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchUpdate(List<ActivityRelationSolutionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        for (ActivityRelationSolutionDO solutionDO : list) {
            solutionDO.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            solutionDO.setLastUpdateDate(new Date());
        }
        return activityRelationSolutionExtMapper.batchUpdateByPrimaryKeySelective(list);
    }

    /**
     * 设置默认值
     *
     * @param solutionDO 实体类
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    private void setDefaultValue(ActivityRelationSolutionDO solutionDO) {
        solutionDO.setRowId(Optional.ofNullable(solutionDO.getRowId()).orElse(keyIdService.getKeyId()));
        solutionDO.setCreatedBy(Optional.ofNullable(solutionDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        solutionDO.setLastUpdatedBy(Optional.ofNullable(solutionDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        solutionDO.setSolutionOrder(Optional.ofNullable(solutionDO.getSolutionOrder()).orElse(NumberConstant.ONE));
        solutionDO.setCreationDate(new Date());
        solutionDO.setLastUpdateDate(new Date());
        solutionDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return activityRelationSolutionExtMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return activityRelationSolutionExtMapper.deleteByRowIds(operator, rowIds);
    }

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效方案
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationSolutionDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityRelationSolutionDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityRelationSolutionExtMapper.queryAllActivityWithNotEnable(activityRowId);
    }
}
