package com.zte.mcrm.activity.repository.mapper.people;

import java.util.List;
import java.util.Set;

import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.service.activitylist.param.ActivityRelationZtePeopleQuery;
import com.zte.mcrm.activity.service.model.people.ActivityRelationZtePeople;
import com.zte.mcrm.activity.service.resource.vo.ActivityTimesCountVO;

@Mapper
public interface ActivityRelationZtePeopleExtMapper extends ActivityRelationZtePeopleMapper {

    /**
     * 获取活动所有我司人员
     *
     * @param activityRowId
     * @return
     */
    List<ActivityRelationZtePeopleDO> queryAllZtePeopleForActivity(@Param("activityRowId") String activityRowId);

    /**
     * 根据条件获取我司人员
     * @param query
     * @return
     */
    List<ActivityRelationZtePeopleDO> queryInfoList(ActivityRelationZtePeopleQuery query);

    /**
     * 查找用户最近创建的活动中使用的中兴参与人
     *
     * @param param 查询入参
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:57
     */
    List<ActivityRelationZtePeopleDO> selectRecentlyZtePeopleByUser(ActivityRecentlySearchParam param);

    /**
     * 根据工号统计活动参与数量
     *
     * @param codeList              工号
     * @param activityStatusList    活动状态
     * @return {@link List<ActivityTimesCountVO>}
     * <AUTHOR>
     * @date 2023/5/22 下午2:29
     */
    List<ActivityTimesCountVO> selectCountByPeopleCode(@Param("codeList") List<String> codeList,
                                                       @Param("activityStatusList") List<String> activityStatusList);

    /**
     * 批量插入
     *
     * @param list 项目列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list")List<ActivityRelationZtePeopleDO> list);

    /**
     * 根据活动id和活动角色查询参与人数据
     *
     * @param activityRowId
     * @param codeList
     * @return {@link List}<{@link ActivityRelationZtePeople}>
     */
    List<ActivityRelationZtePeopleDO> queryByActivityRowIdAndPeopleType(@Param("activityRowId") String activityRowId, @Param("codeList") List<String> codeList);

    /**
     * 根据活动ids和活动角色查询参与人数据
     *
     * @param activityRowIds
     * @param codeList
     * @return {@link List}<{@link ActivityRelationZtePeople}>
     */
    List<ActivityRelationZtePeopleDO> queryByActivityRowIdsAndPeopleType(@Param("activityRowIds") List<String> activityRowIds, @Param("codeList") List<String> codeList);


    /**
     * 中兴联系人
     * @param activityRowIds
     * @return
     */
    List<ActivityRelationZtePeopleDO> getZtePeopleListByActivityRowIds(@Param("activityRowIds") Set<String> activityRowIds);

    /**
     * 批量动态更新
     *
     * @param updateList    更新列表
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/23 10:32
     */
    int batchUpdateByPrimaryKey(@Param(value = "updateList") List<ActivityRelationZtePeopleDO> updateList);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效联系人
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationZtePeopleDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    /**
     * 查询没有名字的联系人
     *
     * @param activityRowId
     * @param rowId
     * @param limit
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2024/1/4 上午12:56
     */
    List<ActivityRelationZtePeopleDO> selectNoNameList(@Param("activityRowId") String activityRowId, @Param("rowId") String rowId, @Param("limit") int limit);

    /**
     * 根据迁移活动主键和专家编码,获取需要打专家标签的记录id(每次查询1000条)
     * @param activityRowIds
     * @param expertCodes
     * @return
     */
    List<String> getUpdateSaventLabelRowIds(@Param("activityRowIds") List<String> activityRowIds, @Param("expertCodes") List<String> expertCodes);

    /**
     * 根据主键id打赏专家标签
     * @param rowIds
     */
    int updateSaventLabel(@Param("rowIds") List<String> rowIds);
}