package com.zte.mcrm.activity.repository.model.sample;

import java.math.BigDecimal;
import java.util.Date;

/**
 * table:sample_visit_fee_config -- 
 */
public class SampleVisitFeeConfigDO {
    /** 主键 */
    private String rowId;

    /** 是否海外，参考BooleanEnum枚举。Y-海外，N-国内 */
    private String international;

    /** 参观模式。快码类型：SAMPLE_VISIT_MODEL */
    private String visitModel;

    /** 参观模式名称 */
    private String visitModelName;

    /** 费用 */
    private BigDecimal visitFee;

    /** 费用币种 */
    private String visitFeeCurrency;

    /** 申请方（需求提出方）费用规定。快码类型：SAMPLE_VISIT_FEE_REQUIRE */
    private String applyFeeRequire;

    /** 接收方（样板点维护方）费用规定。快码类型：SAMPLE_VISIT_FEE_REQUIRE */
    private String receiveFeeRequire;

    /** 显示顺序 */
    private Integer showOrder;

    /** 版本 */
    private Integer version;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最后更新人 */
    private String lastUpdatedBy;

    /** 记录最后更新时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。见枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getInternational() {
        return international;
    }

    public void setInternational(String international) {
        this.international = international == null ? null : international.trim();
    }

    public String getVisitModel() {
        return visitModel;
    }

    public void setVisitModel(String visitModel) {
        this.visitModel = visitModel == null ? null : visitModel.trim();
    }

    public String getVisitModelName() {
        return visitModelName;
    }

    public void setVisitModelName(String visitModelName) {
        this.visitModelName = visitModelName == null ? null : visitModelName.trim();
    }

    public BigDecimal getVisitFee() {
        return visitFee;
    }

    public void setVisitFee(BigDecimal visitFee) {
        this.visitFee = visitFee;
    }

    public String getVisitFeeCurrency() {
        return visitFeeCurrency;
    }

    public void setVisitFeeCurrency(String visitFeeCurrency) {
        this.visitFeeCurrency = visitFeeCurrency == null ? null : visitFeeCurrency.trim();
    }

    public String getApplyFeeRequire() {
        return applyFeeRequire;
    }

    public void setApplyFeeRequire(String applyFeeRequire) {
        this.applyFeeRequire = applyFeeRequire == null ? null : applyFeeRequire.trim();
    }

    public String getReceiveFeeRequire() {
        return receiveFeeRequire;
    }

    public void setReceiveFeeRequire(String receiveFeeRequire) {
        this.receiveFeeRequire = receiveFeeRequire == null ? null : receiveFeeRequire.trim();
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}