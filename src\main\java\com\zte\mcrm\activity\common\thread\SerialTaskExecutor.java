package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/***
 * 串行任务执行器
 * <pre>
 *     所有需要串行执行的任务。如：ABC三步必须是ABC的顺序执行，则可以依次添加ABC三个任务
 *     备注：分布式串行任务无法保证。当然暂时无此场景，如果后续有，则是通过分布式任务调度 + 锁等方式实现
 *
 * </pre>
 *
 * <AUTHOR>
 */
public class SerialTaskExecutor extends BaseTaskExecutor {
    private static final int ONE = 1;

    public SerialTaskExecutor() {
        // 时长对于他无意义
        super("Serial", new ThreadPoolExecutor.AbortPolicy());
        initParam(ONE, ONE, 10, new LinkedBlockingQueue<>());
    }
}
