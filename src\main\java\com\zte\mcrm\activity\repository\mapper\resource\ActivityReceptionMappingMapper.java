package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityReceptionMappingDO;

public interface ActivityReceptionMappingMapper {
    /**
     * all field insert
     */
    int insert(ActivityReceptionMappingDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityReceptionMappingDO record);

    /**
     * query by primary key
     */
    ActivityReceptionMappingDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityReceptionMappingDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityReceptionMappingDO record);
}