package com.zte.mcrm.activity.repository.rep.sample.param;

import com.zte.mcrm.activity.common.model.PageQuery;
import com.zte.mcrm.activity.service.sample.param.SamplePointDirectionParam;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * SamplePointInfoQuery
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SamplePointInfoQuery extends PageQuery {
    /**
     * 主键
     */
    private String rowId;
    /**
     * 样板点编号
     */
    private String samplePointNo;
    /**
     * 样板点名称
     */
    private String samplePointName;
    /**
     * 样板点是否在海外，参考BooleanEnum枚举。Y-海外，N-国内
     */
    private String international;
    /**
     * 样板点所在国家/地区code
     */
    private String countryCode;
    /**
     * 样板点所在城市code
     */
    private String cityCode;
    /**
     * 样板点详细地址
     */
    private String samplePointAddress;
    /**
     * 样板点类型
     */
    private String samplePointType;
    /**
     * 所属客户
     */
    private String belongCustomerCode;
    /**
     * 样板点管理员
     */
    private String adminEmpNo;
    /**
     * 样板点管理员所在部门编码
     */
    private String adminDepartmentNo;
    /**
     * 样板点管理员所在部门编码全路径
     */
    private String adminFullDepartmentNo;
    /**
     * 样板点状态。见枚举：BooleanEnum。Y-可参观申请，N-不能
     */
    private String samplePointStatus;
    /**
     * 是否样板点支持远程参观。见枚举：BooleanEnum。Y-支持远程参观，N-不能
     */
    private String samplePointOnline;
    /**
     * 记录创建人
     */
    private String createdBy;
    /**
     * 记录创建时间
     */
    private Date creationDate;
    /**
     * 记录最后更新人
     */
    private String lastUpdatedBy;
    /**
     * 记录最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 记录有效标识。见枚举：BooleanEnum
     */
    private String enabledFlag = "Y";
    /**
     * 样板点介绍
     */
    private String content;
    /**
     * 参观须知
     */
    private String visitingNotice;
    /**
     * 创建时间-开始
     */
    private Date createdStartDate;
    /**
     * 创建时间-结束
     */
    private Date createdEndDate;

    /** 展示方向 */
    private List<SamplePointDirectionParam> samplePointDirectionList;

    public void initCreateSamplePointDateRange() {
        LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
        this.createdStartDate = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        this.createdEndDate = new Date();
        this.enabledFlag = null;
    }
}
