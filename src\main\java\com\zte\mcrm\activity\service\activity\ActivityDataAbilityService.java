package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;

import java.util.List;

/**
 * 活动数据能力服务类
 * <AUTHOR> 10333830
 * @date 2023-08-30 14:54
 */
public interface ActivityDataAbilityService {

    /**
     * 删除活动所有信息
     * @param activityRowId 活动Id
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/30 15:18
     */
    boolean deleteActivityAllOtherInfo(String activityRowId);

    /**
     * 保存活动方案
     * @param dataList  数据列表
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2024/1/19 13:55
     */
    int saveSolution(List<ActivityRelationSolutionDO> dataList, String activityRowId);

    /**
     * 保存活动附件
     * @param dataList  数据列表
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2024/1/19 13:55
     */
    int saveAttachment(List<ActivityRelationAttachmentDO> dataList, String activityRowId);

}
