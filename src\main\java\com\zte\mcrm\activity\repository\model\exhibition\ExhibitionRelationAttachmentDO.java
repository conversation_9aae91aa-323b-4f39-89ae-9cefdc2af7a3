package com.zte.mcrm.activity.repository.model.exhibition;

import java.util.Date;

/**
 * table:exhibition_relation_attachment -- 
 */
public class ExhibitionRelationAttachmentDO {
    /** 主键 */
    private String rowId;

    /** 展会id */
    private String exhibitionRowId;

    /** 附件所属场景。ExhibitionAttachmentSceneTypeEnum */
    private String exhibitionAttachmentSceneType;

    /** 文件token。如果是云文档这是对应key */
    private String fileToken;

    /** 文件名 */
    private String fileName;

    /** 附件大小byte */
    private Long fileSize;

    /** 备注 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getExhibitionRowId() {
        return exhibitionRowId;
    }

    public void setExhibitionRowId(String exhibitionRowId) {
        this.exhibitionRowId = exhibitionRowId == null ? null : exhibitionRowId.trim();
    }

    public String getExhibitionAttachmentSceneType() {
        return exhibitionAttachmentSceneType;
    }

    public void setExhibitionAttachmentSceneType(String exhibitionAttachmentSceneType) {
        this.exhibitionAttachmentSceneType = exhibitionAttachmentSceneType == null ? null : exhibitionAttachmentSceneType.trim();
    }

    public String getFileToken() {
        return fileToken;
    }

    public void setFileToken(String fileToken) {
        this.fileToken = fileToken == null ? null : fileToken.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}