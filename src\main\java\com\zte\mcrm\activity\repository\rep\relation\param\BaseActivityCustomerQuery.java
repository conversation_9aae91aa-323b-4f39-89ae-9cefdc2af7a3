package com.zte.mcrm.activity.repository.rep.relation.param;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 拓展活动关联客户信息查询
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BaseActivityCustomerQuery {
    /**
     * 活动ID
     */
    private List<String> activityRowIdList;
    /**
     * 客户编码
     */
    private List<String> customerCodeList;
    /**
     * 是否主客户。Y-是，N-不是。BooleanEnum
     */
    private String mainCust;

    // ……其他参数可再加，记住这里是单表查询

    /**
     * 最大条数据（正常来说，这里算是所有数据了）
     */
    private int max = 1 << 16;

    /**
     * 是否有参数
     *
     * @return
     */
    public boolean hasParam() {
        return CollectionUtils.isNotEmpty(activityRowIdList) || CollectionUtils.isNotEmpty(customerCodeList);
    }


}
