package com.zte.mcrm.activity.integration.ai.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/4 16:57
 */
@Setter
@Getter
@ToString
public class StudioParamDTO implements Serializable {
    private static final long serialVersionUID = 7541249246563927608L;

    @ApiModelProperty(value = "会话唯一标识，由数字和字母组成的32位字符串，用户如果不填，则由系统自动生成")
    private String chatUuid;
    @ApiModelProperty(value = "会话名称")
    private String chatName;
    @ApiModelProperty(value = "是否使用流式返回，true表示流式返回，false表示非流式返回")
    private boolean stream;
    @ApiModelProperty(value = "是否保持会话, true表示保持会话")
    private boolean keep;
    @ApiModelProperty(value = "用户的聊天文本")
    private String text;
    @ApiModelProperty(value = "温度设置范围为0-1")
    private String temperature;
    /**
     * 使用的模型信息，用户不填，则默认使用应用配置的模型，如果应用未配置模型，则默认使用星云研发大模型nebulacoder（高级编排时此参数不生效）
     * 当前支持的模型名称：
     * 星云大模型：nebulacoder
     * 星云大模型(8k): nebulacoder
     * 电信大模型-Saturn：ZTEAIM-Saturn
     * 电信大模型-Saturn(2.0)：ZTEAIM-Saturn-2.0
     * 通义千问：Qwen-72B-Chat
     * ZTEAIM-Venus-128K：ZTEAIM-Venus-128k
     * 其中，不同模型支持的上下文长度为：
     * 星云大模型(8k): 8K
     * 电信大模型：32K
     * 通义千问：4K
     * ZTEAIM-Venus-128K：128K
     */
    private String model;
    /**
     * 如果应用在简易配置中使用了用户引导配置，或者高级编排中引入用户引导组件并配置了变量信息，接口调用时，需要传此参数，代表本次对话使用的环境变量。
     * 在多轮对话中，第二次对话可以不传；如果需要修改本次会话中的环境变量，则重新传入新的环境变量即可。
     */
    private Map<String, String> variables;
}
