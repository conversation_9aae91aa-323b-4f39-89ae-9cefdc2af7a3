package com.zte.mcrm.activity.repository.rep.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointRelationAttachmentDO;

import java.util.List;
import java.util.Map;

/**
 * 样板点关联的附件信息
 *
 * <AUTHOR>
 */
public interface SamplePointRelationAttachmentRepository {
    /**
     * 新增样板点关联的附件信息
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<SamplePointRelationAttachmentDO> recordList);

    /**
     * 更新样板点关联的附件信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SamplePointRelationAttachmentDO record);

    /**
     * 删除样板点关联的附件信息
     *
     * @param samplePointRowId
     * @return
     */
    int deleteBySamplePointRowId(String samplePointRowId);

    /**
     * description 根据样板点id列表查询关联附件
     *
     * @param samplePointRowIdList 样板点id列表
     * @return 关联附件
     * <AUTHOR>
     * @createDate 2024/2/7 下午4:16
     */
    Map<String,List<SamplePointRelationAttachmentDO>> queryAttachmentBySamplePointRowId(List<String> samplePointRowIdList);

}
