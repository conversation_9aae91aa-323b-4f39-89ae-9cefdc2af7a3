package com.zte.mcrm.activity.repository.mapper.reception;

import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmContactDO;

public interface CustReceptionLcmContactMapper {
    /**
     * all field insert
     */
    int insert(CustReceptionLcmContactDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(CustReceptionLcmContactDO record);

    /**
     * query by primary key
     */
    CustReceptionLcmContactDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(CustReceptionLcmContactDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(CustReceptionLcmContactDO record);
}