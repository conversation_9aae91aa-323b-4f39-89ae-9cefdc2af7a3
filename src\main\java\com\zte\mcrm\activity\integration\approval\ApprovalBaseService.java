package com.zte.mcrm.activity.integration.approval;

import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.iss.approval.sdk.bean.OpinionDTO;
import com.zte.iss.approval.sdk.bean.ReassignDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ApprovalCenterAppService.java
 * @Description 审批中心接口
 * @createTime 2023年05月16日 15:44:00
 */
public interface ApprovalBaseService {
    /**
     * 启动审批流程
     * @param flowCode
     * @param receiveId
     * @param params
     * @return
     */
    String approvalFlowStartByRid(String flowCode, String receiveId, Map<String, Object> params);

    /**
     * 根据流程实例ID查询流程进展
     * @param flowInstId
     * @return
     */
    ApprovalProcessDTO getApprovalProcessByFid(String flowInstId);

    /**
     * 提交审批任务
     * @param opinionDTO
     * @return
     */
    String taskOpinionSubmit(OpinionDTO opinionDTO);

    /**
     * 转交审批任务
     * @param reassignDTO
     * @return
     */
    String taskReassign(ReassignDTO reassignDTO);

    /**
     * 撤销流程实例
     *
     * @param flowInstId 流程实例id
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/8/29 下午8:20
     */
    String approvalReassignByFid(String flowInstId);


}
