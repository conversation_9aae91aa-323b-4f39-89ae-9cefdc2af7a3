package com.zte.mcrm.activity.integration.accountinfo.dto;

import com.zte.mcrm.activity.common.enums.activity.SanctionedPartyEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户基本信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class OutCustomerBaseInfoDTO {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称（中）
     */
    private String customerName;
    /**
     * 客户名称（英）
     */
    private String customerEnglishName;
    /**
     * 客户状态。客户管理枚举：CustomerStatusEnum")
     */
    private String customerStatus;
    /**
     * 客户主管部门ID
     */
    private String buId;
    /**
     * 这个字段主要是以前SS同步给MDM数据，MDM返回的关联ID。MDM又同步客户数据给其他系统，其他系统保存的是integrationId，导致很多数据不同源
     * 目前不再给MDM同步数据，该字段SS维护兼容外部，一般来说该字段不要用
     */
    private String integrationId;
    /**
     * 客户别名
     */
    private String aliasName;
    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 国家/地区编码
     */
    private String countryCode;
    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 受限制主体状态SanctionedPartyEnum
     */
    private String sanctionedParty;

    /**
     * 获取标准化的受限制主体状态
     * @return
     */
    public String fetchSanctionedParty() {
        return SanctionedPartyEnum.getEnumByCode(sanctionedParty).getCode();
    }
}
