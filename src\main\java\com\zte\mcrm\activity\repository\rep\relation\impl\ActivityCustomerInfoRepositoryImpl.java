package com.zte.mcrm.activity.repository.rep.relation.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.relation.ActivityCustomerInfoExtMapper;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.param.BaseActivityCustomerQuery;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ActivityCustomerInfoRepositoryImpl implements ActivityCustomerInfoRepository {
    @Autowired
    private ActivityCustomerInfoExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityCustomerInfoDO record) {
        setDefaultValue(record);
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityCustomerInfoDO record) {
        if(StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityCustomerInfoDO> baseActivityCustomerInfoQuery(BaseActivityCustomerQuery query) {
        if (!query.hasParam()) {
            return Collections.emptyList();
        }
        return extMapper.baseActivityCustomerInfoQuery(query);
    }

    @Override
    public List<ActivityCustomerInfoDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public List<ActivityCustomerInfoDO> queryAllByActivityRowIds(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowIds(activityRowIds);
    }

    @Override
    public int insertSelective(List<ActivityCustomerInfoDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityCustomerInfoDO record : recordList) {
            this.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public Map<String, List<ActivityCustomerInfoDO>> getActivityCustomerListByActivityRowIds(Set<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                extMapper.getActivityCustomerListByActivityRowId(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityCustomerInfoDO::getActivityRowId));
    }

    @Override
    public List<ActivityCustomerInfoDO> getActivityCustomerListByActivityRowIdSet(Set<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList() :
                extMapper.getActivityCustomerListByActivityRowId(activityRowIds);
    }
    /**
     * 查找用户最近创建的活动中使用的客户
     *
     * @param pageQuery
     * @return {@link List< ActivityCustomerInfoDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:52
     */
    @Override
    public List<ActivityCustomerInfoDO> selectRecentlyCustomerByUser(PageQuery<ActivityRecentlySearchParam> pageQuery) {
        if (!pageQuery.validatePage() || StringUtils.isBlank(pageQuery.getParam().getEmpNo())) {
            return Collections.emptyList();
        }
        PageInfo<ActivityCustomerInfoDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> extMapper.selectRecentlyCustomerByUser(pageQuery.getParam()));
        return pageInfo.getList();
    }

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchInsert(List<ActivityCustomerInfoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        list.forEach(this::setDefaultValue);
        extMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 批量修改数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public void batchUpdate(List<ActivityCustomerInfoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ActivityCustomerInfoDO customerInfoDO : list) {
                this.updateByPrimaryKeySelective(customerInfoDO);
        }
    }

    /**
     * 设置默认值
     *
     * @param customerInfoDO 实体类
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    private void setDefaultValue(ActivityCustomerInfoDO customerInfoDO) {
        customerInfoDO.setRowId(Optional.ofNullable(customerInfoDO.getRowId()).orElse(keyIdService.getKeyId()));
        customerInfoDO.setCreatedBy(Optional.ofNullable(customerInfoDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        customerInfoDO.setLastUpdatedBy(Optional.ofNullable(customerInfoDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        customerInfoDO.setCreationDate(new Date());
        customerInfoDO.setLastUpdateDate(new Date());
        customerInfoDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }


    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public List<ActivityCustomerInfoDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllActivityWithNotEnable(activityRowId);
    }

    @Override
    public List<ActivityCustomerInfoDO> listMktNameByMktCode(Collection<String> codes) {
        return CollectionUtils.isEmpty(codes) ? Collections.emptyList()
                : extMapper.listMktNameByMktCode(codes);
    }

    @Override
    public List<String> getActivityRowIdsByCustomerInfo(String mtoCode, String mktCode, String customerCode) {
        if (BooleanUtils.and(new boolean[] {StringUtils.isBlank(mtoCode), StringUtils.isBlank(mktCode), StringUtils.isBlank(customerCode)})) {
            return Collections.emptyList();
        }

        return extMapper.getActivityRowIdsByCustomerInfo(mtoCode, mktCode, customerCode);
    }
}
