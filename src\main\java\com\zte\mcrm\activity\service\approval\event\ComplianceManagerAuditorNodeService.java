package com.zte.mcrm.activity.service.approval.event;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityStatusLifecycleRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.service.approval.event.helper.ApprovalHelper;
import com.zte.mcrm.activity.service.approval.param.*;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessNodeConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityStatusLifecycleConverter;
import com.zte.mcrm.activity.service.converter.notice.ActivityPendingNoticeConverter;
import com.zte.mcrm.activity.service.converter.people.ActivityRelationZtePeopleConverter;
import com.zte.mcrm.activity.service.model.people.ActivityRelationZtePeople;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * <AUTHOR>
 */
@Service
public class ComplianceManagerAuditorNodeService extends AbstractApprovalNodeService {

    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Override
    String getCurrentActivityStatus() {
        return ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode();
    }

    @Override
    String getNodeType() {
        return PendingBizTypeEnum.APPROVAL_COMPLIANCE.getType();
    }

    @Override
    String getApprovalCompleteStatus(String approvalResult) {
        return BooleanEnum.Y.getCode().equals(approvalResult) ? ActivityStatusEnum.BUSINESS_APPROVAL.getCode()
                : ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS.getCode();
    }

    /**
     * 节点创建-kafka回调
     *
     * @param bizRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nodeCreateOfKafka(BizRequest<ActivityApprovalProcessAddParam> bizRequest) {
        ActivityApprovalProcessAddParam processAddParam = bizRequest.getParam();
        ValidationUtils.validateObject(processAddParam);
        ActivityApprovalProcessNodeAddParam processNodeAddParam = processAddParam.getProcessNodeAddParam();
        ValidationUtils.validateObject(processNodeAddParam);
        EmployeeInfoDTO approve = userCenterService.getUserInfo(processNodeAddParam.getApproveBy());
        AssertUtil.assertNotNull(approve);
        ApprovalHelper approvalHelper = new ApprovalHelper();
        approvalHelper.setApprove(approve);
        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(processNodeAddParam.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        ActivityInfoDO activityInfoDO =
                activityInfoRepository.selectByPrimaryKey(activityApprovalInfoDO.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        // 更新process状态
        List<ActivityApprovalProcessDO> approvalProcessDOList = approvalProcessRepository
                .queryByActivityRowIdAndProcessType(activityApprovalInfoDO.getActivityRowId(), ApprovalTypeEnum.COMPLIANCE_AUDITOR.getCode());
        approvalProcessDOList = approvalProcessDOList.stream().filter(e -> ProcessStatusEnum.DEFAULT.isMe(e.getProcessStatus()))
                .collect(Collectors.toList());
        AssertUtil.assertNotEmpty(approvalProcessDOList);
        ActivityApprovalProcessDO approvalProcessDO = approvalProcessDOList.get(ZERO);
        approvalProcessDO.setProcessStatus(ProcessStatusEnum.ACTIVE.getCode());
        approvalProcessDO.setLastUpdatedBy(processAddParam.getLastUpdatedBy());
        // 更新节点详情
        List<ActivityApprovalProcessNodeDO> approvalProcessNodeDOList = approvalProcessNodeRepository.queryByProcessRowId(approvalProcessDO.getRowId());
        ActivityApprovalProcessNodeDO approvalProcessNodeDO = approvalProcessNodeDOList.get(ZERO);
        approvalProcessNodeDO.setLastUpdatedBy(processNodeAddParam.getLastUpdatedBy());
        approvalProcessNodeDO.setApprovalFlowNo(processNodeAddParam.getApprovalFlowNo());
        approvalProcessNodeDO.setNodeStatus(ApproveNodeStatusEnum.ACTIVE.getCode());
        // 更新主表状态数据
        ActivityInfoDO activityInfoUpdate = ActivityInfoConverter.buildOfUpdateStatus(approvalProcessNodeDO.getActivityRowId(),
                getCurrentActivityStatus(), approvalProcessNodeDO.getApproveBy());
        // 插入生命周期
        ActivityStatusLifecycleDO activityStatusLifecycleDO
                = ActivityStatusLifecycleConverter.buildOfAdd(activityInfoDO, getCurrentActivityStatus(),
                approvalProcessNodeDO.getApproveBy());
        approvalHelper.setApprovalProcessNodeAdd(approvalProcessNodeDO);
        approvalHelper.setApprovalProcessAdd(approvalProcessDO);
        approvalHelper.setActivityInfo(activityInfoDO);
        activityInfoRepository.updateByPrimaryKeySelective(activityInfoUpdate);
        approvalProcessRepository.updateByPrimaryKeySelective(approvalProcessDO);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(approvalProcessNodeDO);
        lifecycleRepository.insertSelective(Collections.singletonList(activityStatusLifecycleDO));
        //新增待办
        addPendingNotice(approvalHelper);

        super.sync2Es(activityInfoDO.getRowId());
    }

    /**
     * 审批节点完成-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nodeCompleteOfKafka(BizRequest<ActivityApprovalProcessNodeCompleteParam> bizRequest) {
        // 校验数据
        ActivityApprovalProcessNodeCompleteParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);

        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(param.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityApprovalInfoDO.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        if (!ActivityStatusEnum.COMPLIANCE_APPROVAL.isMe(activityInfoDO.getActivityStatus())) {
            throw new RuntimeException("activity status is error!");
        }
        // 获取节点详情数据
        List<ActivityApprovalProcessNodeDO> processNodeDOList = approvalProcessNodeRepository
                .queryAllByActivityRowId(activityApprovalInfoDO.getActivityRowId());
        AssertUtil.assertNotEmpty(processNodeDOList);
        List<ActivityApprovalProcessNodeDO> complianceNodeList = processNodeDOList.stream().filter(e -> ApproveNodeStatusEnum.ACTIVE.isMe(e.getNodeStatus())).collect(Collectors.toList());
        AssertUtil.assertNotEmpty(complianceNodeList);
        EmployeeInfoDTO approve = userCenterService.getUserInfo(param.getApproveBy());
        AssertUtil.assertNotNull(approve);
        ActivityApprovalProcessNodeDO processNodeDO = complianceNodeList.get(ZERO);
        // 获取审批process数据
        ActivityApprovalProcessDO processDO = approvalProcessRepository.selectByPrimaryKey(processNodeDO.getApprovalProcessRowId());
        AssertUtil.assertNotNull(processDO);
        // 更新节点详情数据
        ActivityApprovalProcessNodeDO processNodeUpdate = ActivityApprovalProcessNodeConverter.buildOfComplete(processDO, param);
        processNodeUpdate.setNodeStatus(ApproveNodeStatusEnum.COMPLETED.getCode());
        processNodeUpdate.setApproverName(approve.getEmpName());
        processNodeUpdate.setRowId(processNodeDO.getRowId());
        // 更新process状态
        ActivityApprovalProcessDO approvalProcessUpdate = ActivityApprovalProcessConverter
                .buildOfComplete(processDO.getRowId(), processNodeUpdate.getLastUpdatedBy());
        // 更新主表状态数据
        String newStatus = BooleanEnum.Y.getCode().equals(param.getApproveResult()) ? ActivityStatusEnum.BUSINESS_APPROVAL.getCode()
                : ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS.getCode();
        // 填充活动状态
        ActivityInfoDO activityInfoUpdate = ActivityInfoConverter.buildOfUpdateStatus(processDO.getActivityRowId(),
                newStatus, processNodeUpdate.getLastUpdatedBy());

        activityInfoRepository.updateByPrimaryKeySelective(activityInfoUpdate);
        approvalProcessRepository.updateByPrimaryKeySelective(approvalProcessUpdate);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(processNodeUpdate);
        pendingNoticeRepository.updateByActivityRowIdAndBizType(processDO.getActivityRowId(),
                getNodeType(), PendingNoticeStatusEnum.FINISH.getStatus());

        super.sync2Es(activityInfoDO.getRowId());
    }

    /**
     * 审批节点任务转交-kafka回调
     *
     * @param bizRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskReassignOfKafka(BizRequest<ActivityApprovalTaskReassignParam> bizRequest) throws Exception {
        ApprovalHelper approvalHelper = new ApprovalHelper();
        // 校验数据
        ActivityApprovalTaskReassignParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);
        // 获取审批人数据
        EmployeeInfoDTO approve = userCenterService.getUserInfo(param.getApproveBy());

        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(param.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        // 获取上一节点数据
        ActivityApprovalProcessNodeDO lastNode = approvalProcessNodeRepository.queryByActIdAndApprovalFlowNo(activityApprovalInfoDO.getActivityRowId(), param.getApprovalFlowNo());

        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(lastNode.getActivityRowId());
        ActivityApprovalProcessDO approvalProcessDO = approvalProcessRepository.selectByPrimaryKey(lastNode.getApprovalProcessRowId());
        // 转交新增节点详情数据
        ActivityApprovalProcessNodeDO approvalProcessNodeDO = buildActivityApprovalProcessNodeDO(param, approve, lastNode);
        // 旧审批节点更新
        lastNode.setApproveResult(ApproveResultEnum.transfer.getCode());
        lastNode.setApproveTime(new Date());
        lastNode.setRemark(param.getRemark());
        lastNode.setLastUpdatedBy(param.getLastUpdatedBy());
        lastNode.setNodeStatus(ApproveNodeStatusEnum.COMPLETED.getCode());
        // 查询审批人信息
        approvalHelper.setApprove(approve);
        approvalHelper.setApprovalProcessAdd(approvalProcessDO);
        approvalHelper.setActivityInfo(activityInfoDO);
        approvalHelper.setApprovalProcessNodeAdd(approvalProcessNodeDO);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(lastNode);
        pendingNoticeRepository.updateByActivityRowIdAndBizType(lastNode.getActivityRowId(), getNodeType(), PendingNoticeStatusEnum.FINISH.getStatus());
        approvalProcessNodeRepository.insertSelective(approvalProcessNodeDO);
        addTransPendingNotice(approvalHelper, param.getExtendedCode());
    }

    /**
     * 构建节点数据
     *
     * @param param
     * @param approve
     * @param lastNode
     * @return
     */
    private ActivityApprovalProcessNodeDO buildActivityApprovalProcessNodeDO(ActivityApprovalTaskReassignParam param, EmployeeInfoDTO approve, ActivityApprovalProcessNodeDO lastNode) {
        ActivityApprovalProcessNodeDO approvalProcessNodeDO = new ActivityApprovalProcessNodeDO();
        // taskId
        approvalProcessNodeDO.setApprovalFlowNo(param.getNewApprovalFlowNo());
        approvalProcessNodeDO.setApproverName(approve.getEmpName());
        approvalProcessNodeDO.setApproveBy(param.getApproveBy());
        approvalProcessNodeDO.setRemark(param.getRemark());
        approvalProcessNodeDO.setLastUpdatedBy(param.getApproveBy());
        approvalProcessNodeDO.setCreatedBy(param.getApproveBy());
        approvalProcessNodeDO.setActivityRowId(lastNode.getActivityRowId());
        approvalProcessNodeDO.setApprovalProcessRowId(lastNode.getApprovalProcessRowId());
        approvalProcessNodeDO.setTransFrom(lastNode.getRowId());
        approvalProcessNodeDO.setNodeStatus(ApproveNodeStatusEnum.ACTIVE.getCode());
        approvalProcessNodeDO.setNodeType(lastNode.getNodeType());
        return approvalProcessNodeDO;
    }

    /**
     * 新增待办信息
     *
     * @param approvalHelper
     */
    private void addTransPendingNotice(ApprovalHelper approvalHelper, String extendCode) {
        String nodeType = ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.isMe(extendCode) ? PendingBizTypeEnum.APPROVAL_COMPLIANCE.getType() : PendingBizTypeEnum.APPROVAL_LEADER.getType();
        // 新增待办
        ActivityPendingNoticeDO activityPendingNoticeAdd = ActivityPendingNoticeConverter.buildOfAddApproval(nodeType, approvalHelper);
        // 新增参与人
        ActivityRelationZtePeople ztePeople = ActivityRelationZtePeopleConverter.buildOfAdd(approvalHelper);
        pendingNoticeRepository.insertSelective(Collections.singletonList(activityPendingNoticeAdd));
        activityRelationZtePeopleRepository.insertSelective(Collections.singletonList(ActivityRelationZtePeopleConverter.INSTANCE.toEntity(ztePeople)));
    }

    /**
     * 审批全流程完成-kafka回调
     *
     * @param bizRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOfKafka(BizRequest<ActivityApprovalProcessCompleteParam> bizRequest) {
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(bizRequest.getParam().getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        bizRequest.getParam().setActivityRowId(activityApprovalInfoDO.getActivityRowId());
        super.completeOfKafka(bizRequest);
    }
}
