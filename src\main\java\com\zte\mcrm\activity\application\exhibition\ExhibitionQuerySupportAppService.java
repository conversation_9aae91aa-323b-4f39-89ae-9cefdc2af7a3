package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.activity.web.controller.exhibition.param.ExhibitionSelectParam;
import com.zte.mcrm.activity.web.controller.exhibition.vo.JoinExhibitionSelPeopleVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.SelectExhibitionVO;

/**
 * <AUTHOR>
 */
public interface ExhibitionQuerySupportAppService {

    /**
     * 可选展会列表
     *
     * @param request
     * @return
     */
    PageRows<SelectExhibitionVO> selectableList(BizRequest<PageQuery<ExhibitionSelectParam>> request);

    /**
     * 选择中兴参加展会人员
     *
     * @param request
     * @return
     */
    JoinExhibitionSelPeopleVO selectZteJoinExhibitionPeople(BizRequest<PageQuery<ActivityRecentlySearchParam>> request);
}
