package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointDirectionDO;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface SamplePointDirectionExtMapper extends SamplePointDirectionMapper {

    /**
     * 删除样板点展示方向
     * @param samplePointRowId
     * @return
     */
    int deleteBySamplePointRowId(String samplePointRowId);
    /**
     * 根据样板点id列表获取关联展示方向
     *
     * @param samplePointRowIdList 样板点id列表
     * @return List<SamplePointRelationAttachmentDO>
     * <AUTHOR>
     * @date 2024/2/4
     */
    List<SamplePointDirectionDO> getDirectionBySamplePointRowIds(List<String> samplePointRowIdList);
}