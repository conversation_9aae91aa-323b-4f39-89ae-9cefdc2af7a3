package com.zte.mcrm.activity.repository.rep.summary.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryApExtMapper;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryApRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ActivitySummaryApRepositoryImpl implements ActivitySummaryApRepository {
    @Resource
    private ActivitySummaryApExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivitySummaryApDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivitySummaryApDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            extMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivitySummaryApDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivitySummaryApDO> queryAllApForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : extMapper.queryAllApForActivity(activityRowId);
    }

    @Override
    public Map<String, List<ActivitySummaryApDO>> queryAllByActivityRowId(List<String> activityRowId) {
        List<ActivitySummaryApDO> list = CollectionUtils.isEmpty(activityRowId) ? Collections.emptyList() : extMapper.queryAllByActivityRowId(activityRowId);

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap():list.stream().collect(Collectors.groupingBy(ActivitySummaryApDO::getActivityRowId));
    }

    @Override
    public int deleteBatch(List<String> rowIdList) {
        return CollectionUtils.isEmpty(rowIdList) ? 0 : extMapper.deleteBatch(rowIdList);
    }

    /**
     * 根据活动Id批次删除
     *
     * @param operator          用户工号
     * @param activityRowIds 活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:47
     */
    @Override
    public int deleteByActivityIds(String operator, List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? 0 : extMapper.softDeleteByActivityIds(operator, activityRowIds);
    }

}
