package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标准订单数据源（如果后面还有信息，在此增加，并增加对应的筛选方法）
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class StandardActivityDetailDataSource {

    /**
     * 《扩展活动id，活动分页查询信息》
     */
    private PageRows<ActivityInfoDO> activityInfoDoPageRows;

    /**
     * 展会及大会活动 需要 对应展会及大会信息
     * 活动id映射展会信息
     */
    private Map<String, ExhibitionInfoDO> activityRowId2ExhibitionMap;

    /**
     * 活动id映射样板点信息
     */
    private Map<String, SamplePointInfoDO> activityRowId2SamplePointMap;

    /**
     * 《扩展活动id，活动主信息》
     */
    private List<ActivityInfoDO> activityInfoList;

    /**
     * 《扩展活动id，审批流程信息》
     */
    private Map<String, List<ActivityApprovalProcessDO>> approvalProcessMap;

    /**
     * 《扩展活动id，客户信息》
     */
    private Map<String, List<ActivityCustomerInfoDO>> customerInfoMap;

    /**
     * 《扩展活动id，客户联系人》
     */
    private Map<String, List<ActivityRelationCustPeopleDO>> custPeopleMap;

    /**
     * 《扩展活动id，中兴联系人》
     */
    private Map<String, List<ActivityRelationZtePeopleDO>> ztePeopleMap;

    /**
     * 《拓展活动id，交流方向》
     */
    private Map<String, List<ActivityCommunicationDirectionDO>> directionMap;
    /**
     * 《拓展活动id，关联项目》
     */
    private Map<String, List<ActivityRelationProjectDO>> projectMap;
    /**
     * 《拓展活动id，会议纪要》
     */
    private Map<String, List<ActivitySummaryDO>> summaryMap;
    /**
     * 《拓展活动id，AP任务》
     */
    private Map<String, List<ActivitySummaryApDO>> summaryApMap;
    /**
     * 《拓展活动id，RDC任务》
     */
    private Map<String, List<ActivitySummaryRdcDO>> summaryRdcMap;
    /**
     * 《拓展活动id，遗留问题》
     */
    private Map<String, List<ActivitySummaryIssueDO>> summaryIssueMap;

    /**
     * 《待办信息》
     */
    private Map<String, List<ActivityPendingNoticeDO>> pendingNoticeMap;

    /**
     * 《活动，日程》
     */
    private Map<String, List<ActivityScheduleItemDO>> activity2ScheduleItem;
    /**
     * 《日程，参与人》
     */
    private Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItem2People;

    /**
     * 《日程，客户参与人》
     */
    private Map<String, Map<String, ActivityRelationCustPeopleDO>> relationCustPeople;
    /**
     * 《活动，酒店信息》
     */
    private Map<String, List<ActivityResourceHotelDO>> hotelMap;

    /**
     * 《活动，酒店信息》
     */
    private Map<String, List<ActivityResourceCarDO>> carMap;

    /**
     * 《组织编码， 业绩组织信息》
     */
    private Map<String, OrgInfoVO> orgInfoMap;
    /**
     * 展会负责人信息
     */
    Map<String,List<ExhibitionDirectorDO>> exhibitionDirectionMap;
    /**
     * 获取对应展会负责人信息
     *
     * @param exhibitionId 展会ID
     * @return
     */
    public List<ExhibitionDirectorDO> fetchExhibitionDirection(String exhibitionId) {
        List<ExhibitionDirectorDO> list = exhibitionDirectionMap == null ? null : exhibitionDirectionMap.get(exhibitionId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取对应
     *
     * @param activityRowId 扩展活动ID
     * @return
     */
    public ActivityInfoDO fetchActivityInfo(String activityRowId) {
        return activityInfoList == null ? null : activityInfoList.stream()
                .filter(e -> StringUtils.equalsIgnoreCase(activityRowId, e.getRowId()))
                .findFirst().orElse(null);
    }

    public ExhibitionInfoDO fetchExhibitionInfo(String activityRowId) {
        return activityRowId2ExhibitionMap == null ? null : activityRowId2ExhibitionMap.get(activityRowId);
    }

    public SamplePointInfoDO fetchSamplePointInfo(String activityRowId) {
        return activityRowId2SamplePointMap == null ? null : activityRowId2SamplePointMap.get(activityRowId);
    }

    /**
     * 获取所有扩展活动ID
     *
     * @return
     */
    public List<String> fetchAllRowIds() {
        return activityInfoList == null ? Collections.emptyList()
                : activityInfoList.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());
    }

    /**
     * 获取当前活动对应审批信息
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityApprovalProcessDO> fetchActivityApprovalProcess(String activityRowId) {
        List<ActivityApprovalProcessDO> approvalProcessDOList = approvalProcessMap == null
                ? Collections.emptyList() : approvalProcessMap.get(activityRowId);
        return approvalProcessDOList == null ? Collections.emptyList() : approvalProcessDOList;
    }

    /**
     * 获取活动涉及的所有客户信息
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityCustomerInfoDO> fetchCustomerList(String activityRowId) {
        // getOrDefault如果对应value是null还是返回的null
        List<ActivityCustomerInfoDO> list = customerInfoMap == null ? null : customerInfoMap.get(activityRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取当前活动对应客户联系人信息
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityRelationCustPeopleDO> fetchActivityCustPeople(String activityRowId, String customerCode) {
        List<ActivityRelationCustPeopleDO> custPeopleDOList = custPeopleMap == null
                ? Collections.emptyList() : custPeopleMap.get(activityRowId);

        return custPeopleDOList == null ? Collections.emptyList() : custPeopleDOList.stream()
                .filter(e -> StringUtils.equals(customerCode, e.getCustomerCode()))
                .collect(Collectors.toList());
    }

    /**
     * 获取当前活动对应【主】客户信息
     *
     * @param activityRowId
     * @return
     */
    public ActivityCustomerInfoDO fetchActivityMainCustomerInfo(String activityRowId) {
        List<ActivityCustomerInfoDO> customerInfoDOList = customerInfoMap == null
                ? null : customerInfoMap.get(activityRowId);
        return customerInfoDOList == null ? null : customerInfoDOList.stream()
                .filter(e -> BooleanEnum.Y.isMe(e.getMainCust()))
                .findFirst()
                .orElse(new ActivityCustomerInfoDO());
    }

    /**
     * 获取当前活动对应【主】客户联系人信息
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityRelationCustPeopleDO> fetchActivityMainCustPeople(String activityRowId, String customerCode) {
        List<ActivityRelationCustPeopleDO> custPeopleDOList = custPeopleMap == null
                ? Collections.emptyList() : custPeopleMap.get(activityRowId);

        return custPeopleDOList == null ? Collections.emptyList() : custPeopleDOList.stream()
                .filter(e -> BooleanEnum.Y.isMe(e.getMainCust()) && customerCode.equals(e.getCustomerCode()))
                .collect(Collectors.toList());
    }

    /**
     * 获取当前活动对应【主】客户联系人信息
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityRelationCustPeopleDO> fetchActivityMainCustPeople(String activityRowId) {
        ActivityCustomerInfoDO mainCust = fetchActivityMainCustomerInfo(activityRowId);

        return mainCust == null ? Collections.emptyList() : fetchActivityMainCustPeople(activityRowId, mainCust.getCustomerCode());
    }

    /**
     * 获取当前活动对应中兴联系人信息,如果peopleType没有值，则查询所有类型的中兴联系人
     *
     * @param activityRowId
     * @return
     */
    public List<ActivityRelationZtePeopleDO> fetchActivityZtePeople(String activityRowId, String peopleType) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = ztePeopleMap == null
                ? Collections.emptyList() : ztePeopleMap.get(activityRowId);

        return ztePeopleDOList == null ? Collections.emptyList() : ztePeopleDOList.stream()
                .filter(e -> StringUtils.equals(e.getPeopleType(), peopleType))
                .collect(Collectors.toList());
    }

    /**
     * 获取当前活动对应中兴联系人信息,如果peopleType没有值，则查询所有类型的中兴联系人
     *
     * @param activityRowId 活动ID
     * @param peopleType    人员类型
     * @return
     */
    public List<ActivityRelationZtePeopleDO> fetchActivityZtePeople(String activityRowId, ActivityPeopleTypeEnum... peopleType) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = ztePeopleMap == null
                ? Collections.emptyList() : ztePeopleMap.get(activityRowId);
        ztePeopleDOList = CollectionExtUtils.getListOrDefaultEmpty(ztePeopleDOList);
        if (peopleType == null || peopleType.length < 1) {
            return ztePeopleDOList;
        }

        return CollectionExtUtils.getListOrDefaultEmpty(ztePeopleDOList == null ? Collections.emptyList() : ztePeopleDOList.stream()
                .filter(e -> ActivityPeopleTypeEnum.in(e.getPeopleType(), peopleType))
                .collect(Collectors.toList()));
    }

    /**
     * 获取活动的交流方向
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivityCommunicationDirectionDO> fetchCommDirection(String activityRowId) {
        return directionMap == null ? Collections.emptyList() : directionMap.get(activityRowId);
    }

    /**
     * 获取活动的关联项目
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivityRelationProjectDO> fetchProject(String activityRowId) {
        return projectMap == null ? Collections.emptyList() : projectMap.get(activityRowId);
    }

    /**
     * 获取活动的关联会议纪要
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivitySummaryDO> fetchSummary(String activityRowId) {
        return summaryMap == null ? Collections.emptyList() : summaryMap.get(activityRowId);
    }

    /**
     * 获取活动的关联会议纪要AP
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivitySummaryApDO> fetchSummaryAp(String activityRowId) {
        return summaryApMap == null ? Collections.emptyList() : summaryApMap.get(activityRowId);
    }

    /**
     * 获取活动的关联会议纪要RDC
     *
     * @param activityRowId
     * @return {@link List< ActivitySummaryRdcDO>}
     * <AUTHOR>
     * @date 2025/3/6 下午4:27
     */
    public List<ActivitySummaryRdcDO> fetchSummaryRdc(String activityRowId) {
        return summaryRdcMap == null ? Collections.emptyList() : summaryRdcMap.get(activityRowId);
    }

    /**
     * 获取活动的关联会议纪要遗留问题
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivitySummaryIssueDO> fetchSummaryIssue(String activityRowId) {
        return summaryIssueMap == null ? Collections.emptyList() : summaryIssueMap.get(activityRowId);
    }

    /**
     * 获取活动所有对应日程
     *
     * @param activityRowId 活动ID
     * @return
     */
    public List<ActivityScheduleItemDO> fetchActivityScheduleItem(String activityRowId) {
        List<ActivityScheduleItemDO> list = activity2ScheduleItem == null ? null : activity2ScheduleItem.get(activityRowId);

        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取日程中涉及到的各人员信息
     *
     * @param scheduleItemRowId 日程ID
     */
    public List<ActivityScheduleItemPeopleDO> fetchActivityScheduleItemPeople(String scheduleItemRowId) {
        List<ActivityScheduleItemPeopleDO> list = scheduleItem2People == null ? null : scheduleItem2People.get(scheduleItemRowId);

        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 活动维度获取日程中涉及到的各人员信息
     *
     * @param activityRowId 活动ID
     */
    public List<ActivityScheduleItemPeopleDO> fetchSchedulePeopleByActivityRowId(String activityRowId) {
        List<ActivityScheduleItemPeopleDO> list = scheduleItem2People == null ? null : scheduleItem2People.get(activityRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取活动相关酒店信息
     *
     * @param activityRowId 活动ID
     */
    public List<ActivityResourceHotelDO> fetchActivityResourceHotel(String activityRowId) {
        List<ActivityResourceHotelDO> list = hotelMap == null ? null : hotelMap.get(activityRowId);

        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取活动相关车辆信息
     *
     * @param activityRowId 活动ID
     */
    public List<ActivityResourceCarDO> fetchActivityResourceCar(String activityRowId) {
        List<ActivityResourceCarDO> list = carMap == null ? null : carMap.get(activityRowId);

        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取营销组织消息
     *
     * @param orgId 组织编码
     */
    public OrgInfoVO fetchOrgInfo(String orgId) {
        OrgInfoVO orgInfo = orgInfoMap == null ? null : orgInfoMap.get(orgId);

        return orgInfo == null ? null : orgInfo;
    }

}
