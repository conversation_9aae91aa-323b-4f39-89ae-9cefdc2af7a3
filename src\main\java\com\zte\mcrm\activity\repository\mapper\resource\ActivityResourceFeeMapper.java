package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceFeeDO;

public interface ActivityResourceFeeMapper {
    /**
     * all field insert
     */
    int insert(ActivityResourceFeeDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityResourceFeeDO record);

    /**
     * query by primary key
     */
    ActivityResourceFeeDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityResourceFeeDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceFeeDO record);
}