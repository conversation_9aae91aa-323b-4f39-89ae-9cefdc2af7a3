package com.zte.mcrm.activity.application.exhibition.util;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.web.controller.schedule.vo.LastScheduleOrchestrationVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationDetailVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.web.controller.schedule.vo.LastScheduleOrchestrationVO.*;

/**
 * 日程差异工具
 *
 * <AUTHOR>
 */
public class ScheduleItemDiffUtils {

    /**
     * 计算自我发布后最新发布版本差异
     *
     * @param myLastDetailList 我的最后版本日程明细
     * @param lastVo           最新日程数据
     */
    public static void computeMyLastPublishDiff(List<ActivityScheduleOrchestrationDetailDO> myLastDetailList, LastScheduleOrchestrationVO lastVo) {
        if (CollectionUtils.isEmpty(myLastDetailList) || CollectionUtils.isEmpty(lastVo.getDetailList())) {
            return;
        }

        Map<String, ActivityScheduleOrchestrationDetailDO> myMap = myLastDetailList.stream().collect(
                Collectors.toMap(ActivityScheduleOrchestrationDetailDO::getScheduleItemRowId, Function.identity(), (v1, v2) -> v2));
        Map<String, ScheduleOrchestrationDetailVO> lastMap = lastVo.getDetailList().stream().collect(
                Collectors.toMap(ScheduleOrchestrationDetailVO::getScheduleItemRowId, Function.identity(), (v1, v2) -> v2));

        /*
        计算差异：以日程ID作为连接点
        1、在myMap不存在，在lastMap中存在的，则说明是新增的日程，即：时间、地点、专家/领导都变了
        2、在myMap存在，在lastMap中不存在的，说明该日程被删除了（变更过），不用处理
        3、在myMap和lastMap中都存在，则比较各字段是否变化了
         */
        lastMap.forEach((itemRowId, detailVO) -> {
            ActivityScheduleOrchestrationDetailDO myDO = myMap.get(itemRowId);
            if (myDO == null) {
                lastVo.addChange(detailVO.getOrderNum(), LEADER, EXPERT, TIME, PLACE);
            } else {
                lastVo.addChange(detailVO.getOrderNum(), diffChange(myDO, detailVO));
            }
        });
    }


    /**
     * @param myDO
     * @param detailVO
     * @return
     */
    static String[] diffChange(ActivityScheduleOrchestrationDetailDO myDO, ScheduleOrchestrationDetailVO detailVO) {
        List<String> res = new ArrayList<>(4);
        if (!isSame(myDO.getPlaceType(), detailVO.getPlaceType()) || !isSame(myDO.getPlaceName(), detailVO.getPlaceName())) {
            res.add(PLACE);
        }

        if (!isSame(myDO.getScheduleDate(), detailVO.getScheduleDateTime()) || !isSame(myDO.getScheduleTime(), detailVO.getScheduleTime())) {
            res.add(TIME);
        }

        if (!isSame(myDO.getZteLeader(), detailVO.getZteLeader(), CharacterConstant.COMMA)) {
            res.add(LEADER);
        }

        if (!isSame(myDO.getZteExpert(), detailVO.getZteExpert(), CharacterConstant.COMMA)) {
            res.add(EXPERT);
        }

        String[] diff = new String[res.size()];
        res.toArray(diff);

        return diff;
    }

    /**
     * 是否相同
     *
     * @param s1
     * @param s2
     * @return s1, s2都为空/空白或者s1与s2相等equals，则true；其他false
     */
    static boolean isSame(String s1, String s2) {
        return (StringUtils.isBlank(s1) && StringUtils.isBlank(s2)) || StringUtils.equals(s1, s2);
    }

    /**
     * 是否相同
     *
     * @param s1
     * @param s2
     * @param split
     * @return s1, s2都为空/空白或者s1与s2分割后内容相等，则true；其他false
     */
    static boolean isSame(String s1, String s2, String split) {
        if (StringUtils.isNotBlank(s1) && StringUtils.isNotBlank(s2)) {
            Set<String> set1 = Arrays.stream(s1.split(split)).collect(Collectors.toSet());
            Set<String> set2 = Arrays.stream(s2.split(split)).collect(Collectors.toSet());
            return isSame(set1, set2);
        }

        return isSame(s1, s2);
    }

    /**
     * 时间相等
     *
     * @param d1
     * @param d2
     * @return d1, d2都空或compareTo一样，则true；其他false
     */
    static boolean isSame(Date d1, Date d2) {
        return (d1 == null && d2 == null) || (d1 != null && d2 != null && d1.compareTo(d2) == 0);
    }

    /**
     * @param s1
     * @param s2
     * @return s1, s2都为空/空白或者s1与s2内容相同，则true；其他false
     */
    static boolean isSame(Set<String> s1, Set<String> s2) {
        if (CollectionUtils.isEmpty(s1) && CollectionUtils.isEmpty(s2)) {
            return true;
        }

        boolean flag = false;
        if (CollectionUtils.isNotEmpty(s1) && CollectionUtils.isNotEmpty(s2) && s1.size() == s2.size()) {
            flag = true;
            for (String s : s1) {
                if (!s2.contains(s)) {
                    flag = false;
                    break;
                }
            }
        }

        return flag;
    }
}
