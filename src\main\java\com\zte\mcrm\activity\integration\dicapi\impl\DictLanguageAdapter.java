package com.zte.mcrm.activity.integration.dicapi.impl;

import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.ErrorMsgConstant;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.integration.dicapi.param.DictLanguageQueryParam;
import com.zte.mcrm.activity.integration.dicapi.IDictLanguageAdapter;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.common.MsbParamDTO;
import com.zte.mcrm.common.enums.ServiceAliasEnum;
import com.zte.mcrm.isearch.enums.RequestTypeEnum;
import com.zte.mcrm.util.MSBUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据字典Adapter
 * <AUTHOR> YeZhibin
 * @date 2019/7/19
 */
@Service
public class DictLanguageAdapter implements IDictLanguageAdapter {
    private Logger logger = LoggerFactory.getLogger(DictLanguageAdapter.class);

    private static final String DICT_GET_URL = "/dict/get";
    private static final String DICT_EXACT_LIST_POST_URL = "/dict/exact/list";
    private static final String DICT_FUZZY_QUERY_BY_TYPE_URL = "/dict/fuzzyquerybytype";

    /**
     * 精确查询一个数据字典
     * @param request   查询请求
     * @return MsaRpcResponse<DictLanguageDTO>
     * <AUTHOR>
     * date: 2023/7/24 14:44
     */
    @Override
    public MsaRpcResponse<DictLanguageDTO> getExactQueryDictLanguage(MsaRpcRequest<DictLanguageQueryParam> request) {
        DictLanguageQueryParam requestBody = request.getBody();
        MsbParamDTO paramDTO = new MsbParamDTO();
        paramDTO.setUrl(DICT_GET_URL);
        paramDTO.setMethodType(RequestTypeEnum.GET);
        paramDTO.setServiceAliasEnum(ServiceAliasEnum.COMMON);
        paramDTO.setParams(requestBody);

        MsaRpcResponse<DictLanguageDTO> msaRpcResponse = new MsaRpcResponse<>();
        try {
            DictLanguageDTO dictLanguageDTO = MSBUtils.invokeServiceAndReturnBO(paramDTO, new TypeReference<ServiceData<DictLanguageDTO>>() {
            });
            msaRpcResponse.setBo(dictLanguageDTO);
            msaRpcResponse.setCode(MsaRpcResponse.SUCCESS_CODE);
            logger.info("数据字典查询成功，params：{}，res：{}", paramDTO, msaRpcResponse);
        } catch (Exception e) {
            msaRpcResponse.setEx(e);
            msaRpcResponse.setCode(RetCode.BUSINESSERROR_CODE);
            msaRpcResponse.setMsg(ErrorMsgConstant.DICT_QUERY_ERR);
            logger.error("数据字典查询失败，params：{}，res：{}", paramDTO, msaRpcResponse);
        }
        return msaRpcResponse;
    }

    /**
     * 精确查询数据字典
     * @param request   查询请求
     * @return MsaRpcResponse<List<DictLanguageDTO>>
     * <AUTHOR>
     * date: 2023/7/24 14:44
     */
    @Override
    public MsaRpcResponse<List<DictLanguageDTO>> postExactQueryDictLanguage(MsaRpcRequest<DictLanguageQueryParam> request) {
        return this.list(request, DICT_EXACT_LIST_POST_URL, RequestTypeEnum.POST);
    }

    /**
     * 根据字典类型模糊查询，其他条件为精确查询
     * @param request
     * @return MsaRpcResponse<java.util.List<DictLanguageDTO>>
     * <AUTHOR>
     * date: 2023/7/24 14:47
     */
    @Override
    public MsaRpcResponse<List<DictLanguageDTO>> fuzzQueryByType(MsaRpcRequest<DictLanguageQueryParam> request) {
        return this.list(request, DICT_FUZZY_QUERY_BY_TYPE_URL, RequestTypeEnum.GET);
    }

    /**
     * 根据条件查询列表
     * @param request   查询入参
     * @param url   查询url
     * @param methodType    方法类型
     * @return MsaRpcResponse<List<DictLanguageDTO>>
     * <AUTHOR>
     * date: 2023/7/24 14:36
     */
    private MsaRpcResponse<List<DictLanguageDTO>> list(MsaRpcRequest<DictLanguageQueryParam> request, String url,
                                                      RequestTypeEnum methodType) {
        DictLanguageQueryParam requestBody = request.getBody();
        MsbParamDTO paramDTO = new MsbParamDTO();
        paramDTO.setUrl(url);
        paramDTO.setMethodType(methodType);
        paramDTO.setServiceAliasEnum(ServiceAliasEnum.COMMON);
        paramDTO.setParams(requestBody);

        MsaRpcResponse<List<DictLanguageDTO>> msaRpcResponse = new MsaRpcResponse<>();
        try {
            List<DictLanguageDTO> list = MSBUtils.invokeServiceAndReturnBO(paramDTO, new TypeReference<ServiceData<List<DictLanguageDTO>>>() {
            });
            msaRpcResponse.setBo(list);
            msaRpcResponse.setCode(MsaRpcResponse.SUCCESS_CODE);
            logger.info("数据字典查询成功，params：{}，res：{}", paramDTO, msaRpcResponse);
        } catch (Exception e) {
            msaRpcResponse.setEx(e);
            msaRpcResponse.setCode(RetCode.BUSINESSERROR_CODE);
            msaRpcResponse.setMsg(ErrorMsgConstant.DICT_QUERY_ERR);
            logger.error("数据字典查询错误，params：{}，res：{}", paramDTO, msaRpcResponse);
        }
        return msaRpcResponse;
    }

}
