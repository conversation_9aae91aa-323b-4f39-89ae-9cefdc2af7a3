package com.zte.mcrm.activity.application.activity;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.isearch.model.vo.HomePageQueryRequestVO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryResponseVO;

/**
 * 活动查询
 *
 * <AUTHOR>
 * @date 2024/2/2 上午11:23
 */
public interface ActivityQueryAppService {

    /**
     * 活动统计信息查询
     *
     * @param bizRequest
     * @return {@link ServiceData<HomePageQueryResponseVO>}
     * <AUTHOR>
     * @date 2024/2/2 上午11:25
     */
    ServiceData<HomePageQueryResponseVO> activityCountInfo(BizRequest<HomePageQueryRequestVO> bizRequest);
}
