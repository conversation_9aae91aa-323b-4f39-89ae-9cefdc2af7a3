package com.zte.mcrm.activity.service.activitylist.param;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 根据信息查询，将结果填充到对应的：
 * {@link  com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource}
 * <pre>
 *     对于如果已经有《活动信息》，则可以直接设置setActivityInfoList进来，这可以可以避免不必要的查询
 *     对于参数：activityRowIdList、activityInfoList二选一。服务优先使用activityRowIdList
 *
 *     对于要获取展会、样板点。需要将needActivity=true（默认是true），因为他们的信息依赖活动信息
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ActivityDataSourceQuery {
    /**
     * 活动ID
     */
    private List<String> activityRowIdList;
    /**
     * 《扩展活动id，活动主信息》 （setActivityInfoList）
     */
    private List<ActivityInfoDO> activityInfoList;

    /**
     * 需要活动信息
     */
    private boolean needActivity = true;

    /**
     * 需要活动《关联的交流方向信息》 setDirectionMap
     */
    private boolean needActivityDirection;
    /**
     * 需要活动《关联的客户信息》 setCustomerInfoMap
     */
    private boolean needActivityCustomerInfo;
    /**
     * 需要活动《关联的客户参与人信息》 setCustPeopleMap
     */
    private boolean needActivityCustomerPeople;
    /**
     * 需要活动《关联的我司参与人信息》 setZtePeopleMap
     */
    private boolean needActivityZtePeople;
    /**
     * 需要活动《关联的AP信息》 setSummaryApMap
     */
    private boolean needAp;
    /**
     * 需要活动《关联的RDC信息》 summaryRdcMap
     */
    private boolean needRdc;

    /**
     * 需要活动《关联的展会信息》 setActivityRowId2ExhibitionMap
     */
    private boolean needExhibition;
    /**
     * 需要活动《关联的样板点信息》 setActivityRowId2SamplePointMap
     */
    private boolean needSample;

    /**
     * 需要活动《关联的待办信息》 setPendingNoticeMap
     */
    private boolean needPending;

    /**
     * 获取需要查询的活动id
     *
     * @return
     */
    public List<String> fetchActivityRowIdList() {
        if (CollectionUtils.isNotEmpty(activityRowIdList)) {
            return activityRowIdList;
        }

        if (CollectionUtils.isNotEmpty(activityInfoList)) {
            return activityInfoList.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
