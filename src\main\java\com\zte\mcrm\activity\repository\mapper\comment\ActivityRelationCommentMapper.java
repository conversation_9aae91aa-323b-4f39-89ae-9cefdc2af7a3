package com.zte.mcrm.activity.repository.mapper.comment;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.comment.ActivityRelationCommentDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityRelationCommentMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationCommentDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationCommentDO record);

    /**
     * query by primary key
     */
    ActivityRelationCommentDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationCommentDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationCommentDO record);

    /**
     * 新工号切换
     */
    List<ActivityRelationCommentDO> queryEmpNoTransList(DataTransParam searchParam);
}