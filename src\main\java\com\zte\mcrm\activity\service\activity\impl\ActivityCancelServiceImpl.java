package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.enums.resource.ReserveScheduleOriTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.NoticeUtil;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.integration.approval.ApprovalBaseService;
import com.zte.mcrm.activity.integration.infocenter.InfoCenterService;
import com.zte.mcrm.activity.integration.infocenter.vo.SendWorkOrderSimpleVO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceReservationScheduleRepository;
import com.zte.mcrm.activity.service.activity.ActivityCancelService;
import com.zte.mcrm.activity.service.activity.param.ActivityCancelMessageParam;
import com.zte.mcrm.activity.service.activity.param.ActivityCancelParam;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.event.ActivityEventService;
import com.zte.mcrm.activity.service.event.convert.ActivityEventConvert;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.service.model.activity.ActivityBO;
import com.zte.mcrm.activity.service.reservation.ResourceReleaseService;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleParam;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleService;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventVO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.customermgr.util.LocalMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.enums.activity.ActivityEventOperationTypeEnum.*;

/**
 * 活动取消
 *
 * <AUTHOR>
 * @date 2023/9/1 下午1:43
 */
@Slf4j
@Service
public class ActivityCancelServiceImpl implements ActivityCancelService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityCancelServiceImpl.class);

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ActivityStatusLifecycleRepository activityStatusLifecycleRepository;

    @Autowired
    private ActivityApprovalInfoRepository activityApprovalInfoRepository;

    @Autowired
    private ApprovalBaseService approvalBaseService;

    @Autowired
    private ResourceReleaseService resourceReleaseService;

    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Autowired
    private InfoCenterService infoCenterService;

    @Autowired
    private ActivityEventService activityEventService;
    @Autowired
    private ActivityApprovalProcessRepository activityApprovalProcessRepository;
    @Autowired
    private ActivityApprovalProcessNodeRepository activityApprovalProcessNodeRepository;
    @Autowired
    private ActivityUrlConfig activityUrlConfig;
    @Autowired
    private ActivityISearchService iSearchService;
    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;
    @Autowired
    private ActivityResourceReservationScheduleRepository activityResourceReservationScheduleRepository;
    @Autowired
    private ScheduleOrchestrationSynScheduleService synScheduleService;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    /**
     * 活动取消
     *
     * @param request
     * @return {@link ServiceData<Void>}
     * <AUTHOR>
     * @date 2023/9/1 下午1:44
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Override
    public ServiceData<Void> cancel(BizRequest<ActivityCancelParam> request) {
        ActivityCancelParam activityCancelParam = request.getParam();
        String activityRowId = activityCancelParam.getActivityRowId();
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityRowId);
        if (Objects.isNull(activityInfoDO)) {
            return ServiceDataUtils.buildServiceData(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage(I18nConstant.ACTIVITY_NOT_FIND));
        }
        if (!ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), ActivityStatusEnum.COMPLIANCE_APPROVAL,
                ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.PROGRESS)) {
            return ServiceDataUtils.buildServiceData(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage(I18nConstant.ACTIVITY_CURRENT_STATUS_CAN_NOT_CANCEL));
        }

        cancelActivity(activityInfoDO);
        // 生命周期
        saveLifecycle(activityRowId, activityInfoDO.getActivityStatus(), ActivityStatusEnum.REVOKED.getCode());
        // 会议纪要,评价
        cancelPendingNotice(activityRowId);
        // 关闭事件
        closeEvent(activityInfoDO);
        // 日程
        releaseSchedule(activityRowId);

        try {
            // 审批
            revokeApproval(activityRowId);
            // 发送通知
            sendMessage(activityInfoDO,activityCancelParam.isChangeNoticeFlag());
        } catch (Exception e) {
            logger.error("发送icenter通知异常, activity: {}", activityRowId, e);
        }
        return ServiceDataUtils.success(null);
    }

    /**
     * 取消活动
     *
     * @param activityInfoDO
     * <AUTHOR>
     * @date 2023/9/1 下午3:55
     */
    private void cancelActivity(ActivityInfoDO activityInfoDO) {
        ActivityInfoDO update = new ActivityInfoDO();
        update.setRowId(activityInfoDO.getRowId());
        update.setActivityStatus(ActivityStatusEnum.REVOKED.getCode());
        activityInfoRepository.updateByPrimaryKeySelective(update);
    }

    /**
     * 保存生命周期记录
     *
     * @param activityId 活动id
     * @param statusFrom 变更前状态
     * @param statusTo   变更后状态
     * <AUTHOR>
     * @date 2023/8/29 下午7:47
     */
    private void saveLifecycle(String activityId, String statusFrom, String statusTo) {
        ActivityStatusLifecycleDO lifecycleDO = new ActivityStatusLifecycleDO();
        lifecycleDO.setActivityRowId(activityId);
        lifecycleDO.setStatusFrom(statusFrom);
        lifecycleDO.setStatusTo(statusTo);
        lifecycleDO.setEnterTime(new Date());
        activityStatusLifecycleRepository.insertSelective(Collections.singletonList(lifecycleDO));
    }

    /**
     * 取消流程实例
     *
     * @param activityId 活动id
     * <AUTHOR>
     * @date 2023/8/29 下午8:25
     */
    @Override
    public void revokeApproval(String activityId) {
        List<ActivityApprovalInfoDO> approvalInfoDOList = activityApprovalInfoRepository.queryAllByActivityRowId(activityId);
        approvalInfoDOList = approvalInfoDOList.stream().filter(e -> BooleanEnum.N.isMe(e.getInstanceStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalInfoDOList)) {
            return;
        }

        this.cancelActivityApprovalInfo(approvalInfoDOList);
        approvalInfoDOList.forEach(e -> {
            approvalBaseService.approvalReassignByFid(e.getInstanceId());
        });
    }

    private void cancelActivityApprovalInfo(List<ActivityApprovalInfoDO> approvalInfoDOList) {
        // 当前业务只会存在一条审批中的记录
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoDOList.get(0);
        String activityRowId = activityApprovalInfoDO.getActivityRowId();

        ActivityApprovalInfoDO updateApprovalInfoDo = new ActivityApprovalInfoDO();
        updateApprovalInfoDo.setRowId(activityApprovalInfoDO.getRowId());
        updateApprovalInfoDo.setInstanceStatus(BooleanEnum.Y.getCode());
        activityApprovalInfoRepository.updateByActivityRowIdSelective(updateApprovalInfoDo);
        Optional<ActivityApprovalProcessDO> activityApprovalProcessDOOptional =
                activityApprovalProcessRepository.queryByActivityRowId(activityRowId)
                        .stream().filter(e -> StringUtils.isBlank(e.getActivityApprovalInfoRowId()) ||
                                StringUtils.equals(activityApprovalInfoDO.getRowId(), e.getActivityApprovalInfoRowId()))
                        .filter(e -> ProcessStatusEnum.ACTIVE.isMe(e.getProcessStatus())).findAny();
        if (!activityApprovalProcessDOOptional.isPresent()) {
            return;
        }

        ActivityApprovalProcessDO activityApprovalProcessDO = activityApprovalProcessDOOptional.get();
        ActivityApprovalProcessDO updateApprovalProcessDO = new ActivityApprovalProcessDO();
        updateApprovalProcessDO.setRowId(activityApprovalProcessDO.getRowId());
        updateApprovalProcessDO.setProcessStatus(ProcessStatusEnum.CANCELED.getCode());
        activityApprovalProcessRepository.updateByPrimaryKeySelective(updateApprovalProcessDO);
        Optional<ActivityApprovalProcessNodeDO> activityApprovalProcessNodeDOOptional =
                activityApprovalProcessNodeRepository.queryAllByActivityRowId(activityRowId)
                        .stream().filter(e -> StringUtils.isBlank(e.getApprovalProcessRowId()) ||
                                StringUtils.equals(activityApprovalProcessDO.getRowId(), e.getApprovalProcessRowId()))
                        .filter(e -> ApproveNodeStatusEnum.ACTIVE.isMe(e.getNodeStatus()) || StringUtils.isBlank(e.getApproveResult())).findAny();
        if (!activityApprovalProcessNodeDOOptional.isPresent()) {
            return;
        }

        ActivityApprovalProcessNodeDO activityApprovalProcessNodeDO = activityApprovalProcessNodeDOOptional.get();
        ActivityApprovalProcessNodeDO updateApprovalProcessNodeDO = new ActivityApprovalProcessNodeDO();
        updateApprovalProcessNodeDO.setRowId(activityApprovalProcessNodeDO.getRowId());
        updateApprovalProcessNodeDO.setApproveResult(ApproveResultEnum.CANCELED.getCode());
        updateApprovalProcessNodeDO.setNodeStatus(ApproveNodeStatusEnum.CANCELED.getCode());
        activityApprovalProcessNodeRepository.updateByPrimaryKeySelective(updateApprovalProcessNodeDO);
    }

    /**
     * 释放日程资源
     *
     * @param activityId
     * <AUTHOR>
     * @date 2023/8/30 下午9:29
     */
    private void releaseSchedule(String activityId) {
        BizRequest<String> withCurrentUser = BizRequestUtil.createWithCurrentUser(activityId);
        //预约资源日程
        resourceReleaseService.releaseResource(withCurrentUser);
        //资源编排日程
        scheduleItemCancel(activityId);
    }

    /**
     * 取消关联了展会的日程数据
     * @param activityId
     * @return
     * <AUTHOR>
     * @date 2024/2/21
     */
    void scheduleItemCancel(String activityId) {
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityId);
        if (StringUtils.isBlank(activityInfoDO.getOriginRowId())){
            return;
        }

        //资源编排日程
        List<ActivityScheduleItemDO> relationScheduleInfoList = scheduleItemRepository.getRelationScheduleInfoList(Lists.newArrayList(activityId));
        if (CollectionUtils.isEmpty(relationScheduleInfoList)){
            return;
        }

        Map<String, ActivityResourceReservationScheduleDO> ori2Schedule = activityResourceReservationScheduleRepository.queryScheduleWithOriRowIds(
                relationScheduleInfoList.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList()), ReserveScheduleOriTypeEnum.ACTIVITY_SCHEDULE);
        for (ActivityScheduleItemDO item : relationScheduleInfoList) {
            ActivityResourceReservationScheduleDO schedule = ori2Schedule.get(item.getRowId());
            if (schedule == null) {
                continue;
            }
            ScheduleOrchestrationSynScheduleParam scheduleParam = new ScheduleOrchestrationSynScheduleParam();
            BeanUtils.copyProperties(item,scheduleParam);
            synScheduleService.cancelIcenterSchedule(BizRequestUtil.createWithCurrentUser(scheduleParam),schedule);
        }
    }

    /**
     * 取消待办
     *
     * @param activityId
     * <AUTHOR>
     * @date 2023/8/31 下午5:00
     */
    private void cancelPendingNotice(String activityId) {
        List<ActivityPendingNoticeDO> pendingNoticeDOList
                = activityPendingNoticeRepository.queryAllPending(activityId, null, PendingNoticeStatusEnum.WAIT_DEAL);
        if (CollectionUtils.isEmpty(pendingNoticeDOList)) {
            return;
        }
        List<String> ids = pendingNoticeDOList.stream().map(ActivityPendingNoticeDO::getRowId).collect(Collectors.toList());
        ActivityPendingNoticeDO update = new ActivityPendingNoticeDO();
        update.setPendingStatus(PendingNoticeStatusEnum.DELETE.getStatus());
        activityPendingNoticeRepository.batchUpdate(ids, update);
    }

    /**
     * 发送icenter通知
     *
     * @param activityInfoDO
     * <AUTHOR>
     * @date 2023/9/1 上午9:57
     */
    private void sendMessage(ActivityInfoDO activityInfoDO,boolean changeNoticeFlag) {
        List<ActivityRelationZtePeopleDO> ztePeopleList = activityRelationZtePeopleRepository.queryAllZtePeopleForActivity(activityInfoDO.getRowId());

        List<String> sendToEmpNos = ActivityInfoConverter.getSendICenterEmpNo(activityInfoDO, ztePeopleList,changeNoticeFlag);
        //活动撤销 新增客户参与人
        List<ActivityCustomerInfoDO> customerInfoList = activityCustomerInfoRepository.queryAllByActivityRowId(activityInfoDO.getRowId());
        List<ActivityRelationCustPeopleDO> listCustPeopleInfo = activityRelationCustPeopleRepository.queryAllByActivityRowId(activityInfoDO.getRowId());
        ActivityBO activityBO = new ActivityBO();
        activityBO.setActivityInfoDO(activityInfoDO);
        activityBO.setListCustInfo(customerInfoList);
        activityBO.setListCustPeopleInfo(listCustPeopleInfo);
        List<String> addCustomNoticePeopleList = NoticeUtil.getAddOrRemoveCustomPeopleList(Lists.newArrayList(), activityBO, true,false);
        sendToEmpNos.addAll(addCustomNoticePeopleList);
        log.info("[取消活动]新增通知人：{}", sendToEmpNos);

        if (CollectionUtils.isEmpty(sendToEmpNos)) {
            return;
        }
        String detailUrl = activityUrlConfig.fetchDetailUrl(activityInfoDO.getRowId(), activityInfoDO.getActivityType(), "first");
        ActivityCancelMessageParam messageParam = new ActivityCancelMessageParam();
        String operator = HeadersProperties.getXEmpNo();
        messageParam.setOperatorEmpNo(operator);
        messageParam.setSendToEmpNos(sendToEmpNos);
        messageParam.setActivityId(activityInfoDO.getRowId());
        messageParam.setDetailUrl(detailUrl);
        MsaRpcResponse<Map<String, String>> rpcResponse =
                hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(Sets.newHashSet(operator)));
        if (MapUtils.isNotEmpty(rpcResponse.getBo())) {
            messageParam.setOperatorName(rpcResponse.getBo().get(operator));
        }


        messageParam.setActivityTitle(activityInfoDO.getActivityTitle());
        messageParam.setStartTime(activityInfoDO.getStartTime());
        messageParam.setEndTime(activityInfoDO.getEndTime());
        messageParam.setActivityPlace(activityInfoDO.getActivityPlace());
        //拓展活动类型、国家、城市
        messageParam.setActivityType(activityInfoDO.getActivityType());
        messageParam.setCountryCodeName(activityInfoDO.getCountryCodeName());
        messageParam.setCityCodeName(activityInfoDO.getCityCodeName());


        SendWorkOrderSimpleVO simpleVO = ActivityInfoConverter.buildActivityCancelNotice(messageParam);
        infoCenterService.sendActivityWorkNoticeMessage(simpleVO);
    }

    /**
     * 关闭 关闭会议纪要 事件
     *
     * @param activityInfoDO
     * <AUTHOR>
     * @date 2023/9/1 下午3:48
     */
    private void closeEvent(ActivityInfoDO activityInfoDO) {
        ActivityRelationEventVO activityRelationEventVO = ActivityEventConvert
                .buildEventVoByActivityDo(activityInfoDO, null, StringUtils.EMPTY);
        BizRequest<ActivityRelationEventVO> request = BizRequestUtil.createWithCurrentUser(activityRelationEventVO);
        activityEventService.closeTargetEvent(request, CLOSE_SUMMARY, UPLOAD_SUMMARY, UPLOAD_SUMMARY_PENDING, UPLOAD_SUMMARY_ICENTER);
    }

}
