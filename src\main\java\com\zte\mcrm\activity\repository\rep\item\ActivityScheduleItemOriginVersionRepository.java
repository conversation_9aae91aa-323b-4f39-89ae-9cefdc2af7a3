package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginVersionDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivityScheduleItemOriginVersionRepository {

    /**
     * 批量插入数据
     * @param recordList
     * @return
     */
    int batchInsert(List<ActivityScheduleItemOriginVersionDO> recordList);


    /**
     * 根据活动RowId获取[最新]源数据信息
     *
     * @param activityRowIds 活动RowId
     * @return
     */
    Map<String, ActivityScheduleItemOriginVersionDO> getRelationScheduleItemOriginInfos(List<String> activityRowIds);

    /**
     * 根据活动RowId获取[指定版本层级的]源数据信息
     *
     * @param activityRowIds   活动RowId
     * @param backVersionLevel 指定版本层级 如：获取最新版本则传0，获取上一个版本传1，获取上上个版本传2，依次类推
     * @return
     */
    Map<String, ActivityScheduleItemOriginVersionDO> getRelationScheduleItemOriginInfos(List<String> activityRowIds, Integer backVersionLevel);
}
