package com.zte.mcrm.activity.integration.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.ai.dto.AiAssistantDTO;
import com.zte.mcrm.activity.integration.ai.dto.AiAssistantResponseDTO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.common.ServiceDataUtil;
import com.zte.mcrm.common.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.zte.itp.msa.core.constant.SysGlobalConst.HTTP_HEADER_X_LANG_ID;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/23 15:54
 */
@Component
@Slf4j
public class AiAssistantService {

    @Value("${ai.assistant.url:https://boassistant.test.zte.com.cn/zte-icrm-ai-assistant/generateActivity}")
    private String aiAssistantUrl;

    /**
     * @param content
     * @return
     */
    public AiAssistantResponseDTO aiAssistantProcess(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        Map<String, String> headerParamsMap = Maps.newHashMap();
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, HeadersProperties.getXEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, HeadersProperties.getXAuthValue());
        headerParamsMap.put(HTTP_HEADER_X_LANG_ID, "zh_CN");
        headerParamsMap.put("Content-Type", "application/json");
        AiAssistantDTO assistantDTO = new AiAssistantDTO();
        assistantDTO.setContext(content);
        // 请求
        try {
            log.info("AI解析文本入参:{}", JSON.toJSONString(assistantDTO));
            String result = HttpClientUtil.httpPostWithJSON(aiAssistantUrl, JSON.toJSONString(assistantDTO), headerParamsMap);
            log.info("AI解析文本返回结果:{}", result);
            result = JsonSanitizer.sanitize(result);
            ServiceData<AiAssistantResponseDTO> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<AiAssistantResponseDTO>>() {
            }.getType());
            if (ServiceDataUtil.validSuccess(serviceData)) {
                return serviceData.getBo();
            }
        } catch (Exception e) {
            log.error("Error occurs when queryUserInfo", e);
        }
        return null;
    }

}
