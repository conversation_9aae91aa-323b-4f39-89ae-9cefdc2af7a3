package com.zte.mcrm.activity.service.activity.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.cache.client.HrOrgDataCacheClient;
import com.zte.mcrm.activity.common.cache.model.HrOrgDataModel;
import com.zte.mcrm.activity.common.config.CustomerIntegrationUppConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupDto;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupSearchDTO;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationSolutionRepository;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.service.activity.ActivityCheckService;
import com.zte.mcrm.activity.service.activity.ActivityCustomerService;
import com.zte.mcrm.activity.service.activity.convert.ActivityCheckConvert;
import com.zte.mcrm.activity.service.activity.util.ActivityApprovalUtil;
import com.zte.mcrm.activity.service.activitylist.impl.ActivityInfoListQueryServiceImpl;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityOperatorFlagVO;
import com.zte.mcrm.activity.service.common.lookup.CommunicationDirectorComponent;
import com.zte.mcrm.activity.service.common.lookup.model.CommunicationDirectorTwoLevelModel;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.dict.impl.DictServiceImpl;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityDepartmentParam;
import com.zte.mcrm.activity.web.controller.activity.vo.*;
import com.zte.mcrm.activity.web.controller.activitylist.vo.*;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.constant.ExternalConstant;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.common.util.StringExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.*;
import static com.zte.mcrm.activity.common.constant.DictConstant.*;
import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.common.constant.LookupConstant.COMMUNICATION_DIRECTOR_LEVEL1;
import static com.zte.mcrm.activity.common.constant.LookupConstant.PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.LECTURER;
import static com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum.PROGRESS;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.CustBelongDepartmentTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.SanctionedPartyEnum.SANCTIONED_PARTY_EMBARGO;
import static com.zte.mcrm.activity.service.common.lookup.CommunicationDirectorComponent.PRODUCT_OPT;

/**
 * 活动校验服务类
 *
 * <AUTHOR> 10333830
 * @since  2023-08-31 21:14
 */
@Slf4j
@Service
public class ActivityCheckServiceImpl implements ActivityCheckService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityCheckServiceImpl.class);

    @Autowired
    private ActivityInfoRepository infoRepository;
    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;
    @Autowired
    private ActivityInfoListQueryServiceImpl infoListQueryService;
    @Autowired
    private ActivityCustomerService customerService;
    @Autowired
    private ActivityRelationZtePeopleRepository ztePeopleRepository;
    @Autowired
    private ActivityRelationSolutionRepository solutionRepository;
    @Autowired
    private ActivityRelationAttachmentRepository attachmentRepository;
    @Autowired
    private ActivityCustomerInfoRepository customerInfoRepository;
    @Autowired
    private LocaleMessageSourceBean lmsb;

    @Autowired
    private SamplePointInfoRepository samplePointInfoRepository;

    @Autowired
    private ActivityCommunicationDirectionRepository communicationDirectionRepository;

    @Autowired
    private CustomerIntegrationUppConfig customerIntegrationUppConfig;

    @Autowired
    private CommunicationDirectorComponent communicationDirectorComponent;
    @Autowired
    private DictServiceImpl dictService;
    @Autowired
    private LookUpExtService lookUpExtService;
    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;
    @Autowired
    private HrOrgDataCacheClient hrOrgDataCacheClient;

    @Override
    public boolean checkActivityChangePermissionAndStatusIsValid(BizRequest<ActivityBaseInfoVO> request) {
        if (request == null || request.getParam() == null) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ACTIVITY_ID_EMPTY);
        }

        ActivityBaseInfoVO activityBaseInfo = request.getParam();
        String activityRowId = activityBaseInfo.getRowId();
        ActivityInfoDO activityInfoDo = infoRepository.selectByPrimaryKey(activityRowId);
        if (activityInfoDo == null) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ACTIVITY_ID_EMPTY);
        }

        // 1. 活动状态校验-仅进行中的活动可以变更
        if (!ActivityStatusEnum.in(activityInfoDo.getActivityStatus(), PROGRESS)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ONLY_PROGRESS_ACTIVITY_CAN_CHANGE);
        }

        // 2. 操作人权限校验 - 仅有变更权限的人才允许变更（需要对不同的活动类型进行区分）
        List<ActivityInfoVO> infoVOList =
                infoListQueryService.getActivityAuthInfo(BizRequestUtil.createWithCurrentUser(Collections.singletonList(activityRowId)));
        if (CollectionUtils.isEmpty(infoVOList)) {
            throw new BizRuntimeException(RetCode.PERMISSIONDENIED_CODE, RetCode.PERMISSIONDENIED_MSGID);
        }
        ActivityInfoVO activityInfoVO = infoVOList.get(ZERO);
        ActivityOperatorFlagVO operatorFlagVO = activityInfoVO.getOperatorFlagInfo();
        if (operatorFlagVO == null || !Boolean.TRUE.equals(operatorFlagVO.getChangeable())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NO_OPERATOR_AUTH);
        }
        return true;
    }

    /**
     * 校验讲师方案信息
     * @param request   请求
     * @return com.zte.mcrm.activity.common.model.BizResult<java.lang.String>
     * <AUTHOR>
     * date: 2024/1/18 21:12
     */
    @Override
    public BizResult<String> checkLectureSolution(BizRequest<String> request){
        String activityRowId = request.getParam();
        if (StringUtils.isBlank(activityRowId)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ACTIVITY_ID_EMPTY);
        }
        List<ActivityRelationZtePeopleDO> peopleList = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        List<ActivityRelationSolutionDO> listSolution = solutionRepository.queryAllSolutionForActivity(activityRowId);
        List<ActivityRelationAttachmentDO> listAttachment = attachmentRepository.queryAllByActivityRowId(activityRowId);
        List<ZtePeopleVO> listZtePeopleVo = ActivityInfoConverter.assembleZtePeopleInfo(peopleList, listSolution, listAttachment);
        List<ZtePeopleVO> listLecturer = CollectionExtUtils.getListOrDefaultEmpty(listZtePeopleVo)
                .stream().filter(e -> LECTURER.isMe(e.getPeopleType())).collect(Collectors.toList());

        //获取活动
        String activityDepartmentType = getDepartmentTypeByActivityId(activityRowId);

        String res = StringUtils.EMPTY;
        for (ZtePeopleVO e : CollectionExtUtils.getListOrDefaultEmpty(listLecturer)) {
            res = StringExtUtils.getOrDefault(res, checkLecture(activityDepartmentType, e));
        }
        return BizResult.buildSuccessRes(StringExtUtils.getOrDefault(res, activityRowId));
    }

    /**
     * 通过活动Id获取活动归属类型
     * @param activityRowId
     * @return
     * <AUTHOR>
     * @date 2024/5/28
     */
    private String getDepartmentTypeByActivityId(String activityRowId) {
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        String parentCommunication = getActivityCommunicationParentType(activityRowId);
        String secondLevelOrg = getSecondLevelOrgCode(activityInfoDO.getApplyDepartmentNo());
        String activityDepartmentType = getActivityDepartmentType(secondLevelOrg, parentCommunication);
        return activityDepartmentType;
    }

    /**
     * 获取业务归属部门对应的二层组织编码
     * @param applyDepartmentNo
     * @return
     * <AUTHOR>
     * @date 2024/5/15
     */
    public String getSecondLevelOrgCode(String applyDepartmentNo) {
        Map<String, OrgInfoVO> orgInfoMap = getOrgInfoMap(Collections.singletonList(applyDepartmentNo));
        OrgInfoVO vo = orgInfoMap.get(applyDepartmentNo);
        if (null == vo){
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.department.can.not.empty");
        }
        String orgIDPath = vo.getOrgIDPath();
        String[] departmentArray = orgIDPath.split(CharacterConstant.SHORT_BAR_ZH);
        return departmentArray.length > NumberConstant.ONE ? departmentArray[NumberConstant.ONE] : "";
    }

    /**
     * 获取活动交流方向对应的父层级
     * @param activityRowId
     * @return
     * <AUTHOR>
     * @date 2024/5/15
     */
    private String getActivityCommunicationParentType(String activityRowId) {
        List<ActivityCommunicationDirectionDO> listCommunication = communicationDirectionRepository.queryAllByActivityRowId(activityRowId);
        if (CollectionUtils.isEmpty(listCommunication)){
            return StringUtils.EMPTY;
        }
        List<CommunicateDirectionVO> communicateDirections = listCommunication.stream().map(x -> {
            CommunicateDirectionVO vo = new CommunicateDirectionVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());

        List<List<String>> listCommunicationResult = getListCommunicationResult(communicateDirections);
        return listCommunicationResult.stream().map(x -> x.get(0)).distinct().collect(Collectors.toList()).get(0);
    }

    /**
     * 根据业务归属部门二层组织和活动交流方向父节点
     * @param secondLevelOrg
     * @param parentCommunication
     * @return
     * <AUTHOR>
     * @date 2024/5/15
     */
    public String getActivityDepartmentType(String secondLevelOrg, String parentCommunication) {
        List<DictLanguageDTO> dictLanguages = dictService.exactQueryListByType(ACTIVITY_BUSINESS_DEPARTMENT);
        if (CollectionUtils.isEmpty(dictLanguages)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.department.type.is.empty");
        }
        return getDepartmentType(secondLevelOrg, parentCommunication, dictLanguages);
    }

    /**
     * 获取活动类型
     * @param secondLevelOrg
     * @param parentCommunication
     * @param dictLanguages
     * @return
     * <AUTHOR>
     * @date 2024/5/16
     */
    private static String getDepartmentType(String secondLevelOrg, String parentCommunication, List<DictLanguageDTO> dictLanguages) {
        String departmentType = OTHER.getType();
        for (int i = 0; i < dictLanguages.size(); i++) {
            DictLanguageDTO dictLanguageDTO = dictLanguages.get(i);
            String dictKey = dictLanguageDTO.getDictKey();
            String dictValue = dictLanguageDTO.getDictValue();
            //三营
            if (DEPARTMENT_SALES.equals(dictKey)) {
                if (ActivityCheckConvert.salesJudgment(dictValue, secondLevelOrg, parentCommunication)) {
                    departmentType = SALE3.getType();
                    break;
                }
            }
            //政企
            if (DEPARTMENT_GOV.equals(dictKey)) {
                if (ActivityCheckConvert.govJudgment(dictValue, secondLevelOrg, parentCommunication)) {
                    departmentType = GOV_ENTERPRISE.getType();
                    break;
                }
            }
            //海外
            if (DEPARTMENT_OVERSEAS.equals(dictKey) && dictValue.contains(secondLevelOrg)) {
                departmentType = OVERSEAS.getType();
                break;
            }
        }
        return departmentType;
    }

    /**
     * 校验活动提交数据
     * @param baseInfoVO    活动基本信息
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/31 21:23
     */
    @Override
    public boolean checkActivitySubmitData(ActivityBaseInfoVO baseInfoVO) {
        // 客户信息校验
        customerService.operatorCheckAndFillCustomerInfo(baseInfoVO.getListCustInfo(), baseInfoVO.getListCustPeopleInfo(), baseInfoVO.getActivityType());
        if (StringUtils.isBlank(baseInfoVO.getActivityType())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.type.can.not.be.null");
        }
        ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.getEnumByType(baseInfoVO.getActivityType());
        if (activityTypeEnum == null) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.type.is.error");
        }

        checkCommonInfo(baseInfoVO);

        // 根据汤老师的建议，创建对应的VO使用Validation框架进行校验
        switch (activityTypeEnum) {
            case UNIVERSAL_EXPANSION_ACTIVITY:
                ValidationUtils.validateMessage(baseInfoVO);
                break;
            case DAILY_VISIT_ACTIVITY:
                DailyVisitActivityBaseInfoVO dailyVo = new DailyVisitActivityBaseInfoVO();
                BeanUtils.copyProperties(baseInfoVO, dailyVo);
                ValidationUtils.validateMessage(dailyVo);
                break;
            case SENIOR_VISIT_EXPANSION:
                SeniorVisitActivityBaseInfoVO cuseniorVo = new SeniorVisitActivityBaseInfoVO();
                BeanUtils.copyProperties(baseInfoVO, cuseniorVo);
                ValidationUtils.validateMessage(cuseniorVo);
                break;
            case CUSTOMER_VISIT_ACTIVITY:
                CustomerVisitActivityBaseInfoVO custVo = new CustomerVisitActivityBaseInfoVO();
                BeanUtils.copyProperties(baseInfoVO, custVo);
                ValidationUtils.validateMessage(custVo);
                break;
            case JOIN_EXHIBITION: case JOIN_CONFERENCE: case VISITING_SAMPLE:
                JoinExhibitionActivityVO exhibitionActivityVO = new JoinExhibitionActivityVO();
                BeanUtils.copyProperties(baseInfoVO, exhibitionActivityVO);
                ValidationUtils.validateMessage(exhibitionActivityVO);
                break;
            default:
                break;
        }
        //校验客户信息、客户参与人、我司参与人
        checkOtherInfo(baseInfoVO);
        //校验活动来源,只校验展会和样板点
        checkActivityOrigin(baseInfoVO);
        // 校验资源申请信息
        checkResourceInfo(baseInfoVO);
        return true;
    }

    /**
     * 校验资源申请信息
     *
     * @param baseInfoVO
     * <AUTHOR>
     * @date 2024/10/15 下午8:11
     */
    private void checkResourceInfo(ActivityBaseInfoVO baseInfoVO) {
        // 目前只有展会报名需要校验资源申请信息
        if (!JOIN_EXHIBITION.isMe(baseInfoVO.getActivityType())) {
            return;
        }
        checkHotelResourceInfo(baseInfoVO);
        checkCarResourceInfo(baseInfoVO);
    }

    /**
     * 校验酒店资源
     *
     * @param baseInfoVO
     * <AUTHOR>
     * @date 2024/10/15 下午8:12
     */
    private void checkHotelResourceInfo(ActivityBaseInfoVO baseInfoVO) {
        if (!BooleanEnum.valid(baseInfoVO.getProvideHotelFlag())) {
            // 展会报名是否需要客户部提供酒店不能为空
            log.error("是否需要客户部提供酒店不能为空, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_HOTEL_RESOURCE_FLAG_CHECK_NULL_ERROR);
        }
        if (BooleanEnum.Y.isMe(baseInfoVO.getProvideHotelFlag()) && CollectionUtils.isEmpty(baseInfoVO.getListActivityResourceHotel())) {
            // 选是时，资源必填
            log.error("需要客户部提供酒店时，酒店资源申请不能为空, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_HOTEL_RESOURCE_RECORDS_CHECK_EMPTY_ERROR);
        }
        boolean isAmountInvalid = Optional.ofNullable(baseInfoVO.getListActivityResourceHotel()).orElse(Collections.emptyList()).stream()
                .anyMatch(item -> Objects.isNull(item.getAmount())
                        || (!BooleanEnum.Y.isMe(item.getPaySelf()) && item.getAmount().compareTo(BigDecimal.ZERO) <= 0));
        if (isAmountInvalid) {
            // 金额异常
            log.error("酒店资源申请费用信息异常, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_HOTEL_RESOURCE_AMOUNT_CHECK_ERROR);
        }
    }

    /**
     * 校验车辆资源
     *
     * @param baseInfoVO
     * <AUTHOR>
     * @date 2024/10/15 下午8:12
     */
    private void checkCarResourceInfo(ActivityBaseInfoVO baseInfoVO) {
        if (!BooleanEnum.valid(baseInfoVO.getProvideCarFlag())) {
            // 展会报名是否需要客户部提供车辆不能为空
            log.error("是否需要客户部提供车辆不能为空, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_CAR_RESOURCE_FLAG_CHECK_NULL_ERROR);
        }
        if (BooleanEnum.Y.isMe(baseInfoVO.getProvideCarFlag()) && CollectionUtils.isEmpty(baseInfoVO.getListActivityResourceCar())) {
            // 选是时，资源必填
            log.error("需要客户部提供车辆时，车辆资源申请不能为空, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_CAR_RESOURCE_RECORDS_CHECK_EMPTY_ERROR);
        }
        boolean isAmountInvalid = Optional.ofNullable(baseInfoVO.getListActivityResourceCar()).orElse(Collections.emptyList()).stream()
                .anyMatch(item -> Objects.isNull(item.getAmount()) || item.getAmount().compareTo(BigDecimal.ZERO) <= 0);
        if (isAmountInvalid) {
            log.error("车辆资源申请费用信息异常, 单号{}, 标题{}, 申请人{}", baseInfoVO.getRowId(), baseInfoVO.getActivityTitle(), baseInfoVO.getApplyPeopleNo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.ACTIVITY_CAR_RESOURCE_AMOUNT_CHECK_ERROR);
        }
    }

    /**
     * 校验是否有谈参编辑权限
     *
     * @param request
     * @return boolean
     * <AUTHOR>
     * date: 2024/1/24 17:11
     */
    @Override
    public BizResult<Boolean> hasSolutionEditAuth(BizRequest<String> request) {
        // 校验是否有方案操作权限-查询提单人/组织者/申请人/
        List<ActivityRelationZtePeopleDO> peopleList = ztePeopleRepository.queryByActivityRowIdAndPeopleType(request.getParam(),
                Lists.newArrayList(ActivityPeopleTypeEnum.CREATE_BY.getCode(), ActivityPeopleTypeEnum.ORGANIZER.getCode(),
                        ActivityPeopleTypeEnum.APPLICANT.getCode(), LECTURER.getCode()));
        List<ActivityRelationZtePeopleDO> hasAuthList = CollectionExtUtils.getListOrDefaultEmpty(peopleList).stream()
                .filter(e -> StringUtils.equals(request.getEmpNo(), e.getPeopleCode()))
                .collect(Collectors.toList());
        return BizResult.buildSuccessRes(CollectionUtils.isNotEmpty(hasAuthList));
    }

    @Override
    public BizResult<String> queryActivityDepartmentType(BizRequest<ActivityDepartmentParam> request) {
        ActivityDepartmentParam param = request.getParam();
        logger.info("获取业务归属类型参数param={}",JSON.toJSONString(param));
        //按照活动Id获取业务归属类型
        if (StringUtils.isNotBlank(param.getActivityId())){
            return BizResult.buildSuccessRes(getDepartmentTypeByActivityId(param.getActivityId()));
        }
        //按照业务部门和交流方向获取业务归属类型
        if (StringUtils.isNotBlank(param.getApplyDepartmentNo()) && CollectionUtils.isNotEmpty(param.getListCommunicationDirection())){
            List<List<String>> listCommunicationResult = getListCommunicationResult(param.getListCommunicationDirection());
            String parentCommunication = listCommunicationResult.stream().map(x -> x.get(0)).distinct().collect(Collectors.toList()).get(0);
            String secondLevelOrgCode = getSecondLevelOrgCode(param.getApplyDepartmentNo());
            return BizResult.buildSuccessRes(getActivityDepartmentType(secondLevelOrgCode, parentCommunication));
        }
        return BizResult.buildSuccessRes(null);
    }

    /**
     * 校验通用信息
     * @param baseInfoVO    活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/8/31 21:28
     */
    private void checkCommonInfo(ActivityBaseInfoVO baseInfoVO) {
//        checkCommunicationDirection(baseInfoVO);
        checkActivityPlace(baseInfoVO);
        //商务/技术/项目型交流时，需要校验交流方向：“运营商“或”新业务”或“政企”页签，产品必填
        checkCommunication(baseInfoVO);
        //活动类型为 商务/技术/项目型交流，需增加 至少选择一个讲师的校验，如校验讲师为空，提示：商务/技术/项目型交流要求至少选择一位讲师
        checkUniversalLecturer(baseInfoVO.getActivityType(),baseInfoVO.getListZtePeopleInfo());

        // 如果填写了交流层级，则必须是枚举中的
        boolean errorCommuLevel = StringUtils.isNotBlank(baseInfoVO.getCommunicationLevel())
                && null == CommunicationLevelEnum.getByCode(baseInfoVO.getCommunicationLevel());
        if (errorCommuLevel) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.communication.level.is.error");
        }

        // 除日程拜访、高层拜访、客户来访活动外的活动的归属部门为三营和政企的交流层级必填。
        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), DAILY_VISIT_ACTIVITY, SENIOR_VISIT_EXPANSION, CUSTOMER_VISIT_ACTIVITY)) {
            return;
        }
        List<CustUnitInfoVO> listCustInfo = baseInfoVO.getListCustInfo();
        if (CollectionUtils.isEmpty(listCustInfo)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.customer.info.can.not.be.null");
        }

        List<CommunicateDirectionVO> listCommunicationDirection = baseInfoVO.getListCommunicationDirection();
        List<List<String>> listCommunicationResult = getListCommunicationResult(listCommunicationDirection);
        String parentCommunication = listCommunicationResult.stream().map(x -> x.get(0)).distinct().collect(Collectors.toList()).get(0);
        String secondLevelOrgCode = getSecondLevelOrgCode(baseInfoVO.getApplyDepartmentNo());
        String activityDepartmentType = getActivityDepartmentType(secondLevelOrgCode, parentCommunication);
        // 三营和政企交流层级必填
        if (CustBelongDepartmentTypeEnum.in(activityDepartmentType, SALE3, GOV_ENTERPRISE) && StringUtils.isBlank(baseInfoVO.getCommunicationLevel())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.communication.level.is.error");
        }
    }

    /**
     * 活动类型为 商务/技术/项目型交流，需增加 至少选择一个讲师的校验，如校验讲师为空，提示：商务/技术/项目型交流要求至少选择一位讲师
     * @param activityType
     * @param listZtePeopleInfo
     * @return
     * <AUTHOR>
     * @date 2024/5/13
     */
    public void checkUniversalLecturer(String activityType,List<ZtePeopleVO> listZtePeopleInfo) {
        if (UNIVERSAL_EXPANSION_ACTIVITY.isMe(activityType)){
            if (CollectionUtils.isNotEmpty(listZtePeopleInfo)){
                boolean anyMatch = listZtePeopleInfo.stream().anyMatch(x -> LECTURER.isMe(x.getPeopleType()));
                if (!anyMatch){
                    throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_UNIVERSAL_EXPANSION_LECTURER_IS_EMPTY));
                }
            }
        }
    }

    /**
     * 商务/技术/项目型交流时，需要校验交流方向：“运营商“或”新业务”或“政企”页签，产品必填
     * @param baseInfoVO
     * @return
     * <AUTHOR>
     * @date 2024/5/9
     */
    public void checkCommunication(ActivityBaseInfoVO baseInfoVO) {
        //商务、技术、项目型交流时
        if (UNIVERSAL_EXPANSION_ACTIVITY.isMe(baseInfoVO.getActivityType())){
            String applyDepartmentNo = baseInfoVO.getApplyDepartmentNo();
            Map<String, OrgInfoVO> orgInfoMap = getOrgInfoMap(Collections.singletonList(applyDepartmentNo));
            OrgInfoVO vo = orgInfoMap.get(applyDepartmentNo);
            if (null != vo) {
                String orgIDPath = vo.getOrgIDPath();
                String[] departmentArray = orgIDPath.split(CharacterConstant.SHORT_BAR_ZH);
                String secondLevelOrg = departmentArray.length > NumberConstant.TWO ? departmentArray[NumberConstant.ONE] : "";

                DictLanguageDTO dictByTypeAndKey = dictService.getDictByTypeAndKey(APPLY_BUSINESS_DEPARTMENT, APPLY_BUSINESS_DEPARTMENT_KEY);
                String secondLevelDepartmentNos = Optional.ofNullable(dictByTypeAndKey).map(DictLanguageDTO::getDictValue).orElse(StringUtils.EMPTY);
                validateCommunication(baseInfoVO, secondLevelDepartmentNos, secondLevelOrg);
            }

        }
    }

    /**
     * 校验交流方向
     * @param baseInfoVO
     * @param secondLevelOrg
     * @param secondLevelDepartmentNos
     * @return
     * <AUTHOR>
     * @date 2024/5/13
     */
    public void validateCommunication(ActivityBaseInfoVO baseInfoVO, String secondLevelDepartmentNos, String secondLevelOrg) {
        //国内营销+国内营销+第三营销事业部
        if (secondLevelDepartmentNos.contains(secondLevelOrg)){
            List<CommunicateDirectionVO> listCommunicationDirection = baseInfoVO.getListCommunicationDirection();
            //获取交流方向父子级
            List<List<String>> listCommunicationResult = getListCommunicationResult(listCommunicationDirection);
            String parent = listCommunicationResult.stream().map(x -> x.get(0)).distinct().collect(Collectors.toList()).get(0);
            extractedValidate(parent, listCommunicationResult);
        }
    }

    public List<List<String>> getListCommunicationResult(List<CommunicateDirectionVO> listCommunicationDirection) {
        FastLookupSearchDTO searchDTOByParentType = new FastLookupSearchDTO();
        List<String> listParentLookupType = Arrays.asList(COMMUNICATION_DIRECTOR_LEVEL1, PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR);
        searchDTOByParentType.setListParentLookupType(listParentLookupType);
        List<FastLookupDto> listParentFastLook = lookUpExtService.batchSearch(searchDTOByParentType);
        Map<String, List<FastLookupDto>> mapByParentType = listParentFastLook.stream().collect(Collectors.groupingBy(FastLookupDto::getParentLookupType));
        List<FastLookupDto> listFastLookupParent = Lists.newArrayList();
        List<FastLookupDto> listFastLookupChild = Lists.newArrayList();
        if (MapUtils.isNotEmpty(mapByParentType)){
            listFastLookupParent = mapByParentType.get(COMMUNICATION_DIRECTOR_LEVEL1);
            listFastLookupChild = mapByParentType.get(PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR);
        }
        return ActivityInfoConverter.buildCommunicationResult(listCommunicationDirection, listFastLookupParent, listFastLookupChild);
    }

    /**
     * 根据部门编码获取组织信息
     * @param orgIdList
     * @return
     * <AUTHOR>
     * @date 2024/5/13
     */
    public Map<String, OrgInfoVO> getOrgInfoMap(List<String> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Maps.newHashMap();
        }
        try {
            Map<String, HrOrgDataModel> map = hrOrgDataCacheClient.fetchAllCache(new HashSet<>(orgIdList));
            Map<String, OrgInfoVO> res = new HashMap<>();
            map.forEach((id, val)-> res.put(id, val.toOrgInfoVO()));
            return res;
        } catch (Exception e) {
            logger.error("根据组织编码获取部门信息异常", e);
        }
        return Maps.newHashMap();
    }

    /**
     * 校验交流方向
     * @param parent
     * @param listCommunicationResult
     * @return
     * <AUTHOR>
     * @date 2024/5/11
     */
    public void extractedValidate(String parent, List<List<String>> listCommunicationResult) {
        //运营商“或”新业务”，产品必填
        if (ACTIVITY_COMMUNICATION_OPERATOR.equals(parent) || ACTIVITY_COMMUNICATION_NEW_BUSINESS.equals(parent)){
            List<List<String>> targetList = listCommunicationResult.stream().filter(x -> communicationProductValidate(x)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(targetList)){
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_COMMUNICATION_IS_EMPTY));
            }
        }

        if (ACTIVITY_COMMUNICATION_GOVERNMENT_ENTERPRISE.equals(parent)){
            //“政企”页签，产品和行业必填
            List<List<String>> targetProductList = listCommunicationResult.stream().filter(x -> ACTIVITY_COMMUNICATION_GOVERNMENT_ENTERPRISE_PRODUCT.contains(x.get(1))).collect(Collectors.toList());
            List<List<String>> targetIndustryList = listCommunicationResult.stream().filter(x -> ACTIVITY_COMMUNICATION_GOVERNMENT_ENTERPRISE_INDUSTRY_GE.contains(x.get(1))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(targetProductList) || CollectionUtils.isEmpty(targetIndustryList)){
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_COMMUNICATION_IS_EMPTY));
            }
        }
    }


    public Boolean communicationProductValidate(List<String> communications){
        if (ACTIVITY_COMMUNICATION_OPERATOR.contains(communications.get(0)) && ACTIVITY_COMMUNICATION_OPERATOR_PRODUCT.contains(communications.get(1))){
            return true;
        }
        if (ACTIVITY_COMMUNICATION_NEW_BUSINESS.contains(communications.get(0)) && ACTIVITY_COMMUNICATION_NEW_BUSINESS_PRODUCT.contains(communications.get(1))){
            return true;
        }
        return false;
    }

    void checkCommunicationDirection(ActivityBaseInfoVO baseInfoVO) {
        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), DAILY_VISIT_ACTIVITY)) {
            return;
        }

        List<CustUnitInfoVO> listCustInfo = baseInfoVO.getListCustInfo();
        AtomicBoolean isSales3Customer = new AtomicBoolean(false);
        listCustInfo.forEach(item -> {
            if (BooleanEnum.Y.isMe(item.getMainCust()) && SALE3.isMe(item.getCustBelongType())) {
                isSales3Customer.set(true);
            }
        });

        if (!isSales3Customer.get()) {
            return;
        }

        AtomicBoolean isContainProductOption = new AtomicBoolean(false);
        List<CommunicateDirectionVO> listCommunicationDirection = baseInfoVO.getListCommunicationDirection();
        CommunicationDirectorTwoLevelModel model = communicationDirectorComponent.fetchActivityCommunicationDirectorTwoLevelModel();
        for (CommunicateDirectionVO communicateDirectionVO : listCommunicationDirection) {
            String modelLookType = model.fetchLookType(communicateDirectionVO.getCommunicationDirection());
            if (PRODUCT_OPT.equals(modelLookType)) {
                isContainProductOption.set(true);
            }
        }
        if (!isContainProductOption.get()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.communication.direction.is.error");
        }

    }

    /**
     * 校验活动交流地点
     * @param baseInfoVO
     */
    public void checkActivityPlace(ActivityBaseInfoVO baseInfoVO) {
        // 这里public仅仅只是为了快速覆盖代码、分支
        String activityPlace = baseInfoVO.getActivityPlace();

        if (StringUtils.isNotBlank(activityPlace) && activityPlace.length() > NumberConstant.HUNDRED) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.communicate.place.error");
        }
    }

    /**
     * 校验其他信息
     * @param baseInfoVO    活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/8/31 21:28
     */
    private void checkOtherInfo(ActivityBaseInfoVO baseInfoVO) {
        List<CustUnitInfoVO> listCustInfo = baseInfoVO.getListCustInfo();
        List<CustUnitInfoVO> listMainCust = listCustInfo.stream().filter(e -> MainCustEnum.Y.isMe(e.getMainCust())).collect(Collectors.toList());
        List<CustPeopleInfoVO> listCustPeopleInfo = baseInfoVO.getListCustPeopleInfo();
        List<ZtePeopleVO> listZtePeopleInfo = baseInfoVO.getListZtePeopleInfo();
        List<ActivityScheduleItemVO> listScheduleInfo = baseInfoVO.getListScheduleInfo();
        if (CollectionUtils.isEmpty(listCustInfo)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.customer.info.can.not.be.null");
        }
        if (listMainCust.size() == 0) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "main.cust.should.be.least.one");
        }
        if (listMainCust.size() > 1) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "main.cust.should.be.only.one");
        }
        if (CollectionUtils.isEmpty(listCustPeopleInfo)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.customer.contact.can.not.be.null");
        }
        if (CollectionUtils.isEmpty(listZtePeopleInfo)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.zte.contact.can.not.be.null");
        }
        ValidationUtils.validateListMessage(baseInfoVO.getListCustInfo());
        ValidationUtils.validateListMessage(baseInfoVO.getListCustPeopleInfo());
        ValidationUtils.validateListMessage(baseInfoVO.getListZtePeopleInfo());
        ValidationUtils.validateListMessage(baseInfoVO.getListCommunicationDirection());

        //客户单位受限制主体扫描，客户联系人受限制主体扫描
        checkCustSanctionedPatry(listCustInfo, listCustPeopleInfo);
        // 活动日程信息字段校验
        checkActivityScheduleItem(listScheduleInfo);
    }

    void checkActivityScheduleItem(List<ActivityScheduleItemVO> listScheduleInfo) {
        if (CollectionUtils.isEmpty(listScheduleInfo)) {
            return;
        }

        for (ActivityScheduleItemVO scheduleItem : listScheduleInfo) {
            String placeName = scheduleItem.getPlaceName();

            if (StringUtils.isNotBlank(placeName) && placeName.length() > NumberConstant.TWO_HUNDRED) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "schedule.place.name.error");
            }
        }
    }

    /**
     * 校验讲师
     * @param activityDepartmentType  活动归属
     * @param lecture   讲师
     * @return void
     * <AUTHOR>
     * date: 2023/8/31 21:28
     */
    protected String checkLecture(String activityDepartmentType, ZtePeopleVO lecture) {
        //三营客户/政企 讲师必须要有方案
        if (departmentTypeIn(activityDepartmentType)) {
            if (StringUtils.isBlank(lecture.getSolutionUrl())) {
                return lmsb.getMessage("sales.division.third.need.solution",new Object[] {getDepartmentTypeEnumByType(activityDepartmentType)});
            }
        } else {
            //非三营客户 方案或者附件必须要有一个
            if (StringUtils.isAllBlank(lecture.getSolutionUrl(), lecture.getFileToken())) {
                return lmsb.getMessage("other.customer.need.attachment");
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 客户和联系人受限制主体扫描
     * @param listCustInfo 客户信息
     * @param listCustPeopleInfo 客户联系人
     */
    private void checkCustSanctionedPatry(List<CustUnitInfoVO> listCustInfo, List<CustPeopleInfoVO> listCustPeopleInfo) {
        for (CustUnitInfoVO custUnitInfo : listCustInfo) {
            if (SANCTIONED_PARTY_EMBARGO.isMe(custUnitInfo.getSanctionedPatryCode())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "embargo.check.error.info");
            }
            if (StringUtils.isBlank(custUnitInfo.getSanctionedPatryCode())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "new.customer.error.info");
            }
            // 每个客户至少要有一个联系人参与活动
            if (listCustPeopleInfo.stream().noneMatch(e -> StringUtils.equals(e.getCustomerCode(), custUnitInfo.getCustomerCode()))) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.customer.contact.can.not.be.null");
            }
        }
        // 为了解决sonar圈复杂度问题
        for (CustPeopleInfoVO custPeopleVO : listCustPeopleInfo) {
            if (SANCTIONED_PARTY_EMBARGO.isMe(custPeopleVO.getSanctionedPatryCode())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "embargo.check.error.info");
            }
            if (StringUtils.isBlank(custPeopleVO.getSanctionedPatryCode())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "new.customer.error.info");
            }
        }

    }

    /**
     * 校验活动来源信息
     * @param baseInfoVO
     */
    private void checkActivityOrigin(ActivityBaseInfoVO baseInfoVO) {
        //如果活动类型是展会，校验展会信息
        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE)) {
            //校验展会信息
            checkExhibitionInfo(baseInfoVO);
        }else if(ActivityTypeEnum.in(baseInfoVO.getActivityType(), VISITING_SAMPLE)){
            //校验样板点信息
            checkSamplePointInfo(baseInfoVO);
        }
    }

    private void checkExhibitionInfo(ActivityBaseInfoVO baseInfoVO) {
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(baseInfoVO.getOriginRowId());
        if (exhibitionInfo == null) {
            log.error("can not find exhibition {}, detail：{}", baseInfoVO.getOriginRowId(), JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "exhibition.info.not.exist");
        }
        if (!Objects.equals(exhibitionInfo.getPlaceResourceType(), baseInfoVO.getOriginType())) {
            log.error("exhibition type {} not equal activityOriginType {}", exhibitionInfo.getPlaceResourceType(), baseInfoVO.getOriginType());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "exhibition.type.dose.not.match");
        }
        // 只有新提交的报名才需要校验报名状态是否开启，变更的通过后面的变更权限来控制
        if (!BooleanEnum.Y.isMe(exhibitionInfo.getEntryOpenStatus())) {
            log.error("exhibition not open {}, detail：{}", exhibitionInfo.getEntryOpenStatus(), JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "exhibition.status.not.open");
        }
        if (!ActivityApprovalUtil.checkExhibitionApproval(exhibitionInfo.getApproveLevel(), baseInfoVO.getApprovalList())) {
            log.error("Approval List Can Not Empty, detail：{}", JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.approver.not.empty");
        }
    }

    private void checkSamplePointInfo(ActivityBaseInfoVO baseInfoVO) {
        //校验样板点是否存在
        SamplePointInfoDO samplePointInfo = samplePointInfoRepository.selectByPrimaryKey(baseInfoVO.getOriginRowId());
        if (samplePointInfo == null) {
            log.error("can not find sample {}, detail：{}", baseInfoVO.getOriginRowId(), JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "samplePoint.info.not.exist");
        }
        if (BooleanEnum.N.isMe(samplePointInfo.getEnabledFlag()) || BooleanEnum.N.isMe(samplePointInfo.getSamplePointStatus())) {
            log.error("sample not open {}, detail：{}", samplePointInfo.getEnabledFlag(), JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "samplePoint.status.not.visit");
        }
        if (ActivityApprovalUtil.checkSamplePointApproval(baseInfoVO, samplePointInfo,customerIntegrationUppConfig)) {
            log.error("Approval List Can Not Empty, detail：{}", JSON.toJSONString(baseInfoVO));
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.approver.not.empty");
        }
    }

}
