package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;

import java.util.List;
import java.util.Map;

public interface ExhibitionRelationLeaderRepository {
    /**
     * 插入数据
     *
     * @param record
     * @return
     */
    int insertSelective(ExhibitionRelationLeaderDO record);

    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ExhibitionRelationLeaderDO record);


    /**
     * 通过展会id 查询对应领导信息
     *
     * @param exhibitionRowIds
     * @return 《展会ID，领导列表》
     */
    Map<String, List<ExhibitionRelationLeaderDO>> queryLeadersWithExhibitionRowId(List<String> exhibitionRowIds);

    /***
     * <p>
     * 通过展会id 获取展会领导工号与实体对应Map
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 上午10:06
     * @param exhibitionRowId 展会Id
     * @return java.util.Map<java.lang.String,com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO>
     */
    Map<String, ExhibitionRelationLeaderDO> getLeaderNoMapWithExhibitionRowId(String exhibitionRowId);

}
