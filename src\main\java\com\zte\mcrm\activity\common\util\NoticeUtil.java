package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.service.model.activity.ActivityBO;
import com.zte.mcrm.activity.web.controller.activity.vo.ZtePeopleVO;
import com.zte.mcrm.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.UNDER_LINE;
import static com.zte.mcrm.activity.common.constant.DictConstant.INTERNAL_CUSTOMER_TYPE;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.JOIN_CONFERENCE;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.JOIN_EXHIBITION;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.LEADER;

/**
 * <AUTHOR>  魏振东
 * @description: 通知相关工具类
 * @date 2024/4/15 上午10:06
 */
@Slf4j
public class NoticeUtil {

    public static String NO_NOTICE = "NoNotice";
    public static String CHANGE_NOTICE = "ChangeNotice";
    public static String ALL_NOTICE = "AllNotice";


    public static boolean isEffectiveFlag(String flag){
        return StringUtils.isNotBlank(flag) && (NO_NOTICE.equals(flag) || CHANGE_NOTICE.equals(flag) || ALL_NOTICE.equals(flag));
    }


    /**
     * 我司参与人移除领导和专家
     *
     * @param list
     * @return
     */
    public static void removeSavantLeader(List<ActivityRelationZtePeopleDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.removeIf(e -> PeopleRoleLabelEnum.in(e.getPeopleLabel(), LEADER, PeopleRoleLabelEnum.SAVANT));
    }

    /**
     * 是否是展会/大会/参观样板点
     *
     * @param activityType
     * @return
     */
    public static boolean isExhibitionOrConference(String activityType) {
        return ActivityTypeEnum.in(activityType, JOIN_EXHIBITION, JOIN_CONFERENCE);
    }
    public static boolean isChangeApplicant(List<ActivityRelationZtePeopleDO> oldList, String applicantNo) {
        List<String> applicantNos = oldList.stream().filter(e -> APPLICANT.isMe(e.getPeopleType())).map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toList());
        return !applicantNos.contains(applicantNo);
    }


    /**
     * 获取新增的我司通知人
     * 去掉大团队接口人
     *如果是展会/大会 移除专家 领导
     * @param oldList
     * @param newList
     * @return
     */
    public static List<String> getAddNoticePeopleList(List<ActivityRelationZtePeopleDO> oldList, List<ActivityRelationZtePeopleDO> newList, boolean isExhibitionOrConferenceFlag) {
        if (CollectionUtils.isEmpty(newList)) {
            return new ArrayList<>();
        }
        List<String> newCodes = newList.stream()
                .filter(e -> !ActivityPeopleTypeEnum.in(e.getPeopleType(),APPLICANT,CREATE_BY,LPCONTACTPEOPLE))
                .filter(e -> isExhibitionOrConferenceFlag ? !PeopleRoleLabelEnum.in(e.getPeopleLabel(), LEADER, PeopleRoleLabelEnum.SAVANT) : Boolean.TRUE)
                .filter(e -> isExhibitionOrConferenceFlag ? !ActivityPeopleTypeEnum.REFERENCE_CONTROLLER.getCode().equals(e.getPeopleType()) : Boolean.TRUE)
                .map(ActivityRelationZtePeopleDO::getPeopleCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oldList)) {
            return newCodes;
        }
        List<String> oldCodes = oldList.stream()
                .filter(e -> !ActivityPeopleTypeEnum.REFERENCE_CONTROLLER.getCode().equals(e.getPeopleType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toList());
        return newCodes.stream().filter(e -> !oldCodes.contains(e)).collect(Collectors.toList());
    }


    /**
     * 获取变更的我司通知人code
     * 如果是展会/大会 移除专家 领导
     * 人员变更角色不做变更
     * @param oldList
     * @param newList
     * @return
     */
    public static List<String> getChangeNoticePeopleList(List<ActivityRelationZtePeopleDO> oldList, List<ActivityRelationZtePeopleDO> newList, boolean isAll, boolean isExhibitionOrConferenceFlag) {
        if (CollectionUtils.isEmpty(oldList)) {
            return new ArrayList<>();
        }
        List<ActivityRelationZtePeopleDO> optOldList = oldList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getPeopleCode()) &&
                        ActivityPeopleTypeEnum.in(e.getPeopleType(),INFORMED,ORGANIZER,PARTICIPANTS,LECTURER,APPLICANT,SITE_PEOPLE) &&
                        (isExhibitionOrConferenceFlag ? !PeopleRoleLabelEnum.in(e.getPeopleLabel(), LEADER, PeopleRoleLabelEnum.SAVANT) : Boolean.TRUE))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newList) || isAll) {
            return optOldList.stream().map(ActivityRelationZtePeopleDO::getPeopleCode).distinct().collect(Collectors.toList());
        }

        List<String> newCodes = newList.stream()
                .filter(e -> !ActivityPeopleTypeEnum.in(e.getPeopleType(), LPCONTACTPEOPLE, CREATE_BY, REFERENCE_CONTROLLER))
                .filter(e -> !isExhibitionOrConferenceFlag || !PeopleRoleLabelEnum.in(e.getPeopleLabel(), LEADER, PeopleRoleLabelEnum.SAVANT))
                .map(ActivityRelationZtePeopleDO::getPeopleCode)
                .collect(Collectors.toList());
//        Map<String, List<String>> newCode2Types = newList.stream()
//                .filter(e -> !ActivityPeopleTypeEnum.in(e.getPeopleType(),LPCONTACTPEOPLE,CREATE_BY) &&
//                        (isExhibitionOrConferenceFlag ? !PeopleRoleLabelEnum.in(e.getPeopleLabel(), LEADER, PeopleRoleLabelEnum.SAVANT) : Boolean.TRUE))
//                .collect(Collectors.groupingBy(ActivityRelationZtePeopleDO::getPeopleCode
//                        , Collectors.mapping(ActivityRelationZtePeopleDO::getPeopleType, Collectors.toList())));
        // 变更的通知人
//        Set<String> changeCodes = optOldList.stream()
//                .filter(e -> newCode2Types.containsKey(e.getPeopleCode())
//                        && !newCode2Types.get(e.getPeopleCode()).contains(e.getPeopleType()))
//                .map(ActivityRelationZtePeopleDO::getPeopleCode)
//                .collect(Collectors.toSet());

        // 移除的通知人
        Set<String> removeCodes = optOldList.stream()
                .filter(e -> !newCodes.contains(e.getPeopleCode()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode)
                .collect(Collectors.toSet());
        removeCodes.removeIf(StringUtils::isBlank);
        return new ArrayList<>(removeCodes);
    }


    public static List<String> getLecturerNoticePeopleList(List<ZtePeopleVO> lecturerList) {
        if (CollectionUtils.isEmpty(lecturerList)) {
            return new ArrayList<>();
        }
        return lecturerList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getPeopleCode())
                        && LECTURER.getCode().equals(e.getPeopleType())
                        && StringUtils.isBlank(e.getSolutionUrl())
                        && StringUtils.isBlank(e.getFileToken()))
                .map(ZtePeopleVO::getPeopleCode)
                .distinct()
                .collect(Collectors.toList());
    }

    /* Started by AICoder, pid:ycc854450fta63014d400aa240455e5d3d86bd2d */
    /**
     * 获取客户参与人工号列表
     *
     * @param oldList     变更前的关联客户
     * @param activityBO  活动传输对象
     * @param isAdd       是否新增客户参与人
     * @param isAll       是否通知所有人
     * @return 新增或移除的客户参与人工号列表
     */
    public static List<String> getAddOrRemoveCustomPeopleList(List<ActivityRelationCustPeopleDO> oldList, ActivityBO activityBO, boolean isAdd, boolean isAll) {
        // 获取旧联系人工号列表，如果为空则返回包含空字符串的列表
        List<String> oldContactNos = CollectionUtils.isEmpty(oldList)
                ? Collections.emptyList()
                : oldList.stream().map(ActivityRelationCustPeopleDO::getContactNo).collect(Collectors.toList());

        if (isAll) {
            return oldContactNos;
        }

        //查询字典
        DictService dictService = SpringContextUtils.getBean(DictService.class);
        List<DictLanguageDTO> dictLanguageDTOS = dictService.exactQueryListByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + activityBO.getActivityInfoDO().getActivityType());
        String joinDictKey = dictLanguageDTOS.stream().map(DictLanguageDTO::getDictKey).collect(Collectors.joining(","));
        log.info("[内部客户字典]查询类型：{}，字典key为：{}",INTERNAL_CUSTOMER_TYPE,joinDictKey);

        // 检查是否只有一个客户信息且该客户信息为中兴通讯股份有限公司
        boolean isOnlyZTE = Optional.ofNullable(activityBO.getListCustInfo())
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.size() == 1 && joinDictKey.contains(list.get(0).getCustomerCode()))
                .orElse(false);

        // 检查活动类型是否为日常拜访
        boolean isDailyVisit = Optional.ofNullable(activityBO.getActivityInfoDO())
                .map(ActivityInfoDO::getActivityType)
                .map(ActivityTypeEnum.DAILY_VISIT_ACTIVITY::isMe)
                .orElse(false);

        // 如果是 ZTE 客户且活动类型为日常拜访，则处理联系人信息
        if (isOnlyZTE && isDailyVisit) {
            List<String> contactNos = Optional.ofNullable(activityBO.getListCustPeopleInfo())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(person -> StringUtils.isNotBlank(person.getContactNo()))
                    .map(ActivityRelationCustPeopleDO::getContactNo)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("[构建工作通知]增加客户参与人，接收人：{}", contactNos);

            List<String> addList = contactNos.stream()
                    .filter(e -> !oldContactNos.contains(e))
                    .collect(Collectors.toList());

            List<String> removeList = oldContactNos.stream()
                    .filter(e -> !contactNos.contains(e))
                    .collect(Collectors.toList());
            return isAdd ? addList : removeList;
        }

        return Collections.emptyList();
    }

    /* Ended by AICoder, pid:ycc854450fta63014d400aa240455e5d3d86bd2d */
}
