package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ExportActivityHotelVO {

    @Excel(name = "序号", orderNum = "1")
    private String index;
    @Excel(name = "提单人/申请人", orderNum = "2")
    private String applicantDesc;
    @Excel(name = "事业部", orderNum = "3")
    private String orgName2;
    @Excel(name = "片区", orderNum = "4")
    private String orgName3;
    @Excel(name = "代表处", orderNum = "5")
    private String orgName4;
    @Excel(name = "国家", orderNum = "6")
    private String orgName5;
    @Excel(name = "现场陪同人员及联系电话", orderNum = "7")
    private String interfacePeopleAndPhone;
    @Excel(name = "客户单位", orderNum = "8")
    private String customerName;
    @Excel(name = "客户姓名", orderNum = "9")
    private String contactName;
    @Excel(name = "客户级别", orderNum = "10")
    private String custLevel;
    @Excel(name = "客户职务", orderNum = "11")
    private String position;
    @Excel(name = "性别", orderNum = "12")
    private String gender;
    @Excel(name = "客户部提供酒店", orderNum = "13")
    private String provideHotelFlagName;
    @Excel(name = "是否住宿", orderNum = "14")
    private String useHotel;
    @Excel(name = "酒店星级", orderNum = "15")
    private String hotelStar;
    @Excel(name = "酒店房型", orderNum = "16")
    private String hotelType;
    @Excel(name = "房间系数", orderNum = "17")
    private String roomCoefficient;
    @Excel(name = "入住日期", orderNum = "18")
    private String checkInTime;
    @Excel(name = "退房日期", orderNum = "19")
    private String checkOutTime;
    @Excel(name = "是否自付", orderNum = "20")
    private String paySelf;
    @Excel(name = "费用", orderNum = "21")
    private String amount;
    @Excel(name = "合规编号", orderNum = "22")
    private String complianceNo;
    @Excel(name = "备注", orderNum = "23")
    private String memo;
    @Excel(name = "活动编号", orderNum = "24")
    private String activityRequestNo;
    @Excel(name = "单据状态", orderNum = "25")
    private String activityStatus;
    @Excel(name = "最后更新日期", orderNum = "26")
    private String lastUpdatedDate;

}
