package com.zte.mcrm.activity.repository.model.item;

import java.util.Date;

/**
 * table:activity_schedule_item_origin_detail -- 
 */
public class ActivityScheduleItemOriginDetailDO {
    /** 主键 */
    private String rowId;

    /** 活动ID */
    private String activityRowId;

    /** 活动日程源数据版本RowId */
    private String scheduleItemOriginRowId;

    /** 关联日程ID */
    private String scheduleItemRowId;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    /** 源数据。json对象对应类：ActivityScheduleItemData */
    private String sourceData;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getScheduleItemOriginRowId() {
        return scheduleItemOriginRowId;
    }

    public void setScheduleItemOriginRowId(String scheduleItemOriginRowId) {
        this.scheduleItemOriginRowId = scheduleItemOriginRowId == null ? null : scheduleItemOriginRowId.trim();
    }

    public String getScheduleItemRowId() {
        return scheduleItemRowId;
    }

    public void setScheduleItemRowId(String scheduleItemRowId) {
        this.scheduleItemRowId = scheduleItemRowId == null ? null : scheduleItemRowId.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getSourceData() {
        return sourceData;
    }

    public void setSourceData(String sourceData) {
        this.sourceData = sourceData == null ? null : sourceData.trim();
    }
}