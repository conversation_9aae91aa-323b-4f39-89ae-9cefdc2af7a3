package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityReceptionMappingDO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityReceiveInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityReceptionMappingExtMapper extends ActivityReceptionMappingMapper {

    /**
     * 根据活动id查询关联的接待信息
     * @param activityRowId 活动id
     * @return 关联记录列表
     */
    List<ActivityReceptionMappingDO> getListByActivityRowId(String activityRowId);

    /**
     * 根据接待单据id查询接待基本信息
     * @param receiveIds
     * @return
     */
    List<ActivityReceiveInfo> getDetailByReceiveIdList(List<String> receiveIds);

    /***
     * <p>
     * 批量逻辑删除
     *
     * </p>
     * <AUTHOR>
     * @since 2024/5/30 下午8:12
     * @param operator 操作人
     * @param rowIds 主键id列表
     * @return int
     */
    int softDeleteByIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /***
     * <p>
     * 根据客户接待单id查询绑定关系
     *
     * </p>
     * <AUTHOR>
     * @since 2024/5/31 下午2:04
     * @param receiveId 接待id
     * @return com.zte.mcrm.activity.repository.model.resource.ActivityReceptionMappingDO
     */
    ActivityReceptionMappingDO getActivityReceptionMappingByReceiveId(String receiveId);

}