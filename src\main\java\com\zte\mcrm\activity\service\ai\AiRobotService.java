package com.zte.mcrm.activity.service.ai;

import com.zte.mcrm.activity.web.controller.ai.vo.IcenterRobotVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataQueryVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataRespVO;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/5 9:31
 */
public interface AiRobotService {
    /**
     * 调用studio接口，回答问题
     *
     * @param req
     * @author: 汤踊10285568
     * @date: 2024/7/5 9:31
     */
    String aiRobotForIcenter(IcenterRobotVO req);

    /**
     *
     * 调用接口查询营销信息
     *
     * @param queryVO
     * @return
     */
    String getMarketDataInfo(MarketDataQueryVO queryVO);

}
