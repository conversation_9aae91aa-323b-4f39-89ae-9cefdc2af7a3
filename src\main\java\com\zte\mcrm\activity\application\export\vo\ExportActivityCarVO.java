package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ExportActivityCarVO {

    @Excel(name = "序号", orderNum = "1")
    private String index;
    @Excel(name = "事业部", orderNum = "2")
    private String orgName2;
    @Excel(name = "片区", orderNum = "3")
    private String orgName3;
    @Excel(name = "代表处", orderNum = "4")
    private String orgName4;
    @Excel(name = "国家", orderNum = "5")
    private String orgName5;
    @Excel(name = "提单人/申请人", orderNum = "6")
    private String applicantDesc;
    @Excel(name = "客户单位", orderNum = "7")
    private String customerName;
    @Excel(name = "客户部提供车辆", orderNum = "8")
    private String provideCarFlagName;
    @Excel(name = "开始日期", orderNum = "9")
    private String startDate;
    @Excel(name = "结束日期", orderNum = "10")
    private String endDate;
    @Excel(name = "车型", orderNum = "11")
    private String carType;
    @Excel(name = "数量", orderNum = "12")
    private String carNum;
    @Excel(name = "对接人", orderNum = "13")
    private String contactPeople;
    @Excel(name = "对接人联系电话", orderNum = "14")
    private String phoneNum;
    @Excel(name = "费用（元）", orderNum = "15")
    private String amount;
    @Excel(name = "合规编号", orderNum = "16")
    private String complianceNo;
    @Excel(name = "备注", orderNum = "17")
    private String memo;
    @Excel(name = "酒店", orderNum = "18")
    private String hotel;
    @Excel(name = "单据状态", orderNum = "19")
    private String activityStatus;
    @Excel(name = "最后更新时间", orderNum = "20")
    private String lastUpdatedDate;

}
