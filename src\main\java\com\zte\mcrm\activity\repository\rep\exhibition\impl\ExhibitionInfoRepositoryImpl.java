package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionInfoExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ExhibitionInfoRepositoryImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会信息业务层接口
 * @date 2023/9/614:26
 */
@Component
public class ExhibitionInfoRepositoryImpl implements ExhibitionInfoRepository {
    @Resource
    private ExhibitionInfoExtMapper exhibitionInfoExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public ExhibitionInfoDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : exhibitionInfoExtMapper.selectByPrimaryKey(rowId.trim());
    }

    @Override
    public List<ExhibitionInfoDO> listByRowIds(List<String> exhibitionRowIds) {
        return CollectionUtils.isEmpty(exhibitionRowIds) ? new ArrayList<>()
                : exhibitionInfoExtMapper.queryExhibitionInfo(exhibitionRowIds.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public int insertSelective(List<ExhibitionInfoDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)){
            return NumberConstant.ZERO;
        }
        for (ExhibitionInfoDO infoDO : recordList){
            if (StringUtils.isBlank(infoDO.getRowId())){
                infoDO.setRowId(keyIdService.getKeyId());
            }
            exhibitionInfoExtMapper.insertSelective(infoDO);
        }
        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionInfoDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }

        return exhibitionInfoExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int getCountExhibitionInfo(ExhibitionInfoQuery query) {
        return exhibitionInfoExtMapper.getCountExhibitionInfo(query);
    }

    @Override
    public Map<String,ExhibitionInfoDO> queryExhibitionInfoByRowId(List<String> rowIds) {
        return CollectionUtils.isEmpty(rowIds) ? Collections.emptyMap() :
                exhibitionInfoExtMapper.queryExhibitionInfo(rowIds).stream().collect(Collectors.toMap(ExhibitionInfoDO::getRowId, v -> v, (key1, key2) -> key2));
    }

    @Override
    public PageRows<ExhibitionInfoDO> queryExhibitionInfoList(com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery  query) {
        if(!query.check()){
            return PageRowsUtil.buildEmptyPage(query);
        }

        List<ExhibitionInfoDO> exhibitionInfoDOList = exhibitionInfoExtMapper.getList(query);
        int total = exhibitionInfoExtMapper.countList(query);

       return PageRowsUtil.buildPageRow(query.getPageNo().longValue(), query.getPageSize().longValue(), total, exhibitionInfoDOList);
    }

    @Override
    public int getExhibitionInfoCount(ExhibitionInfoQuery query){
        return exhibitionInfoExtMapper.countList(query);
    }

    @Override
    public PageRows<ExhibitionInfoDO> searchListNoAuth(ExhibitionInfoQuery query) {
        if(!query.check()){
            return PageRowsUtil.buildEmptyPage(query);
        }
        PageInfo<ExhibitionInfoDO> pageInfo = PageHelper.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> exhibitionInfoExtMapper.searchListNoAuth(query));
        return PageRowsUtil.buildPageRow(pageInfo);
    }
}
