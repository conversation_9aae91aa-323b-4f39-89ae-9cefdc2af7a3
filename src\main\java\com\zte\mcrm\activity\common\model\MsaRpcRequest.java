package com.zte.mcrm.activity.common.model;

import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.enums.LanguageEnum;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 所有集成层（对其他微服务的接口封装的方法参数）必须是该类
 * <pre>
 *     为了治理封装方法随意定义的问题，所有封装外部微服务的接口方法的入参必须是该类
 * </pre>
 * 辅助创建MsaRpcRequest可以根据{@link MsaRpcRequestUtil}
 *
 * <AUTHOR>
 */
public class MsaRpcRequest<T> {

    /**
     * 请求参数头信息。该数据只允许通过方法调用修改。
     */
    private Map<String, String> reqHeader = new HashMap<>();
    /**
     * 请求参数
     */
    private T body;

    public MsaRpcRequest() {
        this(null, null);
    }

    public MsaRpcRequest(Map<String, String> reqHeaderMap, T paramBody) {
        this.setBody(paramBody);
        addAllHeader(reqHeaderMap);
        addHeader(RequestHeaderConstant.X_ORIGIN_SERVICENAME, RequestHeaderConstant.SERVICE_NAME);
    }

    /**
     * 添加请求头信息(只有key不为空（null、空白）的会添加进去)
     *
     * @param key 请求头key
     * @param val 请求头值
     * @return 返回对象本身
     */
    public MsaRpcRequest<T> addHeader(String key, String val) {
        if (StringUtils.isNotBlank(key)) {
            reqHeader.put(key, val);
        }

        return this;
    }

    /**
     * 如果request中不存在则添加请求头信息(只有key不为空（null、空白）的会添加进去)
     *
     * @param key 请求头key
     * @param val 请求头值
     * @return 返回对象本身
     */
    public MsaRpcRequest<T> addHeaderIfNotExit(String key, String val) {
        if (StringUtils.isNotBlank(key) && StringUtils.isBlank(getHeader(key))) {
            reqHeader.put(key, val);
        }

        return this;
    }

    /**
     * 获取头信息中的内容
     *
     * @param key
     * @return
     */
    public String getHeader(String key) {

        return reqHeader.get(key);
    }

    /**
     * 获取当前请求语言环境
     *
     * @return
     */
    public LanguageEnum language() {
        return LanguageEnum.getEnumByLanguage(getHeader(RequestHeaderConstant.X_LANG_ID));
    }

    /**
     * 添加请求头信息(只有key不为空（null、空白）的会添加进去)
     *
     * @param reqHeaderMap 请求头
     * @return 返回对象本身
     */
    public MsaRpcRequest<T> addAllHeader(Map<String, String> reqHeaderMap) {
        if (reqHeaderMap != null) {
            reqHeaderMap.forEach(this::addHeader);
        }

        return this;
    }

    /**
     * 删除请求头信息(只有key不为空（null、空白）才会去删除
     *
     * @param key 请求头key
     * @return 返回对象本身
     */
    public MsaRpcRequest<T> removeHeader(String key) {
        if (StringUtils.isNotBlank(key)) {
            reqHeader.remove(key);
        }

        return this;
    }

    /**
     * 返回请求头Map信息
     *
     * @return 返回请求头Map副本
     */
    public Map<String, String> fetchHeaderMap() {
        return new HashMap<>(reqHeader);
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }
}
