package com.zte.mcrm.activity.repository.rep.summary.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryRdcExtMapper;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRdcRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-12-05 15:19
 **/
@Component
public class ActivitySummaryRdcRepositoryImpl implements ActivitySummaryRdcRepository {
    @Resource
    private ActivitySummaryRdcExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;
    @Override
    public int insertSelective(List<ActivitySummaryRdcDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivitySummaryRdcDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            extMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivitySummaryRdcDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ActivitySummaryRdcDO>> queryAllRdcForActivity(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap()
                : extMapper.queryAllRdcByActivityRowId(activityRowIds)
                .stream().collect(Collectors.groupingBy(ActivitySummaryRdcDO::getActivityRowId));
    }
}
