package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExhibitionDirectorExtMapper extends ExhibitionDirectorMapper {

    /**
     * 根据展会Id获取展会负责人信息
     * @param exhibitionRowIds
     * @return
     * <AUTHOR>
     * @date 2023/9/14
     */
    List<ExhibitionDirectorDO> queryDirectorByExhibitionRowId(@Param("exhibitionRowIds") List<String> exhibitionRowIds);

    /**
     * 根据员工编号获取该员工对应的展会负责人信息
     * @param employeeNos
     * @return
     */
    List<ExhibitionDirectorDO> queryDirectorByEmployNo(@Param("employeeNos") List<String> employeeNos);

    /***
     * <p>
     * 根据人员编号和展会id查询指定人员在指定展会下的角色配置信息
     *
     * </p>
     * <AUTHOR>
     * @since  2024/1/11 下午4:49
     * @param exhibitionRowId 展会Id
     * @param employeeNo 人员编号
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO>
     */
    List<ExhibitionDirectorDO> queryDirectorByExhibitionRowIdAndEmpNo(@Param("exhibitionRowId") String exhibitionRowId,
                                                                      @Param("employeeNo") String employeeNo);

    List<ExhibitionDirectorDO> queryEmpNoTransList(DataTransParam searchParam);
}
