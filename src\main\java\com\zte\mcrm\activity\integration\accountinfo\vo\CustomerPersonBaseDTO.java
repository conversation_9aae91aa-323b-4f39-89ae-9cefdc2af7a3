package com.zte.mcrm.activity.integration.accountinfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户联系人基本信息
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Getter
@Setter
public class CustomerPersonBaseDTO {
    @ApiModelProperty("联系人所属客户编码")
    private String customerCode;
    @ApiModelProperty("联系人编码")
    private String contactNo;
    @ApiModelProperty("联系人名称")
    private String name;
    @ApiModelProperty("联系人英文名称")
    private String nameEn;
    /**
     * 枚举：SexTypeEnum
     */
    @ApiModelProperty("联系人性别。1-男，0-女")
    private Byte sex;
    @ApiModelProperty("联系人级别")
    private String personLevel;

    @ApiModelProperty("所属部门code")
    private String belongDeptCode;

    @ApiModelProperty("岗位")
    private String job;

    @ApiModelProperty("当前登录人客户经理类型(A/B)")
    private String managerType;
}
