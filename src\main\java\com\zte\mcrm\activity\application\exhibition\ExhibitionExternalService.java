package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.exhibition.param.app.*;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.*;

import java.util.List;

import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionAppTabAuthorityVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionAnalysisVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionBasicInfoVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleInfoVO;

/**
 * <AUTHOR> 10307200
 * @since 2023-12-19 上午10:24
 **/
public interface ExhibitionExternalService {

    /***
     * <p>
     * 获取展会基本信息
     *
     * </p>
     * <AUTHOR>
     * @since  2023/12/19 上午10:07
     * @param exhibitionRowIdRequest 展会RowId
     * @return com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionBasicInfoVO
     */
    ExhibitionBasicInfoVO getExhibitionBasicInfo(BizRequest<String> exhibitionRowIdRequest);

    /***
     * <p>
     * 根据场景查询当前登陆人的议程
     *      -- 我的议程：当前登陆人为议程参与人的议程
     *      -- 全部议程：当前登陆人有权限的全部议程
     * </p>
     * <AUTHOR>
     * @since  2024/1/2 下午8:17
     * @param scheduleQueryParamBizRequest 议程查询入参
     * @return com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleInfoVO
     */
    PageRows<ExhibitionScheduleInfoVO> getExhibitionScheduleInfoByScope(BizRequest<PageQuery<ExhibitionPersonalScheduleQueryParam>> scheduleQueryParamBizRequest);

    /***
     * <p>
     * 根据展会id 获取 当前人员在app端 是否拥有展会议程 和 展会分析权限
     *
     * </p>
     * <AUTHOR>
     * @since 2024/2/2 下午2:39
     * @param exhibitionRowIdBizRequest 展会id
     * @return com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionAppTabAuthorityVO
     */
    ExhibitionAppTabAuthorityVO getExhibitionAppTabAuthority(BizRequest<String> exhibitionRowIdBizRequest);

    /**
     * qqq
     * @param bizRequestParam
     */
    ExhibitionAnalysisVO exhibitionAnalysisList(BizRequest<ExhibitionAnalysisQueryParam> bizRequestParam) throws Exception;

    /**
     * 展会关联会议室资源占用情况查询
     * @param scheduleQueryParamBizRequest
     * @return
     */
    List<ExhibitionRoomOccupyInfoVO> queryExhibitionRoomOccupyInfo(BizRequest<ExhibitionRoomOccupyQueryParam> scheduleQueryParamBizRequest);

    /**
     * 编辑展会议程
     * @param bizRequest
     * @return
     */
    String editExhibitionSchedule(BizRequest<ExhibitionScheduleParam> bizRequest);

    /**
     * 确认展会议程
     * @param bizRequest
     * @return
     */
    String confirmExhibitionSchedule(BizRequest<ExhibitionScheduleUpdateParam> bizRequest);

    /**
     * 回退展会议程
     *
     * @param bizRequest
     * @return
     */
    boolean rollbackExhibitionSchedule(BizRequest<ExhibitionScheduleUpdateParam> bizRequest);

}
