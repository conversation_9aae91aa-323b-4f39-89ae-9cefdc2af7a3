package com.zte.mcrm.activity.application.cto;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoHandshakeExpertVO;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceBizExpertParam;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceCtoPersonParam;

public interface CtoHandshakeExpertService {
    /**
     * 查询CTO类型的专家列表
     * @param param
     * @return
     */
    LowCodePageRow<CtoHandshakeExpertVO> queryHandshakeExpertList(BizRequest<PageQuery<ResourceCtoPersonParam>> param);
}