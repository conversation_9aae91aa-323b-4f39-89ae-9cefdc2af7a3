package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityOpportunityInfoExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationQueryParam;
import com.zte.mcrm.activity.repository.model.activity.ActivityWithOpportunityDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityOpportunityRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityOpportunityQueryParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityOpportunityQueryRecordParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ActivityOpportunityRepositoryImpl implements ActivityOpportunityRepository {

    @Autowired
    private IKeyIdService keyIdService;
    @Autowired
    private ActivityOpportunityInfoExtMapper extMapper;

    @Override
    public List<ActivityWithOpportunityDO> getActivityWithOpportunityList(ActivityOpportunityQueryParam param) {
        if (CollectionUtils.isEmpty(param.getOpportunityCodeList())) {
            return new ArrayList<>();
        }

        return extMapper.queryActivityOpportunityInfo(param);
    }

    @Override
    public PageRows<ActivityWithOpportunityDO> queryActivityOpportunityInfo(PageQuery<ActivityOpportunityQueryParam> pageQuery) {
        PageInfo<ActivityWithOpportunityDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), true)
                .doSelectPageInfo(() -> this.getActivityWithOpportunityList(pageQuery.getParam()));

        List<ActivityWithOpportunityDO> activityWithOpportunityList = pageInfo.getList();
        return PageRowsUtil.buildPageRow(pageQuery.getPageNo(), pageQuery.getPageSize(), pageInfo.getTotal(), activityWithOpportunityList);
    }

    @Override
    public PageRows<ActivityOpportunityRelationDO> queryActivityOpportunityRelation(int pageNo, int pageSize, ActivityOpportunityRelationQueryParam queryParam) {
        PageInfo<ActivityOpportunityRelationDO> pageInfo = PageHelper.startPage(pageNo, pageSize, true)
                .doSelectPageInfo(() -> extMapper.queryActivityOpportunityRelation(queryParam));
        List<ActivityOpportunityRelationDO> activityOpportunityRelationList = pageInfo.getList();
        return PageRowsUtil.buildPageRow(pageNo, pageSize, pageInfo.getTotal(), activityOpportunityRelationList);
    }

    @Override
    public Map<String, Set<String>> getMapByActivityRowIds(List<String> activityRowIds) {
        if (CollectionUtils.isEmpty(activityRowIds)) {
            return new HashMap<>();
        }

        Map<String, Set<String>> acId2OppIdListMap = new HashMap<>();
        this.getList(activityRowIds, new ArrayList<>()).forEach(item -> {
            Set<String> opportunityIds = Optional.ofNullable(acId2OppIdListMap.get(item.getActivityRowId())).orElse(new HashSet<>());
            opportunityIds.add(item.getOpportunityId());
            acId2OppIdListMap.putIfAbsent(item.getActivityRowId(), opportunityIds);
        });

        return acId2OppIdListMap;
    }

    @Override
    public Map<String, Set<String>> getMapByOpportunityIds(List<String> opportunityIds) {
        if (CollectionUtils.isEmpty(opportunityIds)) {
            return new HashMap<>();
        }

        Map<String, Set<String>> oppId2AcIdListMap = new HashMap<>();
        this.getList(new ArrayList<>(), opportunityIds).forEach(item -> {
            Set<String> activityRowIds = Optional.ofNullable(oppId2AcIdListMap.get(item.getOpportunityId())).orElse(new HashSet<>());
            activityRowIds.add(item.getActivityRowId());
            oppId2AcIdListMap.putIfAbsent(item.getOpportunityId(), activityRowIds);
        });

        return oppId2AcIdListMap;
    }

    @Override
    public List<ActivityOpportunityInfoDO> getList(List<String> activityRowIds, List<String> opportunityIds) {
        if (CollectionUtils.isEmpty(activityRowIds) && CollectionUtils.isEmpty(opportunityIds)) {
            return new ArrayList<>();
        }

        return extMapper.getByActIdOrOppIdList(activityRowIds, opportunityIds);
    }

    @Override
    public int insertByBatch(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList) {
        if (CollectionUtils.isEmpty(activityOpportunityInfoDOList)) {
            return NumberConstant.ZERO;
        }

        activityOpportunityInfoDOList.forEach(record -> {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            String createdBy = Optional.ofNullable(record.getCreatedBy()).orElse(HeadersProperties.getXEmpNo());
            record.setCreatedBy(createdBy);
            Date creationDate = new Date();
            record.setLastUpdatedBy(createdBy);
            record.setCreationDate(creationDate);
            record.setLastUpdateDate(creationDate);
            record.setEnabledFlag(BooleanEnum.Y.getCode());
            record.setBindFlag(NumberConstant.ONE);
            record.setBindBy(createdBy);
            record.setBindDate(creationDate);
        });

        extMapper.insertByBatch(activityOpportunityInfoDOList);
        return activityOpportunityInfoDOList.size();
    }

    @Override
    public int batchUpdateByActIdAndOppId(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList) {
        if (CollectionUtils.isEmpty(activityOpportunityInfoDOList)) {
            return NumberConstant.ZERO;
        }

        activityOpportunityInfoDOList.forEach(record -> {
            String lastUpdatedBy = Optional.ofNullable(record.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo());
            Date lastUpdateDate = new Date();
            record.setLastUpdatedBy(lastUpdatedBy);
            record.setLastUpdateDate(lastUpdateDate);
        });
        extMapper.updateBatchByActIdAndOppId(activityOpportunityInfoDOList);
        return activityOpportunityInfoDOList.size();
    }

    @Override
    public List<ActivityWithOpportunityDO> queryRecord(ActivityOpportunityQueryRecordParam param) {
        if (Objects.isNull(param)) {
            return new ArrayList<>();
        }

        String activityRowId = param.getActivityRowId();
        String opportunityId = param.getOpportunityCode();
        if (StringUtils.isEmpty(activityRowId) && StringUtils.isEmpty(opportunityId)) {
            return new ArrayList<>();
        }

        return extMapper.queryRecord(activityRowId, opportunityId);
    }

    @Override
    public int batchUpdateByPrimaryKey(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList) {
        if (CollectionUtils.isEmpty(activityOpportunityInfoDOList)) {
            return 0;
        }

        return extMapper.batchUpdateByPrimaryKey(activityOpportunityInfoDOList);
    }
}
