package com.zte.mcrm.activity.repository.rep.plancto.impl;
/* Started by AICoder, pid:ue996ibecd0f12314c0309f1207ca1052c319e52 */

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.plancto.CtoPlanExeReportExtMapper;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanExeReportRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024年12月09日16:34
 */
@Component
public class CtoPlanExeReportRepositoryImpl implements CtoPlanExeReportRepository {

    @Autowired
    private CtoPlanExeReportExtMapper ctoPlanExeReportExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<CtoPlanExeReportDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        for (CtoPlanExeReportDO cto : list) {
            if (StringUtils.isBlank(cto.getRowId())) {
                cto.setRowId(keyIdService.getKeyId());
            }
            cto.setEnabledFlag(BooleanEnum.Y.getCode());
            cto.setCreationDate(new Date());
            cto.setLastUpdateDate(new Date());
        }
        return ctoPlanExeReportExtMapper.batchInsert(list);
    }

    @Override
    public int updateByPrimaryKeySelective(CtoPlanExeReportDO record) {
        if (Objects.isNull(record) || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return ctoPlanExeReportExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<CtoPlanExeReportDO> getByCtoPlanInfoId(String ctoPlanInfoId) {
        if (StringUtils.isBlank(ctoPlanInfoId)) {
            return Collections.emptyList();
        }

        return ctoPlanExeReportExtMapper.getByCtoPlanInfoId(ctoPlanInfoId)
                .stream().sorted(Comparator.comparing(CtoPlanExeReportDO::getScopeEnd)).collect(Collectors.toList());
    }

    /* Started by AICoder, pid:8f46828a54ee3ea146190b86b038c90b0d41dfe5 */


    @Override
    public List<CtoPlanExeReportDO> listExeReportWait(String planInfoId) {
        if (StringUtils.isBlank(planInfoId)) {
            return Collections.emptyList();
        }
        return ctoPlanExeReportExtMapper.listExeReportWait(planInfoId);
    }

    @Override
    public int batchUpdate(List<CtoPlanExeReportDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        for (CtoPlanExeReportDO cto : list) {
            cto.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            cto.setLastUpdateDate(new Date());
        }
        return ctoPlanExeReportExtMapper.batchUpdate(list);
    }

    @Override
    public List<CtoPlanExeReportDO> selectByPrimaries(List<String> ids) {
        return ctoPlanExeReportExtMapper.selectByPrimaries(ids);
    }

    /* Ended by AICoder, pid:8f46828a54ee3ea146190b86b038c90b0d41dfe5 */
}

/* Ended by AICoder, pid:ue996ibecd0f12314c0309f1207ca1052c319e52 */