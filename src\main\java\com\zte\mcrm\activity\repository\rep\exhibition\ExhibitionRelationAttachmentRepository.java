package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ExhibitionRelationAttachmentRepository
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会关联的附件信息业务接口
 * @date 2023/9/615:02
 */
public interface ExhibitionRelationAttachmentRepository {
    /**
     * 批量新增展会关联的附件信息
     * @param record 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int insertSelective(ExhibitionRelationAttachmentDO record);

    /**
     * 更新展会关联的附件信息
     * @param record 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int updateByPrimaryKeySelective(ExhibitionRelationAttachmentDO record);

    /**
     * 通过展会ID获取日程附件信息
     * @param exhibitionRowIds 展会ID
     * @return
     * <AUTHOR>
     * @date 2023/9/9
     */
    Map<String,List<ExhibitionRelationAttachmentDO>> queryAttachmentByExhibitionRowId(List<String> exhibitionRowIds);
}
