package com.zte.mcrm.activity.repository.model.log;

/* Started by AICoder, pid:sd303g9cdcr96e114db208fc10afd972b8c54dfe */

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Wither;

import java.util.Date;

@Getter
@Setter
/* Started by AICoder, pid:l64dfrddb9k1f1a141f9093fb0ae8c8bcd261e66 */
public class ActivityResourceOperationLogDO {

    /**
     * 主键
     */
    private String rowId;

    /**
     * 活动id
     */
    private String activityRowId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务对象id
     */
    private String bizRelatedId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作前
     */
    private String operationBefore;

    /**
     * 操作前描述
     */
    private String operationBeforeDesc;

    /**
     * 操作后
     */
    private String operationAfter;

    /**
     * 操作后描述
     */
    private String operationAfterDesc;

    /**
     * 有效标识，Y有效，N失效
     */
    private String enabledFlag;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date creationDate;

    /**
     * 更新人
     */
    private String lastUpdatedBy;

    /**
     * 更新时间
     */
    private Date lastUpdateDate;
}
/* Ended by AICoder, pid:l64dfrddb9k1f1a141f9093fb0ae8c8bcd261e66 */

/* Ended by AICoder, pid:sd303g9cdcr96e114db208fc10afd972b8c54dfe */
