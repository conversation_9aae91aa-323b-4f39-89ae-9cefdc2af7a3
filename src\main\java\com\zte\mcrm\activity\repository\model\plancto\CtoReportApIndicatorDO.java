package com.zte.mcrm.activity.repository.model.plancto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-21
 */
@Data
public class CtoReportApIndicatorDO {
    /**
     * 考核单位
     */
    private String assessmentUnit;
    /**
     * 全年AP目标数
     */
    private BigDecimal yearAPTarget;

    /**
     * 全年已完成的AP数
     */
    private BigDecimal yearAPFinish;

    /**
     * 全年已完成AP率
     */
    private String yearAPFinishRate;
}
