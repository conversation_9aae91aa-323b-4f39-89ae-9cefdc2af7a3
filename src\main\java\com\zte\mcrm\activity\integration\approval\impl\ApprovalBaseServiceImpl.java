package com.zte.mcrm.activity.integration.approval.impl;

import com.zte.iss.approval.sdk.bean.ApprovalProcessDTO;
import com.zte.iss.approval.sdk.bean.OpinionDTO;
import com.zte.iss.approval.sdk.bean.ReassignDTO;
import com.zte.iss.approval.sdk.exception.ApprovalRuntimeException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.integration.approval.ApprovalBaseService;
import com.zte.mcrm.expansion.business.IApprovalManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.zte.mcrm.activity.common.constant.I18Constants.TIPS_COMMON_BUSINESS_ERROR;

/**
 * @ClassName ApprovalCenterAppServiceImpl
 * @description: 审批中心接口
 * @author: 李龙10317843
 * @create: 2023-05-16 15:45
 * @Version 1.0
 **/
@Service
@Slf4j
public class ApprovalBaseServiceImpl implements ApprovalBaseService {
    @Autowired
    private IApprovalManageService approvalManageService;

    @Override
    public String approvalFlowStartByRid(String flowCode, String receiveId, Map<String, Object> params) {
        try {
            return approvalManageService.approvalFlowStartByRid(flowCode, receiveId, params);
        } catch (ApprovalRuntimeException e) {
            log.error("call approvalCenter error!{}", e.getExtMsg());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TIPS_COMMON_BUSINESS_ERROR);
        }
    }

    @Override
    public ApprovalProcessDTO getApprovalProcessByFid(String flowInstId) {
        try {
            return approvalManageService.getApprovalProcessByFid(flowInstId);
        } catch (ApprovalRuntimeException e) {
            log.error("call approvalCenter error!{}", e.getExtMsg());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TIPS_COMMON_BUSINESS_ERROR);
        }
    }

    @Override
    public String taskOpinionSubmit(OpinionDTO opinionDTO) {
        try {
            return approvalManageService.taskOpinionSubmit(opinionDTO);
        } catch (ApprovalRuntimeException e) {
            log.error("call approvalCenter error!{}", e.getExtMsg());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TIPS_COMMON_BUSINESS_ERROR);
        }
    }

    @Override
    public String taskReassign(ReassignDTO reassignDTO) {
        try {
            return approvalManageService.taskReassign(reassignDTO);
        } catch (ApprovalRuntimeException e) {
            log.error("call approvalCenter error!{}", e.getExtMsg());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TIPS_COMMON_BUSINESS_ERROR);
        }
    }

    /**
     * 撤销流程实例
     *
     * @param flowInstId 流程实例id
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/8/29 下午8:20
     */
    @Override
    public String approvalReassignByFid(String flowInstId) {
        // 取消流程实例的方法里面已经catch了异常，无需重复处理
        return approvalManageService.approvalReassignByFid(flowInstId);
    }


}