package com.zte.mcrm.activity.repository.rep.reception;

import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResDO;
import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResWithOldPairRowIdDO;

import java.util.List;

/**
 * 客户接待-LCM扫描结果暂存信息
 * <AUTHOR> 10333830
 * @date 2024-01-05 14:22
 */
public interface CustReceptionLcmTempResRepository {
    /**
     * 批量插入客户LCM扫描信息
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    int batchInsert(List<TCustReceptionLcmTempResDO> list);
    /**
     * 批量更新客户LCM扫描信息
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    int batchUpdateByPairRowId(List<TCustReceptionLcmTempResWithOldPairRowIdDO> list);

    /**
     * 根据客户接待拓展活动ID获取对应客户LCM扫描暂存信息
     *
     * @param headerId  活动头Id
     * @return
     */
    List<TCustReceptionLcmTempResDO> getByHeaderId(String headerId);

    /**
     * 批量更新-根据主键和赋值字段选择性更新
     * @param updateList    更新列表
     * @return int
     * <AUTHOR>
     * date: 2024/1/8 14:30
     */
    int batchUpdateByPrimaryKey(List<TCustReceptionLcmTempResDO> updateList);
}
