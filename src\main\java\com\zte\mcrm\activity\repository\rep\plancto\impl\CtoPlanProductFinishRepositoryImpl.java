package com.zte.mcrm.activity.repository.rep.plancto.impl;
/* Started by AICoder, pid:y5f115edf16f7881439609de90cc7a0bbb31e26d */

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.plancto.CtoPlanProductFinishExtMapper;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024年12月09日16:27
 */
@Component
public class CtoPlanProductFinishRepositoryImpl implements CtoPlanProductFinishRepository {

    @Autowired
    private CtoPlanProductFinishExtMapper ctoPlanProductFinishExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<CtoPlanProductFinishDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        for (CtoPlanProductFinishDO cto : list) {
            if (StringUtils.isBlank(cto.getRowId())) {
                cto.setRowId(keyIdService.getKeyId());
            }
            cto.setEnabledFlag(BooleanEnum.Y.getCode());
            cto.setCreationDate(new Date());
            cto.setLastUpdateDate(new Date());
        }
        return ctoPlanProductFinishExtMapper.batchInsert(list);
    }

    @Override
    public int updateByPrimaryKeySelective(CtoPlanProductFinishDO record) {
        if (Objects.isNull(record) || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return ctoPlanProductFinishExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchUpdate(List<CtoPlanProductFinishDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        Date now = new Date();
        list.forEach(item -> item.setLastUpdateDate(now));
        return ctoPlanProductFinishExtMapper.batchUpdate(list);
    }

    @Override
    public PageRows<CtoPlanProductFinishDO> pageProductFinish(PageQuery<String> pageQuery) {
        String planInfoId = pageQuery.getParam();
        if (StringUtils.isBlank(planInfoId)) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        PageInfo<CtoPlanProductFinishDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> ctoPlanProductFinishExtMapper.listProductFinishSorted(pageQuery.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }

    @Override
    public List<CtoPlanProductFinishDO> listProductFinish(String planInfoId) {
        if (StringUtils.isBlank(planInfoId)) {
            return Collections.emptyList();
        }
        return ctoPlanProductFinishExtMapper.listProductFinish(planInfoId, null);
    }

    @Override
    public List<CtoPlanProductFinishDO> listProductFinishByRole(String planInfoId, String role) {
        if(PeopleRoleLabelEnum.ALL.isMe(role)){
            return ctoPlanProductFinishExtMapper.listProductFinish(planInfoId, null);
        }
        return ctoPlanProductFinishExtMapper.listProductFinish(planInfoId, role);
    }

    @Override
    public List<String> listByUndoProcess() {
        return ctoPlanProductFinishExtMapper.listByUndoProcess();
    }

    @Override
    public List<SelectCtoActivityInfoResDTO> selectCtoActivityInfo(SelectCtoActivityParamDTO param) {
        return ctoPlanProductFinishExtMapper.selectCtoActivityInfo(param);
    }

    @Override
    public CtoPlanDetailDTO queryProductDetail(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return new CtoPlanDetailDTO();
        }
        return ctoPlanProductFinishExtMapper.queryProductDetail(rowId);
    }

    @Override
    public List<CtoPlanProductFinishDO> listProductFinishByPlanId(String planInfoId) {
        if (StringUtils.isBlank(planInfoId)) {
            return Collections.emptyList();
        }
        return ctoPlanProductFinishExtMapper.listProductFinishByPlanId(planInfoId);
    }
}

/* Ended by AICoder, pid:y5f115edf16f7881439609de90cc7a0bbb31e26d */
