package com.zte.mcrm.activity.repository.model.resource;

import java.util.Date;

/**
 * table:activity_resource_reservation_schedule -- 
 */
public class ActivityResourceReservationScheduleDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 关联日程ID */
    private String scheduleId;

    /** 关联日程外部系统ID */
    private String outSystemId;

    /** 会议组织人员工编号 */
    private String userNo;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 预约日程源对对象类型（是谁要预约日程）。枚举：ReserveScheduleOriTypeEnum。默认是活动activity */
    private String reserveOriType;

    /**  */
    private String oriRowId;

    /** 预约ICenter日程状态ReservationScheduleStatusEnum */
    private String reservationScheduleStatus;

    /** 备注长度50 */
    private String remark;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId == null ? null : scheduleId.trim();
    }

    public String getOutSystemId() {
        return outSystemId;
    }

    public void setOutSystemId(String outSystemId) {
        this.outSystemId = outSystemId == null ? null : outSystemId.trim();
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo == null ? null : userNo.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getReserveOriType() {
        return reserveOriType;
    }

    public void setReserveOriType(String reserveOriType) {
        this.reserveOriType = reserveOriType == null ? null : reserveOriType.trim();
    }

    public String getOriRowId() {
        return oriRowId;
    }

    public void setOriRowId(String oriRowId) {
        this.oriRowId = oriRowId == null ? null : oriRowId.trim();
    }

    public String getReservationScheduleStatus() {
        return reservationScheduleStatus;
    }

    public void setReservationScheduleStatus(String reservationScheduleStatus) {
        this.reservationScheduleStatus = reservationScheduleStatus == null ? null : reservationScheduleStatus.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}