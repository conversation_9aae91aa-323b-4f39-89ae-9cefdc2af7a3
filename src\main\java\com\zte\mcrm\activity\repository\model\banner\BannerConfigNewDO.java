package com.zte.mcrm.activity.repository.model.banner;

import java.util.Date;

/**
 * table:banner_config -- 
 */
public class BannerConfigNewDO {
    /** 主键 */
    private String bannerConfigId;

    /** bannerId */
    private String bannerId;

    /** key */
    private String key;

    /** 中文值 */
    private String valueZh;

    /** 英文值 */
    private String valueEn;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后更新人 */
    private String lastUpdatedBy;

    /** 最后更新时间 */
    private Date lastUpdateDate;

    /** 逻辑有效标识，Y-有效，N-无效 */
    private String enabledFlag;

    public String getBannerConfigId() {
        return bannerConfigId;
    }

    public void setBannerConfigId(String bannerConfigId) {
        this.bannerConfigId = bannerConfigId == null ? null : bannerConfigId.trim();
    }

    public String getBannerId() {
        return bannerId;
    }

    public void setBannerId(String bannerId) {
        this.bannerId = bannerId == null ? null : bannerId.trim();
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key == null ? null : key.trim();
    }

    public String getValueZh() {
        return valueZh;
    }

    public void setValueZh(String valueZh) {
        this.valueZh = valueZh == null ? null : valueZh.trim();
    }

    public String getValueEn() {
        return valueEn;
    }

    public void setValueEn(String valueEn) {
        this.valueEn = valueEn == null ? null : valueEn.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}