package com.zte.mcrm.activity.repository.rep.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivitySummaryIssueRepository {

    /**
     * 添加关联的会议纪要遗留问题（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivitySummaryIssueDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivitySummaryIssueDO record);

    /**
     * 查询活动关联的所有会议纪要遗留问题
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivitySummaryIssueDO> queryAllSummaryIssueForActivity(String activityRowId);

    /**
     *
     * @param activityRowId
     * @return
     */
    Map<String, List<ActivitySummaryIssueDO>> queryAllByActivityRowId(List<String> activityRowId);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 删除指定活动下的所有待办
     * @param operator  操作者
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 16:17
     */
    int deleteByActivityId(String operator, String activityRowId);
}
