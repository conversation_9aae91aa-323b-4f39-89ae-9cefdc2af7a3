package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityCustomerInfoVO;
import com.zte.mcrm.adapter.vo.Account;

import java.util.List;

/**
 * 客户活动客户参与人查询
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
public interface ActivityCustomerSearchService {

    /**
     * 查询活动创建中用户最近使用的客户
     *
     * @param request
     * @return {@link List<ActivityCustomerInfoVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午2:59
     */
    List<Account> searchRecentlyCustomers(BizRequest<PageQuery<ActivityRecentlySearchParam>> request);

    /**
     * 获取客户信息
     *
     * @param customerCodes
     * @return {@link List< Account>}
     * <AUTHOR>
     * @date 2023/5/28 下午12:47
     */
    List<Account> fetchAccount(List<String> customerCodes);

    /**
     * 获取客户信息
     *
     * @param customerCodes 客户编码
     * @param orgCode 组织编码
     * @return {@link List<Account>}
     * <AUTHOR>
     * @date 2025/3/17
     */
    List<Account> fetchAccountByTenant(List<String> customerCodes, String orgCode);
}
