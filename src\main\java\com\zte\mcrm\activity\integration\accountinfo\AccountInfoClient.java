package com.zte.mcrm.activity.integration.accountinfo;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.integration.accountinfo.dto.AccountInfoDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerDetailDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.ContactDetailParam;
import com.zte.mcrm.activity.integration.accountinfo.param.CustomerDetailQueryParamDTO;
import com.zte.mcrm.activity.integration.accountinfo.vo.CustomerPersonDetailDTO;
import com.zte.mcrm.adapter.vo.AccountVO;
import com.zte.mcrm.config.FeignRequestHeaderInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * zte-crm-account-info 客户管理服务端代理
 *
 * <AUTHOR>
 * @date 2024-04-18 15:45:00
 */
@FeignClient(name = "zte-crm-account-info", path = "zte-crm-account-info/", configuration = FeignRequestHeaderInterceptor.class)
public interface AccountInfoClient {

    /**
     * 获取登录人作为A/B角色，所关联的客户联系人
     *
     * @param empNo 登陆人工号
     * @param token 登陆人token
     * @param lang  环境语言
     * @param managerType  A角/B角
     * @return ServiceData<List < AccountInfoDTO>>
     */
    @GetMapping("api/person/myServeList")
    ServiceData<List<AccountInfoDTO>> queryABAccountContacts(@RequestHeader(name = "X-Emp-No") String empNo,
                                                             @RequestHeader(name = "X-Auth-Value") String token,
                                                             @RequestHeader(name = "X-Lang-Id") String lang,
                                                             @RequestParam("managerType") String managerType);

    /**
     * 根据客户编码获取客户联系人详细信息-基础/部门/合规
     *
     * @param contactDetailParam 查客户联系人详细信息入参
     * @return ServiceData<List<CustomerPersonDetailVO>>
     */
    @PostMapping("api/person/contactDetailList")
    ServiceData<List<CustomerPersonDetailDTO>> queryContactDetailList(@RequestHeader(name = "X-Emp-No") String empNo,
                                                                      @RequestHeader(name = "X-Auth-Value") String token,
                                                                      @RequestHeader(name = "X-Lang-Id") String lang,
                                                                      @RequestBody ContactDetailParam contactDetailParam);
    /**
     * 客户信息多法人
     */
    @PostMapping("/api/customer/batchDetail/v2")
    ServiceData<List<OutCustomerDetailDTO>> batchCustomerQueryV2(@RequestBody CustomerDetailQueryParamDTO customerDetailQueryParam);

    /**
     * 带法人信息查询客户信息
     * @return ServiceData<List <AccountVO>>
     */
    @GetMapping("/noPermisonAccountBatch/v2")
    ServiceData<List<AccountVO>> getAccountByCodeListV2(@RequestParam("accountCode") String accountCode,
                                                        @RequestParam("filterHistoryName") String filterHistoryName,
                                                        @RequestParam("filterFrozenCustonmer") String filterFrozenCustonmer,
                                                        @RequestParam("corporateNo") String corporateNo);


}