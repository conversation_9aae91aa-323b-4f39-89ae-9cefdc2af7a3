package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;

public interface ActivityCustomerInfoMapper {
    /**
     * all field insert
     */
    int insert(ActivityCustomerInfoDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityCustomerInfoDO record);

    /**
     * query by primary key
     */
    ActivityCustomerInfoDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityCustomerInfoDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityCustomerInfoDO record);
}