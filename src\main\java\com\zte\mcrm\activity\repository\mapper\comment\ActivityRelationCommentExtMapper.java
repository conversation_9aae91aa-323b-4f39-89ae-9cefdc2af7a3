package com.zte.mcrm.activity.repository.mapper.comment;

import com.zte.mcrm.activity.repository.model.comment.ActivityRelationCommentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityRelationCommentExtMapper extends ActivityRelationCommentMapper {
    List<ActivityRelationCommentDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);
}