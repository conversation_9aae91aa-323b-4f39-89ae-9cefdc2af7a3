package com.zte.mcrm.activity.integration.authorityclient.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * UPP 查询角色所有用户
 *
 * <AUTHOR>
 * @date 2023/8/26 下午3:54
 */
@Getter
@Setter
@ToString
public class UppRoleUserSearchParam implements Serializable {
    private static final long serialVersionUID = 5121774354947312001L;

    /**
     * 角色编码列表
     */
    private List<String> roleCodeList;

    /**
     * 语言ID
     */
    private String langId;
}
