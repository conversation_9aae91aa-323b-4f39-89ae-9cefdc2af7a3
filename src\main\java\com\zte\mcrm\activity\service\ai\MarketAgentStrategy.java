package com.zte.mcrm.activity.service.ai;

import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationRespVO;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/10/31 14:05
 */
public interface MarketAgentStrategy {

    boolean support(String apiType);

    List<IgptRespVO> processBusiness(AiApplicationRespVO req);
}
