package com.zte.mcrm.activity.application.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import com.zte.mcrm.activity.common.constant.CTOReportHeaderConstants;
import com.zte.mcrm.activity.common.enums.CtoPlanProductTypeEnum;
import com.zte.mcrm.activity.common.enums.SaleDivisionEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;


/* CTO报表握手指标
 */
@Accessors(chain = true)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CtoReportIndicatorVO {

    /**
     * 握手数
     */
    @ExcelProperty(CTOReportHeaderConstants.HANDSHAKE_TARGET)
    private String handshakeTarget;

    /**
     * 已完成握手数
     */
    @ExcelProperty(CTOReportHeaderConstants.HANDSHAKE_FINISH)
    private String handshakeFinish;

    /**
     * 完成率
     */
    @ExcelProperty(CTOReportHeaderConstants.HANDSHAKE_FINISH_RATE)
    private String handshakeFinishRate;

    /**
     * 客户数
     */
    @ExcelProperty(CTOReportHeaderConstants.ACCOUNT_TARGET)
    private String accountTarget;

    /**
     * 已覆盖客户数
     */
    @ExcelProperty(CTOReportHeaderConstants.ACCOUNT_FINISH)
    private String accountFinish;

    /**
     * 已覆盖率
     */
    @ExcelProperty(CTOReportHeaderConstants.ACCOUNT_FINISH_RATE)
    private String accountFinishRate;

    /**
     * 平均完成率
     */
    @ExcelProperty(CTOReportHeaderConstants.AVERAGE_FINISH_RATE)
    private String averageFinishRate;

    /* Started by AICoder, pid:nf06bh5418h017814477081d80342c4600e6f448 */
    /**
     * 填充完成率指标
     */
    private void fillFinishRate() {
        // 使用局部变量来避免重复创建 BigDecimal 对象
        BigDecimal handshakeTarget = new BigDecimal(this.handshakeTarget);
        BigDecimal handshakeFinish = new BigDecimal(this.handshakeFinish);
        BigDecimal accountTarget = new BigDecimal(this.accountTarget);
        BigDecimal accountFinish = new BigDecimal(this.accountFinish);

        // 如果完成数大于计划数，则将完成数设为计划数
        if (handshakeFinish.compareTo(handshakeTarget) > 0) {
            handshakeFinish = handshakeTarget;
            this.handshakeFinish = handshakeFinish.toString();
        }

        if (accountFinish.compareTo(accountTarget) > 0) {
            accountFinish = accountTarget;
            this.accountFinish = accountFinish.toString();
        }

        // 计算完成率并更新字段
        BigDecimal handshakeFinishRateDecimal = computeFinishRate(handshakeFinish, handshakeTarget);
        BigDecimal accountFinishRateDecimal = computeFinishRate(accountFinish, accountTarget);

        this.handshakeFinishRate = handshakeFinishRateDecimal + "%";
        this.accountFinishRate = accountFinishRateDecimal + "%";

        // 计算平均完成率并更新字段
        BigDecimal averageFinishRateDecimal = handshakeFinishRateDecimal.add(accountFinishRateDecimal)
                .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
        this.averageFinishRate = averageFinishRateDecimal + "%";
    }

    /**
     * 计算完成率
     *
     * @param finish 完成
     * @param target 目标
     * @return
     */
    public BigDecimal computeFinishRate(BigDecimal finish, BigDecimal target) {
        if (target.signum() == 0) {
            // 防止除以零的情况
            return BigDecimal.ZERO.setScale(1,RoundingMode.HALF_UP);
        }
        return finish.multiply(BigDecimal.valueOf(100)).divide(target, 1, RoundingMode.HALF_UP);
    }

    /* Ended by AICoder, pid:nf06bh5418h017814477081d80342c4600e6f448 */


    /* Started by AICoder, pid:cb7a4hb58bdd50c14f4d09b000301a467852655e */
    /**
     * 统计维度：事业部，构建握手指标、国家/客户指标
     *
     * @param saleDivisionEnum    销售部门枚举
     * @param ctoPlanOrgFinishDOS 数据列表
     */
    private void buildNonLeaderIndicatorByOrg(SaleDivisionEnum saleDivisionEnum,
                                              List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS) {
        // 过滤出特定事业部的数据
        List<CtoPlanOrgFinishDO> filteredData = ctoPlanOrgFinishDOS.stream()
                .filter(e -> saleDivisionEnum.isMe(e.getOrgDivision()))
                .collect(Collectors.toList());

        // 计算总的握手计划数和完成数
        int targetTotal = filteredData.stream().mapToInt(entry ->
                entry.getRanTarget() +
                        entry.getCcnTarget() +
                        entry.getBnTarget() +
                        entry.getFmTarget() +
                        entry.getSnTarget()
        ).sum();

        int finishTotal = filteredData.stream().mapToInt(entry ->
                Math.min(entry.getRanFinish(),entry.getRanTarget()) +
                Math.min(entry.getCcnFinish(),entry.getCcnTarget()) +
                Math.min(entry.getBnFinish(),entry.getBnTarget()) +
                Math.min(entry.getFmFinish(),entry.getFmTarget()) +
                Math.min(entry.getSnFinish(),entry.getSnTarget())
        ).sum();

        // 计算总的客户计划数和完成数
        int accountTargetTotal = computeAccountTotal(filteredData, true);
        int accountFinishTotal = computeAccountTotal(filteredData, false);

        // 设置握手指标的目标和完成值
        setHandshakeTarget(String.valueOf(targetTotal))
                .setHandshakeFinish(String.valueOf(finishTotal));

        // 设置客户指标的目标和完成值
        setAccountTarget(String.valueOf(accountTargetTotal))
                .setAccountFinish(String.valueOf(accountFinishTotal));
    }

    /* Ended by AICoder, pid:cb7a4hb58bdd50c14f4d09b000301a467852655e */

    /* Started by AICoder, pid:k6786t12bfs02d01457d091b20d43b3b69a621fd */
    /**
     * 计算账户总目标或完成数。
     *
     * @param filteredData 数据列表
     * @param isTarget     是否计算目标值，否则计算完成值
     * @return 总计数
     */
    public int computeAccountTotal(List<CtoPlanOrgFinishDO> filteredData, boolean isTarget) {
        int totalCount = 0;

        for (CtoPlanOrgFinishDO data : filteredData) {
            List<String> accountCodes = JSONArray.parseArray(data.getAccountCode(), String.class);
            int totalProductTargetCount = 0;
            int totalProductFinishCount = 0;

            // 使用数组来简化代码
            int[] targets = {data.getRanTarget(), data.getCcnTarget(), data.getBnTarget(), data.getFmTarget(), data.getSnTarget()};
            int[] finishes = {data.getRanFinish(), data.getCcnFinish(), data.getBnFinish(), data.getFmFinish(), data.getSnFinish()};

            for (int i = 0; i < targets.length; i++) {
                totalProductTargetCount += targets[i] > 0 ? 1 : 0;
                totalProductFinishCount += finishes[i] > 0 ? 1 : 0;
            }

            int countToUse = isTarget ? totalProductTargetCount : totalProductFinishCount;
            totalCount += accountCodes.size() * countToUse;
        }

        return totalCount;
    }
    /* Ended by AICoder, pid:k6786t12bfs02d01457d091b20d43b3b69a621fd */

    int computeAccountTotal(List<CtoPlanOrgFinishDO> filteredData, ToIntFunction<CtoPlanOrgFinishDO> func) {

        int totalCount = 0;
        for (CtoPlanOrgFinishDO filteredDatum : filteredData) {
            String accountCode = filteredDatum.getAccountCode();
            List<String> accountCodes = JSONArray.parseArray(accountCode, String.class);
            int countByProduct = func.applyAsInt(filteredDatum);

            totalCount = totalCount + accountCodes.size() * (countByProduct > 0 ? 1 : 0);
        }
        return totalCount;
    }


    /**
     * 统计维度：产品，构建握手指标、国家/客户指标
     *
     * @param productTypeEnum     产品类型枚举
     * @param ctoPlanOrgFinishDOS 数据列表
     */
    void buildNonLeaderIndicatorByProduct(CtoPlanProductTypeEnum productTypeEnum,
                                                  List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS) {
        ToIntFunction<CtoPlanOrgFinishDO> targetFunc = getProductTargetFunction(productTypeEnum);
        ToIntFunction<CtoPlanOrgFinishDO> finishFunc = getProductFinishFunction(productTypeEnum);

        int targetTotal = ctoPlanOrgFinishDOS.stream().mapToInt(targetFunc).sum();
        int finishTotal = ctoPlanOrgFinishDOS.stream().mapToInt(finishFunc).sum();
        //握手指标
        this.setHandshakeTarget(String.valueOf(targetTotal))
                .setHandshakeFinish(String.valueOf(finishTotal));

        // 计算总的客户计划数
        int accountTargetTotal = this.computeAccountTotal(ctoPlanOrgFinishDOS, targetFunc);
        // 计算总的客户完成数
        int accountFinishTotal = this.computeAccountTotal(ctoPlanOrgFinishDOS, finishFunc);

        //国家/客户指标
        this.setAccountTarget(String.valueOf(accountTargetTotal))
                .setAccountFinish(String.valueOf(accountFinishTotal));
    }


    private ToIntFunction<CtoPlanOrgFinishDO> getProductTargetFunction(CtoPlanProductTypeEnum productTypeEnum) {
        switch (productTypeEnum) {
            case RAN:
                return CtoPlanOrgFinishDO::getRanTarget;
            case CCN:
                return CtoPlanOrgFinishDO::getCcnTarget;
            case BN:
                return CtoPlanOrgFinishDO::getBnTarget;
            case FM:
                return CtoPlanOrgFinishDO::getFmTarget;
            case SN:
                return CtoPlanOrgFinishDO::getSnTarget;
            default:
                throw new IllegalArgumentException("Unknown product type");
        }
    }

    private ToIntFunction<CtoPlanOrgFinishDO> getProductFinishFunction(CtoPlanProductTypeEnum productTypeEnum) {
        switch (productTypeEnum) {
            case RAN:
                return org -> Math.min(org.getRanFinish(), org.getRanTarget());
            case CCN:
                return org->Math.min(org.getCcnFinish(),org.getCcnTarget());
            case BN:
                return org->Math.min(org.getBnFinish(),org.getBnTarget());
            case FM:
                return org->Math.min(org.getFmFinish(),org.getFmTarget());
            case SN:
                return org->Math.min(org.getSnFinish(),org.getSnTarget());
            default:
                throw new IllegalArgumentException("Unknown product type");
        }
    }

    /**
     * 构建指标报告。
     *
     * @param division                事业部枚举
     * @param productType             产品类型枚举
     * @param ctoPlanOrgFinishDOS     事业部完成数据列表
     * @param ctoPlanProductFinishDOS 产品完成数据列表
     * @param isLeader                是否为领导
     * @return 构建好的指标报告对象
     */
    public CtoReportIndicatorVO buildIndicators(SaleDivisionEnum division, CtoPlanProductTypeEnum productType,
                                                List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS,
                                                List<CtoPlanProductFinishDO> ctoPlanProductFinishDOS, boolean isLeader) {
        if (isLeader) {
            //核心领导:构建事业部维度、产品维度的握手指标和国家/客户指标
            buildLeaderIndicatorByOrgOrProduct(division, productType, ctoPlanProductFinishDOS);
        } else {
            //非核心领导:构建事业部维度、产品维度的握手指标和国家/客户指标
            buildNonLeaderIndicatorByOrgOrProduct(division, productType, ctoPlanOrgFinishDOS);
        }

        // 填充完成率指标
        fillFinishRate();
        return this;
    }


    /* Started by AICoder, pid:v1826ae55bz09af14d940a34e0f9472c9446ec6b */
    void buildLeaderIndicatorByOrgOrProduct(SaleDivisionEnum saleDivisionEnum, CtoPlanProductTypeEnum productTypeEnum,
                                            List<CtoPlanProductFinishDO> ctoPlanProductFinishDOS) {

        // 使用流进行过滤和计算，避免多次遍历列表
        List<CtoPlanProductFinishDO> filteredData = ctoPlanProductFinishDOS.stream()
                .filter(e -> PeopleRoleLabelEnum.LEADER.isMe(e.getEmployeeType()))
                .filter(e -> saleDivisionEnum == null || saleDivisionEnum.isMe(e.getOrgDivision()))
                .filter(e -> productTypeEnum == null || productTypeEnum.isMe(e.getProductCode()))
                .collect(Collectors.toList());

        // 计算总的握手计划数和完成数
        int targetTotal = filteredData.stream().mapToInt(CtoPlanProductFinishDO::getActivityTarget).sum();
        int finishTotal = filteredData.stream().mapToInt(obj->Math.min(obj.getActivityFinish(),obj.getActivityTarget())).sum();

        // 计算总的客户计划数和完成数
        int accountTargetTotal = filteredData.stream().mapToInt(CtoPlanProductFinishDO::getAccountTarget).sum();
        int accountFinishTotal = filteredData.stream().mapToInt(CtoPlanProductFinishDO::getAccountFinish).sum();

        // 设置握手指标的目标和完成值
        this.setHandshakeTarget(String.valueOf(targetTotal))
                .setHandshakeFinish(String.valueOf(finishTotal));

        // 设置客户指标的目标和完成值
        this.setAccountTarget(String.valueOf(accountTargetTotal))
                .setAccountFinish(String.valueOf(accountFinishTotal));
    }

    /* Ended by AICoder, pid:v1826ae55bz09af14d940a34e0f9472c9446ec6b */


    void buildNonLeaderIndicatorByOrgOrProduct(SaleDivisionEnum saleDivisionEnum, CtoPlanProductTypeEnum productTypeEnum,
                                                       List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS) {
        // 构建事业部维度的握手指标和国家/客户指标
        if (saleDivisionEnum != null) {
            buildNonLeaderIndicatorByOrg(saleDivisionEnum, ctoPlanOrgFinishDOS);

        }
        // 构建产品维度的握手指标和国家/客户指标
        if (productTypeEnum != null) {
            buildNonLeaderIndicatorByProduct(productTypeEnum, ctoPlanOrgFinishDOS);
        }
    }


}


