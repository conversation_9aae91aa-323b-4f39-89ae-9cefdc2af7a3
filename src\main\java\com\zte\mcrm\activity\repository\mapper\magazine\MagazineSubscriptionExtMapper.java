package com.zte.mcrm.activity.repository.mapper.magazine;

import com.zte.mcrm.activity.repository.model.magazine.MagazineSubscriptionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 杂志订阅扩展Mapper
 * 继承基础Mapper，包含业务相关的复杂查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface MagazineSubscriptionExtMapper extends MagazineSubscriptionMapper {
    
    /**
     * 按联系人编号分页查询（去重）
     * 用于SUBSCRIBED维度查询
     * 
     * @param contactPersonNo 联系人编号（模糊查询）
     * @param magazineName 杂志名称（模糊查询）
     * @return 去重的联系人编号列表
     */
    List<String> selectDistinctContactPersonNoByConditionWithPage(
        @Param("contactPersonNo") String contactPersonNo, 
        @Param("magazineName") String magazineName);
    
    /**
     * 统计去重后的联系人总数
     * 用于SUBSCRIBED维度查询的总数统计
     * 
     * @param contactPersonNo 联系人编号（模糊查询）
     * @param magazineName 杂志名称（模糊查询）
     * @return 去重后的联系人总数
     */
    long countDistinctContactPersonNoByCondition(
        @Param("contactPersonNo") String contactPersonNo, 
        @Param("magazineName") String magazineName);
    
    /**
     * 根据联系人编号列表查询订阅记录
     * 用于根据联系人编号查询对应的所有订阅记录
     * 
     * @param contactPersonNos 联系人编号列表
     * @return 订阅记录列表
     */
    List<MagazineSubscriptionDO> selectByContactPersonNos(
        @Param("contactPersonNos") List<String> contactPersonNos);
} 