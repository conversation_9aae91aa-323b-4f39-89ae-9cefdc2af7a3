package com.zte.mcrm.activity.application.reservation.convert;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ResourceTypeEnum;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.service.reservation.param.ReserveResourceParam;
import com.zte.mcrm.activity.web.controller.reservation.param.ReserveScheduleResourceParam;
import org.apache.commons.collections.CollectionUtils;

/**
 * 资源预约参数转换类
 *
 * <AUTHOR>
 */
public class ResourceReservationParamConvert {

    /**
     * 转换为日程资源的参数
     *
     * @param param
     * @param activityInfo
     * @return
     */
    public static ReserveResourceParam toScheduleResourceParam(ReserveScheduleResourceParam param, ActivityInfoDO activityInfo) {
        ReserveResourceParam reserveResourceParam = new ReserveResourceParam();
        reserveResourceParam.setActivityRowId(activityInfo.getRowId());
        // 日程的都在同一个里面，专家/领导一样的
        reserveResourceParam.setResourceType(ResourceTypeEnum.SCHEDULE.getType());

        if (CollectionUtils.isNotEmpty(param.getResourceList())) {
            for (ReserveScheduleResourceParam.ScheduleResource sr : param.getResourceList()) {
                ReserveResourceParam.ResourceInfo r = new ReserveResourceParam.ResourceInfo();
                r.setResourceType(ResourceTypeEnum.SCHEDULE.getType());
                r.setResourceNo(sr.getEmpNo());
                r.setReserveDealer(sr.getEmpNo());
                r.setReserveFrom(activityInfo.getStartTime());
                r.setReserveTo(activityInfo.getEndTime());

                reserveResourceParam.addResource(r);
            }
        }

        return reserveResourceParam;
    }
}
