package com.zte.mcrm.activity.repository.rep.plancto;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-12
 */
public interface CtoPlanSaleDivisionMappingRepository {

    /**
     * 插入数据
     *
     * @param list
     */
    int batchInsert(@Param("list") List<CtoPlanSaleDivisionMappingDO> list);

    /**
     * 动态更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CtoPlanSaleDivisionMappingDO record);

    List<CtoPlanSaleDivisionMappingDO> queryCtoPlanSaleDivisionMappingList(CtoPlanSaleDivisionMappingDO param);

    /**
     * 批量更新
     * @param value
     * @return
     */
    int batchUpdate(List<CtoPlanSaleDivisionMappingDO> value);

    /**
     * 获取所有有效关系
     *
     * @return
     */
    List<CtoPlanSaleDivisionMappingDO> listAll();
}
