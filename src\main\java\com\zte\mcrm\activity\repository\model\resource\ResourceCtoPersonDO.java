package com.zte.mcrm.activity.repository.model.resource;

import com.zte.mcrm.adapter.bo.EmployeeBO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * @Description: CTO握手专家资源
 * @author: 罗振6005002932
 * @Date: 2024-12-11
 */
/* Started by AICoder, pid:s3f5at77dahf96f142330a19103ca95eb097c12e */
@Data
public class ResourceCtoPersonDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * 专家员工编号
     */
    private String employeeNo;

    /**
     * 专家员工姓名
     */
    private String employeeName;

    /**
     * 专家员工姓名英文
     */
    private String employeeNameEn;

    /**
     * 产品方向。以顿号隔开
     */
    private String productDirection;

    /**
     * 专家启用状态Y-生效，N-失效。枚举：BooleanEnum
     */
    private String enabledStatus;

    /**
     * 职位
     */
    private String position;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;
    /**
     * 员工姓名+编号
     */
    private String employeeNameNo;

    public void buildEmployeeNameNo(EmployeeBO employee) {
        if (Objects.isNull(employee)){
            return;
        }
        this.employeeNameNo = employee.getName();
        if (StringUtils.isNotEmpty(this.employeeNameNo)){
            this.employeeNameNo = this.employeeNameNo + employee.getEmpidUi();
            return;
        }
        this.employeeNameNo = employee.getEmpidUi();
    }
}
/* Ended by AICoder, pid:s3f5at77dahf96f142330a19103ca95eb097c12e */
