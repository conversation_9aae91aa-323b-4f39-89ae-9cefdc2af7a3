package com.zte.mcrm.activity.common.util;

import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import javax.validation.groups.Default;
import java.util.List;
import java.util.Set;

/**
 *
 */
public class ValidationUtils {

    private static final ValidatorFactory VALIDATOR_FACTORY = Validation.buildDefaultValidatorFactory();

    /**
     * 参数校验
     *
     * @param paramObject 需要校验的参数
     */
    public static void validateObject(Object paramObject) {
        Validator validator = VALIDATOR_FACTORY.getValidator();
        Set<ConstraintViolation<Object>> validateResult = validator.validate(paramObject);
        if (CollectionUtils.isEmpty(validateResult)) {
            return;
        }

        StringBuilder errorMessageSb = new StringBuilder();
        for (ConstraintViolation<Object> violation : validateResult) {
            errorMessageSb.append("[").append(violation.getPropertyPath()).append("]").append(violation.getMessage())
                .append(";");
        }
        throw new IllegalArgumentException(errorMessageSb.toString());
    }

    /**
     * 实体校验
     *
     * @param obj
     * <AUTHOR>
     * @date 2021/06/28
     */
    public static <T> void validateMessage(T obj) {
        Validator validator = VALIDATOR_FACTORY.getValidator();
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj, Default.class);
        if (!CollectionUtils.isEmpty(constraintViolations)) {
            ConstraintViolation<T> validateInfo = constraintViolations.iterator().next();
            // validateInfo.getMessage() 校验不通过时的信息，即message对应的值
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, validateInfo.getMessage());
        }
    }
    /**
     * 实体校验
     *
     * @param list
     * <AUTHOR>
     * @date 2021/06/28
     */
    public static <T> void validateListMessage(List<T> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Validator validator = VALIDATOR_FACTORY.getValidator();
        for(T obj : list){
            Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj, Default.class);
            if (!CollectionUtils.isEmpty(constraintViolations)) {
                ConstraintViolation<T> validateInfo = constraintViolations.iterator().next();
                // validateInfo.getMessage() 校验不通过时的信息，即message对应的值
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, validateInfo.getMessage());
            }
        }
    }
}
