package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.sample.param.SamplePointInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface SamplePointInfoExtMapper extends SamplePointInfoMapper {
    /**
     * 查询样板点列表
     *
     * @param query 样板点信息
     * @return 样板点列表
     */
    List<SamplePointInfoDO> getList(SamplePointInfoQuery query);

    /**
     * 查询样板点列表数量
     *
     * @param query 样板点信息
     * @return 样板点数量
     */
    int countList(SamplePointInfoQuery query);
    /**
     * 获取按月样板点数量
     * @param queryParams
     * @return
     */
    int getMonthCount(SamplePointInfoQuery queryParams);

    /**
     * 查询有效的样板点
     * @return
     */
    List<SamplePointInfoDO> queryEffectiveSamplePointList();

    /**
     * 通过id列表查询样板点信息
     * @param rowIds
     * @return
     */
    List<SamplePointInfoDO> querySamplePointInfoByIds(@Param("rowIds") List<String> rowIds);
}