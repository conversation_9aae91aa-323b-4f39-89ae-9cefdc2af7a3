package com.zte.mcrm.activity.application.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.application.exhibition.ScheduleOrchestrationAppService;
import com.zte.mcrm.activity.application.model.ScheduleOrchestrationDataSource;
import com.zte.mcrm.activity.common.config.ExhibitionUrlConfig;
import com.zte.mcrm.activity.common.constant.ErrorMsgConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.infocenter.InfoCenterService;
import com.zte.mcrm.activity.integration.infocenter.vo.SendExhibitionWorkOrderVO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.comment.vo.SendWorkOrderContentVO;
import com.zte.mcrm.activity.service.exhibition.ExhibitionQueryService;
import com.zte.mcrm.activity.service.schedule.ScheduleOrchestrationMailService;
import com.zte.mcrm.activity.service.schedule.ScheduleOrchestrationService;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticeEnum;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticeParam;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticerClient;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleParam;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleService;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationParam;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationSynNoticeParam;
import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.*;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.*;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ELEVEN;

/**
 * 展会资源编排应用服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScheduleOrchestrationAppServiceImpl implements ScheduleOrchestrationAppService {

    @Autowired
    protected ExhibitionUrlConfig exhibitionUrlConfig;
    @Autowired
    private InfoCenterService infoCenterService;
    @Autowired
    private ExhibitionQueryService exhibitionQueryService;
    @Autowired
    private ScheduleOrchestrationService scheduleOrchestrationService;
    @Autowired
    private ScheduleOrchestrationMailService scheduleOrchestrationMailService;
    @Autowired
    private ScheduleOrchestrationSynScheduleService scheduleOrchestrationSynScheduleService;
    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;
    @Autowired
    private ExhibitionDirectorRepository exhibitionDirectorRepository;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;
    @Autowired
    private ScheduleOrchestrationNoticerClient scheduleOrchestrationNoticerClient;
    /**
     * 这两个控制发送邮件和工作通知的，暂时用Apollo配置
     */
    @Value("${schedule.orchestration.sendMail:false}")
    boolean sendMail;
    @Value("${schedule.orchestration.sendWorkNotice:false}")
    boolean sendWorkNotice;

    @Override
    public BizResult<String> doScheduleOrchestration(BizRequest<ScheduleOrchestrationParam> req) {
        // 【1】业务处理
        BizResult<String> res = scheduleOrchestrationService.doScheduleOrchestration(req);

        if (StringUtils.isNotBlank(res.getData()) && req.getParam().isPublish()) {
            // 【2】发布-邮件通知
            if (req.getParam().isSynNotice()) {
                ScheduleOrchestrationNoticeParam scheduleOrchestrationNoticeParam = new ScheduleOrchestrationNoticeParam();
                scheduleOrchestrationNoticeParam.setExhibitionRowId(req.getParam().getExhibitionRowId());
                scheduleOrchestrationNoticeParam.setNoticeScene(ScheduleOrchestrationNoticeEnum.SEND_TO_ACTIVITY_APPLICATE);
                scheduleOrchestrationNoticeParam.setScheduleOrchestrationVersionRowId(res.getData());
                BizRequest<ScheduleOrchestrationNoticeParam> sendEmailReq = BizRequestUtil.copyRequest(req, e -> scheduleOrchestrationNoticeParam);
                // 分营、总营发布都同步给一线
                ThreadManager.submitToAsync(() -> {
                    try {
                        BizResult<Void> voidBizResult = scheduleOrchestrationNoticerClient.sendNotice(sendEmailReq);
                        log.info("展会[{}]编排人员[{}]资源编排发布，同步发送通知[申请]邮件结果{}", sendEmailReq.getParam(), sendEmailReq.getEmpNo(), voidBizResult.isFlag());
                    } catch (Exception e) {
                        log.error("展会[{}]编排人员[{}]资源编排发布，同步发送通知[申请人]邮件异常", sendEmailReq.getParam(), sendEmailReq.getEmpNo(), e);
                    }
                });
            }

            // 【3】资源编排发布后，取消《已编排 > 已拒绝/待编排》的ICenter日程
            BizRequest<ScheduleOrchestrationSynScheduleParam> cancelScheduleReq = BizRequestUtil.copyRequest(req, e -> {
                ScheduleOrchestrationSynScheduleParam param = new ScheduleOrchestrationSynScheduleParam();
                param.setExhibitionRowId(e.getExhibitionRowId());
                param.setScheduleOrchestrationVersionRowId(res.getData());
                return param;
            });
            ThreadManager.submitToAsync(() -> scheduleOrchestrationSynScheduleService.cancelScheduleForScheduleOrchestration(cancelScheduleReq));
        }

        return res;
    }

    @Override
    public BizResult<Void> doSynScheduleOrchestrationAndNotice(BizRequest<ScheduleOrchestrationSynNoticeParam> req) {
        ScheduleOrchestrationSynNoticeParam scheduleOrchestrationSynNoticeParam = req.getParam();
        if (!hasRoleDoSynScheduleOrchestrationAndNotice(req)) {
            return BizResult.buildFailRes(ErrorMsgConstant.NON_GENERAL_SYNCHRONIZATION, null);
        }
        String exhibitionRowId = scheduleOrchestrationSynNoticeParam.getExhibitionRowId();
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(exhibitionRowId);
        if (Objects.isNull(exhibitionInfo)) {
            return BizResult.buildFailRes(ErrorMsgConstant.EXHIBITION_ID, null);
        }

        List<ActivityScheduleItemDO> itemList = fetchScheduleItem(req.getParam());
        if (CollectionUtils.isEmpty(itemList)) {
            return BizResult.buildSuccessRes(null);
        }
        ThreadManager.submitToAsync(() -> {
            /*
            占用日程（这里需要使用查询后全量的日程，因为日程处理分为如下几种情况：
            1、日程创建（待编排/已拒绝-> 已编排））
            2、日程取消（已编排->待编排/已拒绝）
            3、日程更新（已编排->已编排）
            4、日程不处理（状态不变）
             */
            ScheduleOrchestrationSynScheduleParam scheduleOrchestrationSynScheduleParam = new ScheduleOrchestrationSynScheduleParam();
            scheduleOrchestrationSynScheduleParam.setExhibitionRowId(exhibitionRowId);
            scheduleOrchestrationSynScheduleParam.setScheduleRowIds(itemList.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList()));
            scheduleOrchestrationSynScheduleService.synSchedule(BizRequestUtil.copyRequest(req, e -> scheduleOrchestrationSynScheduleParam));
        });

        List<String> dealedItemList = itemList.stream().filter(e -> !ResourceOrchestrationDealStatusEnum.WAIT.isMe(e.getDealStatus())).map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dealedItemList)) {
            ThreadManager.submitToAsync(() -> sendNotice(req, exhibitionInfo, dealedItemList));
        }

        return BizResult.buildSuccessRes(null);
    }

    /**
     * 判断是否有权操作
     *
     * @param req
     * @return true-有权，false-无权操作
     */
    boolean hasRoleDoSynScheduleOrchestrationAndNotice(BizRequest<ScheduleOrchestrationSynNoticeParam> req) {
        String exhibitionRowId = req.getParam().getExhibitionRowId();
        List<ExhibitionDirectorDO> directorList = exhibitionDirectorRepository.queryDirectorByExhibitionRowId(Collections.singletonList(exhibitionRowId))
                .get(exhibitionRowId);
        ScheduleOrchestrationDataSource ds = new ScheduleOrchestrationDataSource();
        ds.setDirectorList(directorList);
        return BooleanEnum.Y.isMe(ds.fetchScheduleOrchestrationType(req.getEmpNo()));
    }

    /**
     * 发送通知等
     *
     * @param req
     * @param exhibitionInfo
     * @param dealedItemList
     */
    void sendNotice(BizRequest<ScheduleOrchestrationSynNoticeParam> req, ExhibitionInfoDO exhibitionInfo, List<String> dealedItemList) {
        try {
            if (sendWorkNotice) {
                // 发送工作通知
                sendExhibitionWorkNotice(exhibitionInfo, dealedItemList);
            }
            if (sendMail) {
                // 发送邮件
                ScheduleOrchestrationNoticeParam scheduleOrchestrationNoticeParam = new ScheduleOrchestrationNoticeParam();
                scheduleOrchestrationNoticeParam.setExhibitionRowId(exhibitionInfo.getRowId());
                scheduleOrchestrationNoticeParam.setScheduleRowIds(dealedItemList);
                scheduleOrchestrationMailService.sendMailToSavantAndLeader(BizRequestUtil.copyRequest(req, e -> scheduleOrchestrationNoticeParam));
            }
        } catch (Exception e) {
            log.error("展会[{}]编排人员[{}]资源编排发布，同步日程和邮件通知邮件异常", req.getParam(), req.getEmpNo(), e);
        }
    }

    /**
     * 根据条件获取需要处理的日程列表
     *
     * @param scheduleOrchestrationSynNoticeParam
     * @return
     */
    List<ActivityScheduleItemDO> fetchScheduleItem(ScheduleOrchestrationSynNoticeParam scheduleOrchestrationSynNoticeParam) {
        if (CollectionUtils.isNotEmpty(scheduleOrchestrationSynNoticeParam.getScheduleItemRowIdList())) {
            // 指定《日程》查询
            return scheduleItemRepository.getScheduleItemByRowIds(scheduleOrchestrationSynNoticeParam.getScheduleItemRowIdList());
        } else {
            // 按展会维度查询《日程》
            ActivityInfoQuery query = new ActivityInfoQuery();
            query.setOriginRowIdList(Lists.newArrayList(scheduleOrchestrationSynNoticeParam.getExhibitionRowId()));
            query.setActivityStatus(Lists.newArrayList(ActivityStatusEnum.PROGRESS.getCode(), ActivityStatusEnum.FINISH.getCode(), ActivityStatusEnum.PROGRESS.getCode()));
            Map<String, ActivityInfoDO> activityInfoDOMap = activityInfoRepository.searchByParam(query)
                    .stream().collect(Collectors.toMap(ActivityInfoDO::getRowId, Function.identity(), (v1, v2) -> v1));

            return scheduleItemRepository.getRelationScheduleInfoList(Lists.newArrayList(activityInfoDOMap.keySet()));
        }
    }

    /**
     * 发送工作通知
     *
     * @param exhibitionInfo
     * @param selectedScheduleItemRowIdList
     */
    void sendExhibitionWorkNotice(ExhibitionInfoDO exhibitionInfo, List<String> selectedScheduleItemRowIdList) {
        String exhibitionRowId = exhibitionInfo.getRowId();

        // 1. 根据展会查出所有干系人
        Set<String> exhibitionStakeholder = exhibitionQueryService.getExhibitionStakeholder(exhibitionRowId,
                Lists.newArrayList(ActivityStatusEnum.PROGRESS.getCode()), new HashSet<>(selectedScheduleItemRowIdList));
        if (CollectionUtils.isEmpty(exhibitionStakeholder)) {
            return;
        }

        // 2. 调用工作通知接口分批发送展会工作通知
        Lists.partition(Lists.newArrayList(exhibitionStakeholder), NumberConstant.HUNDRED).forEach(peopleCodes -> {
            this.scheduleSendExhibitionWorkOrder(exhibitionInfo, peopleCodes);
        });
    }

    /**
     * 发送展会工作通知给 相关人员
     *
     * @param exhibitionInfoDO
     * @param sendMessageToPeopleCodes
     */
    private void scheduleSendExhibitionWorkOrder(ExhibitionInfoDO exhibitionInfoDO, List<String> sendMessageToPeopleCodes) {
        String exhibitionRowId = exhibitionInfoDO.getRowId();
        SendExhibitionWorkOrderVO sendExhibitionWorkOrderVO = new SendExhibitionWorkOrderVO();
        sendExhibitionWorkOrderVO.setId(exhibitionRowId);
        sendExhibitionWorkOrderVO.setInputSource(NumberConstant.ONE);
        sendExhibitionWorkOrderVO.setCustomMsgType(ELEVEN);
        SendWorkOrderContentVO subject = new SendWorkOrderContentVO();
        subject.setZh(EXHIBITION_INVITE_TITLE_PRE + QUOT + exhibitionInfoDO.getExhibitionName() + QUOT);
        subject.setEn(EXHIBITION_INVITE_TITLE_PRE_EN + QUOT + exhibitionInfoDO.getExhibitionName() + QUOT);
        sendExhibitionWorkOrderVO.setSubject(subject);
        SendWorkOrderContentVO summary = new SendWorkOrderContentVO();
        String exhibitionStartTime = DateUtils.getDateFormatDay(exhibitionInfoDO.getStartTime());
        String exhibitionEndTime = DateUtils.getDateFormatDay(exhibitionInfoDO.getEndTime());
        summary.setZh(ACTIVITY_TIME + exhibitionStartTime + WAVE + exhibitionEndTime + WRAP + ACTIVITY_PLACE + exhibitionInfoDO.getExhibitionPlace());
        summary.setEn(ACTIVITY_TIME_EN + exhibitionStartTime + WAVE + exhibitionEndTime + WRAP + ACTIVITY_PLACE_EN + exhibitionInfoDO.getExhibitionPlace());
        sendExhibitionWorkOrderVO.setSummary(summary);
        sendExhibitionWorkOrderVO.setTo(sendMessageToPeopleCodes);
        sendExhibitionWorkOrderVO.setUrl(exhibitionUrlConfig.getDefaultRedirectUrl());
        infoCenterService.sendExhibitionWorkOrderBySignature(sendExhibitionWorkOrderVO);
    }
}
