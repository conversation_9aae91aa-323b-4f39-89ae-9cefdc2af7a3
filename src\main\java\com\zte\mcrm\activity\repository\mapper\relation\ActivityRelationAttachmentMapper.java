package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;

public interface ActivityRelationAttachmentMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationAttachmentDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationAttachmentDO record);

    /**
     * query by primary key
     */
    ActivityRelationAttachmentDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationAttachmentDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationAttachmentDO record);
}