package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginVersionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleItemOriginVersionExtMapper extends ActivityScheduleItemOriginVersionMapper {

    /**
     * 根据活动ID获取对应日程源数据版本信息
     *
     * @param activityRowIds
     * @return
     */
    List<ActivityScheduleItemOriginVersionDO> getByActivityRowIds(@Param("activityRowIds") List<String> activityRowIds);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<ActivityScheduleItemOriginVersionDO> list);

}