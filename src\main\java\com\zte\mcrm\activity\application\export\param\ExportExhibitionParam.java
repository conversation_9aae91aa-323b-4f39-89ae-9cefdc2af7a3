package com.zte.mcrm.activity.application.export.param;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午2:04
 **/
@Setter
@Getter
public class ExportExhibitionParam {

    /**
     * 展会Id
     */
    @NotBlank(message = "exhibitionRowId.is.null")
    private String exhibitionRowId;

    /**
     * 领导工号
     */
    private String employeeNo;

    /**
     * 会议室名称
     */
    private String roomName;

    /**
     * 是否导出资源编排数据
     */
    private Boolean needExportOrchestration;

    /**
     * 是否导出酒店/车辆信息
     */
    private Boolean needExportHotelAndCar;

    /**
     * 是否导出客户参与人
     */
    private Boolean needExportCusContacts = false;

    /**
     * 是否导出费用
     */
    private Boolean needExportFee = false;

    /**
     * 专家工号
     */
    private String expertNo;

}
