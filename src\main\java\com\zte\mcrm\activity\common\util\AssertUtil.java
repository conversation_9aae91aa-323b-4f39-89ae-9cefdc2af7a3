package com.zte.mcrm.activity.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.Collection;

/**
 * @ClassName AssertUtil
 * @description: 断言工具类
 * @author: 李龙10317843
 * @create: 2023-03-13 19:53
 * @Version 1.0
 **/
@Slf4j
public class AssertUtil {
    /**
     * 期望不为空
     *
     * @param object
     * @param message
     */
    public static void assertNotNull(Object object, String message) {
        if (object == null) {
            throw new RuntimeException(message);
        }
    }
    /**
     * 期望不为空
     *
     * @param object
     */
    public static void assertNotNull(Object object) {
       assertNotNull(object,"this data can not be null!");
    }
    /**
     * 期望集合不为空
     *
     * @param collection
     * @param message
     */
    public static void assertNotEmpty(Collection collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new RuntimeException(message);
        }
    }
    /**
     * 期望集合不为空
     *
     * @param collection
     */
    public static void assertNotEmpty(Collection collection) {
       assertNotNull(collection,"this collection can not be empty!");
    }

    /**
     * 期望集合为空
     *
     * @param collection
     */
    public static void assertEmpty(Collection collection) {
        assertEmpty(collection, MessageFormat.format("this collection must be empty! data:{0}",collection));
    }
    /**
     * 期望集合为空
     *
     * @param collection
     * @param message
     */
    public static void assertEmpty(Collection collection, String message) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new RuntimeException(message);
        }
    }
}