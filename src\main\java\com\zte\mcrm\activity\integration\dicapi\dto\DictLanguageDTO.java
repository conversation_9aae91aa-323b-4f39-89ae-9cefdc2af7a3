package com.zte.mcrm.activity.integration.dicapi.dto;

/**
 * <AUTHOR> Ye<PERSON>hibin
 * @date 2019/7/19
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * b2b字典信息表
 * <AUTHOR>
 * date: 2023/7/24 14:55
 */
@ApiModel(description="b2b字典信息表")
@JsonIgnoreProperties(ignoreUnknown = true)
public class DictLanguageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**设置效验规则,主键字段不能为空*/
    @NotNull(message="{DictLanguageDTO.dictId.NotNull}")
    @ApiModelProperty(value = "id主键")
    private String dictId;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "")
    private String lastUpdatedBy;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "有效标记：Y 有效 N 无效")
    private String enabledFlag;

    @ApiModelProperty(value = "字典类型")
    private String dictType;

    @ApiModelProperty(value = "字典key")
    private String dictKey;

    @ApiModelProperty(value = "字典value")
    private String dictValue;

    @ApiModelProperty(value = "字典冗余字段")
    private String dictRedundancy;

    @ApiModelProperty(value = "备注")
    private String memo;

    //========================getter ===========================setter=========================================

    public String getDictId() {
        return dictId;
    }
    public void setDictId(String dictId) {
        this.dictId = dictId;
    }
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }
    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }
    public Date getCreationDate() {
        return creationDate;
    }
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public String getEnabledFlag() {
        return enabledFlag;
    }
    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }
    public String getDictType() {
        return dictType;
    }
    public void setDictType(String dictType) {
        this.dictType = dictType;
    }
    public String getDictKey() {
        return dictKey;
    }
    public void setDictKey(String dictKey) {
        this.dictKey = dictKey;
    }
    public String getDictValue() {
        return dictValue;
    }
    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }
    public String getDictRedundancy() {
        return dictRedundancy;
    }
    public void setDictRedundancy(String dictRedundancy) {
        this.dictRedundancy = dictRedundancy;
    }
    public String getMemo() {
        return memo;
    }
    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Override
    public String toString() {
        return "DictLanguageDTO{" +
                "dictId='" + dictId + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdatedBy='" + lastUpdatedBy + '\'' +
                ", creationDate=" + creationDate +
                ", createdBy='" + createdBy + '\'' +
                ", enabledFlag='" + enabledFlag + '\'' +
                ", dictType='" + dictType + '\'' +
                ", dictKey='" + dictKey + '\'' +
                ", dictValue='" + dictValue + '\'' +
                ", dictRedundancy='" + dictRedundancy + '\'' +
                ", memo='" + memo + '\'' +
                '}';
    }
}
