package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.*;
import com.zte.mcrm.activity.web.controller.activity.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivityOpportunityService {

    /**
     * 查询商机对应的活动
     *
     * @param request
     * @return
     */
    PageRows<ActivityOpportunityVO> queryActivityForOpportunity(BizRequest<PageQuery<ActivityOpportunityQueryParam>> request);

    /**
     * 查询商机无绑定关系对应活动
     *
     * @param request
     * @return
     */
    PageRows<ActivityOpportunityRelationVO> queryActivityOpportunityRelation(BizRequest<PageQuery<ActivityOpportunityRelationParam>> request);

    /**
     * 为商机修改活动绑定关系
     *
     * @param request
     * @return
     */
    int changeBind2Opportunity(BizRequest<ChangeBind2OpportunityParam> request);

    /**
     * 为活动修改商机绑定关系
     *
     * @param request
     * @return
     */
    int changeBind2Activity(BizRequest<ChangeBind2ActivityParam> request);

    /**
     * 通过商机编码或拓展活动id查询绑定解绑历史记录
     *
     * @param request
     * @return
     */
    List<ActivityOpportunityRecordVO> queryRecord(BizRequest<ActivityOpportunityQueryRecordParam> request);

    /**
     * 通过拓展活动id查询绑定的商机列表
     *
     * @param request
     * @return
     */
    List<OpportunityVO> queryBindOpportunityList(BizRequest<String> request);

    /**
     * 通过活动id获取该活动绑定商机操作记录 并按照活动状态分组
     *
     * @param activityRowId
     * @return
     */
    Map<String, List<ActivityOpportunityRecordVO>> getActivityStatusRecordMap(String activityRowId);

    /**
     * 修改活动商机绑定记录
     * @param request 请求
     * @return
     */
    Integer batchUpdateRecord(BizRequest<List<ActivityOpportunityUpdateVO>> request);
}
