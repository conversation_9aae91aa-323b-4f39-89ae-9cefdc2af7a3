package com.zte.mcrm.activity.service.activity.support;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.service.activity.param.ActivityDataSource;


/**
 * 活动数据源支撑
 * <AUTHOR>
 */
public interface IActivityDataSourceSupport {
    /**
     * 获取活动数据源
     *
     * @param bizRequest
     * @return
     */
    ActivityDataSource getActivitySamplePointDataSource(BizRequest<String> bizRequest);
}
