package com.zte.mcrm.activity.repository.rep.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivitySummaryRepository {

    /**
     * 添加关联的会议纪要（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivitySummaryDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivitySummaryDO record);

    /**
     * 查询活动关联的所有会议纪要
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivitySummaryDO> queryAllSummaryForActivity(String activityRowId);

    /**
     * 查询活动相关的会议纪要信息
     * @param activityRowId
     * @return
     */
    Map<String, List<ActivitySummaryDO>> queryAllByActivityRowId(List<String> activityRowId);

    /**
     * 根据活动Id批次删除
     * @param operator 用户工号
     * @param activityRowIds    活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:47
     */
    int deleteByActivityIds(String operator, List<String> activityRowIds);

    /**
     * 通过主键id查询记录
     *
     * @param rowId
     * @return {@link ActivitySummaryDO}
     * <AUTHOR>
     * @date 2025/3/4 下午5:25
     */
    ActivitySummaryDO selectByPrimaryKey(String rowId);
}
