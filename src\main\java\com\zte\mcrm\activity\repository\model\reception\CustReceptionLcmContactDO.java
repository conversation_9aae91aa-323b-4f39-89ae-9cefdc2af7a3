package com.zte.mcrm.activity.repository.model.reception;

import java.util.Date;

/**
 * table:t_cust_reception_lcm_contact -- 
 */
public class CustReceptionLcmContactDO {
    /** 主键 */
    private String rowId;

    /** t_cust_expansion_header#id */
    private String headerId;

    /** 关联对应客户t_cust_expansion_contact#id */
    private String expansionContactId;

    /** 一行客户信息-客户和联系人结对ID */
    private String pairRowId;

    /** 联系人名字 */
    private String contactName;

    /** 联系人拼音1（中国的才有） */
    private String contactPinyin1;

    /** 联系人拼音2（中国的才有） */
    private String contactPinyin2;

    /** LCM扫描单号 */
    private String lcmScanNo;

    /** LCM扫描状态.枚举：LcmScanStatusEnum */
    private String lcmScanStatus;

    /** LCM扫描受限制结果 */
    private String sanctionedParty;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getHeaderId() {
        return headerId;
    }

    public void setHeaderId(String headerId) {
        this.headerId = headerId == null ? null : headerId.trim();
    }

    public String getExpansionContactId() {
        return expansionContactId;
    }

    public void setExpansionContactId(String expansionContactId) {
        this.expansionContactId = expansionContactId == null ? null : expansionContactId.trim();
    }

    public String getPairRowId() {
        return pairRowId;
    }

    public void setPairRowId(String pairRowId) {
        this.pairRowId = pairRowId == null ? null : pairRowId.trim();
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName == null ? null : contactName.trim();
    }

    public String getContactPinyin1() {
        return contactPinyin1;
    }

    public void setContactPinyin1(String contactPinyin1) {
        this.contactPinyin1 = contactPinyin1 == null ? null : contactPinyin1.trim();
    }

    public String getContactPinyin2() {
        return contactPinyin2;
    }

    public void setContactPinyin2(String contactPinyin2) {
        this.contactPinyin2 = contactPinyin2 == null ? null : contactPinyin2.trim();
    }

    public String getLcmScanNo() {
        return lcmScanNo;
    }

    public void setLcmScanNo(String lcmScanNo) {
        this.lcmScanNo = lcmScanNo == null ? null : lcmScanNo.trim();
    }

    public String getLcmScanStatus() {
        return lcmScanStatus;
    }

    public void setLcmScanStatus(String lcmScanStatus) {
        this.lcmScanStatus = lcmScanStatus == null ? null : lcmScanStatus.trim();
    }

    public String getSanctionedParty() {
        return sanctionedParty;
    }

    public void setSanctionedParty(String sanctionedParty) {
        this.sanctionedParty = sanctionedParty == null ? null : sanctionedParty.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}