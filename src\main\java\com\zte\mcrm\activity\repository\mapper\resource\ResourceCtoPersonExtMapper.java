package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ResourceCtoPersonDO;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceCtoPersonParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-11
 */
@Mapper
public interface ResourceCtoPersonExtMapper extends ResourceCtoPersonMapper{

    /**
     * 查询CTO专家资源列表
     * @param ctoPersonParam
     * @return
     */
    List<ResourceCtoPersonDO> getCtoPersonListByProductDirection(ResourceCtoPersonParam ctoPersonParam);
}
