package com.zte.mcrm.activity.repository.mapper.log;

import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;

import java.util.List;

/* Started by AICoder, pid:oe9bbs2b24qe1931444e087fd0ec931436711330 */
public interface ActivityResourceOperationLogMapper {

    /**
     * 批量新增
     *
     * @param records
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:32
     */
    int batchInsert(List<ActivityResourceOperationLogDO> records);

    /**
     * 新增
     *
     * @param record
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:32
     */
    int insertSelective(ActivityResourceOperationLogDO record);

    /**
     * 批量更新
     *
     * @param records
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:32
     */
    int batchUpdate(List<ActivityResourceOperationLogDO> records);

    /**
     * 更新
     *
     * @param record
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:33
     */
    int updateByPrimaryKeySelective(ActivityResourceOperationLogDO record);

    /**
     * 根据主键查询
     *
     * @param rowIds
     * @return {@link List< ActivityResourceOperationLogDO>}
     * <AUTHOR>
     * @date 2024/11/13 上午10:33
     */
    List<ActivityResourceOperationLogDO> selectByPrimaries(List<String> rowIds);
}
/* Ended by AICoder, pid:oe9bbs2b24qe1931444e087fd0ec931436711330 */