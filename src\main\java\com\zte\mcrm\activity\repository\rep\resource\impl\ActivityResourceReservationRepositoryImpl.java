package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityResourceReservationExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceReservationRepository;
import com.zte.mcrm.activity.web.controller.reservation.param.ActivityResourceReservationParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ActivityResourceReservationRepositoryImpl implements ActivityResourceReservationRepository {
    @Resource
    private ActivityResourceReservationExtMapper activityResourceReservationExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivityResourceReservationDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityResourceReservationDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            activityResourceReservationExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityResourceReservationDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityResourceReservationExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public ActivityResourceReservationDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : activityResourceReservationExtMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public List<ActivityResourceReservationDO> fetchExpiredReservation(int size) {
        return size < NumberConstant.ONE ? Collections.emptyList() : activityResourceReservationExtMapper.fetchExpiredReservation(new Date(), size);
    }

    @Override
    public List<ActivityResourceReservationDO> queryAllReservationForActivity(ActivityResourceReservationParam reservationParam) {
        return CollectionUtils.isEmpty(reservationParam.getAcIds()) ? Collections.emptyList() : activityResourceReservationExtMapper.queryAllReservationForActivity(reservationParam);
    }

    @Override
    public int softDeleteResourceReservationRecords(String operator, String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return 0;
        }

        operator = StringUtils.isBlank(operator) ? HeadersProperties.getXEmpNo() : operator;
        return activityResourceReservationExtMapper.softDeleteByActivityRowIds(operator, activityRowId);
    }
}
