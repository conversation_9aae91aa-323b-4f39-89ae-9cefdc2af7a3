package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.service.model.activity.ActivityBO;

/**
 * 活动信息服务类
 *
 * <AUTHOR> 10333830
 * @date 2023-08-30 21:02
 **/
public interface ActivityInfoService {

    /**
     * 对比活动信息
     *
     * @param newActivityBO 新活动信息
     * @param oldActivityBO 旧活动信息
     * <AUTHOR>
     * date: 2023/8/30 21:03
     */
    void compareActivityInfo(ActivityBO newActivityBO, ActivityBO oldActivityBO);

    /**
     * 获取活动信息
     * @param activityRowId 活动Id
     * @return com.zte.mcrm.activity.service.model.activity.ActivityBO
     * <AUTHOR>
     * date: 2023/8/31 21:30
     */
    ActivityBO getActivityBaseInfoById(String activityRowId);

    /**
     * 通过活动Id获取活动推送ISearch的基本信息
     * @param activityRowId 活动Id
     * @return com.zte.mcrm.activity.service.model.activity.ActivityBO
     * <AUTHOR>
     * date: 2023/12/4 16:00
     */
    ActivityBO getActivitySearchInfoById(String activityRowId);

}
