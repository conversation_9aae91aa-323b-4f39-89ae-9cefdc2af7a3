package com.zte.mcrm.activity.repository.model.reception;

import java.util.Date;

/**
 * table:t_cust_reception_lcm_temp_res -- 
 */
public class TCustReceptionLcmTempResDO {
    /** 主键 */
    private String rowId;

    /** t_cust_expansion_header#id */
    private String headerId;

    /** 一行客户信息-客户和联系人结对ID */
    private String pairRowId;

    /** LCM扫描单号 */
    private String lcmScanNo;

    /** LCM扫描状态.枚举：BooleanEnum */
    private String lcmScanStatus;

    /** LCM扫描类型，客户、联系人.枚举：LcmScanTypeEnum */
    private String lcmScanType;

    /** LCM主体名称 */
    private String lcmScanName;

    /** LCM扫描受限制结果 */
    private String sanctionedParty;

    /** 地区编码 */
    private String localCode;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getHeaderId() {
        return headerId;
    }

    public void setHeaderId(String headerId) {
        this.headerId = headerId == null ? null : headerId.trim();
    }

    public String getPairRowId() {
        return pairRowId;
    }

    public void setPairRowId(String pairRowId) {
        this.pairRowId = pairRowId == null ? null : pairRowId.trim();
    }

    public String getLcmScanNo() {
        return lcmScanNo;
    }

    public void setLcmScanNo(String lcmScanNo) {
        this.lcmScanNo = lcmScanNo == null ? null : lcmScanNo.trim();
    }

    public String getLcmScanStatus() {
        return lcmScanStatus;
    }

    public void setLcmScanStatus(String lcmScanStatus) {
        this.lcmScanStatus = lcmScanStatus == null ? null : lcmScanStatus.trim();
    }

    public String getLcmScanType() {
        return lcmScanType;
    }

    public void setLcmScanType(String lcmScanType) {
        this.lcmScanType = lcmScanType == null ? null : lcmScanType.trim();
    }

    public String getLcmScanName() {
        return lcmScanName;
    }

    public void setLcmScanName(String lcmScanName) {
        this.lcmScanName = lcmScanName == null ? null : lcmScanName.trim();
    }

    public String getSanctionedParty() {
        return sanctionedParty;
    }

    public void setSanctionedParty(String sanctionedParty) {
        this.sanctionedParty = sanctionedParty == null ? null : sanctionedParty.trim();
    }

    public String getLocalCode() {
        return localCode;
    }

    public void setLocalCode(String localCode) {
        this.localCode = localCode == null ? null : localCode.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}