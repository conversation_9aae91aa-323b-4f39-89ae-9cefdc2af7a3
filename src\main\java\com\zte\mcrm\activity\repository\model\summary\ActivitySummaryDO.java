package com.zte.mcrm.activity.repository.model.summary;

import java.util.Date;

/**
 * table:activity_summary -- 
 */
public class ActivitySummaryDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String activityRowId;

    /** 会议纪要开始时间。对外显示yyyy-MM-dd HH:mm格式 */
    private Date communicateTime;

    /** 交流地点 */
    private String communicatePlace;

    /** 纪要反馈人 */
    private String feedbackBy;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 会议纪要状态，draft-草稿，submit-已提交。枚举：SummaryStatusEnum */
    private String status;

    /** 会议纪要结束时间 */
    private Date communicateEndTime;

    /** 交流内容，限制2000 */
    private String summaryContent;

    /** 交流材料，限制2000 */
    private String summaryMaterials;

    /** 纪要收件人 */
    private String mailReceiver;

    /**
     * 中文摘要
     */
    private String abstractZh;

    /**
     * 英文摘要
     */
    private String abstractEn;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public Date getCommunicateTime() {
        return communicateTime;
    }

    public void setCommunicateTime(Date communicateTime) {
        this.communicateTime = communicateTime;
    }

    public String getCommunicatePlace() {
        return communicatePlace;
    }

    public void setCommunicatePlace(String communicatePlace) {
        this.communicatePlace = communicatePlace == null ? null : communicatePlace.trim();
    }

    public String getFeedbackBy() {
        return feedbackBy;
    }

    public void setFeedbackBy(String feedbackBy) {
        this.feedbackBy = feedbackBy == null ? null : feedbackBy.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Date getCommunicateEndTime() {
        return communicateEndTime;
    }

    public void setCommunicateEndTime(Date communicateEndTime) {
        this.communicateEndTime = communicateEndTime;
    }

    public String getSummaryContent() {
        return summaryContent;
    }

    public void setSummaryContent(String summaryContent) {
        this.summaryContent = summaryContent == null ? null : summaryContent.trim();
    }

    public String getSummaryMaterials() {
        return summaryMaterials;
    }

    public void setSummaryMaterials(String summaryMaterials) {
        this.summaryMaterials = summaryMaterials == null ? null : summaryMaterials.trim();
    }

    public String getMailReceiver() {
        return mailReceiver;
    }

    public void setMailReceiver(String mailReceiver) {
        this.mailReceiver = mailReceiver == null ? null : mailReceiver.trim();
    }

    public String getAbstractZh() {
        return abstractZh;
    }

    public void setAbstractZh(String abstractZh) {
        this.abstractZh = abstractZh == null ? null : abstractZh.trim();
    }

    public String getAbstractEn() {
        return abstractEn;
    }

    public void setAbstractEn(String abstractEn) {
        this.abstractEn = abstractEn == null ? null : abstractEn.trim();
    }
}