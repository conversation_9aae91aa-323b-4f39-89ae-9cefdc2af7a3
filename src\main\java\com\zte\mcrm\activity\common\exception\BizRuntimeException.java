package com.zte.mcrm.activity.common.exception;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import lombok.Getter;
import lombok.Setter;

/**
 * 通用业务异常，在业务操作中所有异常使用该类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BizRuntimeException extends RuntimeException {

    private static final long serialVersionUID = -7783427288091691803L;
    /**
     * 业务错误码
     */
    public static final String BIZ_ERROR_CODE = "0005";

    /**
     * 异常源
     */
    private Throwable ex;
    /**
     * 业务领域所属模块
     */
    private String module;
    /**
     * 业务参数
     */
    private Object bizParam;
    /**
     * 业务code
     */
    private String code;
    /**
     * 异常消息描述（支持传入国家化key，也支持直接描述）
     */
    private String msg;
    /**
     * 国际化信息的占位符参数
     */
    private Object[] args;

    public BizRuntimeException() {
    }

    /**
     * @param code 业务code码
     * @param msg  消息/key
     * @param args 消息占位参数值
     */
    public BizRuntimeException(String code, String msg, Object... args) {
        this(null, null, code, msg, args);
    }

    /**
     * 没有code，则最终对外默认是0005或0001
     *
     * @param ex   发生的异常
     * @param msg  消息/key
     * @param args 消息占位参数值
     */
    public BizRuntimeException(Throwable ex, String msg, Object... args) {
        this(ex, null, null, msg, args);
    }

    /**
     * @param ex     发生异常
     * @param module 所属模块
     * @param code   业务code码
     * @param msg    消息/key
     * @param args   消息占位参数值
     */
    public BizRuntimeException(Throwable ex, String module, String code, String msg, Object... args) {
        this.ex = ex;
        this.setBelongBiz(module, code);
        this.msg = msg;
        this.args = args;
    }

    /**
     * @param module 所属模块
     * @param code   业务code码
     */
    public void setBelongBiz(String module, String code) {
        this.module = module;
        this.code = code;
    }

    /**
     * 是否有源异常
     *
     * @return
     */
    public boolean hasEx() {
        return ex != null;
    }

    /**
     * 获取参数json字符串
     *
     * @return
     */
    public String fetchParamJson() {
        return bizParam == null ? CharacterConstant.EMPTY_STR : JSON.toJSONString(bizParam);
    }

}
