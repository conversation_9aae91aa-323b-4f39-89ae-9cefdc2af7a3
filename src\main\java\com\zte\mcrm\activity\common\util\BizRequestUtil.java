package com.zte.mcrm.activity.common.util;


import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.adapter.common.HeadersProperties;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * BizRequest工具类（快捷创建BizRequest对象）
 *
 * <AUTHOR>
 */
public class BizRequestUtil {

    /**
     * @param request
     * @return 如果没有操作人信息就报错
     */
    public static void assertHasAuth(BizRequest<?> request) {
        boolean flag = request != null && StringUtils.isNotBlank(request.getEmpNo()) && StringUtils.isNotBlank(request.getToken());
        if (!flag) {
            throw new BizRuntimeException("0004", "not login");
        }
    }

    /**
     * 安全模式下的创建，即：要求登录人信息必有，否则跑【抛出异常{@link BizRuntimeException}
     *
     * @param <T>
     * @return
     */
    public static <T> BizRequest<T> createWithCurrentUserSecurity() {
        return createWithCurrentUserSecurity(null);
    }

    /**
     * 安全模式下的创建，即：要求登录人信息必有，否则跑【抛出异常{@link BizRuntimeException}
     *
     * @param <T>
     * @return
     */
    public static <T> BizRequest<T> createWithCurrentUserSecurity(T param) {
        BizRequest<T> req = createWithCurrentUser(param);
        assertHasAuth(req);

        return req;
    }

    /**
     * 根据当前登录会话信息创建请求对象
     *
     * @param <T>
     * @return
     */
    public static <T> BizRequest<T> createWithCurrentUser() {
        return createWithCurrentUser(null);
    }

    /**
     * 根据当前登录会话信息创建请求对象
     *
     * @param param
     * @param <T>
     * @return
     */
    public static <T> BizRequest<T> createWithCurrentUser(T param) {
        BizRequest<T> req = new BizRequest<>();
        req.setParam(param);

        req.setEmpNo(HeadersProperties.getXEmpNo());
        req.setToken(HeadersProperties.getXAuthValue());
        req.setLangId(HeadersProperties.getXLangId());

        return req;
    }

    /**
     * 复制请求
     *
     * @param source
     * @param <T>
     * @return
     */
    public static <F,T> BizRequest<T> copyRequest(BizRequest<F> source, Function<F, T> covert) {
        BizRequest<T> req = new BizRequest<>();
        req.setParam(covert.apply(source.getParam()));

        req.setEmpNo(source.getEmpNo());
        req.setToken(source.getToken());
        req.setLangId(source.getLangId());

        return req;
    }

}
