package com.zte.mcrm.activity.application.export.convert;

import com.zte.mcrm.activity.application.export.vo.ExportActivityCustVO;
import com.zte.mcrm.activity.application.export.vo.ExportActivityInfoVO;
import com.zte.mcrm.activity.application.export.vo.ExportActivityZteVO;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.DateConstants;
import com.zte.mcrm.activity.common.constant.ExcelConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;
import com.zte.mcrm.activity.web.controller.export.model.ActivityDownLoadVO;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.constant.ExternalConstant;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 导出活动数据转换
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Component
public class ExportActivityConvertor {
    private static final Logger logger = LoggerFactory.getLogger(ExportActivityConvertor.class);

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;


    /**
     * 转为客户活动Excel导出模型
     *
     * @param data 活动数据源
     * @return Excel导出模型
     */
    public SimpleExcelExportModel toSimpleExcelExportModel(StandardActivityDetailDataSource data) {
        SimpleExcelExportModel model = new SimpleExcelExportModel();

        Map<String, OrgInfoVO> orgMap = new HashMap<>(1024);

        List<ExportActivityInfoVO> activityList = toExportActivityInfoVOList(data, orgMap);
        List<ExportActivityCustVO> custList = toExportActivityCustVOList(data, orgMap);
        List<ExportActivityZteVO> ztePeopleList = toExportActivityZteVOList(data);

        model.addSheetData(ExcelConstant.ACTIVITY_SHEET_NAME, activityList, ExportActivityInfoVO.class);
        model.addSheetData(ExcelConstant.CUST_SHEET_NAME, custList, ExportActivityCustVO.class);
        model.addSheetData(ExcelConstant.ZTE_SHEET_NAME, ztePeopleList, ExportActivityZteVO.class);

        return model;
    }

    /**
     * 转为基本信息
     *
     * @param data 活动数据源
     * @return 基本信息
     */
    private List<ExportActivityInfoVO> toExportActivityInfoVOList(StandardActivityDetailDataSource data, Map<String, OrgInfoVO> orgMap) {
        List<ActivityInfoDO> oriActivityList = data.getActivityInfoList();
        if (CollectionUtils.isEmpty(oriActivityList)) {
            return Collections.emptyList();
        }

        List<ExportActivityInfoVO> activityList = new ArrayList<>(oriActivityList.size());
        mergeDepartmentInfo(oriActivityList.stream().map(ActivityInfoDO::getApplyDepartmentNo).filter(StringUtils::isNotBlank).collect(Collectors.toList()), orgMap);

        for (ActivityInfoDO info : oriActivityList) {
            activityList.add(packExportActivityInfoVO(info, data, orgMap));
        }

        return activityList;
    }

    /**
     * 转为客户信息
     *
     * @param data 活动数据源
     * @return 基本信息
     */
    private List<ExportActivityCustVO> toExportActivityCustVOList(StandardActivityDetailDataSource data, Map<String, OrgInfoVO> orgMap) {
        List<ActivityInfoDO> oriActivityList = data.getActivityInfoList();
        if (CollectionUtils.isEmpty(oriActivityList)) {
            return Collections.emptyList();
        }

        List<ExportActivityCustVO> custList = new ArrayList<>(1024);

        for (ActivityInfoDO info : oriActivityList) {
            custList.addAll(packExportActivityCustVO(info, data, orgMap));
        }

        return custList;
    }


    /**
     * 转为基本信息
     *
     * @param data 活动数据源
     * @return 基本信息
     */
    private List<ExportActivityZteVO> toExportActivityZteVOList(StandardActivityDetailDataSource data) {
        List<ActivityInfoDO> oriActivityList = data.getActivityInfoList();
        if (CollectionUtils.isEmpty(oriActivityList)) {
            return Collections.emptyList();
        }

        // 将所有员工的编号整理出来
        Set<String> empNos = oriActivityList.stream().map(e -> {
            List<ActivityRelationZtePeopleDO> peopleList = data.fetchActivityZtePeople(e.getRowId(), ActivityPeopleTypeEnum.LECTURER, ActivityPeopleTypeEnum.ORGANIZER, ActivityPeopleTypeEnum.PARTICIPANTS);
            List<String> list = peopleList == null ? Collections.emptyList()
                    : peopleList.stream().map(ActivityRelationZtePeopleDO::getPeopleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            return list;
        }).flatMap(Collection::stream).collect(Collectors.toSet());

        // 缓存员工人员信息
        Map<String, PersonInfoDTO> peopleMap = new HashMap<>();
        mergeZtePeopleInfo(empNos, peopleMap);

        List<ExportActivityZteVO> ztePeopleList = new ArrayList<>();

        for (ActivityInfoDO info : oriActivityList) {
            // 讲师，组织者，参与人
            List<ActivityRelationZtePeopleDO> peopleList = data.fetchActivityZtePeople(info.getRowId(), ActivityPeopleTypeEnum.LECTURER, ActivityPeopleTypeEnum.ORGANIZER, ActivityPeopleTypeEnum.PARTICIPANTS);
            if (CollectionUtils.isEmpty(peopleList)) {
                continue;
            }

            for (ActivityRelationZtePeopleDO people : peopleList) {
                ExportActivityZteVO vo = new ExportActivityZteVO();
                vo.setActivityRequestNo(info.getActivityRequestNo());
                vo.setActivityTitle(info.getActivityTitle());
                vo.setPeopleName(people.getPeopleName());
                vo.setPeopleDepartmentName(people.getDeptFullName());
                vo.setPositionName(people.getPositionName());
                vo.setPeopleTypeName(PeopleRoleLabelEnum.getDescByCode(people.getPeopleLabel()));

                // 优先使用HR的数据
                PersonInfoDTO personInfo = peopleMap.get(people.getPeopleCode());
                if (personInfo != null) {
                    vo.setPeopleDepartmentCode(personInfo.getOrgID());
                    vo.setPeopleDepartmentName(personInfo.getOrgFullName());
                    vo.setPositionName(personInfo.getPostName());
                }
                ztePeopleList.add(vo);
            }
        }

        return ztePeopleList;
    }

    /**
     * 打包活动对应的活动信息
     *
     * @param info 活动信息
     * @param data 活动数据源
     * @return
     */
    private ExportActivityInfoVO packExportActivityInfoVO(ActivityInfoDO info, StandardActivityDetailDataSource data, Map<String, OrgInfoVO> orgMap) {
        ExportActivityInfoVO vo = new ExportActivityInfoVO();

        vo.setActivityRequestNo(info.getActivityRequestNo());
        vo.setActivityStatusName(ActivityStatusEnum.getNameByCode(info.getActivityStatus()));
        vo.setActivityTypeName(ActivityTypeEnum.getDescByType(info.getActivityType()));
        vo.setActivityTitle(info.getActivityTitle());

        if (info.getStartTime() != null && info.getEndTime() != null) {
            vo.setStartTime(DateFormatUtils.format(info.getStartTime(), DateConstants.YYYY_MM_DD_HH_MM));
            vo.setEndTime(DateFormatUtils.format(info.getEndTime(), DateConstants.YYYY_MM_DD_HH_MM));
        }

        vo.setActivityPlace(info.getActivityPlace());
        vo.setCommunicationWayName(CommunicationWayEnum.getDescByCode(info.getCommunicationWay()));

        List<ActivityCommunicationDirectionDO> directionList = data.fetchCommDirection(info.getRowId());
        if (CollectionUtils.isNotEmpty(directionList)) {
            vo.setDirection(directionList.stream().map(ActivityCommunicationDirectionDO::getCommunicationDirection).collect(Collectors.joining(CharacterConstant.COMMA)));
        }

        List<ActivityRelationProjectDO> projectList = data.fetchProject(info.getRowId());
        if (CollectionUtils.isNotEmpty(projectList)) {
            vo.setRelationProject(projectList.stream().map(ActivityRelationProjectDO::getProjectName).filter(StringUtils::isNotBlank).collect(Collectors.joining(CharacterConstant.COMMA)));
        }

        vo.setCommunicationLevelName(CommunicationLevelEnum.getDescByCode(info.getCommunicationLevel()));
        vo.setApplyDepartmentNo(info.getApplyDepartmentNo());
        OrgInfoVO orgInfo = orgMap.get(info.getApplyDepartmentNo());
        if (orgInfo != null) {
            vo.setApplyDepartmentName(orgInfo.getHrOrgNamePath());
        }

        List<ActivityRelationZtePeopleDO> createdPeopleList = data.fetchActivityZtePeople(info.getRowId(), ActivityPeopleTypeEnum.CREATE_BY);
        List<ActivityRelationZtePeopleDO> applyPeopleList = data.fetchActivityZtePeople(info.getRowId(), ActivityPeopleTypeEnum.APPLICANT);
        List<ActivityRelationZtePeopleDO> bigProjectPeopleList = data.fetchActivityZtePeople(info.getRowId(), ActivityPeopleTypeEnum.LPCONTACTPEOPLE);
        List<ActivityRelationZtePeopleDO> informedPeopleList = data.fetchActivityZtePeople(info.getRowId(), ActivityPeopleTypeEnum.INFORMED);

        // 创建人和申请人
        vo.setCreatedBy(info.getCreatedBy());
        vo.setApplyPeopleDesc(info.getApplyPeopleNo());
        if (CollectionUtils.isNotEmpty(createdPeopleList)) {
            vo.setCreatedBy(createdPeopleList.stream().map(e -> e.getPeopleName() + e.getPeopleCode()).collect(Collectors.joining(CharacterConstant.SEMICOLON)));
        }
        if (CollectionUtils.isNotEmpty(applyPeopleList)) {
            vo.setApplyPeopleDesc(applyPeopleList.stream().map(e -> e.getPeopleName() + e.getPeopleCode()).collect(Collectors.joining(CharacterConstant.SEMICOLON)));
        }
        if (CollectionUtils.isNotEmpty(bigProjectPeopleList)) {
            vo.setBigProjectContractPeople(bigProjectPeopleList.stream().map(e -> e.getPeopleName() + e.getPeopleCode()).collect(Collectors.joining(CharacterConstant.SEMICOLON)));
        }
        if (CollectionUtils.isNotEmpty(informedPeopleList)) {
            vo.setInformedPeople(informedPeopleList.stream().map(e -> e.getPeopleName() + e.getPeopleCode()).collect(Collectors.joining(CharacterConstant.SEMICOLON)));
        }

        List<ActivitySummaryIssueDO> issueList = data.fetchSummaryIssue(info.getRowId());
        List<ActivitySummaryApDO> apList = data.fetchSummaryAp(info.getRowId());

        vo.setIssueNum(String.valueOf(CollectionUtils.isEmpty(issueList) ? NumberConstant.ZERO : issueList.size()));
        vo.setApNum(String.valueOf(CollectionUtils.isEmpty(apList) ? NumberConstant.ZERO : apList.size()));
        vo.setHasSummary(CollectionUtils.isEmpty(data.fetchSummary(info.getRowId())) ? BooleanEnum.N.getCode() : BooleanEnum.Y.getCode());

        return vo;
    }

    /**
     * 打包活动对应的客户信息
     *
     * @param info
     * @param data
     */
    private List<ExportActivityCustVO> packExportActivityCustVO(ActivityInfoDO info, StandardActivityDetailDataSource data, Map<String, OrgInfoVO> orgMap) {
        List<ActivityCustomerInfoDO> customerInfoList = data.fetchCustomerList(info.getRowId());
        if (CollectionUtils.isEmpty(customerInfoList)) {
            return Collections.emptyList();
        }

        // 客户参与人按2倍客户数初始化——避免过多扩容
        List<ExportActivityCustVO> custList = new ArrayList<>(customerInfoList.size() * 2);
        mergeDepartmentInfo(customerInfoList.stream().map(ActivityCustomerInfoDO::getBelongBuId).filter(StringUtils::isNotBlank).collect(Collectors.toList()), orgMap);

        for (ActivityCustomerInfoDO customerInfo : customerInfoList) {
            List<ActivityRelationCustPeopleDO> peopleList = data.fetchActivityCustPeople(info.getRowId(), customerInfo.getCustomerCode());
            if (CollectionUtils.isEmpty(peopleList)) {
                continue;
            }

            for (ActivityRelationCustPeopleDO people : peopleList) {
                ExportActivityCustVO vo = new ExportActivityCustVO();
                vo.setActivityRequestNo(info.getActivityRequestNo());
                vo.setActivityTitle(info.getActivityTitle());
                // 客户信息
                vo.setMtoName(customerInfo.getMtoName());
                vo.setMktName(customerInfo.getMktName());
                vo.setCustomerName(customerInfo.getCustomerName());
                vo.setBelongBuId(customerInfo.getBelongBuId());
                OrgInfoVO orgInfo = orgMap.get(customerInfo.getBelongBuId());
                if (orgInfo != null) {
                    vo.setBelongBuName(orgInfo.getHrOrgNamePath());
                }
                vo.setCustLevel(customerInfo.getCustLevel());
                // 客户联系人信息
                vo.setContactName(people.getContactName());
                vo.setCustomerDepartment(people.getCustomerDepartment());
                vo.setPositionName(people.getPositionName());
                vo.setSanctionedPatryCode(people.getSanctionedPatryCode());
                vo.setContactLevel(people.getContactLevel());
                vo.setMainCust(BooleanEnum.Y.isMe(customerInfo.getMainCust()) ? BooleanEnum.Y.getCode() : BooleanEnum.N.getCode());
                custList.add(vo);
            }
        }
        return custList;
    }

    /**
     * 将查询的结果合并到peopleMap中
     *
     * @param empNos    员工编号
     * @param peopleMap 员工map
     */
    private void mergeZtePeopleInfo(Set<String> empNos, Map<String, PersonInfoDTO> peopleMap) {
        // 过滤已经查过的数据
        Set<String> empNosTp = empNos.stream().filter(e -> !peopleMap.containsKey(e)).collect(Collectors.toSet());
        MsaRpcResponse<Map<String, PersonInfoDTO>> res = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(empNosTp));
        if (res.getBo() != null) {
            peopleMap.putAll(res.getBo());
        }
    }


    /**
     * @param orgIds 部门id
     * @param orgMap 组织map
     */
    public void mergeDepartmentInfo(List<String> orgIds, Map<String, OrgInfoVO> orgMap) {
        String org = "ORG";
        // 中兴
        List<String> zteOrgIds = orgIds.stream().filter(e -> e != null && !orgMap.containsKey(e) && e.startsWith(org)).distinct().collect(Collectors.toList());
        // 子公司
        List<String> subOrgIdsTp = orgIds.stream().filter(e -> e != null && !orgMap.containsKey(e) && !e.startsWith(org)).distinct().collect(Collectors.toList());

        mergeDepartmentInfoBatch(ExternalConstant.IDTYPE_T0001, zteOrgIds, orgMap);
        mergeDepartmentInfoBatch(ExternalConstant.IDTYPE_T0002, subOrgIdsTp, orgMap);
    }

    /**
     * 分批查询组织部门数据
     *
     * @param type
     * @param orgIds
     * @param orgMap
     */
    private void mergeDepartmentInfoBatch(String type, List<String> orgIds, Map<String, OrgInfoVO> orgMap) {
        // 组织信息查询有最大id限制，这里分批查询
        for (int i = 0; i < orgIds.size(); i = i + NumberConstant.HUNDRED) {
            int end = i + NumberConstant.HUNDRED;
            if (end > orgIds.size()) {
                end = orgIds.size();
            }

            List<String> tpOrgIds = orgIds.subList(i, end);

            mergeDepartmentInfoLimit(type, tpOrgIds, orgMap);
        }
    }

    /**
     * 根据查询接口限制条数据查询
     *
     * @param type
     * @param orgIds
     * @param orgMap
     */
    private void mergeDepartmentInfoLimit(String type, List<String> orgIds, Map<String, OrgInfoVO> orgMap) {
        try {
            Map<String, OrgInfoVO> orgInfoMap = orgIds.isEmpty() ? Collections.emptyMap() : hrmUsercenterAdapter.getOrgInfoMap(type, orgIds);
            if (orgInfoMap != null) {
                orgMap.putAll(orgInfoMap);
            }
        } catch (Exception e) {
            logger.error("获取公司部门组织信息失败type={}", type, e);
        }
    }

    /**
     * 转为客户活动Excel导出模型
     *
     * @param result 活动数据源
     * @return Excel导出模型
     */
    public static SimpleExcelExportModel toSimpleExcel(List<ActivityDownLoadVO> result) {
        SimpleExcelExportModel model = new SimpleExcelExportModel();
        model.addSheetData(ExcelConstant.ACTIVITY_LIST_NAME, result, ActivityDownLoadVO.class);
        return model;
    }

}
