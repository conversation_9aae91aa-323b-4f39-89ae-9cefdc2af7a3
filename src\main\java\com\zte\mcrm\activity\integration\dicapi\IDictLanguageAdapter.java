package com.zte.mcrm.activity.integration.dicapi;

import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.integration.dicapi.param.DictLanguageQueryParam;

import java.util.List;

/**
 * 数据字典Adapter 接口类
 * <AUTHOR>
 * date: 2023/7/24 17:05
 */
public interface IDictLanguageAdapter {

	/**
	 * 精确查询一个数据字典
	 * @param request   查询请求
	 * @return MsaRpcResponse<DictLanguageDTO>
	 * <AUTHOR>
	 * date: 2023/7/24 14:44
	 */
	MsaRpcResponse<DictLanguageDTO> getExactQueryDictLanguage(MsaRpcRequest<DictLanguageQueryParam> request);

	/**
	 * 精确查询数据字典
	 * @param request   查询请求
	 * @return MsaRpcResponse<List<DictLanguageDTO>>
	 * <AUTHOR>
	 * date: 2023/7/24 14:44
	 */
	MsaRpcResponse<List<DictLanguageDTO>> postExactQueryDictLanguage(MsaRpcRequest<DictLanguageQueryParam> request);

	/**
	 * 根据字典类型模糊查询，其他条件为精确查询
	 * @param request	查询请求
	 * @return MsaRpcResponse<List<DictLanguageDTO>>
	 * <AUTHOR>
	 * date: 2023/7/24 14:47
	 */
	MsaRpcResponse<List<DictLanguageDTO>> fuzzQueryByType(MsaRpcRequest<DictLanguageQueryParam> request);

}
