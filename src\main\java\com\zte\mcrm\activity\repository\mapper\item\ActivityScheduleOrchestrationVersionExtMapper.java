package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleOrchestrationVersionExtMapper extends ActivityScheduleOrchestrationVersionMapper {
    /**
     * 获取展会资源编排最后一次版本
     * @param query
     * @return
     */
    ActivityScheduleOrchestrationVersionDO getLastScheduleOrchestrationVersion(ActivityScheduleOrchestrationVersionQuery query);

    List<ActivityScheduleOrchestrationVersionDO> queryActivityScheduleOrchestrationVersions(ActivityScheduleOrchestrationVersionQuery query);
}