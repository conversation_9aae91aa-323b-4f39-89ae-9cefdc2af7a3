package com.zte.mcrm.activity.application.export.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.mcrm.activity.application.export.ActivityExportAppService;
import com.zte.mcrm.activity.application.export.convert.ExportActivityConvertor;
import com.zte.mcrm.activity.application.export.param.ExportActivityParam;
import com.zte.mcrm.activity.application.model.ActivityExportDataSource;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.auth.CustomerIntegrationAuthModel;
import com.zte.mcrm.activity.common.config.CloudDiskConfig;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityExportDimensionEnum;
import com.zte.mcrm.activity.common.enums.activity.CommunicationWayEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemTypeEnum;
import com.zte.mcrm.activity.common.export.ExcelExportHelper;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.CopyUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.forwardmessage.ForwardMessageComponent;
import com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteKmCloudUdmCloudDisk.CloudDiskDownloadService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityEsSearchRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationProjectRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryApRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryIssueRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.common.dict.EmailAddressComponent;
import com.zte.mcrm.activity.service.common.lookup.CommunicationDirectorComponent;
import com.zte.mcrm.activity.service.common.lookup.model.CommunicationDirectorTwoLevelModel;
import com.zte.mcrm.activity.service.file.UploadService;
import com.zte.mcrm.activity.service.model.ByteUploadModel;
import com.zte.mcrm.activity.web.controller.export.business.ActivityDownLoadConvertBusiness;
import com.zte.mcrm.activity.web.controller.export.model.ActivityDownLoadVO;
import com.zte.mcrm.activity.web.controller.file.vo.FileInfoVO;
import com.zte.mcrm.activity.web.controller.search.vo.*;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.adapter.vo.SysGlobalConstVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.SLASH;
import static com.zte.mcrm.activity.common.constant.ExcelConstant.ACTIVITY_LIST_NAME;
import static com.zte.mcrm.activity.common.constant.NumberConstant.*;
import static com.zte.mcrm.activity.service.schedule.param.ScheduleOrchestrationMailParam.*;
import static com.zte.mcrm.common.util.DateUtil.getNowDateString;

/**
 * <AUTHOR>
 * @date 2023-08-07
 */
@Service
public class ActivityExportAppServiceImpl implements ActivityExportAppService {
    private static final Logger logger = LoggerFactory.getLogger(ActivityExportAppServiceImpl.class);

    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityCommunicationDirectionRepository activityCommunicationDirectionRepository;
    @Autowired
    private ActivityRelationProjectRepository activityRelationProjectRepository;
    @Autowired
    private ActivitySummaryRepository activitySummaryRepository;
    @Autowired
    private ActivitySummaryApRepository activitySummaryApRepository;
    @Autowired
    private ActivitySummaryIssueRepository activitySummaryIssueRepository;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private ExportActivityConvertor convertor;
    @Autowired
    private ActivityEsSearchRepository esSearchRepository;
    @Autowired
    private EmployeeAdapter employeeAdapter;
    @Autowired
    private UploadService uploadService;
    @Autowired
    private CloudDiskConfig cloudDiskConfig;
    @Autowired
    private CloudDiskDownloadService cloudDiskDownloadService;
    @Autowired
    private ForwardMessageComponent forwardMessageComponent;
    @Autowired
    private CommunicationDirectorComponent component;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private EmailAddressComponent emailAddressComponent;
    @Autowired
    private ActivityScheduleItemRepository activityScheduleItemRepository;
    @Autowired
    private ActivityScheduleItemPeopleRepository activityScheduleItemPeopleRepository;

    @Override
    public ByteArrayBody exportActivity(BizRequest<ExportActivityParam> req) {
        // 【1】查数据，打包成活动数据对象
        StandardActivityDetailDataSource ds = fetchActivityData(req);

        // 【2】转为excel导出模型
        SimpleExcelExportModel exportModel = convertor.toSimpleExcelExportModel(ds);

        try {
            // 【3】生成Excel文件
            return ExcelExportHelper.simpleExportExcel(exportModel, "activity");
        } catch (IOException e) {
            logger.error("客户活动数据导出异常", e);
        }
        return null;
    }

    /**
     * 导出活动列表数
     *
     * @param req
     * <AUTHOR>
     * @date 2024-01-25
     */
    @Override
    @Async
    public void exportActivityList(BizRequest<ActivityInfoQuery> req) {
        setSysGlobalConstVo(req);
        List<ActivityDownLoadVO> result = listDownLoad(req);
        SimpleExcelExportModel excelExportModel = ExportActivityConvertor.toSimpleExcel(result);
        try {
            ByteArrayBody resultByteData = ExcelExportHelper.simpleExportExcel(excelExportModel, ACTIVITY_LIST_NAME + getNowDateString());
            sendMail(req, resultByteData);
        } catch (Exception e) {
            logger.error("客户活动数据导出异常", e);
        }
    }

    /**
     * 异步设置token
     * @param req
     */
    private void setSysGlobalConstVo(BizRequest<ActivityInfoQuery> req){
        SysGlobalConstVo sysGlobalConstVo = new SysGlobalConstVo();
        sysGlobalConstVo.setXEmpNo(req.getEmpNo());
        sysGlobalConstVo.setXAuthValue(req.getToken());
        sysGlobalConstVo.setXLangId(req.getLangId());
        HeadersProperties.setSysGlobalInfo(sysGlobalConstVo);
    }

    /**
     * 发送邮件
     *
     * @param bizRequest
     * @param byteArrayBody
     * @throws Exception
     * <AUTHOR>
     * @date 2024-01-29
     */
    private void sendMail(BizRequest<ActivityInfoQuery> bizRequest, ByteArrayBody byteArrayBody) throws Exception {
        ZmailBodyParam zmailBodyParam = new ZmailBodyParam();
        //设置发件人
        zmailBodyParam.setSendEmpNo(emailAddressComponent.fetchDefaultEmailFromAddress());
        //设置收件人
        zmailBodyParam.setReceiveEmpNo(bizRequest.getEmpNo());
        //设置邮件模板
        zmailBodyParam.setTemplateName(TEMPLATE_NAME_MY_ACTIVITY_EXPORT);
        Map<String, Object> templateParamMap = new HashMap<>();
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();) {
            byteArrayBody.writeTo(out);
            ByteUploadModel uploadModel = new ByteUploadModel();
            uploadModel.setFileName(byteArrayBody.getFilename());
            uploadModel.setData(out.toByteArray());
            BizRequest<ByteUploadModel> uploadRequest = BizRequestUtil.copyRequest(bizRequest, c -> uploadModel);
            FileInfoVO fileInfoVO = uploadService.upload(uploadRequest);

            String token = fileInfoVO.getFileToken();
            String downloadToken = cloudDiskDownloadService.getDownloadFileKey(token, byteArrayBody.getFilename(), bizRequest.getEmpNo());
            String downloadUrl = cloudDiskConfig.getHost() + CLOUD_DISK_DOWNLOAD_URL + cloudDiskConfig.getXOriginServiceName() + SLASH + downloadToken;

            templateParamMap.put(DOWNLOAD_URL, downloadUrl);
            templateParamMap.put(ACTIVITY_TITLE, byteArrayBody.getFilename());
            zmailBodyParam.setTemplateParamMap(templateParamMap);
            MsaRpcRequest<ZmailBodyParam> zmailBodyParamMsaRpcRequest = MsaRpcRequestUtil.createWithBizReq(bizRequest, c -> zmailBodyParam);
            forwardMessageComponent.forwardMessageToZMail(zmailBodyParamMsaRpcRequest);
        } catch (Exception e) {
            logger.error("我的活动列表导出失败", e);
        }
    }

    /**
     * 获取下载数据
     *
     * @param req 请求参数
     * @return 返回结果
     * <AUTHOR>
     * @date 2024-01-29
     */
    private List<ActivityDownLoadVO> listDownLoad(BizRequest<ActivityInfoQuery> req) {
        List<ActivityDownLoadVO> result = Lists.newArrayList();
        CommunicationDirectorTwoLevelModel communicationModel = component.fetchActivityCommunicationDirectorTwoLevelModel();
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(req.getEmpNo());
        ActivityInfoQuery activityInfoQuery = req.getParam();
        activityInfoQuery.setAuthModel(authModel);
        activityInfoQuery.setPage(ONE, ONE_THOUSANDS);
        PageRows<ActivityInfoDO> pageRows = esSearchRepository.searchPageDownLoad(activityInfoQuery);
        List<ActivitySearchInfoVO> listSearchInfo = listActivityData(pageRows.getRows());
        result.addAll(handleActivitySearch(listSearchInfo, communicationModel, activityInfoQuery.getExportDimension()));
        int index = 0;
        while (BooleanUtils.and(new boolean[]{CollectionUtils.isNotEmpty(listSearchInfo), index < MAX_PAGE_SIZE})) {
            index++;
            List<ActivityInfoDO> listActivityInfoDO = pageRows.getRows();
            listActivityInfoDO = listActivityInfoDO.stream().filter( e -> StringUtils.isNotBlank(e.getUniqueSortKeyDownId())).collect(Collectors.toList());
            ActivityInfoDO activityInfoDO=  Collections.max(listActivityInfoDO, Comparator.comparing(ActivityInfoDO::getUniqueSortKeyDownId));
            activityInfoQuery.setUniqueSortKeyDownId(activityInfoDO.getUniqueSortKeyDownId());
            pageRows = esSearchRepository.searchPageDownLoad(activityInfoQuery);
            listSearchInfo = listActivityData(pageRows.getRows());
            result.addAll(handleActivitySearch(listSearchInfo, communicationModel, activityInfoQuery.getExportDimension()));
        }
        handleEmpNoAndName(result);
        return result;
    }

    /**
     * 补充导出信息
     *
     * @param listSearchActivity 查询条件
     * @return 返回结果
     * <AUTHOR>
     * @date 2024-08-13
     */
    protected List<ActivitySearchInfoVO> listActivityData(List<ActivityInfoDO> listSearchActivity) {
        if (CollectionUtils.isEmpty(listSearchActivity)) {
            return Lists.newArrayList();
        }
        Set<String> activityRowIds = listSearchActivity.stream().map(item -> item.getRowId()).collect(Collectors.toSet());
        // 查询项目信息
        Map<String, List<ActivityRelationProjectDO>> projectMap = activityRelationProjectRepository.queryAllByActivityRowId(Lists.newArrayList(activityRowIds));
        // 查询客户信息
        Map<String, List<ActivityCustomerInfoDO>> customerMap = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(activityRowIds);
        // 查询客户联系人
        Map<String, List<ActivityRelationCustPeopleDO>> custPeopleMap = activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(activityRowIds);
        // 查询中兴联系人
        Map<String, List<ActivityRelationZtePeopleDO>> ztePeopleMap = activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(activityRowIds);
        // 交流方向
        Map<String, List<ActivityCommunicationDirectionDO>> directionMap = activityCommunicationDirectionRepository.queryAllByActivityRowId(Lists.newArrayList(activityRowIds));
        //组装数据
        List<ActivitySearchInfoVO> listResult = Lists.newArrayList();
        for (ActivityInfoDO activityInfoDO : listSearchActivity) {
            ActivitySearchInfoVO searchInfoVO = new ActivitySearchInfoVO();
            String activityRowId = activityInfoDO.getRowId();
            searchInfoVO.setRowId(activityRowId);
            searchInfoVO.setBaseCondition(handldBaseInfo(activityInfoDO, projectMap.get(activityRowId)));
            searchInfoVO.setListCustInfo(CopyUtil.copyList(customerMap.get(activityRowId), CustUnitIsearchVO.class));
            searchInfoVO.setListContacts(CopyUtil.copyList(custPeopleMap.get(activityRowId), ContactsIsearchVO.class));
            searchInfoVO.setListZtePeople(CopyUtil.copyList(ztePeopleMap.get(activityRowId), ZtePeopleIsearchVO.class));
            searchInfoVO.setListCommunicateDirection(handldCommunication(directionMap.get(activityRowId)));
            listResult.add(searchInfoVO);
        }
        return listResult;
    }

    /**
     * 补充活动基本信息
     * @param activityInfoDO
     * @return
     */
    private ActivitySearchBaseConditionVO handldBaseInfo(ActivityInfoDO activityInfoDO, List<ActivityRelationProjectDO> listProject){
        ActivitySearchBaseConditionVO baseConditionVO = new ActivitySearchBaseConditionVO();
        BeanUtils.copyProperties(activityInfoDO, baseConditionVO);
        baseConditionVO.setCommunicationWayName(CommunicationWayEnum.getDescByCode(activityInfoDO.getCommunicationWay()));
        baseConditionVO.setCountryName(activityInfoDO.getCountryCodeName());
        baseConditionVO.setCityName(activityInfoDO.getCityCodeName());
        if(CollectionUtils.isNotEmpty(listProject)){
            ActivityRelationProjectDO projectDO = listProject.get(ZERO);
            baseConditionVO.setProjectCode(projectDO.getProjectCode());
            baseConditionVO.setProjectName(projectDO.getProjectName());
        }
        return baseConditionVO;
    }
    /**
     * 解决交流方向问题
     *
     * @param listCommunication 交流方向
     * @return 返回结果
     */
    private List<CommunicateDirectionIsearchVO>  handldCommunication(List<ActivityCommunicationDirectionDO> listCommunication){
        if(CollectionUtils.isEmpty(listCommunication)){
            return Lists.newArrayList();
        }
        List<CommunicateDirectionIsearchVO> listCommunicateDirection = Lists.newArrayList();
        for(ActivityCommunicationDirectionDO communicationDirectionDO : listCommunication){
            CommunicateDirectionIsearchVO isearchVO = new CommunicateDirectionIsearchVO();
            isearchVO.setRowId(communicationDirectionDO.getRowId());
            isearchVO.setActivityRowId(communicationDirectionDO.getActivityRowId());
            isearchVO.setCommunicateDirection(communicationDirectionDO.getCommunicationDirection());
            isearchVO.setEnabledFlag(communicationDirectionDO.getEnabledFlag());
            listCommunicateDirection.add(isearchVO);
        }
        return listCommunicateDirection;
    }

    /**
     * 获取申请人姓名
     *
     * @param result 需要处理的参数
     * <AUTHOR>
     * @date 2024-01-29
     */
    private void handleEmpNoAndName(List<ActivityDownLoadVO> result) {
        Set<String> peopleName = result.stream().map(ActivityDownLoadVO::getApplyPeopleName).collect(Collectors.toSet());
        List<List<String>> peopleList = Lists.partition(Lists.newArrayList(peopleName), NumberConstant.HUNDRED);
        Map<String, String> hrMap = Maps.newHashMap();
        for(List<String> subList:peopleList){
            Map<String, String> subMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(Sets.newHashSet(subList))).getBo();
            hrMap.putAll(subMap);
        }
        for (ActivityDownLoadVO downLoadVO : result) {
            downLoadVO.setApplyPeopleName(hrMap.get(downLoadVO.getApplyPeopleName()) + downLoadVO.getApplyPeopleName());
        }
    }

    /**
     * 处理活动数据
     *
     * @param listSearchInfo 列表数据
     * @param levelModel
     * @param exportDimension 导出维度
     * @return
     * <AUTHOR>
     * @date 2024-01-29
     */
    private List<ActivityDownLoadVO> handleActivitySearch(List<ActivitySearchInfoVO> listSearchInfo, CommunicationDirectorTwoLevelModel levelModel, String exportDimension) {
        List<ActivityDownLoadVO> result = Lists.newArrayList();
        ActivityExportDataSource dataSource = initializeActivityExportDataSource(listSearchInfo, exportDimension);
        Optional.ofNullable(dataSource).ifPresent(ds -> {
            for (ActivitySearchInfoVO searchInfoVO : listSearchInfo) {
                String activityRowId = searchInfoVO.getRowId();
                List<ActivityScheduleItemDO> scheduleItemList = ds.fetchScheduleItemList(activityRowId);
                if (CollectionUtils.isEmpty(scheduleItemList)) {
                    result.add(handleExportDataByActivity(levelModel, ds, activityRowId));
                } else {
                    result.addAll(handleExportDataBySchedule(levelModel, scheduleItemList, ds, activityRowId));
                }
            }
        });
        return result;
    }

    private ActivityExportDataSource initializeActivityExportDataSource(List<ActivitySearchInfoVO> listSearchInfo, String exportDimension) {
        ActivityExportDataSource dataSource = new ActivityExportDataSource();
        Map<String, OrgInfoVO> orgMap = getOrgInfoMap(listSearchInfo);
        hanldOrgMap(listSearchInfo, orgMap);
        Map<String, ActivitySearchBaseConditionVO> baseConditionMap = listSearchInfo.stream().collect(Collectors.toMap(ActivitySearchInfoVO::getRowId, ActivitySearchInfoVO::getBaseCondition));
        Map<String, List<CommunicateDirectionIsearchVO>> communicateDirectionMap = listSearchInfo.stream()
                .collect(Collectors.toMap(ActivitySearchInfoVO::getRowId, vo -> Optional.ofNullable(vo.getListCommunicateDirection()).orElse(Collections.emptyList())));
        Map<String, List<CustUnitIsearchVO>> custUnitMap = listSearchInfo.stream()
                .collect(Collectors.toMap(ActivitySearchInfoVO::getRowId, vo -> Optional.ofNullable(vo.getListCustInfo()).orElse(Collections.emptyList())));
        Map<String, List<ContactsIsearchVO>> contactMap = listSearchInfo.stream()
                .collect(Collectors.toMap(ActivitySearchInfoVO::getRowId, vo -> Optional.ofNullable(vo.getListContacts()).orElse(Collections.emptyList())));
        Map<String, List<ZtePeopleIsearchVO>> ztePeopleMap = listSearchInfo.stream()
                .collect(Collectors.toMap(ActivitySearchInfoVO::getRowId, vo -> Optional.ofNullable(vo.getListZtePeople()).orElse(Collections.emptyList())));

        if (ActivityExportDimensionEnum.SCHEDULE.isMe(exportDimension)) {
            List<String> activityRowIds = listSearchInfo.stream().map(ActivitySearchInfoVO::getRowId).collect(Collectors.toList());
            Map<String, List<ActivityScheduleItemDO>> relationScheduleInfoMap = activityScheduleItemRepository.getRelationScheduleInfoIds(activityRowIds);
            if (MapUtils.isNotEmpty(relationScheduleInfoMap)) {
                dataSource.setScheduleInfoMap(relationScheduleInfoMap);
                List<String> activityScheduleItemRowIds = relationScheduleInfoMap.values().stream().flatMap(List::stream)
                        .map(ActivityScheduleItemDO::getRowId).distinct().collect(Collectors.toList());
                Map<String, List<ActivityScheduleItemPeopleDO>> relationSchedulePeopleInfoIds = activityScheduleItemPeopleRepository.getRelationSchedulePeopleInfoIds(activityScheduleItemRowIds);
                dataSource.setRelationSchedulePeopleInfoMap(relationSchedulePeopleInfoIds);
            }
        }
        dataSource.setOrgMap(orgMap);
        dataSource.setBaseConditionMap(baseConditionMap);
        dataSource.setCommunicateDirectionMap(communicateDirectionMap);
        dataSource.setCustUnitMap(custUnitMap);
        dataSource.setContactMap(contactMap);
        dataSource.setActivityRelationZtePeopleMap(ztePeopleMap);
        return dataSource;
    }

    /**
     * 解决业务归属部门全路径问题
     * @param listSearchInfo
     * @param orgMap
     */
    private void hanldOrgMap(List<ActivitySearchInfoVO> listSearchInfo,  Map<String, OrgInfoVO> orgMap){
        for(ActivitySearchInfoVO searchInfoVO:listSearchInfo){
            ActivitySearchBaseConditionVO baseCondition =  searchInfoVO.getBaseCondition();
            baseCondition.setApplyDepartmentFullName(Optional.ofNullable(orgMap.get(baseCondition.getApplyDepartmentNo())).orElse(new OrgInfoVO()).getHrOrgNamePath());
        }
    }

    private Map<String, OrgInfoVO> getOrgInfoMap(List<ActivitySearchInfoVO> listSearchInfo) {
        Map<String, OrgInfoVO> orgMap = new HashMap<>(1024);

        // 添加客户的orgId
        List<String> orgIds = listSearchInfo.stream()
                .flatMap(info -> Stream.concat(
                        Stream.of(info.getBaseCondition())
                                .map(ActivitySearchBaseConditionVO::getApplyDepartmentNo)
                                .filter(StringUtils::isNotBlank),
                        info.getListCustInfo() == null ? Stream.empty() :
                                info.getListCustInfo().stream().map(CustUnitIsearchVO::getBelongBuId)
                ))
                .distinct()
                .collect(Collectors.toList());

        convertor.mergeDepartmentInfo(orgIds, orgMap);
        return orgMap;
    }

    private static ActivityDownLoadVO handleExportDataByActivity(CommunicationDirectorTwoLevelModel levelModel, ActivityExportDataSource dataSource, String activityRowId) {
        ActivityDownLoadVO downLoadVO = new ActivityDownLoadVO();
        Optional.ofNullable(dataSource).ifPresent(ds -> {
            ActivityDownLoadConvertBusiness.getActivityBaseInfo(downLoadVO, ds.fetchBaseCondition(activityRowId));
            ActivityDownLoadConvertBusiness.getActivityCommunication(downLoadVO, ds.fetchCommunicateDirection(activityRowId), levelModel);
            ActivityDownLoadConvertBusiness.getActivityCustomer(downLoadVO, ds.fetchCustInfoList(activityRowId), ds.fetchContactPeopleList(activityRowId), ds.getOrgMap());
            ActivityDownLoadConvertBusiness.getZtePeople(downLoadVO, ds.fetchZtePeopleList(activityRowId));
        });
        return downLoadVO;
    }

    private List<ActivityDownLoadVO> handleExportDataBySchedule(CommunicationDirectorTwoLevelModel levelModel, List<ActivityScheduleItemDO> scheduleItemList, ActivityExportDataSource dataSource, String activityRowId) {
        List<ActivityDownLoadVO> downLoadList = Lists.newArrayList();
        for (ActivityScheduleItemDO scheduleItem : scheduleItemList) {
            ActivityDownLoadVO downLoadVO = new ActivityDownLoadVO();
            Optional.ofNullable(dataSource.fetchBaseCondition(activityRowId)).ifPresent(baseCondition -> {
                baseCondition.setStartTime(scheduleItem.getScheduleDate());
                baseCondition.setEndTime(scheduleItem.getScheduleDate());
                ActivityDownLoadConvertBusiness.getActivityBaseInfo(downLoadVO, baseCondition);
            });
            ActivityDownLoadConvertBusiness.getActivityCommunication(downLoadVO, dataSource.fetchCommunicateDirection(activityRowId), levelModel);
            // 日程类型
            downLoadVO.setScheduleItemType(ScheduleItemTypeEnum.getDescByType(scheduleItem.getScheduleItemType()));
            // 根据日程查询日程关联的客户联系人
            List<ActivityScheduleItemPeopleDO> activityScheduleItemPeopleList = dataSource.fetchScheduleItemPeopleList(scheduleItem.getRowId());
            List<String> peopleNoList = Optional.ofNullable(activityScheduleItemPeopleList).orElse(Collections.emptyList())
                    .stream().map(ActivityScheduleItemPeopleDO::getPeopleNo).collect(Collectors.toList());
            Set<CustUnitIsearchVO> custInfoSet = new HashSet<>();
            Set<ContactsIsearchVO> contactSet = new HashSet<>();
            Set<ZtePeopleIsearchVO> ztePeopleIsearchSet = new HashSet<>();

            peopleNoList.forEach(peopleNo -> {
                fetchAndAddContactInfo(activityRowId, peopleNo, custInfoSet, contactSet, dataSource);
                fetchAndAddZtePeopleInfo(activityRowId, peopleNo, ztePeopleIsearchSet, dataSource);
            });


            ActivityDownLoadConvertBusiness.getActivityCustomer(downLoadVO, new ArrayList<>(custInfoSet), new ArrayList<>(contactSet), dataSource.getOrgMap());
            ActivityDownLoadConvertBusiness.getZtePeople(downLoadVO, new ArrayList<>(ztePeopleIsearchSet));
            downLoadList.add(downLoadVO);
        }
        return downLoadList;
    }

    private void fetchAndAddContactInfo(String activityRowId, String peopleNo, Set<CustUnitIsearchVO> custInfoSet, Set<ContactsIsearchVO> contactSet, ActivityExportDataSource dataSource) {
        Optional.ofNullable(dataSource.fetchContactPeople(activityRowId, peopleNo)).ifPresent(contactsIsearchVO -> {
            custInfoSet.add(dataSource.fetchCustInfo(activityRowId, contactsIsearchVO.getCustomerCode()));
            contactSet.add(contactsIsearchVO);
        });
    }

    private void fetchAndAddZtePeopleInfo(String activityRowId, String peopleNo, Set<ZtePeopleIsearchVO> ztePeopleIsearchSet, ActivityExportDataSource dataSource) {
        Optional.ofNullable(dataSource.fetchZtePeople(activityRowId, peopleNo)).ifPresent(ztePeopleIsearchSet::add);
    }

    /**
     * 获取所有需要导出的客户活动信息
     *
     * @param req
     * @return
     */
    private StandardActivityDetailDataSource fetchActivityData(BizRequest<ExportActivityParam> req) {
        StandardActivityDetailDataSource ds = new StandardActivityDetailDataSource();
        ActivityInfoQuery query = new ActivityInfoQuery();
        query.setActivityStatus(req.getParam().getStatusList());
        List<ActivityInfoDO> activityList = activityInfoRepository.searchByParam(query);
        if (CollectionUtils.isEmpty(activityList)) {
            return ds;
        }

        List<String> activityRowIdList = activityList.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());
        Set<String> activityRowIdSet = new HashSet<>(activityRowIdList);

        ds.setActivityInfoList(activityList);
        ds.setDirectionMap(activityCommunicationDirectionRepository.queryAllByActivityRowId(activityRowIdList));
        ds.setProjectMap(activityRelationProjectRepository.queryAllByActivityRowId(activityRowIdList));
        ds.setSummaryMap(activitySummaryRepository.queryAllByActivityRowId(activityRowIdList));
        ds.setSummaryApMap(activitySummaryApRepository.queryAllByActivityRowId(activityRowIdList));
        ds.setSummaryIssueMap(activitySummaryIssueRepository.queryAllByActivityRowId(activityRowIdList));
        ds.setZtePeopleMap(activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(activityRowIdSet));
        ds.setCustPeopleMap(activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(activityRowIdSet));
        ds.setCustomerInfoMap(activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(activityRowIdSet));
        return ds;
    }

}
