package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationParam;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationSynNoticeParam;

/**
 * 展会中资源编排应用服务
 *
 * <AUTHOR>
 */
public interface ScheduleOrchestrationAppService {

    /**
     * 资源日程编排处理
     *
     * @param req 参数
     * @return 返回本次编排版本号
     */
    BizResult<String> doScheduleOrchestration(BizRequest<ScheduleOrchestrationParam> req);

    /**
     * 同步日程并发送邮件
     * @param req
     * @return
     */
    BizResult<Void> doSynScheduleOrchestrationAndNotice(BizRequest<ScheduleOrchestrationSynNoticeParam> req);
}
