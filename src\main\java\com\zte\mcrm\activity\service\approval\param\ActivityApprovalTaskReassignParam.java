package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.expansion.access.vo.TaskReassignInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName ActivityApprovalTaskReassignDTO
 * @description: 审批转交对象
 * @author: 李龙10317843
 * @create: 2023-05-17 09:22
 * @Version 1.0
 **/
@Data
public class ActivityApprovalTaskReassignParam {
    @ApiModelProperty("主键id")
    private String rowId;

    @ApiModelProperty("活动id")
    private String activityRowId;
    /**
     * 审批单号-对应审批中心 taskId
     */
    @NotNull
    @ApiModelProperty("单据号")
    private String approvalFlowNo;

    @ApiModelProperty("新单据号")
    private String newApprovalFlowNo;
    /**
     * 审批人
     */
    @NotNull
    @ApiModelProperty("转交人工号：如10317843")
    private String approveBy;
    /**
     * 如果是转交过来的，则该有值，转交来源id（本表的row_id）
     */
    @ApiModelProperty("前一节点id")
    private String transFrom;

    @NotNull
    @ApiModelProperty("转交意见")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("最后更新人")
    private String lastUpdatedBy;

    @ApiModelProperty("节点扩展编码")
    private String extendedCode;

    public static ActivityApprovalTaskReassignParam buildOf(TaskReassignInfoVO param) {
        ActivityApprovalTaskReassignParam data = new ActivityApprovalTaskReassignParam();
        data.setActivityRowId(param.getBusinessId());
        data.setApprovalFlowNo(param.getTaskId());
        data.setNewApprovalFlowNo(param.getNewTaskId());
        data.setApproveBy(param.getTaskReceiver());
        data.setRemark(param.getOpinion());
        data.setCreatedBy(param.getHandler());
        data.setLastUpdatedBy(param.getHandler());
        data.setExtendedCode(param.getExtendedCode());
        return data;
    }
}
