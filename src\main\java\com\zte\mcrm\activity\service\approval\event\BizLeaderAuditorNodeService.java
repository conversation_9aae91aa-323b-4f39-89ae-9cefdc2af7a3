package com.zte.mcrm.activity.service.approval.event;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityStatusLifecycleRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.service.approval.event.helper.ApprovalHelper;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalProcessAddParam;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalProcessCompleteParam;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalProcessNodeAddParam;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalProcessNodeCompleteParam;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessNodeConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityStatusLifecycleConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class BizLeaderAuditorNodeService extends AbstractApprovalNodeService {

    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;
    @Autowired
    private UserCenterService userCenterService;

    @Override
    String getCurrentActivityStatus() {
        return ActivityStatusEnum.BUSINESS_APPROVAL.getCode();
    }

    @Override
    String getNodeType() {
        return PendingBizTypeEnum.APPROVAL_LEADER.getType();
    }

    @Override
    String getApprovalCompleteStatus(String approvalResult) {
        return BooleanEnum.Y.getCode().equals(approvalResult) ? ActivityStatusEnum.PROGRESS.getCode()
                : ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS.getCode();
    }

    /**
     * 节点创建-kafka回调
     *
     * @param bizRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nodeCreateOfKafka(BizRequest<ActivityApprovalProcessAddParam> bizRequest) {
        ActivityApprovalProcessAddParam processAddParam = bizRequest.getParam();
        ValidationUtils.validateObject(processAddParam);
        ActivityApprovalProcessNodeAddParam processNodeAddParam = processAddParam.getProcessNodeAddParam();
        ValidationUtils.validateObject(processNodeAddParam);
        EmployeeInfoDTO approve = userCenterService.getUserInfo(processNodeAddParam.getApproveBy());
        if (null == approve) {
            throw new RuntimeException("this data can not be null!");
        }

        ApprovalHelper approvalHelper = new ApprovalHelper();
        approvalHelper.setApprove(approve);
        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(processNodeAddParam.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityApprovalInfoDO.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        // 首先查询process信息
        List<ActivityApprovalProcessDO> approvalProcessDOList =
                approvalProcessRepository.queryByActivityRowIdAndProcessType(activityApprovalInfoDO.getActivityRowId(), ApprovalTypeEnum.LEADER_AUDITOR.getCode());
        Optional<ActivityApprovalProcessDO> approvalProcessDOOptional = approvalProcessDOList.stream()
                .filter(item -> Objects.equals(activityApprovalInfoDO.getRowId(), item.getActivityApprovalInfoRowId())
                        && ProcessStatusEnum.isActiveOrDefault(item.getProcessStatus())).findFirst();
        if (!approvalProcessDOOptional.isPresent()) {
            throw new RuntimeException("this data can not be null!");
        }
        ActivityApprovalProcessDO approvalProcessDO = approvalProcessDOOptional.get();
        // 查询是否初次进入业务审批
        boolean firstBizApprovalFlag = ProcessStatusEnum.DEFAULT.isMe(approvalProcessDO.getProcessStatus());
        approvalProcessDO.setProcessStatus(ProcessStatusEnum.ACTIVE.getCode());
        approvalProcessDO.setLastUpdatedBy(processAddParam.getLastUpdatedBy());

        // 业务审批节点更新 (注意 processAddParam的processType为流程图的extendedCode)
        List<ActivityApprovalProcessNodeDO> approvalProcessNodeDOList =
                approvalProcessNodeRepository.queryByActIdAndNodeType(activityInfoDO.getRowId(), processAddParam.getProcessType());
        Optional<ActivityApprovalProcessNodeDO> currentApprovalProcessNodeOptional = approvalProcessNodeDOList.stream()
                .filter(item -> Objects.equals(approvalProcessDO.getRowId(), item.getApprovalProcessRowId())
                        && ProcessStatusEnum.DEFAULT.isMe(item.getNodeStatus())).findFirst();
        if (!currentApprovalProcessNodeOptional.isPresent()) {
            throw new RuntimeException("this data can not be null!");
        }

        ActivityApprovalProcessNodeDO currentNode = currentApprovalProcessNodeOptional.get();
        currentNode.setApprovalFlowNo(processNodeAddParam.getApprovalFlowNo());
        currentNode.setNodeStatus(ApproveNodeStatusEnum.ACTIVE.getCode());
        currentNode.setLastUpdatedBy(processNodeAddParam.getLastUpdatedBy());

        // 如果是初次进入业务审批，更新主表状态数据、插入生命周期
        if (firstBizApprovalFlag) {
            // 插入生命周期
            ActivityStatusLifecycleDO activityStatusLifecycleDO = ActivityStatusLifecycleConverter.buildOfAdd(activityInfoDO, getCurrentActivityStatus(),
                    currentNode.getApproveBy());
            lifecycleRepository.insertSelective(Collections.singletonList(activityStatusLifecycleDO));
        }

        approvalHelper.setApprovalProcessAdd(approvalProcessDO);
        approvalHelper.setApprovalProcessNodeAdd(currentNode);
        approvalHelper.setActivityInfo(activityInfoDO);
        approvalProcessRepository.updateByPrimaryKeySelective(approvalProcessDO);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(currentNode);
        //新增待办
        addPendingNotice(approvalHelper);

        super.sync2Es(activityInfoDO.getRowId());
    }

    /**
     * 审批节点完成-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nodeCompleteOfKafka(BizRequest<ActivityApprovalProcessNodeCompleteParam> bizRequest) {
        // 校验数据
        ActivityApprovalProcessNodeCompleteParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);

        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(param.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityApprovalInfoDO.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        if (!ActivityStatusEnum.BUSINESS_APPROVAL.isMe(activityInfoDO.getActivityStatus())) {
            throw new RuntimeException("activity status is error!");
        }
        // 获取节点详情数据
        ActivityApprovalProcessNodeDO processNodeDO = approvalProcessNodeRepository.queryByActIdAndApprovalFlowNo(activityApprovalInfoDO.getActivityRowId(), param.getApprovalFlowNo());
        AssertUtil.assertNotNull(processNodeDO);
        Assert.isTrue(ApproveNodeStatusEnum.ACTIVE.isMe(processNodeDO.getNodeStatus()), "activity status not active");

        ActivityApprovalProcessDO processDO = approvalProcessRepository.selectByPrimaryKey(processNodeDO.getApprovalProcessRowId());
        AssertUtil.assertNotNull(processDO);
        // 更新节点详情数据
        EmployeeInfoDTO approve = userCenterService.getUserInfo(param.getApproveBy());
        AssertUtil.assertNotNull(approve);
        ActivityApprovalProcessNodeDO processNodeUpdate = ActivityApprovalProcessNodeConverter.buildOfComplete(processDO, param);
        processNodeUpdate.setNodeStatus(ApproveNodeStatusEnum.COMPLETED.getCode());
        processNodeUpdate.setApproverName(approve.getEmpName());
        processNodeUpdate.setRowId(processNodeDO.getRowId());

        approvalProcessNodeRepository.updateByPrimaryKeySelective(processNodeUpdate);
        pendingNoticeRepository.updateStatusByBusinessId(param.getApprovalFlowNo(), PendingNoticeStatusEnum.FINISH.getStatus());

        super.sync2Es(activityInfoDO.getRowId());
    }

    /**
     * 审批全流程完成-kafka回调
     *
     * @param bizRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOfKafka(BizRequest<ActivityApprovalProcessCompleteParam> bizRequest) {
        ActivityApprovalProcessCompleteParam param = bizRequest.getParam();

        // 现根据审批businessId 查询审批的活动ID
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByApprovalNo(param.getActivityRowId());
        AssertUtil.assertNotNull(activityApprovalInfoDO);

        param.setActivityRowId(activityApprovalInfoDO.getActivityRowId());
        bizRequest.setParam(param);

        // 之前逻辑放在节点完成的kafka里实现，样板点审批是并行场景，会有问题，因此对代码进行了调整，在流程结束的kafka里处理
        updateApprovalProcess(activityApprovalInfoDO, param);
        updateActivityInfo(param);
        // 流程结束，关闭其他并行审批人待办
        List<ActivityPendingNoticeDO> activityPendingNoticeList = pendingNoticeRepository.queryAllPending(param.getActivityRowId(), PendingBizTypeEnum.APPROVAL_LEADER, PendingNoticeStatusEnum.WAIT_DEAL);
        activityPendingNoticeList.forEach(pendingNotice -> {
            pendingNoticeRepository.updateStatusByBusinessId(pendingNotice.getBusinessId(), PendingNoticeStatusEnum.FINISH.getStatus());
        });
        super.completeOfKafka(bizRequest);

    }

    private void updateApprovalProcess(ActivityApprovalInfoDO activityApprovalInfoDO, ActivityApprovalProcessCompleteParam param) {
        List<ActivityApprovalProcessDO> processList = approvalProcessRepository.queryByActivityRowIdAndProcessType(activityApprovalInfoDO.getActivityRowId(), ApprovalTypeEnum.LEADER_AUDITOR.getCode());
        AssertUtil.assertNotEmpty(processList);
        processList.forEach(process -> {
            ActivityApprovalProcessDO approvalProcessUpdate = ActivityApprovalProcessConverter
                    .buildOfComplete(process.getRowId(), param.getApproveBy());
            approvalProcessRepository.updateByPrimaryKeySelective(approvalProcessUpdate);
        });
    }

    private void updateActivityInfo(ActivityApprovalProcessCompleteParam param) {
        String newStatus = BooleanEnum.Y.getCode().equals(param.getApproveResult()) ? ActivityStatusEnum.PROGRESS.getCode()
                : ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS.getCode();
        // 填充活动状态
        ActivityInfoDO activityInfoUpdate = ActivityInfoConverter.buildOfUpdateStatus(param.getActivityRowId(),
                newStatus, param.getApproveBy());

        activityInfoRepository.updateByPrimaryKeySelective(activityInfoUpdate);
    }
}
