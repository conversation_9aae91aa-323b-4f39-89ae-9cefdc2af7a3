package com.zte.mcrm.activity.service.ai.impl;

import com.zte.mcrm.activity.common.enums.ai.BusinessTypeEnum;
import com.zte.mcrm.activity.integration.ai.AiRobotStudioService;
import com.zte.mcrm.activity.service.ai.AiApplicationStrategy;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationVO;
import com.zte.mcrm.activity.web.controller.ai.vo.IcenterDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: 汤踊10285568
 * @date: 2024/8/1 11:23
 */
@Service
public class AiApplicationRobotStrategy implements AiApplicationStrategy {

    @Autowired
    private AiRobotStudioService studioService;

    /**
     * 判断是否是该应用策略
     *
     * @param businessType
     * @return
     */
    @Override
    public boolean support(String businessType) {
        if (BusinessTypeEnum.ROBOT.isMe(businessType)) {
            return true;
        }
        return false;
    }

    /**
     * 调用studio应用获取解析结果
     *
     * @param req
     * @return
     */
    @Override
    public String processBusiness(AiApplicationVO req) {
        IcenterDataVO msgData = new IcenterDataVO();
        msgData.setMsgBody(req.getContent());
        String aiResult = studioService.getAiRobotResult(msgData);
        return aiResult;
    }
}
