package com.zte.mcrm.activity.application.cto.helper;

import com.alibaba.fastjson.JSONArray;
import com.zte.mcrm.activity.common.constant.ActivityConstant;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.forwardmessage.ForwardMessageComponent;
import com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam;
import com.zte.mcrm.activity.service.common.dict.EmailAddressComponent;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import com.zte.mcrm.customvisit.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * {@code @description 发送邮件辅助类}
 *
 * <AUTHOR>
 * @date 2024/12/23 下午6:46
 */
@Component
public class CtoPlanSendEmailHelper {
    @Autowired
    private ForwardMessageComponent forwardMessageComponent;
    @Autowired
    private EmailAddressComponent emailAddressComponent;

    /**
     * 发送邮件
     *
     * @param req         入参
     * @param receivers   接收人,json结构
     * @param scopeEnd    周期截止日期
     * @param downloadUrl 文件下载地址
     * @return
     */
    public void doSend(BizRequest<CtoPlanExportParam> req, String receivers, Date scopeEnd, String downloadUrl) {
        CtoPlanExportParam param = req.getParam();
        // 初始化邮件消息体
        ZmailBodyParam zmailBodyParam = new ZmailBodyParam();
        String month = DateUtils.convertDateToString(scopeEnd, "MM");

        // 设置模版参数
        Map<String, Object> templateParamMap = new HashMap<>(8);
        templateParamMap.put(ActivityConstant.CTO_MONTH_REPORT_PLAN_NAME, param.getPlanName());
        templateParamMap.put(ActivityConstant.CTO_MONTH_REPORT_MONTH_DESC, month);
        templateParamMap.put(ActivityConstant.FILE_NAME, month + ActivityConstant.CTO_MONTH_REPORT_EXEC_DOWNLOAD);
        templateParamMap.put(ActivityConstant.ATTACHMENT_URL, downloadUrl);

        zmailBodyParam.setTemplateParamMap(templateParamMap);

        // 设置发件人
        zmailBodyParam.setSendEmpNo(emailAddressComponent.fetchDefaultEmailFromAddress());
        // 设置收件人，使用逗号分隔
        List<String> receiverList = JSONArray.parseArray(receivers, String.class);
        String receiverStr = String.join(",", receiverList);
        zmailBodyParam.setReceiveEmpNo(receiverStr);
        // 设置模版名称
        zmailBodyParam.setTemplateName(ActivityConstant.CTO_MONTH_REPORT_TEMPLATE_NAME);

        // 创建并返回MSA RPC请求
        MsaRpcRequest<ZmailBodyParam> msaRpcRequest = MsaRpcRequestUtil.createWithBizReq(req, c -> zmailBodyParam);
        forwardMessageComponent.forwardMessageToZMail(msaRpcRequest);
    }

}
