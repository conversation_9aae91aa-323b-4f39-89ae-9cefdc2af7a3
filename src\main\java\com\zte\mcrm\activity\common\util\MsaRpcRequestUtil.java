package com.zte.mcrm.activity.common.util;


import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.adapter.common.HeadersProperties;

import java.util.function.Function;

/**
 * MsaRpcRequest工具类（主要用于快捷打包登录会话的header信息）
 *
 * <AUTHOR>
 */
public class MsaRpcRequestUtil {

    /**
     * 安全模式创建请求
     *
     * @param param
     * @param <T>
     * @return
     */
    public static <T> MsaRpcRequest<T> createWithCurrentUserSecurity(T param) {
        return createWithBizReq(BizRequestUtil.createWithCurrentUserSecurity(param));
    }

    /**
     * 根据当前登录会话信息创建请求对象
     *
     * @param <T>
     * @return
     */
    public static <T> MsaRpcRequest<T> createWithCurrentUser() {
        return createWithCurrentUser(null);
    }

    /**
     * 根据当前登录会话信息创建请求对象
     *
     * @param paramBody
     * @param <T>
     * @return
     */
    public static <T> MsaRpcRequest<T> createWithCurrentUser(T paramBody) {
        MsaRpcRequest<T> req = new MsaRpcRequest<>();
        req.setBody(paramBody);

        req.addHeader(RequestHeaderConstant.X_AUTH_VALUE, HeadersProperties.getXAuthValue())
                .addHeader(RequestHeaderConstant.X_EMP_NO, HeadersProperties.getXEmpNo())
                .addHeader(RequestHeaderConstant.X_LANG_ID, HeadersProperties.getXLangId());

        return req;
    }

    /**
     * 根据业务对象创建RPC请求
     *
     * @param bizReq 业务请求
     * @param <T>
     * @return
     */
    public static <T> MsaRpcRequest<T> createWithBizReq(BizRequest<T> bizReq) {
        return createWithBizReq(bizReq, Function.identity());
    }

    /**
     * 根据业务对象创建RPC请求
     *
     * @param bizReq  业务请求
     * @param convert 数据参数转换其
     * @param <T>
     * @return
     */
    public static <F, T> MsaRpcRequest<T> createWithBizReq(BizRequest<F> bizReq, Function<F, T> convert) {
        MsaRpcRequest<T> req = new MsaRpcRequest<>();
        if (bizReq.getParam() != null && convert != null) {
            req.setBody(convert.apply(bizReq.getParam()));
        }

        req.addAllHeader(bizReq.fetchHeaderMap());

        return req;
    }

    /**
     * 参数转换
     *
     * @param oriReq  源参数
     * @param convert body转换器
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> MsaRpcRequest<T> trans(MsaRpcRequest<F> oriReq, Function<F, T> convert) {

        return new MsaRpcRequest<>(oriReq.fetchHeaderMap(), convert == null ? null : convert.apply(oriReq.getBody()));
    }

}
