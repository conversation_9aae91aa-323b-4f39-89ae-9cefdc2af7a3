package com.zte.mcrm.activity.common.config;

import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import static com.zte.mcrm.activity.service.schedule.param.ScheduleOrchestrationMailParam.SEVENTH;

/**
 * 活动相关的URL配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@ConfigurationProperties(
        prefix = "activity.url.config",
        ignoreUnknownFields = true
)
public class ActivityUrlConfig {

    /**
     * 活动详情
     */
    private String detail;
    /**
     * 活动待办列表
     */
    private String pendingNoticeList;
    /**
     * 活动详情的日程安排tab页
     */
    private String attendDetail;

    /**
     * 样板店详情
     */
    private String samplePointDetail;

    /**
     * 获取活动详情跳转url。
     *
     * @param activityRowId 活动id
     * @param type 活动类型
     * @return
     */
    public String fetchDetailUrl(String activityRowId, String type, String tab) {
        return detail + "?activityRowId=" + activityRowId + "&activityType=" + type + "&name=" + tab;
    }

    /**
     * 获取活动待办列表
     *
     * @param activityRowId 活动id
     * @return
     */
    public String fetchPendingNoticeUrl(String activityRowId) {
        return pendingNoticeList + "?activityRowId=" + activityRowId;
    }

    public String fetchAttendDetailUrl(String activityRowId, String tab) {
        return attendDetail + "?activityRowId=" + activityRowId + "&name=" + tab;
    }

    /**
     * 获取展会大会详情
     * @param activityRowId
     * @return
     * <AUTHOR>
     * @date 2024/5/31
     */
    public String fetchAttendDetailEmailUrl(String activityRowId){
        return attendDetail + "?name=third&activityRowId=" + activityRowId;
    }

    /**
     * 获取样板点详情
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/5/31
     */
    public String fetchSamplePointDetailEmailUrl(String activityRowId){
        return samplePointDetail + "?name=third&activityRowId=" + activityRowId;
    }

    /**
     * 获取其他活动详情
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/5/31
     */
    public String fetchDetailEmailUrl(String activityRowId){
        return detail + "?name=third&activityRowId=" + activityRowId;
    }

    /**
     * 根据活动类型获取不同详情地址
     *
     * @param activityRowId 活动ID
     * @param activityType  活动类型
     * @return 返回结果
     * <AUTHOR>
     * @date: 2024/11/05 13:51
     */
    public String fetchUrlByActivityType(String activityRowId, String activityType) {
        if (ActivityTypeEnum.JOIN_EXHIBITION.isMe(activityType)) {
            return fetchAttendDetailUrl(activityRowId, SEVENTH);
        } else {
            return fetchDetailUrl(activityRowId, activityType, "first");
        }
    }
}
