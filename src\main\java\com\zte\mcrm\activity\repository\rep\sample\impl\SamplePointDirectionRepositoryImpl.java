package com.zte.mcrm.activity.repository.rep.sample.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.sample.SamplePointDirectionExtMapper;
import com.zte.mcrm.activity.repository.model.sample.SamplePointDirectionDO;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointDirectionRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 样板点展示方向
 *
 * <AUTHOR>
 */
@Component
public class SamplePointDirectionRepositoryImpl implements SamplePointDirectionRepository {
    @Resource
    private SamplePointDirectionExtMapper samplePointDirectionExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<SamplePointDirectionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (SamplePointDirectionDO infoDO : recordList) {
            if (StringUtils.isBlank(infoDO.getRowId())) {
                infoDO.setRowId(keyIdService.getKeyId());
            }
            samplePointDirectionExtMapper.insertSelective(infoDO);
        }
        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(SamplePointDirectionDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return samplePointDirectionExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteBySamplePointRowId(String samplePointRowId) {
        if (StringUtils.isBlank(samplePointRowId)) {
            return NumberConstant.ZERO;
        }
        return samplePointDirectionExtMapper.deleteBySamplePointRowId(samplePointRowId);
    }

    @Override
    public Map<String, List<SamplePointDirectionDO>> queryDirectionBySamplePointRowId(List<String> samplePointRowIdList) {
        return CollectionUtils.isEmpty(samplePointRowIdList) ? Collections.emptyMap() :
                samplePointDirectionExtMapper.getDirectionBySamplePointRowIds(samplePointRowIdList).stream().collect(
                        Collectors.groupingBy(SamplePointDirectionDO::getSamplePointRowId));
    }
}
