package com.zte.mcrm.activity.application.exhibition.impl;

import com.zte.mcrm.activity.application.exhibition.ExhibitionQuerySupportAppService;
import com.zte.mcrm.activity.application.exhibition.convert.ExhibitionQuerySupportConvertor;
import com.zte.mcrm.activity.application.model.StandardExhibitionDataSource;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import com.zte.mcrm.activity.repository.rep.exhibition.*;
import com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery;
import com.zte.mcrm.activity.service.person.ActivityZtePeopleSearchService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityZtePeopleVO;
import com.zte.mcrm.activity.web.controller.exhibition.param.ExhibitionSelectParam;
import com.zte.mcrm.activity.web.controller.exhibition.vo.JoinExhibitionSelPeopleVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.SelectExhibitionVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum.CONFERENCE;
import static com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum.EXHIBITION;

/**
 * <AUTHOR>
 */
@Service
public class ExhibitionQuerySupportAppServiceImpl implements ExhibitionQuerySupportAppService {

    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;
    @Autowired
    private ExhibitionRelationExpertRepository exhibitionRelationExpertRepository;
    @Autowired
    private ExhibitionRelationLeaderRepository exhibitionRelationLeaderRepository;
    @Autowired
    private ExhibitionRelationRoomRepository exhibitionRelationRoomRepository;
    @Autowired
    private ExhibitionRelationHotelRepository exhibitionRelationHotelRepository;
    @Autowired
    private ExhibitionRelationCarRepository exhibitionRelationCarRepository;
    @Autowired
    private ExhibitionRelationAttachmentRepository exhibitionRelationAttachmentRepository;

    @Autowired
    private ActivityZtePeopleSearchService activityZtePeopleSearchService;

    @Autowired
    private ExhibitionQuerySupportConvertor exhibitionQuerySupportConvertor;


    @Override
    public PageRows<SelectExhibitionVO> selectableList(BizRequest<PageQuery<ExhibitionSelectParam>> request) {

        StandardExhibitionDataSource data = selectableExhibition(request.getParam());

        return exhibitionQuerySupportConvertor.convert2SelectExhibitionVO(data);
    }

    @Override
    public JoinExhibitionSelPeopleVO selectZteJoinExhibitionPeople(BizRequest<PageQuery<ActivityRecentlySearchParam>> request) {
        JoinExhibitionSelPeopleVO vo = new JoinExhibitionSelPeopleVO();

        List<ActivityZtePeopleVO> recentList = activityZtePeopleSearchService.searchRecentlyContacts(request);
        vo.setRecentList(recentList);

        ActivityRecentlySearchParam param = request.getParam().getParam();
        if (param == null || StringUtils.isBlank(param.getExhibitionRowId())) {
            return vo;
        }

        String exhibitionRowId = param.getExhibitionRowId();
        vo.setExhibitionRowId(exhibitionRowId);

        StandardExhibitionDataSource data = new StandardExhibitionDataSource();
        data.setExhibitionList(exhibitionInfoRepository.listByRowIds(Collections.singletonList(exhibitionRowId)));
        data.setExpertMap(exhibitionRelationExpertRepository.queryExpertsWithExhibitionRowId(Collections.singletonList(exhibitionRowId)));
        data.setLeaderMap(exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(Collections.singletonList(exhibitionRowId)));

        return exhibitionQuerySupportConvertor.fillJoinExhibitionSelPeopleVO(vo, data);
    }

    /**
     * 获取可选展会（开启报名）
     *
     * @param page
     * @return
     */
    private StandardExhibitionDataSource selectableExhibition(PageQuery<ExhibitionSelectParam> page) {
        ExhibitionSelectParam param = Optional.ofNullable(page.getParam()).orElse(new ExhibitionSelectParam());
        List<String> exhibitionRowIds = new ArrayList<>();
        PageRows<ExhibitionInfoDO> exhibitionPage = this.pageExhibitionInfo(param, page.getPageNo(), page.getPageSize());

        if (CollectionUtils.isNotEmpty(exhibitionPage.getRows())) {
            exhibitionPage.setRows(exhibitionPage.getRows().stream()
                    .filter(x -> null != x.getEntryOpenTime())
                    .sorted(Comparator.comparing(ExhibitionInfoDO::getEntryOpenTime).reversed())
                    .collect(Collectors.toList()));
            exhibitionRowIds = exhibitionPage.getRows().stream().map(ExhibitionInfoDO::getRowId).collect(Collectors.toList());
        }

        Map<String, List<ExhibitionRelationExpertDO>> expertMap = exhibitionRelationExpertRepository.queryExpertsWithExhibitionRowId(exhibitionRowIds);
        Map<String, List<ExhibitionRelationLeaderDO>> leaderMap = exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(exhibitionRowIds);
        Map<String, List<ExhibitionRelationAttachmentDO>> attachmentMap = exhibitionRelationAttachmentRepository.queryAttachmentByExhibitionRowId(exhibitionRowIds);

        StandardExhibitionDataSource data = new StandardExhibitionDataSource();
        data.setExhibitionPage(exhibitionPage);
        data.setExpertMap(expertMap);
        data.setLeaderMap(leaderMap);
        data.setAttachmentMap(attachmentMap);
        data.setRoomMap(exhibitionRelationRoomRepository.getRelationRoomListByExhibitionRowIds(new HashSet<>(exhibitionRowIds)));
        data.setCarMap(exhibitionRelationCarRepository.getRelationCarListByExhibitionRowIds(new HashSet<>(exhibitionRowIds)));
        data.setHotelMap(exhibitionRelationHotelRepository.getRelationHotelListByExhibitionRowIds(new HashSet<>(exhibitionRowIds)));

        return data;
    }

    private PageRows<ExhibitionInfoDO> pageExhibitionInfo(ExhibitionSelectParam param, Integer pageNo, Integer pageSize) {
        String exhibitionRowId = param.getExhibitionRowId();
        if (!StringUtils.isBlank(exhibitionRowId)) {
            // 如果传了特定展会，说明是查看
            ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(param.getExhibitionRowId());
            List<ExhibitionInfoDO> list = new ArrayList<>();
            if (exhibitionInfo != null) {
                list.add(exhibitionInfo);
            }
            return PageRowsUtil.buildPageRow(pageNo, pageSize, list.size(), list);
        }

        String placeSourceType = param.getPlaceResourceType();
        ExhibitionInfoQuery query = new ExhibitionInfoQuery();
        query.setPage(pageNo, pageSize);
        query.setAdmin(1);
        query.setEntryOpenStatus(BooleanEnum.Y.getCode());
        query.setValidExhibitionTime(BooleanEnum.N.getCode());
        if (PlaceResourceTypeEnum.in(placeSourceType, EXHIBITION, CONFERENCE)) {
            query.setPlaceResourceType(placeSourceType);
        }
        return exhibitionInfoRepository.queryExhibitionInfoList(query);
    }

}
