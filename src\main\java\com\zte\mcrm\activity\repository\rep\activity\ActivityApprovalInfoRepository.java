package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动审批流信息，用来关联审批
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityApprovalInfoRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityApprovalInfoDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityApprovalInfoDO record);


    /**
     * 根据活动Id更新
     * @param record  记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/4 14:24
     */
    int updateByActivityRowIdSelective(ActivityApprovalInfoDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
     List<ActivityApprovalInfoDO> queryAllByActivityRowId(String activityRowId);

    /**
     * 根据活动id获取当前数据
     *
     * @param activityRowId
     * @return
     */
    ActivityApprovalInfoDO queryByActivityRowId(String activityRowId);

    /**
     * 根据审批信息id获取当前数据
     * @param approvalNo
     * @return
     */
    ActivityApprovalInfoDO queryByApprovalNo(String approvalNo);
}
