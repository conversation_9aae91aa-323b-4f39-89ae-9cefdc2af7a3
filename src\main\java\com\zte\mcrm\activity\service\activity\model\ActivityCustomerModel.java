package com.zte.mcrm.activity.service.activity.model;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivitySceneEnum;
import com.zte.mcrm.activity.common.enums.activity.SanctionedPartyEnum;
import com.zte.mcrm.activity.web.controller.activity.vo.CustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.CustUnitInfoVO;
import com.zte.mcrm.adapter.vo.Account;
import com.zte.mcrm.adapter.vo.ContactVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ********
 * @date 2023-09-07 10:19
 */
@Data
public class ActivityCustomerModel {

    /**
     *  客户列表
     */
    List<CustUnitInfoVO> listCust;
    /**
     *  客户联系人列表
     */
    List<CustPeopleInfoVO> listCustPeople;
    /**
     *  客户信息
     */
    List<Account> accountList;
    /**
     *  用于联系人校验时排除客户信息
     */
    String excludeCustomers;

    /**
     * 错误的客户联系人
     */
    List<String> errorCustomers = new ArrayList<>();
    /**
     *  客户联系人信息
     */
    List<ContactVO> contactVOList;
    /**
     * 客户联系人Map，key为客户联系人编码
     */
    Map<String, ContactVO> contactVOMap;
    /**
     * 客户联系人列表Map，key为客户编码
     */
    Map<String, List<CustPeopleInfoVO>> custPeopleMap;
    /**
     * 将联系人信息绑定至客户信息中
     */
    private Boolean fillContactInfo2CustomerFlag;
    /**
     *  活动客户数量
     */
    private int customerSize = 0;
    /**
     *  活动存在的客户数量
     */
    private int existCustomerSize = 0;
    /**
     *  活动联系人数量
     */
    private int contactSize = 0;
    /**
     *  活动存在的联系人数量
     */
    private int existContactSize = 0;

    public ActivityCustomerModel() {

    }

    public ActivityCustomerModel(List<CustUnitInfoVO> listCust) {
        this.listCust = CollectionExtUtils.getListOrDefaultEmpty(listCust);
    }

    public ActivityCustomerModel(List<CustUnitInfoVO> listCust, List<CustPeopleInfoVO> listCustPeople) {
        this.listCust = CollectionExtUtils.getListOrDefaultEmpty(listCust);
        this.listCustPeople = CollectionExtUtils.getListOrDefaultEmpty(listCustPeople);
        this.errorCustomers = new ArrayList<>();
    }

    /**
     * 从客户信息中获取客户联系人信息
     * @return java.util.List<com.zte.mcrm.activity.web.controller.activity.vo.CustPeopleInfoVO>
     * <AUTHOR>
     * date: 2023/9/7 10:03
     */
    public void getCustPeopleFromCustInfoDynamic() {
        List<CustPeopleInfoVO> listCustPeople = Lists.newArrayList();
        if (CollectionUtils.isEmpty(this.listCust)) {
            return;
        }
        this.listCust.forEach(e -> {
            List<CustPeopleInfoVO> list = e.getListCustPeople();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
                return;
            }
            listCustPeople.addAll(list);
        });
        this.listCustPeople = listCustPeople;
        this.fillContactInfo2CustomerFlag = Boolean.TRUE;
    }

    /**
     * 填充客户信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/7 10:08
     */
    public void fillAccountInfo() {
        this.customerSize = this.listCust.size();
        if (CollectionUtils.isEmpty(this.accountList)) {
            return;
        }
        Map<String, Account> accountMap = this.accountList.stream()
                .collect(Collectors.toMap(Account::getCustNo, v -> v, (v1, v2) -> v1));
        /* Started by AICoder, pid:64e110ae753f48688a03da918c960809 */
        for (Iterator<CustUnitInfoVO> iterator = this.listCust.iterator(); iterator.hasNext(); ) {
            CustUnitInfoVO e = iterator.next();
            Account account = accountMap.get(e.getCustomerCode());
            if (account == null) {
                iterator.remove();
                continue;
            }
            this.existCustomerSize++;
            String sanctionedPatryCode = account.getSanctionedPatryCode();
            e.setCustomerName(account.getAccountName());
            e.setLocalCode(account.getLocalNum());
            e.setLocalName(account.getLocalName());
            e.setProvinceCode(account.getProvinceId());
            e.setInternational(account.getCustRangeCode());
            e.setCustType(account.getAccntTypeCd());
            e.setCustLevel(account.getAccountLevel());
            e.setOperatorType(account.getOperatorTypeCode());
            e.setBelongBuId(account.getBuId());
            e.setCountryShortName(account.getCountryForShort());
            e.setAccountLocalInfo(account.getAccount1());
            e.setMktName(account.getBelongMkt());
            e.setMktCode(account.getBelongMktCode());
            e.setMtoCode(account.getMtoNameCode());
            e.setMtoName(account.getMtoName());
            e.setFrozenFlag(account.getFrozenFlag());
            e.setAcctMergeFlag(account.getAcctMergeFlag());
            e.setActiveStatusCode(account.getActiveStatusCode());
            e.setSanctionedPatryCode(sanctionedPatryCode);
            ActivitySceneEnum mainCustDepart = ActivitySceneEnum.getEnumByCustomer(e.getCustType(), e.getInternational());
            e.setActivitySceneCode(mainCustDepart.getCode());
            e.setActivitySceneName(mainCustDepart.getName());
            e.setSanctionedPatryName(SanctionedPartyEnum.getDescByCode(sanctionedPatryCode));
        }
        /* Ended by AICoder, pid:64e110ae753f48688a03da918c960809 */
    }

    /**
     * 填充客户联系人信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/7 10:09
     */
    public void fillContactInfo() {
        if (CollectionUtils.isEmpty(this.contactVOList) || CollectionUtils.isEmpty(this.listCustPeople)) {
            return;
        }
        List<CustPeopleInfoVO> peopleInfos = this.listCustPeople.stream().filter(x -> !this.getExcludeCustomers().contains(x.getCustomerCode())).collect(Collectors.toList());
        this.contactSize = peopleInfos.size();
        Map<String, ContactVO> contactMap = this.contactVOList.stream().filter(x -> !this.getExcludeCustomers().contains(x.getOuNum()))
                .collect(Collectors.toMap(contact -> contact.getOuNum() + contact.getConPerNum() ,contact -> contact));
        // 填充需要更新的字段信息
        /* Started by AICoder, pid:0fd8cfc65c134b028a61e57a7164660e */
        for (Iterator<CustPeopleInfoVO> iterator = peopleInfos.iterator(); iterator.hasNext(); ) {
            CustPeopleInfoVO e = iterator.next();
            ContactVO contactVO = contactMap.get(e.getCustomerCode() + e.getContactNo());
            if (contactVO == null) {
                errorCustomers.add(e.getCustomerName() + CharacterConstant.SUBTRACT + e.getContactName());
                iterator.remove();
                continue;
            }
            this.existContactSize++;
            e.setStatusCode(contactVO.getStatusCode());
            e.setAccntManagerA(contactVO.getAccntManagerA());
            e.setAccntManagerAempNo(contactVO.getAccntManagerAempNo());
            e.setAccntManagerAempName(contactVO.getAccntManagerAempName());
            e.setAccntManagerB(contactVO.getAccntManagerB());
            e.setAccntManagerBempNo(contactVO.getAccntManagerBempNo());
            e.setAccntManagerBempName(contactVO.getAccntManagerBempName());
            e.setSanctionedPatryCode(contactVO.getContactSanctionedPartyCode());
            e.setSanctionedPatryName(SanctionedPartyEnum.getDescByCode(e.getSanctionedPatryCode()));
        }
        /* Ended by AICoder, pid:0fd8cfc65c134b028a61e57a7164660e */
        fillContactInfo2Customer();
    }

    /**
     * 仅当需要绑定联系人信息时才执行此操作
     * @param
     * @return void
     * <AUTHOR>
     * date: 2023/9/19 11:03
     */
    private void fillContactInfo2Customer() {
        if (!Boolean.TRUE.equals(this.fillContactInfo2CustomerFlag)) {
            return;
        }
        // 将更新完的联系人信息装回客户信息中
        Map<String, List<CustPeopleInfoVO>> custPeopleMap = this.listCustPeople.stream()
                .collect(Collectors.groupingBy(CustPeopleInfoVO::getCustomerCode));
        listCust.forEach(e -> {
            List<CustPeopleInfoVO> listPeople = custPeopleMap.get(e.getCustomerCode());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(listPeople)) {
                return;
            }
            e.setListCustPeople(listPeople);
        });
    }
}
