package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDirectionDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResourceBizExpertDirectionRepository {


    int insertSelective(List<ResourceBizExpertDirectionDO> recordList);

    /**
     * 查询 工号在empNos中，且交流方向在directions中的数据
     * @param empNos 工号
     * @param directions 交流方向
     * @return
     */
    List<ResourceBizExpertDirectionDO> getByEmpNosAndDirections(List<String> empNos, List<String> directions);

    List<ResourceBizExpertDirectionDO> getByEmpNos(List<String> empNos);

    int deleteByEmpNos(String operator, List<String> empNos);

    int deleteByRowIds(String operator, List<String> rowIds);
}
