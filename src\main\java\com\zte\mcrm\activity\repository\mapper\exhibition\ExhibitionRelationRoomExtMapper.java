package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ExhibitionRelationRoomExtMapper extends ExhibitionRelationRoomMapper {

    /***
     * <p>
     * 根据主键Id列表获取对应记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:08
     * @param rowIds 关联会议室记录主键Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO>
     */
    List<ExhibitionRelationRoomDO> queryAllByRoomRowIds(List<String> rowIds);

    /***
     * <p>
     * 根据展会Id列表获取所有关联会议室记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:09
     * @param exhibitionIds 展会Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO>
     */
    List<ExhibitionRelationRoomDO> queryAllByExhibitionIds(List<String> exhibitionIds);

    /***
     * <p>
     * 批量插入数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联会议室记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationRoomDO> records);

    /***
     * <p>
     * 批量修改数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联会议室记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationRoomDO> records);

    /***
     * <p>
     * 逻辑删除所有与展会Id关联的会议室记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:11
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id列表
     * @return int
     */
    int softDeleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);


}
