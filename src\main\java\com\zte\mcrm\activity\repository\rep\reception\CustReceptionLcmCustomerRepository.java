package com.zte.mcrm.activity.repository.rep.reception;

import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmCustomerDO;

import java.util.List;

/**
 * 客户接待 - 客户LCM扫描信息
 *
 * <AUTHOR>
 */
public interface CustReceptionLcmCustomerRepository {

    /**
     * 根据客户接待拓展活动ID获取对应客户LCM扫描信息
     *
     * @param headerId
     * @return
     */
    List<CustReceptionLcmCustomerDO> getByHeaderId(String headerId);

    /**
     * 通过头Id删除
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 16:01
     */
    int deleteByHeaderId(String headerId);

    /**
     * 批量插入客户LCM扫描信息
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    int batchInsert(List<CustReceptionLcmCustomerDO> list);

}
