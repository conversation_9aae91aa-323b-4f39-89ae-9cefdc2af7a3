package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午2:14
 **/
public interface ExhibitionRelationRoomRepository {

    /***
     * <p>
     * 插入一条记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:42
     * @param record 展会关联会议室记录
     * @return int
     */
    int insertSelective(ExhibitionRelationRoomDO record);

    /***
     * <p>
     * 更新单条记录的指定字段
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:46
     * @param record 更新的展会关联会议室记录
     * @return int
     */
    int updateByPrimaryKeySelective(ExhibitionRelationRoomDO record);

    /***
     * <p>
     * 根据主键Id列表 获取 展会关联车辆记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:46
     * @param roomRowIds 展会关联会议室主键Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO>
     */
    List<ExhibitionRelationRoomDO> queryAllByRoomRowIds(List<String> roomRowIds);

    /***
     * <p>
     * 根据展会Id 获取 对应的关联会议室集合
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:50
     * @param exhibitionRowIds 展会Id集合
     * @return java.util.Map<java.lang.String,com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO>
     */
    Map<String, List<ExhibitionRelationRoomDO>> getRelationRoomListByExhibitionRowIds(Set<String> exhibitionRowIds);

    /***
     * <p>
     * 批量插入数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:50
     * @param records 展会关联会议室记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationRoomDO> records);

    /***
     * <p>
     * 批量更新数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:52
     * @param records 展会关联会议室记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationRoomDO> records);

    /***
     * <p>
     * 根据展会Id 删除 对应的关联会议室记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:53
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id列表
     * @return int
     */
    int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);

    /***
     * <p>
     * 根据主键Id 删除 对应的关联会议室记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:54
     * @param operator 操作人
     * @param rowIds 关联会议室记录主键Id列表
     * @return int
     */
    int deleteByRowIds(String operator, List<String> rowIds);

}
