package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoListQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoPageQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoSearchPushQuery;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityConvergenceInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ConvenientInfoParam;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoVO;
import com.zte.mcrm.activity.web.controller.reservation.param.ActivityResourceReservationParam;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 客户活动信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityInfoRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityInfoDO record);


    /**
     * 根据主键查询
     *
     * @param rowId
     * @return
     */
    ActivityInfoDO selectByPrimaryKey(String rowId);

    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityInfoDO record);

    /**
     * 全脸更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(ActivityInfoDO record);

    /**
     * 提供给预约看板的活动分页列表
     * @param param
     * @return
     */
    PageRows<ActivityResourceReservationVo> pageQueryToReservation(PageQuery<ActivityResourceReservationParam> param);

    /**
     *
     * @param query

     * @return
     */
    PageRows<ActivityInfoDO> searchByPage(ActivityInfoQuery query);

    /**
     * 获取活动汇聚信息
     * @param query
     * @return
     */
    List<ActivityConvergenceInfoVO> getActivityConvergenceInfoList(ActivityInfoQuery query);

    /**
     * 查询列表数量
     *
     * @param query
     * @return {@link int}
     * <AUTHOR>
     * @date 2023/8/17 下午8:05
     */
    long countList(ActivityInfoQuery query);

    /**
     * 按条件查出数据（用于报表导出）
     * @param query
     * @return
     */
    List<ActivityInfoDO> searchByParam(ActivityInfoQuery query);

    /**
     * 根据推送ISearch参数查询
     * @param query 查询参数
     * @return java.util.List<com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO>
     * <AUTHOR>
     * date: 2023/12/6 10:51
     */
    List<ActivityInfoDO> searchByISearchParam(ActivityInfoSearchPushQuery query);

    /**
     * 模糊分页查询
     *
     * @param trans2InfoQueryFromNotice
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/5/23 17:28
     */
    List<ActivityInfoDO> fuzzyQueryByRowIds(ActivityInfoQuery trans2InfoQueryFromNotice);

    /**
     * 根据活动id，删除活动
     * @param rowIds
     * @return
     */
    int deleteByIds(String operator,List<String> rowIds);

    List<ActivityInfoDO> selectByIds(List<String> rowIds);

    List<ActivityInfoDO> selectByOriginRowIds(List<String> originRowIds);

    /**
     * 查询所有-包含无效数据
     * @param rowIds 活动Id
     * @return java.util.List<ActivityInfoDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityInfoDO> selectByIdsWithNoEnable(List<String> rowIds);

    /**
     * 根据活动类型和活动资源id查询最近一次活动的信息
     * @param query
     * @return
     */
    ActivityInfoDO selectLatestVisitingActivity(ActivityInfoQuery query);

    /**
     * 查询最近一条活动信息（order by end_time desc，状态：21、80、90）
     *
     * @param query 参数有：applyPeopleNo、activityTypeList、customerCodeList
     * @return 活动信息
     */
    ActivityInfoDO getLatestActivityInfo(ActivityInfoQuery query);

    /**
     * 返回某种类型活动的参观次数
     * @param query
     * @return
     */
    Map<String, Integer> countVisitingActivity(ActivityInfoQuery query);

    /**
     * 查询状态为已完成，已完成评价，进行中但是时间已经过了活动时间的单据数目
     * @param query
     * @return
     */
    Integer countListForSpecialStatus(ActivityInfoQuery query);

    /**
     * 查询状态为已完成，已完成评价，进行中但是时间已经过了活动时间的单据列表
     * @param query
     * @return
     */
    List<ActivityInfoDO> getListForSpecialStatus(ActivityInfoQuery query);

    /**
     * 插入单条数据（不插入默认值方便数据迁移）
     *
     * @param record
     * @return
     */
    int insertSelectiveWithoutDefaultValue(ActivityInfoDO record);

    /**
     * 获取迁移活动id
     * @return
     */
    List<String> getMigrateActivityIds(int offset);

    /**
     * 查询重复的活动编号
     *
     * @param limit
     * @return
     */
    List<String> getRepeatedActivityReqNoList(int limit);

    /**
     * 批量更新活动信息
     *
     * @param updateList
     * @return
     */
    int batchUpdateByPrimaryKey(List<ActivityInfoDO> updateList);

    /**
     * 查询出交流方式为空的单据进行数据处理
     *
     * @param limit
     * @return
     */
    List<ActivityInfoDO> getCommunicationWayEmptyList(int limit);

    /**
     * 查询最近一次活动的城市和国家
     *
     * @param param
     * @return com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoVO
     * @date 2024/5/8
     */
    ConvenientInfoVO selectLastCityAndCountry(ConvenientInfoParam param);

    /***
     * <p>
     * 分页查询fol活动列表信息
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/28 下午3:20
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param queryParam 查询参数
     * @return com.zte.mcrm.activity.common.model.PageRows<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO>
     */
    PageRows<ActivityInfoFolVO> pageQuery4Fol(int pageNo, int pageSize, ActivityInfoPageQuery queryParam);

    /**
     * FOL客户扩展活动列表
     *
     * @param queryParam
     * @return java.util.List<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO>
     * @date 2024/6/4
     */
    List<ActivityInfoFolVO> queryList4Fol(ActivityInfoListQuery queryParam);

    /**
     * 根据使用id进行游标分页，用于全表等可能深度分页的大数据量处理
     *
     * @param param
     * @param limit
     * @return {@link List< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2024/10/24 下午6:58
     */
    List<ActivityInfoDO> selectListPageById(ActivityInfoDO param, int limit);

    /**
     * 查询参与人相关的展会活动，
     *
     * @param pageQuery
     * @return {@link List< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2025/3/6 下午2:34
     */
    PageRows<ActivityInfoDO> selectExhibitionActivityWithRelationPeople(PageQuery<ActivityInfoQuery> pageQuery);

}
