package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityRelationAttachmentExtMapper extends ActivityRelationAttachmentMapper {
    List<ActivityRelationAttachmentDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);

    /**
     * 根据sceneOriginRowId查询单个的日程安排谈参信息 (这个方法后面不要用)
     * @param sceneOriginRowId
     * @return
     */
    ActivityRelationAttachmentDO queryAllBySceneOriginRowId(@Param("sceneOriginRowId") String sceneOriginRowId);

    /**
     * 根据sceneOriginRowId列表查询日程安排谈参信息
     * @param sceneOriginRowIds
     * @return
     */
    List<ActivityRelationAttachmentDO> queryAttachmentBySceneOriginRowIds(List<String> sceneOriginRowIds);

    /**
     * 根据activityRowId列表查询附件信息
     * @param activityRowIds
     * @return
     */
    List<ActivityRelationAttachmentDO> queryAttachmentByActivityRowIds(List<String> activityRowIds);

    /**
     * 查询活动和会议关联的附件
     * @param activityRowId
     * @param activitySummaryRowId
     * @return
     */
    List<ActivityRelationAttachmentDO> queryByActivityRowIdAndSummaryId(String activityRowId, String activitySummaryRowId);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 批量插入数据
     *
     * @param list
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list") List<ActivityRelationAttachmentDO> list);

    /**
     * 根据sceneOriginRowIds批量软删除
     * @param operator
     * @param sceneOriginRowIds
     * @return
     */
    int deleteBySceneOriginRowIds(@Param("operator") String operator, @Param("sceneOriginRowIds") List<String> sceneOriginRowIds);

    List<ActivityRelationAttachmentDO> queryErrorType(@Param("types")List<String> errorTypeList);

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效附件
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationAttachmentDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationAttachmentDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    /**
     * 根据主键和类型更新
     * @param record    更新记录
     * @return int
     * <AUTHOR>
     * date: 2024/1/18 20:13
     */
    int updateByPrimaryKeySelectiveAndType(ActivityRelationAttachmentDO record);

    ActivityRelationAttachmentDO queryByCondition(ActivityRelationAttachmentDO param);
    
    /**
     * 查询单个的日程安排谈参信息 
     * @param attachmentSceneType
     * @param sceneOriginRowId
     * @return
     */
    ActivityRelationAttachmentDO queryAttachmentBySceneOriginRowIdAndType(
    		@Param("attachmentSceneType") String attachmentSceneType,
    		@Param("sceneOriginRowId") String sceneOriginRowId);
}
