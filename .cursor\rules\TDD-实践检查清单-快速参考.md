# TDD实践检查清单 - 快速参考版

## 🎯 核心要求速查
- ✅ **日志中文，异常英文**
- ✅ **禁止硬编码**  
- ✅ **分支覆盖率100%**
- ✅ **使用PowerMockRunner**
- ✅ **lombok注解替代get/set**
- ✅ **作者：李龙0668001470**

## 🔄 TDD三步循环

### 🔴 Red阶段 (5分钟检查)
```java
@Test
@DisplayName("申请订阅应该返回申请单号")  // ✅ 中文描述
void should_ReturnApplicationNo_When_ValidApplication() {  // ✅ 命名规范
    // Given - 准备数据
    // When - 执行方法  
    // Then - 验证结果
}
```
- [ ] 测试命名：`should_ExpectedBehavior_When_StateUnderTest`
- [ ] 中文描述：`@DisplayName("功能描述")`
- [ ] Given-When-Then结构
- [ ] 测试确实失败（红色）

### 🟢 Green阶段 (最小实现)
```java
@Override
public BizResult<String> applySubscription(BizRequest<MagazineSubscriptionDTO> request) {
    // 最小实现让测试通过
    return BizResult.buildSuccessRes("TEST_APP_NO");
}
```
- [ ] 只写让测试通过的最少代码
- [ ] 不过度设计
- [ ] 测试通过（绿色）

### 🔵 Refactor阶段 (重构优化)
- [ ] 消除重复代码
- [ ] 提取常量到`xxxConstant`类
- [ ] 方法长度 ≤ 30行
- [ ] 参数个数 ≤ 5个
- [ ] 测试始终通过

## 📋 代码质量快检

### 异常和日志
```java
// ✅ 正确示例
log.info("开始处理杂志订阅申请");  // 中文日志
throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, 
    "Magazine code cannot be blank");  // 英文异常
```

### 对象定义
```java
// ✅ 正确示例
@Getter
@Setter  
@ToString
public class MagazineSubscriptionDTO {
    private String magazineCode;
    // 禁用get/set方法
}
```

### 常量提取
```java
// ✅ 正确示例
public class MagazineConstant {
    public static final String MAGAZINE_CODE_BUSINESS = "BUSINESS";
    public static final String STATUS_PENDING = "待审批";
}
```

## 🎭 Mock使用速查

### Mock设置模板
```java
@ExtendWith(MockitoExtension.class)
class ServiceTest {
    
    @InjectMocks
    private MagazineSubscriptionServiceImpl service;
    
    @Mock  // 只Mock外部依赖
    private MagazineSubscriptionRepository repository;
    
    @Test
    void should_SaveData_When_ValidRequest() {
        // Given
        when(repository.insertSelective(any())).thenReturn(1);
        
        // When
        BizResult<String> result = service.applySubscription(request);
        
        // Then
        verify(repository).insertSelective(any());  // 验证调用
    }
}
```

## 🎯 断言速查

### AssertJ常用断言
```java
// 基础断言
assertThat(result.isSuccess()).isTrue();
assertThat(result.getData()).isNotBlank();

// 字符串断言  
assertThat(applicationNo).startsWith("APP_").hasSize(15);

// 异常断言
assertThatThrownBy(() -> service.apply(request))
    .isInstanceOf(BizRuntimeException.class)
    .hasMessageContaining("Magazine code cannot be blank");

// 集合断言
assertThat(subscriptions)
    .hasSize(2)
    .extracting(MagazineSubscriptionVO::getMagazineCode)
    .containsExactly("BUSINESS", "TECH");
```

## 📊 覆盖率检查

### 分支覆盖100%模板
```java
@Test
@DisplayName("所有分支都应该被覆盖")
void should_CoverAllBranches_When_Testing() {
    // 正常分支
    MagazineSubscriptionDTO validDto = buildValidDTO();
    assertThatNoException().isThrownBy(() -> service.apply(validDto));
    
    // 异常分支1：空编码
    MagazineSubscriptionDTO emptyDto = buildValidDTO();
    emptyDto.setMagazineCode("");
    assertThatThrownBy(() -> service.apply(emptyDto))
        .isInstanceOf(BizRuntimeException.class);
    
    // 异常分支2：null编码
    MagazineSubscriptionDTO nullDto = buildValidDTO();
    nullDto.setMagazineCode(null);
    assertThatThrownBy(() -> service.apply(nullDto))
        .isInstanceOf(BizRuntimeException.class);
}
```

## ✅ 提交前检查清单

### 代码质量
- [ ] 分支覆盖率 = 100%
- [ ] 方法长度 ≤ 30行
- [ ] 类长度 ≤ 500行  
- [ ] 参数个数 ≤ 5个
- [ ] 异常信息英文
- [ ] 日志输出中文
- [ ] 无硬编码中文

### 测试质量
- [ ] 测试命名规范
- [ ] @DisplayName中文描述
- [ ] Given-When-Then结构
- [ ] 每个测试只验证一个行为
- [ ] Mock只依赖外部服务
- [ ] 使用AssertJ断言

### 代码规范
- [ ] 使用lombok注解
- [ ] 常量提取到Constant类
- [ ] 使用Converter转换对象
- [ ] Controller只调用Service
- [ ] 作者信息正确

## 🚨 常见错误提醒

### ❌ 避免这些错误
```java
// ❌ 错误：中文硬编码
subscription.setStatus("待审批");

// ✅ 正确：使用常量
subscription.setStatus(StatusConstant.PENDING_APPROVAL);

// ❌ 错误：中文异常信息
throw new RuntimeException("杂志编码不能为空");

// ✅ 正确：英文异常信息
throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, 
    "Magazine code cannot be blank");

// ❌ 错误：英文日志
log.info("Start processing magazine subscription");

// ✅ 正确：中文日志
log.info("开始处理杂志订阅申请");
```

## 🎖️ TDD成熟度自检

### Level 1: 入门级
- [ ] 能写基本单元测试
- [ ] 理解Red-Green-Refactor
- [ ] 覆盖率 ≥ 80%

### Level 2: 熟练级  
- [ ] 测试驱动设计
- [ ] 重构技能熟练
- [ ] 覆盖率 ≥ 90%

### Level 3: 专家级
- [ ] TDD成为自然习惯
- [ ] 能指导团队实践
- [ ] 持续优化流程

## 📞 遇到问题时

### 常见问题解决
1. **测试难写？** → 说明设计有问题，需要重构
2. **Mock太多？** → 依赖过度，考虑简化设计
3. **测试太慢？** → 减少外部依赖，使用纯Mock
4. **覆盖率不够？** → 检查所有分支和异常情况

### 寻求帮助
- 📚 参考项目内`magazine`模块示例
- 📖 查看`TDD开发示例-杂志订阅功能.md`
- 🤝 结对编程解决复杂问题
- 💬 团队内部TDD分享会

---

**💡 记住：TDD不是为了测试，而是为了更好的设计！**

**🎯 目标：让TDD成为自然的开发习惯，而不是额外负担！** 