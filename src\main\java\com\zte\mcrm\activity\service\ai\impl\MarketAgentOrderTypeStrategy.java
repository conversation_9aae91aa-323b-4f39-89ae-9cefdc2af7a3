package com.zte.mcrm.activity.service.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.AiConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.ai.ApiTypeEnum;
import com.zte.mcrm.activity.common.enums.ai.MessageTypeEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.service.ai.MarketAgentStrategy;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationRespVO;
import com.zte.mcrm.custcomm.common.constant.CustCommConstants;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/10/31 14:10
 */
@Service
public class MarketAgentOrderTypeStrategy implements MarketAgentStrategy {

    @Override
    public boolean support(String apiType) {
        return ApiTypeEnum.QUERY_ORDER_TYPE.isMe(apiType);
    }

    @Override
    public List<IgptRespVO> processBusiness(AiApplicationRespVO req) {
        List<IgptRespVO> igptRespVOList = Lists.newArrayList();
        if(StringUtils.isBlank(req.getApiData())){
            return igptRespVOList;
        }
        JSONObject jsonObject = JSONObject.parseObject(req.getApiData());
        String message = jsonObject.getString(AiConstant.MESSAGE);
        if(StringUtils.isBlank(message)){
            return Lists.newArrayList();
        }

        String[] split = message.split(CustCommConstants.COMMA_ZH);
        for (int i = 0; i < split.length; i++) {
            String str = i == split.length - 1 ? split[i] : split[i] + CustCommConstants.COMMA_ZH;
            IgptRespVO igptRespVO = new IgptRespVO();
            igptRespVO.setChatUuid(req.getChatUuid());
            igptRespVO.setStatus(NumberConstant.ZERO);
            JSONObject object = new JSONObject();
            object.put(AiConstant.CONTENT, str);
            object.put(AiConstant.TYPE, MessageTypeEnum.TEXT.getType());
            igptRespVO.setResult(object.toJSONString());
            igptRespVOList.add(igptRespVO);
        }
        return igptRespVOList;
    }
}
