package com.zte.mcrm.activity.integration.accountinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.MsaRpcResponseUtil;
import com.zte.mcrm.activity.integration.accountinfo.dto.ContactPageDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerPersonDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.ContactPersonParam;
import com.zte.mcrm.common.enums.ServiceAliasEnum;
import com.zte.mcrm.isearch.enums.RequestTypeEnum;
import com.zte.mcrm.util.MicroServiceRestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CustomerPersonService
 * @projectName zte-crm-custinfo-custvisit
 * @description: TODO
 * @date 2024/1/18 10:38
 */
@Component
@Slf4j
public class CustomerPersonService {

    /**
     * 获取客户联系人
     * @param request
     * @return {@link List <  ContactVO >}
     * <AUTHOR>
     * @date 2023/5/28 下午2:03
     */
    public MsaRpcResponse<List<OutCustomerPersonDTO>> getAccountPersonList(MsaRpcRequest<ContactPersonParam> request) {
        String url = "/api/person/simple/list";

        try {
            String result = MicroServiceRestUtil.invokeService(ServiceAliasEnum.ACCOUNT.getNormalizedName(), ServiceAliasEnum.ACCOUNT.getNormalizedVersion(),
                    RequestTypeEnum.POST.getValue(), url, JSON.toJSONString(request.getBody()), request.fetchHeaderMap());
            log.info("调用结果：" + result);
            ServiceData<List<OutCustomerPersonDTO>> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<List<OutCustomerPersonDTO>>>() {
            });

            MsaRpcResponse<List<OutCustomerPersonDTO>> response = new MsaRpcResponse<>(serviceData);

            if (response.getBo() == null) {
                response.setBo(Collections.emptyList());
            }
            return response;
        } catch (Exception e) {
            log.error("调用" + ServiceAliasEnum.ACCOUNT.getNormalizedName() + url + "异常", e);
            return new MsaRpcResponse<>(MsaRpcResponse.OUT_SERVERERROR_CODE, "out system error", "", Collections.emptyList());
        }
    }

    /**
     * 统计xxx客户有效联系人数量
     *
     * @param request
     * @return
     */
    public MsaRpcResponse<Integer> countValidPerson(MsaRpcRequest<String> request) {
        String url = "/contact/manage/card?customerCode=" + request.getBody() + "&showSize=1&auth=N";

        try {
            String result = MicroServiceRestUtil.invokeService(ServiceAliasEnum.ACCOUNT.getNormalizedName(), ServiceAliasEnum.ACCOUNT.getNormalizedVersion(),
                    RequestTypeEnum.GET.getValue(), url, "", request.fetchHeaderMap());
            log.info("调用" + url + "结果：" + result);
            ServiceData<LowCodePageRow<ContactPageDTO>> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<LowCodePageRow<ContactPageDTO>>>() {
            });
            MsaRpcResponse<LowCodePageRow<ContactPageDTO>> response = new MsaRpcResponse<>(serviceData);

            return MsaRpcResponseUtil.copy(response, LowCodePageRow::getTotal);
        } catch (Exception e) {
            log.error("调用" + ServiceAliasEnum.ACCOUNT.getNormalizedName() + url + "异常", e);
            return new MsaRpcResponse<>(MsaRpcResponse.OUT_SERVERERROR_CODE, "out system error", "", 0);
        }
    }
}
