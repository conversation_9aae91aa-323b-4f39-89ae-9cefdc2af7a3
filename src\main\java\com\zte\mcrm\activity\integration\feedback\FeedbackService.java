package com.zte.mcrm.activity.integration.feedback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.Sha256Util;
import com.zte.mcrm.activity.integration.feedback.dto.FeedBackUrlDTO;
import com.zte.mcrm.activity.integration.feedback.dto.FeedbackQueryDTO;
import com.zte.mcrm.activity.service.summary.param.FeedbackAddUrlParam;
import com.zte.mcrm.common.util.HttpClientUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ********
 * @date 2023-12-08 14:40
 **/
@Service
public class FeedbackService {
    @Value("${feedback.query.url:http://testty.rdc.zte.com.cn/zte-rdcloud-feedback-api/v2/feedbackTicket/mget?pageNo=1&pageSize=20}")
    private String queryUrl;

    @Value("${feedback.add.url:http://testty.rdc.zte.com.cn/zte-rdcloud-feedback-api/v2/feedbackTicket/temp-ticket}")
    private String addUrl;

    @Value("${feedback.appId:testcrm-account-info}")
    private String appId;

    @Value("${feedback.appSecret: }")
    String appSecret;

    public MsaRpcResponse<FeedbackQueryDTO> queryFeedbackDto(MsaRpcRequest<List<String>> request) throws RouteException {
        List<String> feedbackIds = request.getBody();
        Map<String, String> reqHeader = request.fetchHeaderMap();
        reqHeader.put(RequestHeaderConstant.X_ZSERVICE_SYSTEMCODE, appId);
        String reqData = JSON.toJSONString(feedbackIds);
        String str = StringUtils.join(appId, reqData);
        String sha256Sign =  Sha256Util.generateSha256Sign(str, appSecret);
        reqHeader.put(RequestHeaderConstant.X_ZSERVICE_REQSIGN, sha256Sign);

        String res = HttpClientUtil.httpPostWithJSON(queryUrl, JSON.toJSONString(feedbackIds),reqHeader);
        Type type = new TypeReference<ServiceData<FeedbackQueryDTO>>() {
        }.getType();
        res = JsonSanitizer.sanitize(res);
        ServiceData<FeedbackQueryDTO> rep = JSON.parseObject(res, type);
        return new MsaRpcResponse<>(rep);
    }

    public MsaRpcResponse<FeedBackUrlDTO> getAddUrl(MsaRpcRequest<FeedbackAddUrlParam> request) throws RouteException {
        Map<String, String> reqHeader = request.fetchHeaderMap();
        reqHeader.put(RequestHeaderConstant.X_ZSERVICE_SYSTEMCODE,appId);

        FeedbackAddUrlParam feedbackAddUrlParam = request.getBody();
        String reqData = JSON.toJSONString(feedbackAddUrlParam);

        String str = StringUtils.join(appId, reqData);
        String sha256Sign =  Sha256Util.generateSha256Sign(str,appSecret);
        reqHeader.put(RequestHeaderConstant.X_ZSERVICE_REQSIGN,sha256Sign);

        String res = HttpClientUtil.httpPostWithJSON(addUrl, reqData, reqHeader);
        Type type = new TypeReference<ServiceData<FeedBackUrlDTO>>() {
        }.getType();
        res = JsonSanitizer.sanitize(res);
        ServiceData<FeedBackUrlDTO> rep = JSON.parseObject(res, type);
        return new MsaRpcResponse<>(rep);
    }






}
