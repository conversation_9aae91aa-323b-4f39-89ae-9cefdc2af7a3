package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionDetailsVO;

/**
 * <AUTHOR>
 * @title: ExhibitionDetailsAppService
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会详情接口类
 * @date 2023/9/1114:34
 */
public interface ExhibitionDetailsAppService {

    /**
     * 获取展会详情
     * @param exhibitionRowId 展会Id
     * @return
     * <AUTHOR>
     * @date 2023/9/11
     */
    ExhibitionDetailsVO getExhibitionDetails(String exhibitionRowId);

    /**
     * 完善展会详情
     * @param exhibitionDetails 展会详情
     * @return
     * <AUTHOR>
     * @date 2023/9/11
     */
    void perfectExhibition(ExhibitionDetailsVO exhibitionDetails);
}
