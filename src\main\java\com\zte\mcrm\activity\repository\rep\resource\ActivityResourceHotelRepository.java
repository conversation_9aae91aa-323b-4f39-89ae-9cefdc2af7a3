package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-16 17:14
 **/
public interface ActivityResourceHotelRepository {
    /**
     * description 批量添加活动相关酒店信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午3:55
     */
    int insertSelective(List<ActivityResourceHotelDO> recordList);

    /**
     * description 根据主键更新活动相关酒店信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午4:02
     */
    int updateByPrimaryKeySelective(ActivityResourceHotelDO record);
    /**
     * description 根据活动id批量查询活动相关酒店信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午3:57
     */
    Map<String, List<ActivityResourceHotelDO>> queryActivityResourceHotelsByActivityRowIds(List<String> activityRowIds);

    /**
     * 根据rowId,批量软删除
     *
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量更新数据
     * @param records
     * @return
     */
    void batchUpdate(List<ActivityResourceHotelDO> records);
}

