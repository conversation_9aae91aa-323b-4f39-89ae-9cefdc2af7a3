package com.zte.mcrm.activity.repository.model.log;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 日志DI
 *
 * @author: 汤踊10285568
 * @date: 2023/9/2 13:26
 */
@Getter
@Setter
@ToString
public class ActivityLogDO {

    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 关联的业务ID
     */
    private String contactPrimaryId;

    /**
     * 关联的业务类型
     *
     * @see com.zte.mcrm.activity.common.enums.activity.ContactTypeEnum
     */
    private String contactType;

    /**
     * 数据
     */
    private String data;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;

}
