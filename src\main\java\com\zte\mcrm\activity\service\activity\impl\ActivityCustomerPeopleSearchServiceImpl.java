package com.zte.mcrm.activity.service.activity.impl;

import com.zte.mcrm.activity.common.enums.ContactStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.service.activity.ActivityCustomerPeopleSearchService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.vo.ContactVO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户活动客户查询
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
@Service
public class ActivityCustomerPeopleSearchServiceImpl implements ActivityCustomerPeopleSearchService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityCustomerPeopleSearchServiceImpl.class);

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private AccountAdapter accountAdapter;
    /**
     * 查询活动创建中用户最近使用的客户参与人
     *
     * @param request
     * @return {@link List < ActivityCustomerPeopleVO >}
     * <AUTHOR>
     * @date 2023/5/17 下午2:59
     */
    @Override
    public List<ContactVO> searchRecentlyCustomerPeoples(BizRequest<PageQuery<ActivityRecentlySearchParam>> request) {
        PageQuery<ActivityRecentlySearchParam> pageable = request.getParam();
        pageable.setCount(Boolean.FALSE);
        ActivityRecentlySearchParam param
                = Objects.nonNull(pageable.getParam()) ? pageable.getParam() : new ActivityRecentlySearchParam();
        param.setEmpNo(request.getEmpNo());
        if (CollectionUtils.isEmpty(param.getActivityStatusList())) {
            param.setActivityStatusList(ActivityStatusEnum.getEffectiveActivityStatus());
        }
        pageable.setParam(param);
        List<ActivityRelationCustPeopleDO> custPeopleDOList
                = activityRelationCustPeopleRepository.selectRecentlyCustomerPeopleByUser(pageable);
        if (CollectionUtils.isEmpty(custPeopleDOList)) {
            return Collections.emptyList();
        }
        List<String> accountCode = custPeopleDOList.stream().map(ActivityRelationCustPeopleDO::getCustomerCode).distinct().collect(Collectors.toList());
        List<String> conPerNums = custPeopleDOList.stream()
                .map(ActivityRelationCustPeopleDO::getContactNo).distinct().collect(Collectors.toList());
        List<ContactVO> contactList = this.fetchContactPerson(conPerNums);
        contactList = contactList.stream().filter(e -> accountCode.contains(e.getOuNum()) && ContactStatusEnum.CONTACT_STATUS_VALID.isMe(e.getStatusCode())).collect(Collectors.toList());
        contactList = contactList.stream().filter(e -> ContactStatusEnum.CONTACT_STATUS_VALID.isMe(e.getStatusCode())).collect(Collectors.toList());
        return contactList;
    }

    /**
     * 根据编号批量查询联系人
     *
     * @param conPerNums
     * @return {@link List< ContactVO>}
     * <AUTHOR>
     * @date 2023/5/28 下午2:14
     */
    @Override
    public List<ContactVO> fetchContactPerson(List<String> conPerNums) {
        try {
            List<ContactVO> contactVOList = accountAdapter.getContactNums(conPerNums);
            return CollectionUtils.isEmpty(contactVOList) ? Collections.emptyList() : contactVOList;
        } catch (Exception e) {
            logger.error("获取客户联系人异常, {}", conPerNums, e);
        }
        return Collections.emptyList();
    }
}
