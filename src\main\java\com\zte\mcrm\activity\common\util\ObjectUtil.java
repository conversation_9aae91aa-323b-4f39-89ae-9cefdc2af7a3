package com.zte.mcrm.activity.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR> suntugui
 * @date 2024/8/5 18:05
 */
public class ObjectUtil {

    /**
     * 根据属性过滤，判断是否满足条件
     * @param t
     * @param filterValue
     * @param function
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T,R> boolean filterByProperty(T t, R filterValue, Function<T, R> function) {
        if (Objects.isNull(filterValue)) {
            return true;
        }
        if (filterValue instanceof String && StringUtils.isBlank((String) filterValue)) {
            return true;
        }
        return Objects.equals(filterValue, function.apply(t));
    }

}
