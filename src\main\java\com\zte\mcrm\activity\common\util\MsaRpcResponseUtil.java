package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;

import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class MsaRpcResponseUtil {

    /**
     * 构建成功的结果
     *
     * @param bo
     * @param <T>
     * @return
     */
    public static <T> MsaRpcResponse<T> successRes(T bo) {
        return new MsaRpcResponse<>(MsaRpcResponse.SUCCESS_CODE, "", "", bo);
    }

    /**
     * 构建成功的结果
     *
     * @param msg 错误信息
     * @param <T>
     * @return
     */
    public static <T> MsaRpcResponse<T> fail(String msg) {
        return new MsaRpcResponse<>(MsaRpcResponse.OUT_SERVERERROR_CODE, msg, "", null);
    }

    /**
     * 转换为BizResult
     *
     * @param rpcResponse rpc结果
     * @param <T>
     * @return
     */
    public static <T> BizResult<T> transToResult(MsaRpcResponse<T> rpcResponse) {
        return transToResult(rpcResponse, Function.identity());
    }

    /**
     * 转换为BizResult
     *
     * @param rpcResponse rpc结果
     * @param convert     数据转换器
     * @param <T>
     * @return
     */
    public static <F, T> BizResult<T> transToResult(MsaRpcResponse<F> rpcResponse, Function<F, T> convert) {
        BizResult<T> res = BizResult.buildBizResult(rpcResponse.getCode(), rpcResponse.getMsg(), convert.apply(rpcResponse.getBo()));
        if (rpcResponse.getOther() != null) {
            rpcResponse.getOther().forEach(res::addExtMsg);
        }
        return res;
    }

    /**
     * @param oriRes
     * @param convert
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> MsaRpcResponse<T> copy(MsaRpcResponse<F> oriRes, Function<F, T> convert) {
        T data = null;
        if (oriRes.getBo() != null && convert != null) {
            data = convert.apply(oriRes.getBo());
        }

        MsaRpcResponse<T> res = new MsaRpcResponse<>(oriRes.getCode(), oriRes.getMsg(), oriRes.getMsgId(), data);
        res.setEx(oriRes.getEx());
        res.setOther(oriRes.getOther());
        res.setRequestJson(oriRes.getRequestJson());
        res.setResponseJson(oriRes.getResponseJson());

        return res;
    }

}
