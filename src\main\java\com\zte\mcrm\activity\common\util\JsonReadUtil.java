package com.zte.mcrm.activity.common.util;

import java.io.File;
import java.io.IOException;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;


/**
 * 读取json文件
 *
 * <AUTHOR> 10317843
 * @date 2023/05/23
 */
public class JsonReadUtil {

    public static <T> T readToObject(String fileName, Class<T> clazz) {
        return JSON.parseObject(readToString(fileName), clazz);
    }

    public static <T> T readToObject(File jsonFile, Class<T> clazz) {
        return JSON.parseObject(readToString(jsonFile), clazz);
    }

    public static <T> List<T> readToArray(String fileName, Class<T> clazz) {
        return JSON.parseArray(readToString(fileName), clazz);
    }

    public static <T> List<T> readToArray(File jsonFile, Class<T> clazz) {
        return JSON.parseArray(readToString(jsonFile), clazz);
    }

    public static <T> T readToObject(String fileName, TypeReference<T> typeReference) {
        return JSON.parseObject(readToString(fileName), typeReference);
    }

    public static <T> T readToObject(File jsonFile, TypeReference<T> typeReference) {
        return JSON.parseObject(readToString(jsonFile), typeReference);
    }

    public static String readToString(String filePath) {
        ClassPathResource resource = new ClassPathResource(filePath);
        File file;
        try {
            file = resource.getFile();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return readToString(file);
    }

    public static String readToString(File jsonFile) {
        try {
            return FileUtils.readFileToString(jsonFile, "UTF-8");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
