package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationDO;
import com.zte.mcrm.activity.web.controller.reservation.param.ActivityResourceReservationParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityResourceReservationRepository {

    /**
     * 添加申请的资源预约（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivityResourceReservationDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityResourceReservationDO record);

    /**
     * 根据主键获取记录
     * @param rowId
     * @return
     */
    ActivityResourceReservationDO selectByPrimaryKey(String rowId);

    /**
     * 获取当前过期的资源预约申请单
     * @param size
     * @return
     */
    List<ActivityResourceReservationDO> fetchExpiredReservation(int size);

    /**
     * 查询活动关联的所有预约资源信息
     *
     * @param reservationParam 活动入参
     * @return
     */
    List<ActivityResourceReservationDO> queryAllReservationForActivity(ActivityResourceReservationParam reservationParam);

    /***
     * <p>
     * 逻辑删除预约资源信息记录
     *
     * </p>
     * <AUTHOR>
     * @since 2024/4/28 下午4:44
     * @param operator 操作人
     * @param activityRowId 活动id
     * @return int
     */
    int softDeleteResourceReservationRecords(String operator, String activityRowId);
}
