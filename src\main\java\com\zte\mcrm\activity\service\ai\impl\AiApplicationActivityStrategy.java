package com.zte.mcrm.activity.service.ai.impl;

import com.alibaba.fastjson.JSON;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.application.exhibition.ExhibitionCreateAppService;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.AccountInfoAuthTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.MainCustEnum;
import com.zte.mcrm.activity.common.enums.ai.BusinessTypeEnum;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.accountinfo.CustomerPersonService;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerPersonDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.ContactPersonParam;
import com.zte.mcrm.activity.integration.ai.AiAssistantService;
import com.zte.mcrm.activity.integration.ai.IcenterService;
import com.zte.mcrm.activity.integration.ai.dto.*;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.service.activity.ActivityService;
import com.zte.mcrm.activity.service.ai.AiApplicationStrategy;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.web.controller.activity.vo.CustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.CustUnitInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.ZtePeopleVO;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ActivityBaseInfoVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationVO;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.constant.AreaConstant;
import com.zte.mcrm.adapter.dto.CountryAndCityDTO;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.Account;
import com.zte.mcrm.adapter.vo.MdmAreaVO;
import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.UNDER_LINE;
import static com.zte.mcrm.activity.common.constant.DateConstants.YYYY_MM_DD_HH_MM;
import static com.zte.mcrm.activity.common.constant.DictConstant.INTERNAL_CUSTOMER_TYPE;
import static com.zte.mcrm.activity.common.constant.NumberConstant.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.PC_AI_ACTIVITY;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.LECTURER;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.PARTICIPANTS;
import static com.zte.mcrm.activity.service.evaluation.convert.ActivityEvaluationConvert.*;
import static com.zte.mcrm.adapter.constant.AreaConstant.*;
import static com.zte.mcrm.common.cloududm.UdmConst.SEPARATOR_SLASH;

/**
 * @author: 汤踊********+
 * @date: 2024/7/23 15:47
 */
@Slf4j
@Service
public class AiApplicationActivityStrategy implements AiApplicationStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AiApplicationActivityStrategy.class);

    @Autowired
    private AiAssistantService aiAssistantService;
    @Autowired
    private AccountAdapter accountAdapter;
    @Autowired
    private CustomerPersonService customerPersonService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ExhibitionCreateAppService createAppService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActivityISearchService iSearchService;
    @Autowired
    private IcenterService icenterService;
    @Autowired
    private ActivityInfoRepository infoRepository;
    @Autowired
    private DictService dictService;


    /**
     * 判断是否是该业务
     *
     * @param businessType
     * @return
     */
    @Override
    public boolean support(String businessType) {
        if (BusinessTypeEnum.ACTIVITY.isMe(businessType)) {
            return true;
        }
        return false;
    }

    /**
     * AI创建拓展活动
     *
     * @param req
     */
    @Override
    public String processBusiness(AiApplicationVO req) {
        AiAssistantResponseDTO responseDTO = aiAssistantService.aiAssistantProcess(req.getContent());
        if (Objects.isNull(responseDTO)) {
            return StringUtils.EMPTY;
        }
        //客户联系人
        List<AiAssistantCustPeopleInfoDTO> listCustPeopleInfo = responseDTO.getListCustPeopleInfo();
        //主客户信息
        AiAssistantMainCustDTO mainCust = responseDTO.getMainCust();
        //国家地区
        List<AiAssistantCountryCityDTO> countryCity = responseDTO.getCountryCity();
        //我司参与人列表
        List<String> listZtePeopleInfo =Optional.ofNullable(responseDTO.getListZtePeopleInfo()).orElse(Lists.newArrayList()) ;
        //讲师人员
        List<String> lecturers = Optional.ofNullable(responseDTO.getLecturers()).orElse(Lists.newArrayList());
        listZtePeopleInfo.removeAll(lecturers);
        ActivityBaseInfoVO activityVO = new ActivityBaseInfoVO();
        //处理基本信息
        processBaseInfo(responseDTO, activityVO, req);
        //处理客户及客户联系人信息
        processCustPeople(activityVO, listCustPeopleInfo, mainCust);
        //处理我司参与人列表问题
        processZtePeople(activityVO, listZtePeopleInfo, PARTICIPANTS.getCode());
        //处理讲师人员信息
        processZtePeople(activityVO, lecturers, LECTURER.getCode());
        //处理我司参与人
        processZteParticant(activityVO, req.getContent());
        //处理国家地区信息
        processCountryCity(activityVO, countryCity);
        //保存活动信息
        String activityRowId = activityService.saveActivityBaseInfo(activityVO);
        iSearchService.sendActivityDataToISearch(activityRowId);
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        // 填充活动信息
        req.setOther(activityInfoDO);
        return activityInfoDO.getActivityRequestNo();
    }


    /**
     * 处理国家城市信息
     *
     * @param activityVO  活动基本信息
     * @param countryCity 国家城市
     */
    protected void processCountryCity(ActivityBaseInfoVO activityVO, List<AiAssistantCountryCityDTO> countryCity) {
        if (CollectionUtils.isEmpty(countryCity) || StringUtils.isBlank(countryCity.get(ZERO).getCityCodeName())) {
            return;
        }
        MdmAreaVO areaVO = new MdmAreaVO();
        areaVO.setPageNo(NUMBER_LONG_ONE);
        areaVO.setPageSize(NUMBER_LONG_FIFTY);
        areaVO.setAreaDefaultUrl(QUERY_SERACH_GJDQ);
        areaVO.setSynCode(QUERY_SERACH_GJDQ_SYS_CODE);
        areaVO.setDesc30(STATUS_FLAG_T);
        areaVO.setDesc2(AREA_TYPE_CITY);
        if (LANGUAGE_EN.equals(HeadersProperties.getXLangId())) {
            areaVO.setDesc4("%%" + countryCity.get(ZERO).getCityCodeName() + "%%");
        } else {
            areaVO.setDesc3("%%" + countryCity.get(ZERO).getCityCodeName() + "%%");
        }
        List<MdmAreaDTO> listMdm = createAppService.queryCountryAndCity(BizRequestUtil.createWithCurrentUser(areaVO));
        if (CollectionUtils.isEmpty(listMdm)) {
            return;
        }
        MdmAreaDTO areaDTO = listMdm.get(ZERO);
        String countryAndCityCode = areaDTO.getDesc70();
        if (StringUtils.isNotBlank(countryAndCityCode)) {
            countryAndCityCode = JsonSanitizer.sanitize(countryAndCityCode);
            CountryAndCityDTO countryAndCity = JSON.parseObject(countryAndCityCode, CountryAndCityDTO.class);
            String countryAndCityName = areaDTO.getDesc50();
            activityVO.setCountryCodeName(countryAndCityName.substring(NumberConstant.ZERO, countryAndCityName.indexOf(AreaConstant.SEPARATION_CHARACTER)));
            activityVO.setCityCodeName(countryAndCityName.substring(countryAndCityName.lastIndexOf(AreaConstant.SEPARATION_CHARACTER) + 1));
            if (Objects.nonNull(countryAndCity.getCountry()) && Objects.nonNull(countryAndCity.getCity())) {
                activityVO.setCountryCode(countryAndCity.getCountry().getCode());
                activityVO.setCityCode(countryAndCity.getCity().getCode());
            }
        } else {
            activityVO.setCountryCode(areaDTO.getCode());
            activityVO.setCountryCodeName(areaDTO.getDesc3());
        }
    }


    /**
     * 查询我司参与人
     *
     * @param activityVO        活动基本数据
     * @param listZtePeopleInfo 活动基本数据
     */
    protected void processZtePeople(ActivityBaseInfoVO activityVO, List<String> listZtePeopleInfo, String peopleType) {
        listZtePeopleInfo = listZtePeopleInfo.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listZtePeopleInfo)) {
            return;
        }
        List<ContactDTO> listZtePeople =  Optional.ofNullable(icenterService.getZteContact(listZtePeopleInfo)).orElse(Lists.newArrayList());
        List<String> peopleCodeSet = listZtePeople.stream().map(ContactDTO::getShortId).distinct().collect(Collectors.toList());
        List<EmployeeInfoDTO> list = userCenterService.getUserInfoList(new ArrayList<>(peopleCodeSet));
        if (CollectionUtils.isNotEmpty(list)) {
            List<ZtePeopleVO> listPeople = covertZtePeopleResult(list, peopleType);
            if (CollectionUtils.isNotEmpty(activityVO.getListZtePeopleInfo())) {
                activityVO.getListZtePeopleInfo().addAll(listPeople);
            } else {
                activityVO.setListZtePeopleInfo(listPeople);
            }
        }
    }

    /**
     * 申请人默认我司参与人
     *
     * @param activityVO
     */
    protected void processZteParticant(ActivityBaseInfoVO activityVO, String content) {
        List<EmployeeInfoDTO> list = userCenterService.getUserInfoList(com.google.common.collect.Lists.newArrayList(HeadersProperties.getXEmpNo()));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        EmployeeInfoDTO employeeInfoDTO = Optional.ofNullable(list.get(0)).orElse(new EmployeeInfoDTO());
        //我司参与人信息处理
        String orgFullName = Optional.ofNullable(employeeInfoDTO.getOrgFullName()).orElse(StringUtils.EMPTY);
        String orgFullNameSub = orgFullName.substring(orgFullName.indexOf(SEPARATOR_SLASH) + 1);
        String reqContent = Optional.ofNullable(content).orElse(StringUtils.EMPTY);
        List<ZtePeopleVO> listZtePeople = Optional.ofNullable(activityVO.getListZtePeopleInfo()).orElse(Lists.newArrayList());
        listZtePeople = listZtePeople.stream().filter(e -> StringUtils.isNotBlank(e.getDeptFullName()))
                .filter(e -> e.getDeptFullName().contains(orgFullNameSub)).collect(Collectors.toList());
        listZtePeople = listZtePeople.stream().filter(e -> reqContent.contains(e.getPeopleName())).collect(Collectors.toList());
        activityVO.setListZtePeopleInfo(listZtePeople);
        //申请人信息处理
        List<ZtePeopleVO> listPeople = covertZtePeopleResult(list, PARTICIPANTS.getCode());
        if (CollectionUtils.isNotEmpty(activityVO.getListZtePeopleInfo())) {
            activityVO.getListZtePeopleInfo().addAll(listPeople);
        } else {
            activityVO.setListZtePeopleInfo(listPeople);
        }
        activityVO.setApplyPeopleNo(employeeInfoDTO.getEmpUIID());
        activityVO.setApplyPeopleName(employeeInfoDTO.getEmpName());
        activityVO.setApplyDepartmentNo(employeeInfoDTO.getOrgID());
        activityVO.setApplyDepartmentName(employeeInfoDTO.getOrgName());
        activityVO.setApplyFullDepartmentNo(employeeInfoDTO.getOrgIDPath());

    }

    /**
     * 查询客户和联系人信息
     *
     * @param activityVO         活动基本数据
     * @param listCustPeopleInfo 客户联系人信息
     * @param mainCust           主客户
     */
    protected void processCustPeople(ActivityBaseInfoVO activityVO,
                                     List<AiAssistantCustPeopleInfoDTO> listCustPeopleInfo,
                                     AiAssistantMainCustDTO mainCust) {
        String customerCode = StringUtils.EMPTY;
        if (mainCust != null) {
            //主客户信息
            String mainCustomerCode = mainCust.getCustomerCode();
            String mainCustomerName = mainCust.getCustomerName();
            List<Account> listAccount = fetchAccount(mainCustomerCode, mainCustomerName);
            if (CollectionUtils.isNotEmpty(listAccount)) {
                Account account = listAccount.get(ZERO);
                customerCode = account.getAccountNum();
                List<CustUnitInfoVO> listCust = covertCustUnit(account, MainCustEnum.Y.getCode());
                activityVO.setListCustInfo(listCust);
            }
        }
        //客户联系人信息
        if (CollectionUtils.isNotEmpty(listCustPeopleInfo)) {
            List<String> listName = listCustPeopleInfo.stream().filter(e -> StringUtils.isNotBlank(e.getContactName()))
                    .map(AiAssistantCustPeopleInfoDTO::getContactName)
                    .distinct().collect(Collectors.toList());
            CustUnitInfoVO custUnitInfoVO = CollectionUtils.isEmpty(activityVO.getListCustInfo()) ? null : activityVO.getListCustInfo().get(ZERO);
            //查询字典
            List<DictLanguageDTO> dictLanguageDTOS = dictService.exactQueryListByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + activityVO.getActivityType());
            String joinDictKey = dictLanguageDTOS.stream().map(DictLanguageDTO::getDictKey).collect(Collectors.joining(","));
            log.info("[内部客户字典]查询类型：{}，字典key为：{}",INTERNAL_CUSTOMER_TYPE,joinDictKey);
            if (!joinDictKey.contains(customerCode)) {
                List<OutCustomerPersonDTO> listContact = fetchContactPerson(customerCode, listName);
                List<CustPeopleInfoVO> listCustPeople = covertContactResult(listContact, custUnitInfoVO);
                activityVO.setListCustPeopleInfo(listCustPeople);
            } else {
                List<EmployeeInfoDTO> listZteContact = listZteCust(listName);
                listZteContact = listZteContact.stream().filter(e -> listName.contains(e.getEmpName())).collect(Collectors.toList());
                List<CustPeopleInfoVO> listCustPeople = covertZteContactResult(listZteContact, custUnitInfoVO);
                activityVO.setListCustPeopleInfo(listCustPeople);
            }
        }
    }

    /**
     * 客户联系人
     *
     * @param listZtePeopleInfo
     * @return
     */
    protected List<EmployeeInfoDTO> listZteCust(List<String> listZtePeopleInfo) {
        listZtePeopleInfo = listZtePeopleInfo.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listZtePeopleInfo)) {
            return Lists.newArrayList();
        }
        List<ContactDTO> listZtePeople =  Optional.ofNullable(icenterService.getZteContact(listZtePeopleInfo)).orElse(Lists.newArrayList());
        List<String> peopleCodeSet = listZtePeople.stream().map(ContactDTO::getShortId).distinct().collect(Collectors.toList());
        String applyPeopleCode = Optional.ofNullable(HeadersProperties.getXEmpNo()).orElse(StringUtils.EMPTY);
        peopleCodeSet.add(HeadersProperties.getXEmpNo());
        List<EmployeeInfoDTO> listEmploy = Optional.ofNullable(userCenterService.getUserInfoList(new ArrayList<>(peopleCodeSet))).orElse(Lists.newArrayList());
        EmployeeInfoDTO apply = listEmploy.stream().filter(e ->  applyPeopleCode.equals(e.getEmpUIID())).findFirst().orElse(new EmployeeInfoDTO());
        String departNum = Optional.ofNullable(apply.getOrgFullName()).orElse(StringUtils.EMPTY);
        String orgFullNameSub = departNum.substring(departNum.indexOf(SEPARATOR_SLASH) + 1);
        listEmploy = listEmploy.stream().filter(e->StringUtils.isNotBlank(e.getOrgFullName()))
                .filter(e -> e.getOrgFullName().contains(orgFullNameSub)).collect(Collectors.toList());
        return listEmploy;

    }

    /**
     * 获取客户联系人信息
     *
     * @param customerCode 客户编码
     * @param listName     联系人姓名
     * @return
     */
    protected List<OutCustomerPersonDTO> fetchContactPerson(String customerCode, List<String> listName) {
        if (StringUtils.isEmpty(customerCode) || CollectionUtils.isEmpty(listName)) {
            return Lists.newArrayList();
        }
        ContactPersonParam param = new ContactPersonParam();
        param.setCustomerCodes(com.google.common.collect.Lists.newArrayList(customerCode));
        MsaRpcResponse<List<OutCustomerPersonDTO>> response = customerPersonService.getAccountPersonList(MsaRpcRequestUtil.createWithCurrentUser(param));
        List<OutCustomerPersonDTO> listContact = Optional.ofNullable(response.getBo()).orElse(Lists.newArrayList());
        return listContact.stream().filter(e -> listName.contains(e.getName())).collect(Collectors.toList());
    }

    /**
     * 处理基本信息
     *
     * @param responseDTO 方法参数
     * @param activityVO  活动实体类
     * @author: 汤踊********
     * @date: 2024/7/23 15:47
     */
    protected void processBaseInfo(AiAssistantResponseDTO responseDTO, ActivityBaseInfoVO activityVO, AiApplicationVO req) {
        activityVO.setActivityType(req.getActivityType());
        activityVO.setActivityContent(responseDTO.getActivityContent());
        activityVO.setActivityPlace(responseDTO.getActivityPlace());
        activityVO.setActivityTitle(responseDTO.getActivityTitle());
        activityVO.setCommunicationWay(responseDTO.getCommunicationWay());
        activityVO.setStartTime(getActivityTime(responseDTO.getDate(), responseDTO.getStartMoment()));
        activityVO.setEndTime(getActivityTime(responseDTO.getDate(), responseDTO.getEndMoment()));
        activityVO.setOriginType(PC_AI_ACTIVITY.getType());
    }

    /**
     * 处理活动时间
     *
     * @param date 日期
     * @param time 时间
     * @return
     * @author: 汤踊********
     * @date: 2024/7/23 15:47
     */
    protected Date getActivityTime(String date, String time) {
        try {
            if (StringUtils.isNotBlank(date) && StringUtils.isNotBlank(time)) {
                String activityTime = date + " " + time;
                return DateUtils.convertStringToDate(activityTime, YYYY_MM_DD_HH_MM);
            }
        } catch (Exception e) {
            logger.error("日期转换失败", e);
        }
        return null;
    }

    /**
     * 获取客户
     *
     * @param customerCodes
     * @param customerName
     * @return
     * @author: 汤踊********
     * @date: 2024/7/23 15:47
     */
    protected List<Account> fetchAccount(String customerCodes, String customerName) {
        if (StringUtils.isAllBlank(customerCodes, customerName)) {
            return Collections.emptyList();
        }
        Account account = new Account();
        if (StringUtils.isNotBlank(customerCodes)) {
            account.setAccntNumList(com.google.common.collect.Lists.newArrayList(customerCodes));
        }
        account.setAccountName(customerName);
        try {
            ServiceData<List<Account>> serviceData = accountAdapter.authorityAccountWithAccount1(account,
                    AccountInfoAuthTypeEnum.ALL.getCode(), ONE, ONE_THOUSANDS);
            if (CollectionUtils.isNotEmpty(serviceData.getBo())) {
                List<Account> list = serviceData.getBo();
                list = list.stream().filter(e -> customerName.equals(e.getAccountName())).collect(Collectors.toList());
                list.forEach(e -> e.setBuPath(e.getBuName()));
                return list;
            }
        } catch (Exception e) {
            logger.error("获取客户信息异常, {}", customerCodes, e);
        }
        return Collections.emptyList();
    }


}
