package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/***
 * 异步任务执行器
 * <pre>
 *     主要用于异步任务处理，将和业务无关的其他耗时任务（应该是较短耗时）异步处理。比如：异步发kafka、异步发送邮件、异步处理耗时任务……
 *     建议：尽可能的不要执行非常耗时的任务，如果真有可以和并行任务执行器一起使用
 *     注意：不要将同步对外的接口过多的使用异步任务执行器，否则线程最大数耗尽，线程池将成为同步等待的评价
 * </pre>
 *
 * <AUTHOR>
 */
public class AsyncTaskExecutor extends BaseTaskExecutor {

    public AsyncTaskExecutor() {
        super("Async", new ThreadPoolExecutor.AbortPolicy());
        initParam(8, 32, 120, new LinkedBlockingQueue<>());
    }
}
