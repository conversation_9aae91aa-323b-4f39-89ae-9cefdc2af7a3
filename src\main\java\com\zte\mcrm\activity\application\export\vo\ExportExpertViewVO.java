package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-31 17:22
 **/
@Getter
@Setter
public class ExportExpertViewVO {

    @Excel(name = "序号", orderNum = "1")
    private String index;

    @Excel(name = "事业部", orderNum = "2")
    private String org2Name;

    @Excel(name = "片区", orderNum = "3")
    private String org3Name;

    @Excel(name = "代表处", orderNum = "4")
    private String org4Name;

    @Excel(name = "会见日期", orderNum = "5")
    private String scheduleDate;

    @Excel(name = "时间", orderNum = "6")
    private String scheduleTime;

    @Excel(name = "地点", orderNum = "7")
    private String placeName;

    @Excel(name = "日程名称", orderNum = "8")
    private String scheduleItemName;

    @Excel(name = "二层领导", orderNum = "9")
    private String zteLeader;

    @Excel(name = "专家", orderNum = "10")
    private String zteExpertDes;

    @Excel(name = "客户单位", orderNum = "11")
    private String customerName;

    @Excel(name = "Account", orderNum = "12")
    private String mktName;

    @Excel(name = "Group", orderNum = "13")
    private String mtoName;

    @Excel(name = "国家", orderNum = "14")
    private String localName;

    @Excel(name = "客户参与人及职务", orderNum = "15")
    private String contractDesc;

    @Excel(name = "现场接口人及电话", orderNum = "16")
    private String sitePeopleDesc;

    @Excel(name = "申请人", orderNum = "17")
    private String applicant;

    @Excel(name = "交流方向", orderNum = "18")
    private String communicateDirection;

    @Excel(name = "备注", orderNum = "19")
    private String remark;
}
