package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO;

public interface ExhibitionRelationHotelMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationHotelDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationHotelDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationHotelDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationHotelDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationHotelDO record);
}