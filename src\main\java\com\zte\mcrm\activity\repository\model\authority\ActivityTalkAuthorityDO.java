package com.zte.mcrm.activity.repository.model.authority;

import java.util.Date;

/**
 * table:activity_talk_authority -- 
 */
public class ActivityTalkAuthorityDO {
    /** 主键 */
    private String rowId;

    /** 人员编码。如：参与人的员工编码 */
    private String peopleCode;

    /** 权限类型。枚举：TalkAuthEnum */
    private String authorityType;

    /** 字段扩展值(交流方向为1级编码，组织编码为全路径编码) */
    private String authorityExtendValue;

    /** 权限内容 */
    private String authorityValue;

    /** 授权范围 */
    private Integer authRange;

    /** 授权批次id */
    private String batchId;

    /** 对应谈参权限的描述内容 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode == null ? null : peopleCode.trim();
    }

    public String getAuthorityType() {
        return authorityType;
    }

    public void setAuthorityType(String authorityType) {
        this.authorityType = authorityType == null ? null : authorityType.trim();
    }

    public String getAuthorityExtendValue() {
        return authorityExtendValue;
    }

    public void setAuthorityExtendValue(String authorityExtendValue) {
        this.authorityExtendValue = authorityExtendValue == null ? null : authorityExtendValue.trim();
    }

    public String getAuthorityValue() {
        return authorityValue;
    }

    public void setAuthorityValue(String authorityValue) {
        this.authorityValue = authorityValue == null ? null : authorityValue.trim();
    }

    public Integer getAuthRange() {
        return authRange;
    }

    public void setAuthRange(Integer authRange) {
        this.authRange = authRange;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}