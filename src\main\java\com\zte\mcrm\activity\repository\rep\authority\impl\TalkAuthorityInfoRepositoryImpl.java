package com.zte.mcrm.activity.repository.rep.authority.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.authority.ActivityTalkAuthorityExtMapper;
import com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO;
import com.zte.mcrm.activity.repository.model.authority.GroupedActivityTalkAuthorityDO;
import com.zte.mcrm.activity.repository.rep.authority.TalkAuthorityInfoRepository;
import com.zte.mcrm.activity.repository.rep.authority.param.TalkAuthorityQuery;
import com.zte.mcrm.activity.web.controller.authority.params.TalkAuthorityChangeParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/12/27 16:39
 */
@Service
public class TalkAuthorityInfoRepositoryImpl implements TalkAuthorityInfoRepository {

    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private ActivityTalkAuthorityExtMapper talkAuthorityExtMapper;

    @Override
    public int batchInsertSelective(List<ActivityTalkAuthorityDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }
        List<String> batchIds = recordList.stream()
                .map(ActivityTalkAuthorityDO::getBatchId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        String batchId = CollectionUtils.isNotEmpty(batchIds) ? batchIds.get(NumberConstant.ZERO) : keyIdService.getKeyId();
        recordList.forEach(record -> {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            record.setCreatedBy(Optional.ofNullable(record.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setCreationDate(new Date());
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.Y.getCode());
            record.setBatchId(batchId);
        });

        return talkAuthorityExtMapper.insertByBatch(recordList);
    }

    @Override
    public List<ActivityTalkAuthorityDO> getListByPeopleCode(String peopleCode) {
        if (StringUtils.isBlank(peopleCode)) {
            return new ArrayList<>();
        }

        return talkAuthorityExtMapper.getList(Lists.newArrayList(peopleCode));
    }

    @Override
    public List<ActivityTalkAuthorityDO> listAllTalkAuthorityInfo() {
        return talkAuthorityExtMapper.getAllRecord();
    }

    @Override
    public List<ActivityTalkAuthorityDO> queryTalkAuthorityInfoList(TalkAuthorityQuery talkAuthorityQuery) {
        if (!talkAuthorityQuery.check()) {
            return Lists.newArrayList();
        }
        List<GroupedActivityTalkAuthorityDO> activityTalkAuthorityDOList = talkAuthorityExtMapper.getListByTalkAuthorityQuery(talkAuthorityQuery);
        if (CollectionUtils.isEmpty(activityTalkAuthorityDOList)) {
            return Lists.newArrayList();
        }
        List<String> peopleCodeList = activityTalkAuthorityDOList.stream().map(GroupedActivityTalkAuthorityDO::getPeopleCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        return CollectionUtils.isEmpty(peopleCodeList) ? Lists.newArrayList() : talkAuthorityExtMapper.getList(peopleCodeList);
    }

    /**
     * 批量更新权限记录信息
     *
     * @param recordList
     * @return
     */
    @Override
    public int batchUpdateSelective(List<ActivityTalkAuthorityDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }
        for (ActivityTalkAuthorityDO record : recordList) {
            record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            record.setLastUpdateDate(new Date());
        }
        return talkAuthorityExtMapper.updateBatchByRowId(recordList);
    }

    /**
     * 根据条件查询谈参权限记录
     *
     * <AUTHOR>
     * @date 2024/11/11 下午5:07
     * @param queryParam 查询条件
     * @return java.util.List<com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO>
     */
    @Override
    public List<ActivityTalkAuthorityDO> getListCondition(TalkAuthorityChangeParam queryParam) {
        if (org.apache.commons.lang3.StringUtils.isBlank(queryParam.getPeopleCode())) {
            return new ArrayList<>();
        }
        return talkAuthorityExtMapper.getListCondition(queryParam);
    }
}
