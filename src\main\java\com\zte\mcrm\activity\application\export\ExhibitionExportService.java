package com.zte.mcrm.activity.application.export;

import com.zte.mcrm.activity.application.export.param.ExportExhibitionParam;
import com.zte.mcrm.activity.common.model.BizRequest;
import org.apache.http.entity.mime.content.ByteArrayBody;

import java.io.IOException;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午2:05
 **/
public interface ExhibitionExportService {

    /***
     * <p>
     * 导出指定展会对应领导的日程安排结果
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 下午4:34
     * @param req 领导视图导出参数
     * @return org.apache.http.entity.mime.content.ByteArrayBody
     */
    ByteArrayBody exportLeaderView(BizRequest<ExportExhibitionParam> req);

    /**
     * description 导出指定展会会议室的日程安排结果
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/26 下午2:45
     */
    ByteArrayBody exportRoomView(BizRequest<ExportExhibitionParam> req);

    /**
     * description 导出指定专家的日程安排结果
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/31 下午8:27
     */
    ByteArrayBody exportExpertView(BizRequest<ExportExhibitionParam> req);

    /**
     * description 导出报名汇总
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/31 下午2:45
     */
    ByteArrayBody exportRegistrationDetail(BizRequest<ExportExhibitionParam> req) throws IOException;
}
