package com.zte.mcrm.activity.service.activitylist.vo;

import com.zte.mcrm.activity.common.enums.activity.PendingBizTypeEnum;
import lombok.Data;

/**
 * <AUTHOR> 10333830
 * @date 2023-09-03 14:07
 */
@Data
public class ActivityOperatorFlagVO {
    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 是否可删除
     */
    private Boolean deletable;

    /**
     * 是否可作废
     */
    private Boolean voidable;

    /**
     * 是否可撤销
     */
    private Boolean cancelable;

    /**
     * 是否可变更
     */
    private Boolean changeable;

    /**
     * 是否可复制
     */
    private Boolean copyable;

    /**
     * 可转发的
     */
    private Boolean forwardable;

    /**
     * 客户接待
     */
    private Boolean receptionFlag;

    /**
     * 是否审批
     */
    private Boolean isApproval;
    /**
     * 谈参是否可以操作
     */
    private Boolean talkOperate;
    /**
     * 是否谈参管理员
     */
    private Boolean talkManager;
}
