package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-17 9:31
 **/
public interface ActivityScheduleOrchestrationVersionRepository {
    /**
     * description 批量添加活动日程编排版本信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:33
     */
    int insertSelective(List<ActivityScheduleOrchestrationVersionDO> recordList);

    /**
     * 根据主键查询信息
     * @param rowId
     * @return
     */
    ActivityScheduleOrchestrationVersionDO selectByPrimaryKey(String rowId);

    /**
     * 获取展会资源编排最新版本
     * @param query
     * @return
     */
    ActivityScheduleOrchestrationVersionDO getLastScheduleOrchestrationVersion(ActivityScheduleOrchestrationVersionQuery query);

    /**
     * 查询展会某个时间点后所有操作人的最新版本记录
     * @param exhibitionRowId 展会ID
     * @param afterPublishTime 某时间
     * @return 《员工编号，对应员工最后一次发布版本》
     */
    Map<String, ActivityScheduleOrchestrationVersionDO> queryAfterPublishTimeVersion(String exhibitionRowId, Date afterPublishTime);

    /**
     * description 根据主键更新活动日程编排版本信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:34
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationVersionDO record);

    /**
     * description 根据活动ID批量查询活动日程编排版本信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:36
     */
    List<ActivityScheduleOrchestrationVersionDO> queryActivityScheduleOrchestrationVersions(ActivityScheduleOrchestrationVersionQuery query);
}
