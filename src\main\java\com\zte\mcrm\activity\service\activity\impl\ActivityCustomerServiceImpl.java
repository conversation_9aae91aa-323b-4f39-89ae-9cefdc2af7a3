package com.zte.mcrm.activity.service.activity.impl;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.enums.ContactStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.AccountFrozenFlagEnum;
import com.zte.mcrm.activity.common.enums.activity.AccountMergeFlagEnum;
import com.zte.mcrm.activity.common.enums.activity.HrmEmpStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.SanctionedPartyEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activity.ActivityCustomerPeopleSearchService;
import com.zte.mcrm.activity.service.activity.ActivityCustomerSearchService;
import com.zte.mcrm.activity.service.activity.ActivityCustomerService;
import com.zte.mcrm.activity.service.activity.model.ActivityCustomerModel;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.web.controller.activity.vo.CustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.CustUnitInfoVO;
import com.zte.mcrm.activity.web.controller.baseinfo.param.CheckInternalParam;
import com.zte.mcrm.adapter.UserCenterPgAdapter;
import com.zte.mcrm.adapter.vo.Account;
import com.zte.mcrm.adapter.vo.ContactVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.expansion.access.external.UserDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.REG;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.UNDER_LINE;
import static com.zte.mcrm.activity.common.constant.DictConstant.INTERNAL_CUSTOMER_TYPE;
import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.service.converter.ActivityInfoConverter.transCustPeopleDo2InfoVo;

/**
 * 客户活动客户参与人查询
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
@Slf4j
@Service
public class ActivityCustomerServiceImpl implements ActivityCustomerService {

    @Autowired
    private ActivityRelationCustPeopleRepository custPeopleRepository;
    @Autowired
    private ActivityCustomerInfoRepository customerInfoRepository;
    @Autowired
    ActivityCustomerPeopleSearchService customerPeopleSearchService;
    @Autowired
    ActivityCustomerSearchService customerSearchService;
    @Autowired
    DictService dictService;
    @Autowired
    UserCenterPgAdapter userCenterPgAdapter;
    @Autowired
    ActivityInfoRepository infoRepository;

    /**
     * 判断活动是否可操作, 含有冻结/合并的客户不可进行操作
     *
     * @param request 请求
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/29 21:03
     */
    @Override
    public String operatorCheck(BizRequest<String> request) {
        String activityRowId = request.getParam();
        this.operatorCheckByActivityRowId(activityRowId);
        return activityRowId;
    }

    /**
     * 通过活动Id判断活动是否可操作, 含有冻结/合并的客户不可进行操作
     * @param activityRowId 活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 10:48
     */
    @Override
    public String operatorCheckByActivityRowId(String activityRowId) {
        ActivityCustomerModel model = new ActivityCustomerModel();
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        getActivityAccountInfoByActivityRowId(activityRowId, model);
        getActivityContactInfoByActivityRowId(activityRowId, activityInfoDO.getActivityType(), model);
        //查询字典
        List<DictLanguageDTO> dictLanguageDTOS = dictService.exactQueryListByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + activityInfoDO.getActivityType());
        String joinDictKey = dictLanguageDTOS.stream().map(DictLanguageDTO::getDictKey).collect(Collectors.joining(","));
        log.info("[内部客户字典]查询类型：{}，字典key为：{}",INTERNAL_CUSTOMER_TYPE,joinDictKey);
        model.setExcludeCustomers(joinDictKey);
        checkCustomerContact(model);
        checkCustomer(model);
        return activityRowId;
    }

    /**
     * 操作校验并更新客户信息
     * @param listCust  客户列表
     * @param acType  活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    @Override
    public List<CustUnitInfoVO> operatorCheckAndFillCustomerInfo(List<CustUnitInfoVO> listCust, String acType) {
        if (CollectionUtils.isEmpty(listCust)) {
            return Lists.newArrayList();
        }
        ActivityCustomerModel model = new ActivityCustomerModel(listCust);
        model.getCustPeopleFromCustInfoDynamic();
        operatorCheckAndFillCustomerInfo(model, acType);
        return model.getListCust();
    }

    /**
     * 操作校验并更新客户信息
     * @param listCust  客户列表
     * @param listCustPeopleInfo 客户联系人列表
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    @Override
    public void operatorCheckAndFillCustomerInfo(List<CustUnitInfoVO> listCust, List<CustPeopleInfoVO> listCustPeopleInfo, String actype) {
        ActivityCustomerModel model = new ActivityCustomerModel(listCust, listCustPeopleInfo);
        operatorCheckAndFillCustomerInfo(model, actype);
    }

    /**
     * 操作校验并更新客户信息
     * @param model  客户数据工具
     * @param acType  活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    private void operatorCheckAndFillCustomerInfo(ActivityCustomerModel model, String acType) {
        checkInternalCustPeople(model, acType);
        fillCustomerInfo(model, acType);
        checkCustomer(model);
        checkCustomerContact(model);
    }

    /**
     * 更新客户信息
     * @param listCust  客户列表
     * @param acType  活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    @Override
    public List<CustUnitInfoVO> fillCustomerInfo(List<CustUnitInfoVO> listCust, String acType) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(listCust)) {
            return Lists.newArrayList();
        }
        ActivityCustomerModel model = new ActivityCustomerModel(listCust);
        model.getCustPeopleFromCustInfoDynamic();
        fillCustomerInfo(model, acType);
        return model.getListCust();
    }

    /**
     * 校验当前活动类型下-客户是否为公司/子公司
     *
     * @param request   请求
     * @return java.lang.Boolean
     * <AUTHOR>
     * date: 2023/10/9 11:09
     */
    @Override
    public Boolean checkInternal(BizRequest<CheckInternalParam> request) {
        CheckInternalParam param = request.getParam();
        if (param == null) {
            return false;
        }
        return null != dictService.getDictByTypeAndKey(INTERNAL_CUSTOMER_TYPE + UNDER_LINE +
                param.getActivityType() , param.getCustomerCode()) ;
    }

    /**
     * 内部员工状态校验
     * @param model
     * @param acType
     */
    private void checkInternalCustPeople(ActivityCustomerModel model, String acType) {
        if (model == null) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.INTERNAL_EMP_STATUS_ERROR);
        }
        List<CustPeopleInfoVO> listCustPeopleInfo = model.getListCustPeople();
        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        List<CustPeopleInfoVO> internalCustList = listCustPeopleInfo.stream().filter(n -> fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<String> internalEmpNoList = internalCustList.stream().map(CustPeopleInfoVO::getContactNo).collect(Collectors.toList());

        Map<String, UserDetailVO> empInfoDetails = userCenterPgAdapter.getEmpInfoDetails(internalEmpNoList);
        if (MapUtils.isEmpty(empInfoDetails)) {
            return;
        }
        List<String> allStatusMsg = HrmEmpStatusEnum.getAllStatusMsg();
        List<String> abnormalEmpNoList = new ArrayList<>();
        for (String empNo : empInfoDetails.keySet()) {
            UserDetailVO userDetailVO = empInfoDetails.get(empNo);
            if (allStatusMsg.contains(userDetailVO.getEmpStatus())) {
                abnormalEmpNoList.add(empNo);
            }
        }
        if (CollectionUtils.isNotEmpty(abnormalEmpNoList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.INTERNAL_EMP_STATUS_ERROR);
        }
    }

    /**
     * 状态客户信息
     * @param model     客户数据工具
     * @param acType     活动类型
     * @return void
     * <AUTHOR>
     * date: 2023/9/18 14:40
     */
    private void fillCustomerInfo(ActivityCustomerModel model, String acType) {
        //查询字典
        List<DictLanguageDTO> dictLanguageDTOS = dictService.exactQueryListByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        String joinDictKey = dictLanguageDTOS.stream().map(DictLanguageDTO::getDictKey).collect(Collectors.joining(","));
        log.info("[内部客户字典]查询类型：{}，字典key为：{}",INTERNAL_CUSTOMER_TYPE,joinDictKey);
        Optional.ofNullable(model).ifPresent(mode -> {
            mode.setAccountList(queryAccountInfoByExternal(mode.getListCust()));
            mode.setContactVOList(getContactInfo(mode.getListCustPeople(), acType));
            mode.setExcludeCustomers(joinDictKey);
            mode.fillAccountInfo();
            mode.fillContactInfo();
        });
    }

    /**
     * 根据外部提供的联系人列表查询实际客户联系人信息
     * @param listCustPeople    客户联系人列表
     * @param acType    活动类型
     * @return java.util.List<com.zte.mcrm.adapter.vo.ContactVO>
     * <AUTHOR>
     * date: 2023/9/7 10:16
     */
    private List<ContactVO> getContactInfo(List<CustPeopleInfoVO> listCustPeople, String acType) {
        List<ContactVO> allContractVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(listCustPeople)) {
            return Lists.newArrayList();
        }
        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        List<CustPeopleInfoVO> externalCustList = listCustPeople.stream().filter(n -> !fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<String> contactNoList = externalCustList.stream().map(CustPeopleInfoVO::getContactNo)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());
        // 添加外部客户
        List<ContactVO> contactVOList = customerPeopleSearchService.fetchContactPerson(contactNoList);

        List<CustPeopleInfoVO> internalCustList = listCustPeople.stream().filter(n -> fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        // 新增内部客户
        List<ContactVO> internalContactVOList = dealInternalContractVO(internalCustList);
        allContractVOList.addAll(contactVOList);
        allContractVOList.addAll(internalContactVOList);

        return CollectionExtUtils.getListOrDefaultEmpty(allContractVOList);
    }

    /**
     * 组装内部数据
     * @param internalCustPeoples
     * @return
     */
    public List<ContactVO> dealInternalContractVO(List<CustPeopleInfoVO> internalCustPeoples) {
        List<ContactVO> contactVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(internalCustPeoples)) {
            return contactVOList;
        }
        List<String> internalEmpNoList = internalCustPeoples.stream().filter(x -> StringUtils.isNotBlank(x.getContactNo())).map(CustPeopleInfoVO::getContactNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalEmpNoList)){
            return contactVOList;
        }
        Map<String, UserDetailVO> empInfoDetails = userCenterPgAdapter.getEmpInfoDetails(internalEmpNoList);
        if (MapUtils.isEmpty(empInfoDetails)) {
            return contactVOList;
        }

        for (CustPeopleInfoVO internalPeople : internalCustPeoples) {
            ContactVO contactVO = new ContactVO();
            UserDetailVO userDetailVO = empInfoDetails.get(internalPeople.getContactNo());
            contactVO.setConPerNum(internalPeople.getContactNo());
            contactVO.setJob(StringUtils.isEmpty(userDetailVO.getPostName()) ? null : userDetailVO.getPostName().split(REG)[0]);
            contactVO.setStatus(userDetailVO.getEmpStatus());
            contactVO.setBelongDept(userDetailVO.getOrgFullName());
            contactVO.setOuNum(internalPeople.getCustomerCode());
            contactVO.setContactSanctionedPartyCode(SanctionedPartyEnum.SANCTIONED_PARTY_NO.getCode());
            contactVO.setContactSanctionedParty(SanctionedPartyEnum.SANCTIONED_PARTY_NO.getDesc());
            contactVOList.add(contactVO);
        }

        return contactVOList;
    }

    /**
     * 根据外部提供的客户列表查询实际客户信息
     * @param listCust  客户列表
     * @return java.util.List<com.zte.mcrm.adapter.vo.Account>
     * <AUTHOR>
     * date: 2023/9/7 10:00
     */
    private List<Account> queryAccountInfoByExternal(List<CustUnitInfoVO> listCust) {
        if (CollectionUtils.isEmpty(listCust)) {
            return Lists.newArrayList();
        }
        List<String> custCodeList = listCust.stream()
                .map(CustUnitInfoVO::getCustomerCode)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        return CollectionExtUtils.getListOrDefaultEmpty(customerSearchService.fetchAccount(custCodeList));
    }

    /**
     * 校验客户联系人状态
     * @param model 活动客户数据工具
     * @return void
     * <AUTHOR>
     * date: 2023/8/29 21:25
     */
    private void checkCustomerContact(ActivityCustomerModel model) {
        if (model.getContactSize() != model.getExistContactSize()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ACTIVITY_CONTACT_CODE_ERROR,StringUtils.join(model.getErrorCustomers(), CharacterConstant.COMMA));
        }
        // 校验联系人状态
        model.getContactVOList().stream().filter(x -> !model.getExcludeCustomers().contains(x.getOuNum())).forEach(this::checkContactStatus);
    }

    /**
     * 查询活动联系人信息
     *
     * @param activityRowId 活动Id
     * @param model
     * @return java.util.List<com.zte.mcrm.adapter.vo.ContactVO>
     * <AUTHOR>
     * date: 2023/8/30 10:41
     */
    private void getActivityContactInfoByActivityRowId(String activityRowId, String acType, ActivityCustomerModel model) {
        List<ActivityRelationCustPeopleDO> custPeopleDOList = CollectionExtUtils.getListOrDefaultEmpty(custPeopleRepository.queryAllByActivityRowId(activityRowId));

        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        List<ContactVO> contactVOList = new ArrayList<>();
        // 查询外部客户最新信息
        List<String> exteralCustPeopleList = custPeopleDOList.stream().map(ActivityRelationCustPeopleDO::getContactNo)
                .filter(n -> StringUtils.isNotBlank(n) && !fieldDictMap.containsKey(n)).distinct().collect(Collectors.toList());
        List<ContactVO> externalContactVOList = customerPeopleSearchService.fetchContactPerson(exteralCustPeopleList);
        // 查询内部客户最新信息
        List<ActivityRelationCustPeopleDO> internalCustPeopleList = custPeopleDOList.stream().filter(n -> fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<ContactVO> internalContactVOList = dealInternalContractVO(transCustPeopleDo2InfoVo(internalCustPeopleList));
        contactVOList.addAll(externalContactVOList);
        contactVOList.addAll(internalContactVOList);
        model.setContactVOList(contactVOList);
        model.setContactSize(custPeopleDOList.size());
        model.setExistContactSize(contactVOList.size());
        model.setErrorCustomers(new ArrayList<>());
    }

    /**
     * 校验联系人状态
     * @param contactVO 联系人信息
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 10:39
     */
    public void checkContactStatus(ContactVO contactVO) {
        if (SanctionedPartyEnum.SANCTIONED_PARTY_EMBARGO.isMe(contactVO.getContactSanctionedPartyCode())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CONTACTS_RESTRICTED_SUBJECT_NO_COMPLIANCE_STOP);
        }
        if (StringUtils.isBlank(contactVO.getContactSanctionedPartyCode()) ||
                SanctionedPartyEnum.SANCTIONED_PARTY_EMPTY.isMe(contactVO.getContactSanctionedPartyCode())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CONTACTS_RESTRICTED_SUBJECT_NO_COMPLIANCE_STOP);
        }
        if (StringUtils.isBlank(contactVO.getStatusCode()) || !ContactStatusEnum.CONTACT_STATUS_VALID.isMe(contactVO.getStatusCode())){
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CUSTOMER_CONTACTS_STATUS_INVALID);
        }
    }

    /**
     * 校验客户状态
     * @param model 活动客户数据工具
     * @return void
     * <AUTHOR>
     * date: 2023/8/29 21:16
     */
    private void checkCustomer(ActivityCustomerModel model) {
        if (model.getCustomerSize() != model.getExistCustomerSize()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ACTIVITY_CUSTOMER_CODE_ERROR);
        }
        // 校验客户状态
        model.getAccountList().forEach(ActivityCustomerServiceImpl::checkAccountStatus);
    }

    /**
     * 查询活动客户信息
     *
     * @param activityRowId 活动Id
     * @param model
     * @return java.util.List<com.zte.mcrm.adapter.vo.Account>
     * <AUTHOR>
     * date: 2023/8/30 10:40
     */
    private void getActivityAccountInfoByActivityRowId(String activityRowId, ActivityCustomerModel model) {
        List<ActivityCustomerInfoDO> customerInfoDOList = customerInfoRepository.queryAllByActivityRowId(activityRowId);
        List<String> customerCodeList = CollectionExtUtils.getListOrDefaultEmpty(customerInfoDOList).stream().map(ActivityCustomerInfoDO::getCustomerCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<Account> accountList = CollectionExtUtils.getListOrDefaultEmpty(customerSearchService.fetchAccount(customerCodeList));
        model.setAccountList(accountList);
        model.setCustomerSize(customerInfoDOList.size());
        model.setExistCustomerSize(accountList.size());
    }

    /**
     * 校验客户状态
     * @param account 客户信息
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 10:38
     */
    public static void checkAccountStatus(Account account) {
        if (AccountFrozenFlagEnum.FROZEN.isMe(account.getFrozenFlag())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CUSTOMER_MERGED_OR_FROZEN_STOP);
        }
        if (AccountMergeFlagEnum.MERGED.isMe(account.getAcctMergeFlag())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CUSTOMER_MERGED_OR_FROZEN_STOP);
        }
        // 校验受限制情况
        if (SanctionedPartyEnum.SANCTIONED_PARTY_EMBARGO.isMe(account.getSanctionedPatryCode())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CUSTOMER_RESTRICTED_SUBJECT_NO_COMPLIANCE_STOP);
        }
        if (StringUtils.isBlank(account.getSanctionedPatryCode()) || SanctionedPartyEnum.SANCTIONED_PARTY_EMPTY.isMe(account.getSanctionedPatryCode())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CUSTOMER_RESTRICTED_SUBJECT_NO_COMPLIANCE_STOP);
        }
    }

}
