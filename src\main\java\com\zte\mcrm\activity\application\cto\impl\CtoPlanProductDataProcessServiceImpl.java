package com.zte.mcrm.activity.application.cto.impl;

import com.alibaba.fastjson.JSONArray;
import com.zte.mcrm.activity.application.cto.CtoPlanProductDataProcessService;
import com.zte.mcrm.activity.application.cto.convert.CtoPlanProductFinishConvert;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanProcessHelper;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.cache.client.HrOrgDataCacheClient;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanSaleDivisionMappingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CtoPlanProductDataProcessServiceImpl implements CtoPlanProductDataProcessService {

    private final HrOrgDataCacheClient hrOrgDataCacheClient;
    private final CtoPlanProductFinishRepository ctoPlanProductFinishRepository;
    private final CtoPlanSaleDivisionMappingRepository ctoPlanSaleDivisionMappingRepository;


    /**
     * 处理所有待执行的计划
     */
    @Override
    public void processAllData() {
        List<String> planIds = ctoPlanProductFinishRepository.listByUndoProcess();
        log.info("Processing {} plans: {}", planIds.size(), planIds);
        planIds.forEach(this::processDataByPlanId);
    }

    /**
     * 处理指定计划的数据
     *
     * @param planId 计划ID
     */
    @Override
    public void processDataByPlanId(String planId) {
        log.debug("Starting processing for planId: {}", planId);
        CtoPlanProcessHelper helper = initializeHelper();

        List<CtoPlanProductFinishDO> finishDataList = ctoPlanProductFinishRepository
                .listProductFinishByRole(planId, null)
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getAccountCode()))
                .collect(Collectors.toList());

        finishDataList.forEach(finishData -> processCustomerCoverage(finishData, helper));
        log.debug("Completed processing for planId: {}", planId);
    }

    /**
     * 初始化 CtoPlanProcessHelper 并加载销售部门映射
     *
     * @return 初始化后的 CtoPlanProcessHelper
     */
    private CtoPlanProcessHelper initializeHelper() {
        CtoPlanProcessHelper helper = new CtoPlanProcessHelper();
        Map<String, List<CtoPlanSaleDivisionMappingDO>> saleDivisionMap = fetchSaleDivisionMap();
        helper.setSaleDivisionMap(saleDivisionMap);
        return helper;
    }

    /**
     * 获取销售部门映射
     *
     * @return 销售部门映射表
     */
    private Map<String, List<CtoPlanSaleDivisionMappingDO>> fetchSaleDivisionMap() {
        return ctoPlanSaleDivisionMappingRepository.listAll()
                .stream()
                .collect(Collectors.groupingBy(CtoPlanSaleDivisionMappingDO::getSalesDivision));
    }

    /**
     * 处理客户覆盖场次数据
     *
     * @param productFinish 产品计划完成数据
     * @param helper        辅助处理对象
     */
    private void processCustomerCoverage(CtoPlanProductFinishDO productFinish, CtoPlanProcessHelper helper) {
        Set<String> participants = new HashSet<>(Collections.singletonList(productFinish.getEmployeeNo()));
        Set<String> accountCodes = new HashSet<>(JSONArray.parseArray(productFinish.getAccountCode(), String.class));

        SelectCtoActivityParamDTO param = SelectCtoActivityParamDTO.builder()
                .scopeStart(productFinish.getScopeStart())
                .scopeEnd(productFinish.getScopeEnd())
                .participants(participants)
                .build();

        List<SelectCtoActivityInfoResDTO> activities = ctoPlanProductFinishRepository.selectCtoActivityInfo(param);
        activities.forEach(activity -> assignSaleDivision(activity, helper));
        // 筛选本营销事业部活动数据 活动-参与人-客户
        List<SelectCtoActivityInfoResDTO> filteredActivities = activities.stream()
                .filter(activity -> productFinish.getOrgDivision().equals(activity.getOrgDivision()))
                .collect(Collectors.toList());
        // 对活动-客户去重
        Map<String, List<SelectCtoActivityInfoResDTO>> accountActivitiesMap = filteredActivities.stream()
                .collect(Collectors.groupingBy(SelectCtoActivityInfoResDTO::getActivityAccountKey));
        // 获取对应活动ID
        List<String> allActivityIds = filteredActivities.stream()
                .filter(Objects::nonNull)              // 过滤 DTO 为 null 的元素
                .map(SelectCtoActivityInfoResDTO::getRowId)
                .collect(Collectors.toList());
        String allActivityId = JSONArray.toJSONString(allActivityIds);
        // 对客户去重
        Map<String, List<SelectCtoActivityInfoResDTO>> accountCoverage = filteredActivities.stream()
                .filter(activity -> accountCodes.contains(activity.getMarketCode()))
                .collect(Collectors.groupingBy(SelectCtoActivityInfoResDTO::getMarketCode));
        // 获取对应重点客户参与的拓展活动ID
        List<String> accountActivityIds = filteredActivities.stream()
                .filter(Objects::nonNull)
                .filter(activity -> accountCodes.contains(activity.getMarketCode()))// 过滤重点客户
                .map(SelectCtoActivityInfoResDTO::getRowId)
                .collect(Collectors.toList());
        String accountActivityId = JSONArray.toJSONString(accountActivityIds);
        updateProductFinish(productFinish, accountActivitiesMap.size(), accountCoverage.size(), accountActivityId, allActivityId);
    }

    /**
     * 分配营销部门信息
     *
     * @param activity 活动信息
     * @param helper   辅助处理对象
     */
    private void assignSaleDivision(SelectCtoActivityInfoResDTO activity, CtoPlanProcessHelper helper) {
        helper.getSaleDivisionMap().forEach((division, mappings) -> {
            List<String> orgPaths = mappings.stream()
                    .map(CtoPlanSaleDivisionMappingDO::getOrgNoFullPath)
                    .collect(Collectors.toList());

            if (hrOrgDataCacheClient.isFromSourceList(orgPaths, activity.getBelongBuId())) {
                activity.setOrgDivision(division);
            }
        });
    }

    /**
     * 更新产品计划完成数据
     *
     * @param productFinish 产品计划数据
     * @param activityCount 活动数量
     * @param accountCount  客户数量
     * @param accountActivityId 活动次数维度统计的拓展活动ID
     * @param allActivityId 重点客户参与的拓展活动ID
     */
    private void updateProductFinish(CtoPlanProductFinishDO productFinish, int activityCount, int accountCount,
    String accountActivityId, String allActivityId) {
        CtoPlanProductFinishDO update = CtoPlanProductFinishConvert.buildUpdate(
                productFinish.getRowId(), activityCount, accountCount, accountActivityId, allActivityId);
        ctoPlanProductFinishRepository.updateByPrimaryKeySelective(update);
    }
}
