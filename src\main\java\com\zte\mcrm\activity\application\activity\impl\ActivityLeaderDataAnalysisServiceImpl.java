package com.zte.mcrm.activity.application.activity.impl;

import com.zte.mcrm.activity.application.activity.ActivityLeaderDataAnalysisService;
import com.zte.mcrm.activity.common.cache.client.AreaDataCacheClient;
import com.zte.mcrm.activity.common.cache.model.AreaDataModel;
import com.zte.mcrm.activity.common.enums.AreaTypeEnum;
import com.zte.mcrm.activity.common.enums.LanguageEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.dataservice.dto.ActivityMtoInfoDTO;
import com.zte.mcrm.dataservice.model.ActivityLeaderInfoQueryDTO;
import com.zte.mcrm.dataservice.service.ActivityDataResultsService;
import com.zte.mcrm.dataservice.vo.ActivityMtoDetailInfoVO;
import com.zte.mcrm.dataservice.vo.ActivityMtoInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @description: 活动领导数据分析
 */
@Service
public class ActivityLeaderDataAnalysisServiceImpl implements ActivityLeaderDataAnalysisService {
    @Autowired
    private ActivityDataResultsService activityDataResultsService;

    @Autowired
    private AreaDataCacheClient areaDataCacheClient;

    @Override
    public List<ActivityMtoInfoVO> listMtoActivityList(BizRequest<ActivityLeaderInfoQueryDTO> param) {
        List<ActivityMtoInfoDTO> mktActivitList = activityDataResultsService.listMtoActivityList(param)
                .stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getMtoName()) && StringUtils.isNotBlank(dto.getMktName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mktActivitList)) {
            return Collections.emptyList();
        }

        // 获取国家信息
        Set<String> countryCodes = mktActivitList.stream()
                .map(ActivityMtoInfoDTO::getCountryCode).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, AreaDataModel> countryMap = areaDataCacheClient.fetchAllCache(AreaTypeEnum.COUNTRY, countryCodes);


        // 预计算 countOther 避免重复计算
        mktActivitList.forEach(dto ->
                dto.setCountOther(ObjectUtils.defaultIfNull(dto.getCountDailyVisitActivity(), 0) +
                        ObjectUtils.defaultIfNull(dto.getCountVisitingSample(), 0) +
                        ObjectUtils.defaultIfNull(dto.getCountJoinConference(), 0) +
                        ObjectUtils.defaultIfNull(dto.getCountJoinExhibition(), 0))
        );

        // 提取排序比较器，避免重复创建
        Comparator<ActivityMtoInfoVO> mtoComparator = Comparator
                .comparing(ActivityMtoInfoVO::getCountSeniorVisitExpansion, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoInfoVO::getCountUniversalExpansionActivity, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoInfoVO::getCountCustomerVisitActivity, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoInfoVO::getCountOther, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoInfoVO::getMtoName, (String::compareTo));

        Comparator<ActivityMtoDetailInfoVO> detailComparator = Comparator
                .comparing(ActivityMtoDetailInfoVO::getCountSeniorVisitExpansion, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoDetailInfoVO::getCountUniversalExpansionActivity, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoDetailInfoVO::getCountCustomerVisitActivity, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoDetailInfoVO::getCountOther, (Comparator.reverseOrder()))
                .thenComparing(ActivityMtoDetailInfoVO::getMktName, (String::compareTo));

        // 使用流式处理完成全部操作
        return mktActivitList.stream()
                .collect(Collectors.groupingBy(ActivityMtoInfoDTO::getMtoName))
                .entrySet().stream()
                .map(entry -> {
                    List<ActivityMtoInfoDTO> dtoList = entry.getValue();
                    ActivityMtoInfoVO vo = new ActivityMtoInfoVO();
                    vo.setMtoName(entry.getKey());

                    // 计算汇总数据
                    vo.setCountSeniorVisitExpansion(dtoList.stream()
                            .mapToInt(dto -> ObjectUtils.defaultIfNull(dto.getCountSeniorVisitExpansion(), 0)).sum());
                    vo.setCountUniversalExpansionActivity(dtoList.stream()
                            .mapToInt(dto -> ObjectUtils.defaultIfNull(dto.getCountUniversalExpansionActivity(), 0)).sum());
                    vo.setCountCustomerVisitActivity(dtoList.stream()
                            .mapToInt(dto -> ObjectUtils.defaultIfNull(dto.getCountCustomerVisitActivity(), 0)).sum());
                    vo.setCountOther(dtoList.stream()
                            .mapToInt(dto -> ObjectUtils.defaultIfNull(dto.getCountOther(), 0)).sum());

                    // 创建详情列表并排序
                    vo.setDetailInfoList(dtoList.stream()
                            .map(dto -> {
                                ActivityMtoDetailInfoVO detail = new ActivityMtoDetailInfoVO();
                                BeanUtils.copyProperties(dto, detail);
                                detail.setCountryName(this.getCountryName(countryMap, detail.getCountryCode(), param.getLangId()));
                                return detail;
                            })
                            .sorted(detailComparator)
                            .collect(Collectors.toList()));

                    return vo;
                })
                .sorted(mtoComparator)
                .collect(Collectors.toList());
    }

    /**
     * 转换国家名称，如果用户语言为英文，取英文名称，否则取中文名称
     *
     * @param countryMap    国家地区数据
     * @param countryCode   国家编码
     * @param language      语言类型
     * @return {@link String}
     * <AUTHOR>
     * @date 2025/4/14 下午2:04
     */
    private String getCountryName(Map<String, AreaDataModel> countryMap, String countryCode, String language) {
        if (StringUtils.isEmpty(countryCode)) {
            return StringUtils.EMPTY;
        }
        AreaDataModel areaDataModel = countryMap.getOrDefault(countryCode, new AreaDataModel());
        return LanguageEnum.EN_US.isMe(language) ? areaDataModel.getAreaNameEn() : areaDataModel.getAreaNameZh();
    }

}
