package com.zte.mcrm.activity.application.cto.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.zte.mcrm.activity.application.cto.CtoPlanOrgDataProcessService;
import com.zte.mcrm.activity.application.cto.convert.CtoPlanOrgFinishConvert;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanOrgFinishRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类，用于处理CTO计划组织数据
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CtoPlanOrgDataProcessServiceImpl implements CtoPlanOrgDataProcessService {

    private final CtoPlanProductFinishRepository ctoPlanProductFinishRepository;
    private final CtoPlanOrgFinishRepository ctoPlanOrgFinishRepository;

    private static final String LEADER_ROLE = PeopleRoleLabelEnum.LEADER.getCode();
    private static final String SAVANT_ROLE = PeopleRoleLabelEnum.SAVANT.getCode();
    private static final String ALL_ROLE = PeopleRoleLabelEnum.ALL.getCode();
    // 总数，核心领导，非核心 领导
    private static final List<String> ROLES = Arrays.asList(LEADER_ROLE, SAVANT_ROLE,ALL_ROLE);

    @Override
    public List<String> getAllProcessPlan() {
        return ctoPlanOrgFinishRepository.listByUndoProcess();
    }

    /**
     * 处理所有待执行的CTO计划数据
     * 该方法用于获取所有待执行的CTO计划ID，并逐个进行处理
     */
    @Override
    public void processAllData() {
        List<String> planIdList = getAllProcessPlan();
        log.info("Processing {} plans: {}", planIdList.size(), planIdList);
        planIdList.forEach(this::processDataByPlanId);
    }

    /**
     * 处理单个CTO计划ID的数据
     * 该方法根据计划ID查询相关的组织完成数据，并根据角色处理完成次数
     *
     * @param planId CTO计划ID
     */
    @Override
    public void processDataByPlanId(String planId) {
        log.debug("Starting processing for planId: {}", planId);
        List<CtoPlanOrgFinishDO> orgFinishList = ctoPlanOrgFinishRepository.selectByPlanId(planId);
        if (orgFinishList.isEmpty()) {
            log.warn("Cannot find CtoPlan with ID: {}", planId);
            return;
        }

        orgFinishList.forEach(this::processFinishCountsForRoles);
        log.debug("Completed processing for planId: {}", planId);
    }

    /**
     * 为不同角色处理完成次数
     * 该方法将角色处理逻辑提取到一个单独的方法中，减少代码重复
     *
     * @param orgFinish CTO计划组织完成对象
     */
    private void processFinishCountsForRoles(CtoPlanOrgFinishDO orgFinish) {
        ROLES.forEach(role -> processFinishCountForRole(orgFinish, role));
    }

    /**
     * 处理特定角色的完成次数
     * 该方法查询相关的产品完成数据，分组并计算完成次数，最后更新组织完成数据
     *
     * @param orgFinish CTO计划组织完成对象
     * @param role      员工角色（例如 LEADER 或 SAVANT）
     */
    private void processFinishCountForRole(CtoPlanOrgFinishDO orgFinish, String role) {
        List<CtoPlanProductFinishDO> productFinishList = ctoPlanProductFinishRepository.listProductFinishByRole(orgFinish.getCtoPlanInfoId(), role);
        if (productFinishList.isEmpty()) {
            log.error("Role {}, cannot find CtoPlan with ctoPlanInfoId: {}", role, orgFinish.getCtoPlanInfoId());
            return;
        }

        Map<String, List<CtoPlanProductFinishDO>> productsGroupedByCode = groupByProductCode(productFinishList);

        productsGroupedByCode.forEach((productCode, productList) -> {
            Set<String> participantList = extractParticipants(productList);
            Set<String> accountCodeList = new HashSet<>(JSONArray.parseArray(orgFinish.getAccountCode(), String.class));

            SelectCtoActivityParamDTO countParam = SelectCtoActivityParamDTO.buildByCount(
                    orgFinish.getScopeStart(),
                    orgFinish.getScopeEnd(),
                    accountCodeList,
                    participantList
            );

            /* Started by AICoder, pid:65baf2fac6ja5c514168099f8078331ab6743531 */
            // 计算拓展活动ID数据，存到表里面
            List<SelectCtoActivityInfoResDTO> activityInfoResDTOS =
                    Optional.ofNullable(ctoPlanProductFinishRepository.selectCtoActivityInfo(countParam))
                            .orElse(Collections.emptyList());

            // 解析accountCode
            List<String> accountCodes = JSON.parseArray(orgFinish.getAccountCode(), String.class);
            Set<String> accountCodeSet = new HashSet<>(accountCodes);

            // 过滤 activityInfoResDTOS，只保留 marketCode 在 accountCodeSet 中的记录
            List<SelectCtoActivityInfoResDTO> filteredActivityInfoResDTOS = activityInfoResDTOS.stream()
                    .filter(dto -> accountCodeSet.contains(dto.getMarketCode()))
                    .collect(Collectors.toList());

            // 计算达成的场次，以活动ID_accountCode的维度去统计，并去重处理计算场次
            Map<String, List<SelectCtoActivityInfoResDTO>> accountActivitiesMap = filteredActivityInfoResDTOS.stream()
                    .collect(Collectors.groupingBy(SelectCtoActivityInfoResDTO::getActivityAccountKey));

            Integer finishCount = accountActivitiesMap.size();

            // 存储达成的拓展活动ID
            Set<String> rowIds = filteredActivityInfoResDTOS.stream()
                    .filter(Objects::nonNull)  // 过滤 DTO 为 null 的元素
                    .map(SelectCtoActivityInfoResDTO::getRowId)
                    .collect(Collectors.toSet());  // 使用 Set 确保 rowId 唯一

            String rowIdsJsonArray = JSONArray.toJSONString(rowIds);
            /* Ended by AICoder, pid:65baf2fac6ja5c514168099f8078331ab6743531 */
            updateOrgFinish(orgFinish, role, productCode, finishCount, rowIdsJsonArray);
        });
    }

    /**
     * 按产品代码分组
     * 将产品完成数据按产品代码进行分组
     *
     * @param productFinishList 产品完成数据列表
     * @return 按产品代码分组后的数据
     */
    private Map<String, List<CtoPlanProductFinishDO>> groupByProductCode(List<CtoPlanProductFinishDO> productFinishList) {
        return productFinishList.stream()
                .collect(Collectors.groupingBy(CtoPlanProductFinishDO::getProductCode));
    }

    /**
     * 提取参与者列表
     * 从产品完成数据中提取出所有参与者的员工编号
     *
     * @param productList 产品完成数据列表
     * @return 参与者员工编号列表
     */
    private Set<String> extractParticipants(List<CtoPlanProductFinishDO> productList) {
        return productList.stream()
                .map(CtoPlanProductFinishDO::getEmployeeNo)
                .collect(Collectors.toSet());
    }

    /**
     * 更新组织完成数据
     * 更新组织的完成次数
     *
     * @param orgFinish   组织完成对象
     * @param role        员工角色
     * @param productCode 产品代码
     * @param finishCount 完成次数
     */
    private void updateOrgFinish(CtoPlanOrgFinishDO orgFinish, String role, String productCode, Integer finishCount, String activityIds) {
        CtoPlanOrgFinishDO updateObj = CtoPlanOrgFinishConvert.buildFinishCount(
                orgFinish.getRowId(), role, productCode, finishCount, activityIds);
        ctoPlanOrgFinishRepository.updateByPrimaryKeySelective(updateObj);
    }
}
