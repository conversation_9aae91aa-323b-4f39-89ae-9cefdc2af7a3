package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.service.activity.param.ActivityResourceOperationLogParam;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityResourceOperationLogQueryParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityResourceOperationLogVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/11/18 20:44
 * @description ：活动资源操作记录接口层
 */
public interface ActivityResourceOperationLogService {

    /**
     * 批量保存活动资源操作日志
     *
     * @param operationLogDOList 活动资源操作日志列表
     * @return 保存结果
     */
    Integer saveActivityResourceOperationLog(List<ActivityResourceOperationLogParam> operationLogDOList);

    /**
     * 查询活动资源操作记录列表
     *
     * @param bizRequest 查询参数
     * @return 活动资源操作记录列表
     */
    PageRows<ActivityResourceOperationLogVO> getActivityResourceOperationLogList(BizRequest<PageQuery<ActivityResourceOperationLogQueryParam>> bizRequest);
}
