package com.zte.mcrm.activity.repository.model.exhibition;

import java.math.BigDecimal;
import java.util.Date;

/**
 * table:exhibition_relation_hotel -- 
 */
public class ExhibitionRelationHotelDO {
    /** 主键 */
    private String rowId;

    /** 展会RowId */
    private String exhibitionRowId;

    /** 星级 */
    private String hotelStar;

    /** 房型 */
    private String hotelType;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 资源类型，ExhibitionResourceTypeEnum
     */
    private String resourceType;

    /**  */
    private String createdBy;

    /**  */
    private Date creationDate;

    /**  */
    private String lastUpdatedBy;

    /**  */
    private Date lastUpdateDate;

    /**  */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getExhibitionRowId() {
        return exhibitionRowId;
    }

    public void setExhibitionRowId(String exhibitionRowId) {
        this.exhibitionRowId = exhibitionRowId == null ? null : exhibitionRowId.trim();
    }

    public String getHotelStar() {
        return hotelStar;
    }

    public void setHotelStar(String hotelStar) {
        this.hotelStar = hotelStar == null ? null : hotelStar.trim();
    }

    public String getHotelType() {
        return hotelType;
    }

    public void setHotelType(String hotelType) {
        this.hotelType = hotelType == null ? null : hotelType.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }
}