package com.zte.mcrm.activity.application.exhibition.business;

import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionAnalysisMarketEnum;
import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDrillDownDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * 展会分析 -按照事业部比较器
 * <AUTHOR>
 *
 */
public class ExhibitionBigCountryComparator extends ExhibitionBaseComparator {

    @Override
    public int compare(ExhibitionAnalysisDrillDownDTO o1, ExhibitionAnalysisDrillDownDTO o2) {
        int compare = StringUtils.compare(o1.getCustomerLevel(), o2.getCustomerLevel());
        if(compare!=0){
            return compare;
        }
        compare = ExhibitionAnalysisMarketEnum.compare(o1.getL2OrgCode(),o2.getL2OrgCode());
        if(compare!=0){
            return compare;
        }
        return super.compare(o1, o2);
    }
}
