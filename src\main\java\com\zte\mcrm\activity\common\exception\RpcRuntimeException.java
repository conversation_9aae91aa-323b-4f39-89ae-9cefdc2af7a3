package com.zte.mcrm.activity.common.exception;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import lombok.Getter;
import lombok.Setter;

/**
 * RPC异常，主要用于封装外部接口时发生的异常（主要在interaction层）
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class RpcRuntimeException extends RuntimeException {

    /**
     * 微服务名，调用谁就填写谁，如：zte-crm-account-info
     */
    private String oriService;
    /**
     * 异常源
     */
    private Throwable ex;
    /**
     * 异常消息描述（支持传入国家化key，也支持直接描述）
     */
    private String msg;
    /**
     * 国际化信息的占位符参数
     */
    private String[] args;

    public RpcRuntimeException() {
        this(CharacterConstant.EMPTY_STR);
    }

    public RpcRuntimeException(String msg, String... args) {
        this(CharacterConstant.EMPTY_STR, msg, args);
    }

    public RpcRuntimeException(Throwable ex, String msg, String... args) {
        this(CharacterConstant.EMPTY_STR, ex, msg, args);
    }

    public RpcRuntimeException(String oriService, String msg, String... args) {
        this(oriService, null, msg, args);
    }


    public RpcRuntimeException(String oriService, Throwable ex, String msg, String... args) {
        super(msg, ex);
        this.oriService = oriService;
        this.ex = ex;
        this.msg = msg;
        this.args = args;
    }

    /**
     * 是否有源异常
     *
     * @return
     */
    public boolean hasEx() {
        return ex != null;
    }
}
