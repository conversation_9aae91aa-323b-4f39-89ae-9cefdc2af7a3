package com.zte.mcrm.activity.repository.rep.notice.impl;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingBizTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingNoticeStatusEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.notice.ActivityPendingNoticeExtMapper;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * <AUTHOR>
 */
@Component
public class ActivityPendingNoticeRepositoryImpl implements ActivityPendingNoticeRepository {
    @Resource
    private ActivityPendingNoticeExtMapper activityPendingNoticeExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<ActivityPendingNoticeDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return ZERO;
        }

        for (ActivityPendingNoticeDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            activityPendingNoticeExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityPendingNoticeDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return activityPendingNoticeExtMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 删除指定活动下的所有待办
     * @param operator  操作者
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 16:17
     */
    @Override
    public int deleteByActivityId(String operator, String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return 0;
        }
        ActivityPendingNoticeDO record = new ActivityPendingNoticeDO();
        record.setLastUpdatedBy(operator);
        record.setLastUpdateDate(new Date());
        record.setActivityRowId(activityRowId);
        record.setEnabledFlag(BooleanEnum.N.getCode());
        return activityPendingNoticeExtMapper.updateByActivityId(record);
    }

    @Override
    public void updateByActivityRowIdAndBizType(String activityRowId, String bizType, String status) {
        activityPendingNoticeExtMapper.updateByActivityRowIdAndBizType(activityRowId, bizType, status);
    }

    /**
     * 更新指定活动状态
     *
     * @param activityRowId 活动主键
     * @param beforeStatus  旧状态
     * @param afterStatus   新状态
     * @return void
     * <AUTHOR>
     * date: 2023/8/29 18:58
     */
    @Override
    public int updateTargetStatusByActivityRowId(String activityRowId, String beforeStatus, String afterStatus) {
        return StringUtils.isBlank(activityRowId) ? ZERO : activityPendingNoticeExtMapper.updateTargetStatusByActivityRowId(activityRowId, beforeStatus, afterStatus);
    }

    @Override
    public int batchInsert(List<ActivityPendingNoticeDO> list) {
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        list.forEach(this::setDefaultValue);
        activityPendingNoticeExtMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 设置默认值
     *
     * @param pendingNoticeDO
     */
    private void setDefaultValue(ActivityPendingNoticeDO pendingNoticeDO) {
        pendingNoticeDO.setRowId(Optional.ofNullable(pendingNoticeDO.getRowId()).orElse(keyIdService.getKeyId()));
        pendingNoticeDO.setCreatedBy(Optional.ofNullable(pendingNoticeDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        pendingNoticeDO.setLastUpdatedBy(Optional.ofNullable(pendingNoticeDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        pendingNoticeDO.setCreationDate(new Date());
        pendingNoticeDO.setLastUpdateDate(new Date());
        pendingNoticeDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public ActivityPendingNoticeDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : activityPendingNoticeExtMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public List<ActivityPendingNoticeDO> fetchExpiredNotice() {

        return activityPendingNoticeExtMapper.fetchExpiredNotice(new Date(), NumberConstant.MAX_PAGE_SIZE);
    }

    @Override
    public List<ActivityPendingNoticeDO> queryAllPending(String activityRowId, PendingBizTypeEnum bizTypeEnum, PendingNoticeStatusEnum statusEnum) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : activityPendingNoticeExtMapper.queryAllPending(activityRowId,
                bizTypeEnum == null ? null : bizTypeEnum.getType(),
                statusEnum == null ? null : statusEnum.getStatus());
    }

    /**
     * 按照条件列表查询
     *
     * @param record 查询条件
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/6/2 11:08
     */
    @Override
    public List<ActivityPendingNoticeDO> getList(ActivityPendingNoticeDO record) {
        String jsonString = JSON.toJSONString(record);
        if (ZERO == JSON.parseObject(jsonString).size()) {
            return Collections.emptyList();
        }

        return activityPendingNoticeExtMapper.getList(record);
    }

    /**
     * 查询活动下个人的待办事项
     *
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/5/24 14:23
     */
    @Override
    public List<ActivityPendingNoticeDO> getMyNoticeByActivityRowId(String activityRowId) {
        ActivityPendingNoticeDO query = new ActivityPendingNoticeDO();
        String empNo = BizRequestUtil.createWithCurrentUserSecurity().getEmpNo();
        if (StringUtils.isBlank(activityRowId) || StringUtils.isBlank(empNo)) {
            return Lists.newArrayList();
        }
        query.setNoticerNo(empNo);
        query.setActivityRowId(activityRowId);
        query.setPendingStatus(PendingNoticeStatusEnum.WAIT_DEAL.getStatus());
        return this.getList(query);
    }

    @Override
    public void updateStatusByBusinessId(String businessId, String status) {
        activityPendingNoticeExtMapper.updateStatusByBusinessId(businessId, status);
    }

    @Override
    public List<ActivityPendingNoticeDO> queryByBusinessIdAndStatus(String businessId, String status) {
        ActivityPendingNoticeDO pendingNoticeDO = new ActivityPendingNoticeDO();
        pendingNoticeDO.setBusinessId(businessId);
        pendingNoticeDO.setPendingStatus(status);
        return activityPendingNoticeExtMapper.getList(pendingNoticeDO);
    }

    /**
     * 批量更新
     *
     * @param ids
     * @param update
     * @return {@link int}
     * <AUTHOR>
     * @date 2023/8/30 下午10:11
     */
    @Override
    public int batchUpdate(List<String> ids, ActivityPendingNoticeDO update) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        if (StringUtils.isBlank(update.getLastUpdatedBy())) {
            update.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        }
        if (Objects.isNull(update.getLastUpdateDate())) {
            update.setLastUpdateDate(new Date());
        }
        return activityPendingNoticeExtMapper.batchUpdate(ids, update);
    }

    /**
     * 根据活动Id查询所有待办信息
     *
     * @param activityRowIds 活动Id列表
     * @return java.util.Map<java.lang.String, java.util.List < ActivityPendingNoticeDO>>
     * <AUTHOR>
     * date: 2023/9/4 7:19
     */
    @Override
    public Map<String, List<ActivityPendingNoticeDO>> queryAllPendingByActivityRowId(Set<String> activityRowIds) {
        List<ActivityPendingNoticeDO> list = CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : activityPendingNoticeExtMapper.queryAllByActivityRowIds(activityRowIds);

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream()
                .collect(Collectors.groupingBy(ActivityPendingNoticeDO::getActivityRowId));
    }

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag='Y'，推送ES不需要无效待办
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityPendingNoticeDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : activityPendingNoticeExtMapper.queryAllActivityWithNotEnable(activityRowId);
    }

    /**
     * 根据业务类型和状态批量查询待办，性能不好，慎用
     *
     * @param pendingBizType
     * @param pendingStatus
     * @param size
     * @return {@link List<ActivityPendingNoticeDO>}
     * <AUTHOR>
     * @date 2024/7/27 上午10:32
     */
    @Override
    public List<ActivityPendingNoticeDO> selectListBatch(String pendingBizType, String pendingStatus, int size) {
        return activityPendingNoticeExtMapper.selectListBatch(pendingBizType, pendingStatus, size);
    }

}
