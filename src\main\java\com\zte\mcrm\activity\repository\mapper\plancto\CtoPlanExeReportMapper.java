package com.zte.mcrm.activity.repository.mapper.plancto;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日15:46
 */
/* Started by AICoder, pid:k9816n555155cbf14ff609ac9080ac15c011fff1 */
public interface CtoPlanExeReportMapper {
    int batchInsert(Iterable<CtoPlanExeReportDO> list);
    int insertSelective(CtoPlanExeReportDO record);
    int batchUpdate(Iterable<CtoPlanExeReportDO> list);
    int updateByPrimaryKeySelective(CtoPlanExeReportDO record);
    List<CtoPlanExeReportDO> selectByPrimaries(List<String> ids);
}

/* Ended by AICoder, pid:k9816n555155cbf14ff609ac9080ac15c011fff1 */
