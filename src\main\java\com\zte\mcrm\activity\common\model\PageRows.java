package com.zte.mcrm.activity.common.model;

import lombok.Getter;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 20:58
 */
@Getter
public class PageRows<T> {

    private long current = 0L;
    /**
     * 同current
     */
    private long currentPage = 0L;
    private long total = 0L;
    /**
     * 同total
     */
    private long totalNumber = 0L;
    private long totalPage = 0L;
    private long pageSize = 0L;
    private List<T> rows = null;

    public PageRows() {
    }

    public void setCurrent(long current) {
        this.current = current;
        this.currentPage = current;
    }

    public void setCurrentPage(long currentPage) {
        setCurrent(currentPage);
    }

    public void setTotal(long total) {
        this.total = total;
        this.totalNumber = total;
    }

    public void setTotalNumber(long totalNumber) {
        setTotal(totalNumber);
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }
}
