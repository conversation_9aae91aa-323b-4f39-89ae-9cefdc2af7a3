package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleOrchestrationDetailExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationDetailRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-17 9:53
 **/
@Component
public class ActivityScheduleOrchestrationDetailRepositoryImpl implements ActivityScheduleOrchestrationDetailRepository {
    @Autowired
    private IKeyIdService keyIdService;
    @Resource
    private ActivityScheduleOrchestrationDetailExtMapper activityScheduleOrchestrationDetailExtMapper;
    @Override
    public int insertSelective(List<ActivityScheduleOrchestrationDetailDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityScheduleOrchestrationDetailDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            if (StringUtils.isBlank(record.getRemark())) {
                record.setRemark(CharacterConstant.EMPTY_STR);
            }
            if (StringUtils.isBlank(record.getDealNote())) {
                record.setDealNote(CharacterConstant.EMPTY_STR);
            }
        }

        return activityScheduleOrchestrationDetailExtMapper.batchInsert(recordList);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityScheduleOrchestrationDetailDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityScheduleOrchestrationDetailExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchDelete(List<String> rowIdList, String operEmpNo) {
        if (CollectionUtils.isEmpty(rowIdList)) {
            return NumberConstant.ZERO;
        }

        return activityScheduleOrchestrationDetailExtMapper.batchDelete(rowIdList, operEmpNo);
    }

    @Override
    public Map<String, List<ActivityScheduleOrchestrationDetailDO>> queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(List<String> orchestrationRowIds) {
        return org.springframework.util.CollectionUtils.isEmpty(orchestrationRowIds) ? Collections.emptyMap() :
                activityScheduleOrchestrationDetailExtMapper.queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(orchestrationRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityScheduleOrchestrationDetailDO::getOrchestrationRowId));
    }

    @Override
    public List<ActivityScheduleOrchestrationDetailDO> getActivityScheduleOrchestrationDetailListByRelationVersionRowIds(List<String> orchestrationRowIds) {
        return CollectionUtils.isEmpty(orchestrationRowIds) ? new ArrayList<>() :
                activityScheduleOrchestrationDetailExtMapper.queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(orchestrationRowIds);
    }

    @Override
    public Map<String, ActivityScheduleOrchestrationDetailDO> queryActivityScheduleOrchestrationDetailsByScheduleItemRowIds(List<String> scheduleItemRowIds) {
        return this.getActivityScheduleOrchestrationDetailListByScheduleItemRowIds(scheduleItemRowIds)
                .stream().collect(Collectors.toMap(ActivityScheduleOrchestrationDetailDO::getScheduleItemRowId, i -> i, (u, v) -> u));
    }

    @Override
    public List<ActivityScheduleOrchestrationDetailDO> getActivityScheduleOrchestrationDetailListByScheduleItemRowIds(List<String> scheduleItemRowIds) {
        return CollectionUtils.isEmpty(scheduleItemRowIds) ? new ArrayList<>() :
                activityScheduleOrchestrationDetailExtMapper.queryActivityScheduleOrchestrationDetailsByScheduleItemRowIds(scheduleItemRowIds);
    }
}
