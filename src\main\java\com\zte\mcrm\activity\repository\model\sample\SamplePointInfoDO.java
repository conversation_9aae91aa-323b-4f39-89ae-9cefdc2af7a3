package com.zte.mcrm.activity.repository.model.sample;

import java.util.Date;

/**
 * table:sample_point_info -- 
 */
public class SamplePointInfoDO {
    /** 主键 */
    private String rowId;

    /** 样板点编号 */
    private String samplePointNo;

    /** 样板点名称 */
    private String samplePointName;

    /** 样板点是否在海外，参考BooleanEnum枚举。Y-海外，N-国内 */
    private String international;

    /** 样板点所在国家/地区code */
    private String countryCode;

    /** 样板点所在城市code */
    private String cityCode;

    /** 样板点详细地址 */
    private String samplePointAddress;

    /** 样板点类型 */
    private String samplePointType;

    /** 所属客户 */
    private String belongCustomerCode;

    /** 样板点管理员 */
    private String adminEmpNo;

    /** 样板点管理员所在部门编码 */
    private String adminDepartmentNo;

    /** 样板点管理员所在部门编码全路径 */
    private String adminFullDepartmentNo;

    /** 样板点状态。见枚举：BooleanEnum。Y-可参观申请，N-不能 */
    private String samplePointStatus;

    /** 是否样板点支持远程参观。见枚举：BooleanEnum。Y-支持远程参观，N-不能 */
    private String samplePointOnline;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最后更新人 */
    private String lastUpdatedBy;

    /** 记录最后更新时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。见枚举：BooleanEnum */
    private String enabledFlag;

    /** 老数据来源 */
    private String oldDataSource;

    /** 样板点介绍 */
    private String content;

    /** 参观须知 */
    private String visitingNotice;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getSamplePointNo() {
        return samplePointNo;
    }

    public void setSamplePointNo(String samplePointNo) {
        this.samplePointNo = samplePointNo == null ? null : samplePointNo.trim();
    }

    public String getSamplePointName() {
        return samplePointName;
    }

    public void setSamplePointName(String samplePointName) {
        this.samplePointName = samplePointName == null ? null : samplePointName.trim();
    }

    public String getInternational() {
        return international;
    }

    public void setInternational(String international) {
        this.international = international == null ? null : international.trim();
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode == null ? null : countryCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getSamplePointAddress() {
        return samplePointAddress;
    }

    public void setSamplePointAddress(String samplePointAddress) {
        this.samplePointAddress = samplePointAddress == null ? null : samplePointAddress.trim();
    }

    public String getSamplePointType() {
        return samplePointType;
    }

    public void setSamplePointType(String samplePointType) {
        this.samplePointType = samplePointType == null ? null : samplePointType.trim();
    }

    public String getBelongCustomerCode() {
        return belongCustomerCode;
    }

    public void setBelongCustomerCode(String belongCustomerCode) {
        this.belongCustomerCode = belongCustomerCode == null ? null : belongCustomerCode.trim();
    }

    public String getAdminEmpNo() {
        return adminEmpNo;
    }

    public void setAdminEmpNo(String adminEmpNo) {
        this.adminEmpNo = adminEmpNo == null ? null : adminEmpNo.trim();
    }

    public String getAdminDepartmentNo() {
        return adminDepartmentNo;
    }

    public void setAdminDepartmentNo(String adminDepartmentNo) {
        this.adminDepartmentNo = adminDepartmentNo == null ? null : adminDepartmentNo.trim();
    }

    public String getAdminFullDepartmentNo() {
        return adminFullDepartmentNo;
    }

    public void setAdminFullDepartmentNo(String adminFullDepartmentNo) {
        this.adminFullDepartmentNo = adminFullDepartmentNo == null ? null : adminFullDepartmentNo.trim();
    }

    public String getSamplePointStatus() {
        return samplePointStatus;
    }

    public void setSamplePointStatus(String samplePointStatus) {
        this.samplePointStatus = samplePointStatus == null ? null : samplePointStatus.trim();
    }

    public String getSamplePointOnline() {
        return samplePointOnline;
    }

    public void setSamplePointOnline(String samplePointOnline) {
        this.samplePointOnline = samplePointOnline == null ? null : samplePointOnline.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getOldDataSource() {
        return oldDataSource;
    }

    public void setOldDataSource(String oldDataSource) {
        this.oldDataSource = oldDataSource == null ? null : oldDataSource.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public String getVisitingNotice() {
        return visitingNotice;
    }

    public void setVisitingNotice(String visitingNotice) {
        this.visitingNotice = visitingNotice == null ? null : visitingNotice.trim();
    }
}