package com.zte.mcrm.activity.application.exhibition.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.exhibition.ExhibitionExternalService;
import com.zte.mcrm.activity.application.exhibition.ScheduleOrchestrationAppService;
import com.zte.mcrm.activity.application.exhibition.business.*;
import com.zte.mcrm.activity.application.model.AppScheduleDataSource;
import com.zte.mcrm.activity.common.config.ExhibitionUrlConfig;
import com.zte.mcrm.activity.common.constant.*;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemPlaceTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.*;
import com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum;
import com.zte.mcrm.activity.common.enums.resource.ReserveScheduleOriTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.authorityclient.VisitUppService;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupDto;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.authority.TalkAuthorityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationExpertRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationLeaderRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationDetailRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationVersionRepository;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceReservationScheduleRepository;
import com.zte.mcrm.activity.service.activity.ActivityService;
import com.zte.mcrm.activity.service.activity.helper.RollbackScheduleItemHelper;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.authority.ExhibitionAuthorityService;
import com.zte.mcrm.activity.service.authority.TalkAuthorityService;
import com.zte.mcrm.activity.service.exhibition.ExhibitionQueryService;
import com.zte.mcrm.activity.service.exhibition.param.ExhibitionBoardDataSource;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticeEnum;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticeParam;
import com.zte.mcrm.activity.service.schedule.support.notice.ScheduleOrchestrationNoticerClient;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityScheduleItemPeopleVO;
import com.zte.mcrm.activity.web.controller.exhibition.param.app.*;
import com.zte.mcrm.activity.web.controller.exhibition.vo.*;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.*;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationParam;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationSynNoticeParam;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDTO;
import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDrillDownDTO;
import com.zte.mcrm.custcomm.common.constant.RiskConst;
import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.*;
import static com.zte.mcrm.activity.common.constant.ExhibitionConstant.EXHIBITION_COMMUNICATION_TAB;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ONE;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum.SCHEDULE_TALK;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.LEADER;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.SAVANT;
import static com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.exhibition.ExhibitionAttachmentSceneTypeEnum.EXHIBITION_SCHEDULE;
import static com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum.RESOURCE_ADMIN;
import static com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN;
import static com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum.ACCEPT;
import static com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum.WAIT;
import static java.lang.Boolean.TRUE;

/**
 * <AUTHOR> 10307200
 * @since 2023-12-19 上午10:25
 **/
@Slf4j
@Service
public class ExhibitionExternalServiceImpl implements ExhibitionExternalService {

    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;

    @Autowired
    private TalkAuthorityInfoRepository talkAuthorityInfoRepository;

    @Autowired
    private ExhibitionDirectorRepository exhibitionDirectorRepository;

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private ActivityRelationAttachmentRepository activityRelationAttachmentRepository;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private ActivityScheduleItemPeopleRepository scheduleItemPeopleRepository;

    @Autowired
    private ExhibitionQueryService exhibitionQueryService;

    @Autowired
    private TalkAuthorityService talkAuthorityService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private ExhibitionUrlConfig exhibitionUrlConfig;

    @Autowired
    private AccountAdapter accountAdapter;

    @Autowired
    private LookUpExtService lookUpExtService;

    @Autowired
    private VisitUppService visitUppService;

    @Autowired
    private ExhibitionAuthorityService exhibitionAuthorityService;

    @Autowired
    private ActivityScheduleOrchestrationVersionRepository versionRepository;

    @Autowired
    private ActivityScheduleOrchestrationDetailRepository activityScheduleOrchestrationDetailRepository;

    @Autowired
    private ExhibitionRelationExpertRepository exhibitionRelationExpertRepository;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ScheduleOrchestrationAppService scheduleOrchestrationAppService;

    @Autowired
    private ExhibitionRelationLeaderRepository exhibitionRelationLeaderRepository;

    @Autowired
    private RollbackScheduleItemHelper rollbackScheduleItemHelper;

    @Autowired
    private ScheduleOrchestrationSynScheduleService scheduleOrchestrationSynScheduleService;

    @Autowired
    private ActivityResourceReservationScheduleRepository activityResourceReservationScheduleRepository;

    @Autowired
    private ScheduleOrchestrationNoticerClient scheduleOrchestrationNoticerClient;

    @Override
    public ExhibitionBasicInfoVO getExhibitionBasicInfo(BizRequest<String> exhibitionRowIdRequest) {
        ExhibitionBasicInfoVO exhibitionBasicInfo = new ExhibitionBasicInfoVO();

        ExhibitionDetailsVO exhibitionDetail = exhibitionQueryService.getExhibitionBasicInfo(exhibitionRowIdRequest);
        ExhibitionInfoDetailsVO exhibitionInfoDetail = exhibitionDetail.getInfoDetailsVO();
        if (Objects.isNull(exhibitionInfoDetail)) {
            return exhibitionBasicInfo;
        }

        List<ExhibitionAttachmentDetailsVO> attachmentDetailsList = Optional.ofNullable(exhibitionDetail.getAttachmentDetailsList()).orElse(new ArrayList<>());
        BeanUtils.copyProperties(exhibitionInfoDetail, exhibitionBasicInfo);
        exhibitionBasicInfo.setStartTime(DateUtils.getDateFormatDay(exhibitionInfoDetail.getStartTime()));
        exhibitionBasicInfo.setEndTime(DateUtils.getDateFormatDay(exhibitionInfoDetail.getEndTime()));
        exhibitionBasicInfo.setPlaceResourceTypeName(PlaceResourceTypeEnum.getDescByType(exhibitionInfoDetail.getPlaceResourceType()));
        exhibitionBasicInfo.setExhibitionNoticeJumpUrl(exhibitionUrlConfig.fetchDetailCommunicationUrl(exhibitionBasicInfo.getRowId(), EXHIBITION_COMMUNICATION_TAB));
        exhibitionBasicInfo.setExhibitionScheduleAttachments(attachmentDetailsList.stream()
                .filter(item -> EXHIBITION_SCHEDULE.getType().equalsIgnoreCase(item.getExhibitionAttachmentSceneType()))
                .collect(Collectors.toList()));

        BizResult<Boolean> bizResult = exhibitionAuthorityService.checkExhibitionPermission(BizRequestUtil.createWithCurrentUser(exhibitionInfoDetail.getRowId()));
        exhibitionBasicInfo.setForwardable(bizResult.getData());

        // 分营编辑按钮
        exhibitionBasicInfo.setSubMarketEditAble(getSubMarketEditAble(exhibitionDetail, exhibitionRowIdRequest.getEmpNo()));
        return exhibitionBasicInfo;
    }

    /* Started by AICoder, pid:g0f4ame3a4s4daf147fe080940daaf262101ee21 */
    /**
     * 分营编辑按钮
     * @param exhibitionDetail 展览详情对象
     * @param empNo 员工编号
     * @return
     */
    boolean getSubMarketEditAble(ExhibitionDetailsVO exhibitionDetail, String empNo) {
        // 获取展览的详细信息和列表
        ExhibitionInfoDetailsVO infoDetailsVO = exhibitionDetail.getInfoDetailsVO();
        List<ExhibitionDirectorDetailsVO> directorDetailsList = exhibitionDetail.getDirectorDetailsList();
        if(BooleanEnum.Y.isMe(infoDetailsVO.getEntryOpenStatus()) || CollectionUtils.isEmpty(directorDetailsList)){
            return false;
        }
        // 查找角色类型为RESOURCE_ADMIN且员工编号匹配
        return directorDetailsList.stream().anyMatch(item -> RESOURCE_ADMIN.isMe(item.getRoleType())
                        && StringUtils.contains(item.getEmployeeNo(), empNo));
    }
    /* Ended by AICoder, pid:g0f4ame3a4s4daf147fe080940daaf262101ee21 */

    @Override
    public PageRows<ExhibitionScheduleInfoVO> getExhibitionScheduleInfoByScope(BizRequest<PageQuery<ExhibitionPersonalScheduleQueryParam>> scheduleQueryParamBizRequest) {
        PageQuery<ExhibitionPersonalScheduleQueryParam> pageQueryParam = scheduleQueryParamBizRequest.getParam();
        int pageNo = pageQueryParam.getPageNo();
        int pageSize = pageQueryParam.getPageSize();
        if (pageNo < NumberConstant.ONE || pageSize < NumberConstant.ZERO) {
            return PageRowsUtil.buildEmptyPage(pageNo, pageSize);
        }

        ExhibitionPersonalScheduleQueryParam queryParam = pageQueryParam.getParam();
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(queryParam.getExhibitionRowId());
        List<ActivityInfoDO> validActivityInfoList = this.listValidActivityInfo(queryParam.getExhibitionRowId());
        if (BooleanUtils.or(new Boolean[]{Objects.isNull(exhibitionInfoDO), CollectionUtils.isEmpty(validActivityInfoList)})) {
            return PageRowsUtil.buildEmptyPage(pageNo, pageSize);
        }
        List<ExhibitionScheduleInfoVO> exhibitionScheduleInfoList = this.getExhibitionScheduleInfoToApp(queryParam, exhibitionInfoDO, validActivityInfoList)
                .stream().filter(item -> {
                    if (CollectionUtils.isEmpty(queryParam.getFilterMeetingRoomNames())) {
                        return true;
                    }
                    return queryParam.getFilterMeetingRoomNames().contains(item.getPlaceName());
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(exhibitionScheduleInfoList)) {
            return PageRowsUtil.buildEmptyPage(pageNo, pageSize);
        }

        return this.pageExhibitionScheduleInfo(exhibitionScheduleInfoList, pageNo, pageSize);
    }

    /**
     * 获取APP日程展会最新发布时间
     * @param exhibitionRowId
     * @return
     * <AUTHOR>
     * @date 2024/2/22
     */
    Date getPublishTime(String exhibitionRowId) {
        Date publishTime = null;
        ActivityScheduleOrchestrationVersionQuery query = new ActivityScheduleOrchestrationVersionQuery();
        query.setExhibitionRowId(exhibitionRowId);
        ActivityScheduleOrchestrationVersionDO version = versionRepository.getLastScheduleOrchestrationVersion(query);
        if (Objects.nonNull(version)){
            publishTime = version.getPublishTime();
        }
        return publishTime;
    }

    @Override
    public ExhibitionAppTabAuthorityVO getExhibitionAppTabAuthority(BizRequest<String> exhibitionRowIdBizRequest) {
        String exhibitionRowId = exhibitionRowIdBizRequest.getParam();
        String empNo = exhibitionRowIdBizRequest.getEmpNo();

        ExhibitionAppTabAuthorityVO appTabAuthority = this.initExhibitionAppTabAuthority();
        // 1. 查询当前登录人是否已谈参授权
        List<ActivityTalkAuthorityDO> talkAuthorityList = talkAuthorityInfoRepository.getListByPeopleCode(empNo);
        if (!CollectionUtils.isEmpty(talkAuthorityList) || this.checkUserAllExhibitionTabAuth(exhibitionRowId, empNo)) {
            appTabAuthority.setExhibitionScheduleTab(true);
        }

        // 2. 查询当前登录人是否为领导
        appTabAuthority.setExhibitionAnalysisTab(this.checkUserExhibitionAnalysisTabAuth(empNo, exhibitionRowId));
        return appTabAuthority;
    }


    /* Started by AICoder, pid:m79fc1b6500a943144ef083d60de25136087acb0 */
    /**
     * 查询登录人是否为编排总营人员、分营编排人员
     * @param exhibitionRowId 展览行ID
     * @param empNo 员工编号
     * @return 如果用户具有总营或分营编排人员的角色，返回true，否则返回false
     */
    boolean checkUserAllExhibitionTabAuth(String exhibitionRowId, String empNo) {
        // 查询登录人是否为编排总营人员、分营编排人员，并直接检查是否存在符合条件的记录
        return exhibitionDirectorRepository.queryDirectorByExhibitionRowIdAndEmpNo(exhibitionRowId, empNo)
                .stream()
                .anyMatch(item -> ExhibitionDirectorRoleTypeEnum.in(item.getRoleType(), RESOURCE_ADMIN, RESOURCE_POINTS_ADMIN));
    }
    /* Ended by AICoder, pid:m79fc1b6500a943144ef083d60de25136087acb0 */


    /**
     * 判断是否有权限
     * @param empNo
     * @param exhibitionRowId
     * @return
     */
    private boolean checkUserExhibitionAnalysisTabAuth(String empNo, String exhibitionRowId) {
        Map<String, Boolean> leaderMap = userCenterService.isLeaderByShortNo(Lists.newArrayList(empNo));
        if (Objects.equals(TRUE, leaderMap.get(empNo))) {
            return true;
        }
        //判断是否有展会管理员的权限
        List<String> roleList = visitUppService.queryUserRoleAuthority(MsaRpcRequestUtil.createWithCurrentUser());
        if (roleList.contains(RoleConstant.AUTHORITY_ROLE_EXHIBITION_ADMIN)){
        	return true;
        }

        // 3. 查询登录人是否为编排总营人员
        Optional<ExhibitionDirectorDO> exhibitionDirectorOptional = exhibitionDirectorRepository.queryDirectorByExhibitionRowIdAndEmpNo(exhibitionRowId, empNo)
                .stream().filter(item -> ExhibitionDirectorRoleTypeEnum.RESOURCE_ADMIN.isMe(item.getRoleType())).findAny();
        return exhibitionDirectorOptional.isPresent();
    }

    private PageRows<ExhibitionScheduleInfoVO> pageExhibitionScheduleInfo(List<ExhibitionScheduleInfoVO> exhibitionScheduleInfoList, int pageNo, int pageSize) {
        int size = exhibitionScheduleInfoList.size();

        if (pageSize == ZERO) {
            return PageRowsUtil.buildPageRow(ONE, size, size, exhibitionScheduleInfoList);
        }
        // 计算起始索引和结束索引
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, size);
        // 手动分页
        List<ExhibitionScheduleInfoVO> pageList = exhibitionScheduleInfoList.subList(startIndex, endIndex);
        pageList.forEach(info -> {
            info.scheduleCusterSort();
        });
        return PageRowsUtil.buildPageRow(pageNo, pageSize, size, pageList);
    }

    private List<ExhibitionScheduleInfoVO> getExhibitionScheduleInfoToApp(ExhibitionPersonalScheduleQueryParam queryParam,
                                                                          ExhibitionInfoDO exhibitionInfoDO, List<ActivityInfoDO> validActivityInfoList) {
        String empNo = HeadersProperties.getXEmpNo();
        boolean isAllSchedule = AppScheduleScopeEnum.ALL.isMe(queryParam.getScheduleScope());
        AppScheduleDataSource ds = new AppScheduleDataSource();
        // 1. 获取登录用户所有有权限的活动id
        Map<Integer, Set<String>> authActivityRowIdMap = talkAuthorityService.getAuthorizedActivityIdsByEmpNo(validActivityInfoList, empNo);
        Set<String> authorizedActivityRowIdSet = new HashSet<>();
        if (isAllSchedule) {
            authActivityRowIdMap.putAll(getUserRoleAuthorizedActivityIdSetToApp(empNo, queryParam.getExhibitionRowId(), validActivityInfoList));
            authActivityRowIdMap.values().stream()
                    .flatMap(Collection::stream)
                    .forEach(authorizedActivityRowIdSet::add);
        }
        Map<String, ActivityInfoDO> activityId2EntityMaps = validActivityInfoList.stream().collect(Collectors.toMap(ActivityInfoDO::getRowId, i -> i, (u, v) -> u));
        // 2. 获取所有活动下所有已编排的日程信息列表
        Set<String> acceptedScheduleItemIdSet = new HashSet<>();
        List<ActivityScheduleItemDO> acceptedScheduleItemList = this.getAcceptedScheduleItemList(acceptedScheduleItemIdSet,
                activityId2EntityMaps, queryParam.getFilterOrchestrationDealStatus());
        // 3. 获取所有日程信息对应的日程参与人列表
        List<ActivityScheduleItemPeopleDO> acceptScheduleItemPeopleList =
                scheduleItemPeopleRepository.getRelationSchedulePeopleInfoList(Lists.newArrayList(acceptedScheduleItemIdSet));
        Map<String, Set<String>> acceptScheduleItem2PeopleCodeMaps = acceptScheduleItemPeopleList.stream()
                .filter(itemPeople -> !ScheduleItemPeopleTypeEnum.in(itemPeople.getPeopleType(), OTHER_INTERFACE_PEOPLE, REFERENCE_CONTROLLER))
                .collect(Collectors.groupingBy(ActivityScheduleItemPeopleDO::getActivityScheduleItemRowId, Collectors.mapping(ActivityScheduleItemPeopleDO::getPeopleNo, Collectors.toSet())));
        // 4. 过滤出所有用户有权限的日程 + 用户是参与人的日程并汇总并按照日程时间排序
        Set<String> allValidScheduleItemRowIds = new HashSet<>();
        Map<String, List<ActivityScheduleItemDO>> activityId2ScheduleItemListMap = new HashMap<>();
        List<ActivityScheduleItemDO> validScheduleItemList = this.getValidScheduleItemList(acceptedScheduleItemList,
                acceptScheduleItem2PeopleCodeMaps, authorizedActivityRowIdSet, allValidScheduleItemRowIds, activityId2ScheduleItemListMap);
        if (CollectionUtils.isEmpty(validScheduleItemList)) {
            return new ArrayList<>();
        }
        // 5. 将用户有权限的日程的参与人分类为我司参与人 及 客户参与人
        Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemZtePeopleMap = new HashMap<>();
        Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemCustomerPeopleMap = new HashMap<>();
        this.assembleScheduleItemRelationPeopleInfo(acceptScheduleItemPeopleList, allValidScheduleItemRowIds, scheduleItemZtePeopleMap, scheduleItemCustomerPeopleMap);
        // 6. 获取所有活动的客户信息 及 活动与主客户信息映射
        Map<String, List<ActivityCustomerInfoDO>> activityId2CustomerInfoListMap = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(activityId2EntityMaps.keySet());
        Map<String, ActivityCustomerInfoDO> activityId2MainCustomerInfoMap = new HashMap<>();
        activityId2CustomerInfoListMap.forEach((activityRowId, customerInfoList) -> {
            Optional<ActivityCustomerInfoDO> mainCustomerInfoOptional = customerInfoList.stream().filter(item -> Objects.equals(Y, item.getMainCust())).findAny();
            if (!mainCustomerInfoOptional.isPresent()) {
                return;
            }
            ActivityCustomerInfoDO customerInfoDo = mainCustomerInfoOptional.get();
            activityId2MainCustomerInfoMap.put(activityRowId, customerInfoDo);
        });
        // 7. 获取活动下 所有议程的谈参信息
        Map<String, List<ActivityRelationAttachmentDO>> activityId2AttachmentListMap = activityRelationAttachmentRepository.queryActivityAttachmentList(Lists.newArrayList(allValidScheduleItemRowIds))
                .stream().filter(item -> SCHEDULE_TALK.isMe(item.getAttachmentSceneType())).collect(Collectors.groupingBy(ActivityRelationAttachmentDO::getSceneOriginRowId));
        // 8. 获取活动关联的客户信息列表
        Map<String, List<ActivityRelationCustPeopleDO>> activityId2CustPeopleListMap =
                activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(activityId2EntityMaps.keySet());
        Map<String, Map<String, ActivityRelationCustPeopleDO>> activityId2CustPeopleMaps = new HashMap<>();
        activityId2CustPeopleListMap.forEach((activityRowId, custPeopleList) -> {
            Map<String, ActivityRelationCustPeopleDO> custPeopleDoMap = custPeopleList.stream()
                    .collect(Collectors.toMap(ActivityRelationCustPeopleDO::getContactNo, i -> i, (u, v) -> u));
            activityId2CustPeopleMaps.put(activityRowId, custPeopleDoMap);
        });
        // 9. 获取日程关联的所有编排版本信息
        Map<String, Set<String>> scheduleItem2OrchIdsMap = activityScheduleOrchestrationDetailRepository.getActivityScheduleOrchestrationDetailListByScheduleItemRowIds(Lists.newArrayList(allValidScheduleItemRowIds))
                .stream().collect(Collectors.groupingBy(ActivityScheduleOrchestrationDetailDO::getScheduleItemRowId, Collectors.mapping(ActivityScheduleOrchestrationDetailDO::getOrchestrationRowId, Collectors.toSet())));

        ActivityScheduleOrchestrationVersionQuery query = new ActivityScheduleOrchestrationVersionQuery();
        query.setExhibitionRowId(validActivityInfoList.get(0).getOriginRowId());
        ActivityScheduleOrchestrationVersionDO version = versionRepository.getLastScheduleOrchestrationVersion(query);
        // 10. 汇总并组装数据返回
        ds.setAllSchedule(isAllSchedule);
        ds.setExhibitionInfoDO(exhibitionInfoDO);
        ds.setKeyword(queryParam.getKeyword());
        ds.setValidActivityInfoList(validActivityInfoList);
        ds.setValidScheduleItemList(validScheduleItemList);
        ds.setActivityId2EntityMaps(activityId2EntityMaps);
        ds.setVersion(version);
        ds.setScheduleItem2OrchIdsMap(scheduleItem2OrchIdsMap);
        ds.setScheduleItemZtePeopleMap(scheduleItemZtePeopleMap);
        ds.setActivityId2CustPeopleMaps(activityId2CustPeopleMaps);
        ds.setActivityId2CustPeopleListMap(activityId2CustPeopleListMap);
        ds.setActivityId2AttachmentListMap(activityId2AttachmentListMap);
        ds.setScheduleItemCustomerPeopleMap(scheduleItemCustomerPeopleMap);
        ds.setActivityId2MainCustomerInfoMap(activityId2MainCustomerInfoMap);
        ds.setActivityId2ScheduleItemListMap(activityId2ScheduleItemListMap);
        return this.listExhibitionScheduleInfo(ds, authActivityRowIdMap, acceptScheduleItem2PeopleCodeMaps);
    }

    /**
     * 获取当前登录用户在指定展会下 被授予的 展会角色权限对应的活动id集合（仅限于APP使用）
     * 目前APP只限于 总营编排人员（11） 和 分营编排人员（12）两种角色
     *
     * @param empNo
     * @param exhibitionRowId
     * @param activityInfoDOList
     * @return
     */
    Map<Integer, Set<String>> getUserRoleAuthorizedActivityIdSetToApp(String empNo, String exhibitionRowId, List<ActivityInfoDO> activityInfoDOList) {
        if (CollectionUtils.isEmpty(activityInfoDOList) || StringUtils.isBlank(exhibitionRowId)) {
            return new HashMap<>();
        }

        Set<String> allActivityRowIds = activityInfoDOList.stream().filter(item -> exhibitionRowId.equals(item.getOriginRowId())).map(ActivityInfoDO::getRowId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(allActivityRowIds)) {
            return new HashMap<>();
        }

        Map<Integer, Set<String>> exhibitionRole2AuthAcIdsMap = new HashMap<>();
        List<ExhibitionDirectorDO> exhibitionDirectors = exhibitionDirectorRepository.queryDirectorByExhibitionRowIdAndEmpNo(exhibitionRowId, empNo);
        Optional<ExhibitionDirectorDO> resourceAdmin = exhibitionDirectors.stream().filter(item -> RESOURCE_ADMIN.isMe(item.getRoleType())).findAny();
        // 1. 编排总负责人 有所有展会活动的权限
        if (resourceAdmin.isPresent()) {
            exhibitionRole2AuthAcIdsMap.put(NumberConstant.ELEVEN, allActivityRowIds);
            return exhibitionRole2AuthAcIdsMap;
        }
        // 2. 分营编排负责人 只有对应授权范围的活动的权限
        Set<String> userOrgIdAuthSet = new HashSet<>();
        exhibitionDirectors.stream().filter(item -> RESOURCE_POINTS_ADMIN.isMe(item.getRoleType()))
                .forEach(director -> {
                    userOrgIdAuthSet.addAll(Arrays.stream(director.getOrgAuth().split(CharacterConstant.COMMA)).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                });

        exhibitionRole2AuthAcIdsMap.put(NumberConstant.TWELVE, activityInfoDOList.stream().filter(activityInfo -> talkAuthorityService.judgeActivityAuthorized(activityInfo,
                new HashSet<>(), userOrgIdAuthSet, new HashSet<>())).map(ActivityInfoDO::getRowId).collect(Collectors.toSet()));
        return exhibitionRole2AuthAcIdsMap;
    }

    private List<ExhibitionScheduleInfoVO> listExhibitionScheduleInfo(AppScheduleDataSource ds, Map<Integer, Set<String>> authActivityRowIdMap,
                                                                      Map<String, Set<String>> acceptScheduleItem2PeopleCodeMaps) {
        List<ExhibitionScheduleInfoVO> exhibitionScheduleInfoList = new ArrayList<>();
        List<ActivityScheduleItemDO> validScheduleItemList = Optional.ofNullable(ds.getValidScheduleItemList()).orElse(new ArrayList<>());

        validScheduleItemList.forEach(scheduleItemDO -> {
            ExhibitionScheduleInfoVO exhibitionScheduleInfo = assembleExhibitionScheduleInfoVo(scheduleItemDO, ds);
            this.assembleCustPeopleList(ds, scheduleItemDO, exhibitionScheduleInfo);
            this.assembleZtePeopleList(ds, scheduleItemDO, exhibitionScheduleInfo);
            this.assembleScheduleOperationAuth(ds, scheduleItemDO, exhibitionScheduleInfo, authActivityRowIdMap);
            this.assembleScheduleAttachmentInfoList(ds, scheduleItemDO, exhibitionScheduleInfo, authActivityRowIdMap, acceptScheduleItem2PeopleCodeMaps);
            if (this.filterExhibitionScheduleItemByKeyword(ds.getKeyword(), exhibitionScheduleInfo)) {
                exhibitionScheduleInfoList.add(exhibitionScheduleInfo);
            }
        });
        Map<String, List<ExhibitionScheduleInfoVO>> exhibitionScheduleInfoMaps = exhibitionScheduleInfoList
                .stream().collect(Collectors.groupingBy(ExhibitionScheduleInfoVO::getScheduleDate, Collectors.toList()));
        return exhibitionScheduleInfoList.stream().peek(item -> {
            List<ExhibitionScheduleInfoVO> scheduleDateItems = Optional.ofNullable(exhibitionScheduleInfoMaps.get(item.getScheduleDate())).orElse(new ArrayList<>());
            item.setScheduleDateItemQuantity(scheduleDateItems.size());
        }).collect(Collectors.toList());
    }

    boolean filterExhibitionScheduleItemByKeyword(String filterWord, ExhibitionScheduleInfoVO scheduleInfoVO) {
        if (StringUtils.isBlank(filterWord)) {
            return true;
        }

        String filterKeyword = filterWord.trim().toLowerCase();
        String scheduleItemName = Optional.ofNullable(scheduleInfoVO.getScheduleItemName()).orElse(EMPTY_STR).toLowerCase();
        String customerName = Optional.ofNullable(scheduleInfoVO.getCustomerName()).orElse(EMPTY_STR).toLowerCase();
        String mktName = Optional.ofNullable(scheduleInfoVO.getMktName()).orElse(EMPTY_STR).toLowerCase();
        String localName = Optional.ofNullable(scheduleInfoVO.getLocalName()).orElse(EMPTY_STR).toLowerCase();
        String accountName = localName + mktName;
        List<ExhibitionScheduleCustPeopleInfoVO> custPeopleList = Optional.ofNullable(scheduleInfoVO.getCustPeopleList()).orElse(new ArrayList<>());
        List<ExhibitionScheduleZtePeopleInfoVO> allZtePeopleList = new ArrayList<>();
        allZtePeopleList.addAll(Optional.ofNullable(scheduleInfoVO.getZtePeopleList()).orElse(new ArrayList<>()));
        allZtePeopleList.addAll(Optional.ofNullable(scheduleInfoVO.getInterfacePeopleList()).orElse(new ArrayList<>()));
        if (scheduleItemName.contains(filterKeyword) || customerName.contains(filterKeyword) || accountName.contains(filterKeyword)) {
            return true;
        }

        for (ExhibitionScheduleCustPeopleInfoVO custPeople : custPeopleList) {
            String custPeopleDesc = Optional.ofNullable(custPeople.getContactName()).orElse(EMPTY_STR).toLowerCase();
            if (custPeopleDesc.contains(filterKeyword)) {
                return true;
            }
        }

        for (ExhibitionScheduleZtePeopleInfoVO ztePeople : allZtePeopleList) {
            String ztePeopleName = Optional.ofNullable(ztePeople.getPeopleName()).orElse(EMPTY_STR).toLowerCase();
            String peopleCode = Optional.ofNullable(ztePeople.getPeopleCode()).orElse(EMPTY_STR).toLowerCase();
            if (ztePeopleName.contains(filterKeyword) || peopleCode.contains(filterKeyword)) {
                return true;
            }
        }

        return false;
    }

    private void assembleScheduleItemRelationPeopleInfo(List<ActivityScheduleItemPeopleDO> itemPeopleList, Set<String> allValidScheduleItemRowIds,
                                                   Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemZtePeopleMap,
                                                   Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemCustomerPeopleMap) {
        if (CollectionUtils.isEmpty(itemPeopleList)) {
            return;
        }
        Map<String, List<ActivityScheduleItemPeopleDO>> validScheduleItemPeopleMap =
                itemPeopleList.stream().filter(item -> allValidScheduleItemRowIds.contains(item.getActivityScheduleItemRowId()))
                .collect(Collectors.groupingBy(ActivityScheduleItemPeopleDO::getActivityScheduleItemRowId));
        validScheduleItemPeopleMap.forEach((scheduleItemRowId, scheduleItemPeopleDoList) -> {
            if (CollectionUtils.isEmpty(scheduleItemPeopleDoList)) {
                return;
            }

            this.assembleScheduleItemRelationPeopleInfoMap(scheduleItemRowId, scheduleItemPeopleDoList, scheduleItemZtePeopleMap, scheduleItemCustomerPeopleMap);
        });

    }

    private void assembleScheduleItemRelationPeopleInfoMap(String scheduleItemRowId, List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDoList,
                                                           Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemZtePeopleMap,
                                                           Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemCustomerPeopleMap) {
        Map<String, ActivityScheduleItemPeopleDO> ztePeopleMap = new HashMap<>();
        List<ActivityScheduleItemPeopleDO> zteInterfacePeopleList = new ArrayList<>();
        List<ActivityScheduleItemPeopleDO> clientParticipantList = new ArrayList<>();
        for (ActivityScheduleItemPeopleDO item : scheduleItemPeopleDoList) {
            if (ZTE_PEOPLE.isMe(item.getPeopleType())) {
                this.filterRepeatedZtePeople(item, ztePeopleMap);
            }

            if (ZTE_INTERFACE_PEOPLE.isMe(item.getPeopleType())) {
                zteInterfacePeopleList.add(item);
            }

            if (CLIENT_PARTICIPANT.isMe(item.getPeopleType())) {
                clientParticipantList.add(item);
            }
        }

        if (!CollectionUtils.isEmpty(clientParticipantList)) {
            scheduleItemCustomerPeopleMap.putIfAbsent(scheduleItemRowId, clientParticipantList);
        }

        // 已存在我司参与人中的陪同人员需要去重 并 按照业务规则排序
        List<ActivityScheduleItemPeopleDO> ztePeopleList = Lists.newArrayList(ztePeopleMap.values());
        ztePeopleList.addAll(zteInterfacePeopleList.stream().filter(item -> !ztePeopleMap.containsKey(item.getPeopleNo())).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(ztePeopleList)) {
            List<ActivityScheduleItemPeopleDO> sortedZtePeopleList = ztePeopleList.stream().sorted(new ExhibitionZtePeopleComparator()).collect(Collectors.toList());
            scheduleItemZtePeopleMap.putIfAbsent(scheduleItemRowId, sortedZtePeopleList);
        }
    }

    private void filterRepeatedZtePeople(ActivityScheduleItemPeopleDO item, Map<String, ActivityScheduleItemPeopleDO> ztePeopleMap) {
        String peopleCode = item.getPeopleNo();
        String peopleLabel = item.getPeopleLabel();

        if (!ztePeopleMap.containsKey(peopleCode)) {
            ztePeopleMap.put(peopleCode, item);
            return;
        }
        // 如果我司参与人中同一个人添加多次 保留领导和专家标签的那条记录
        if (PeopleRoleLabelEnum.in(peopleLabel, LEADER, SAVANT)) {
            ztePeopleMap.put(peopleCode, item);
        }
    }

    private List<ActivityInfoDO> listValidActivityInfo(String exhibitionRowId) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        query.setOriginRowIdList(Lists.newArrayList(exhibitionRowId));
        query.setActivityStatus(Lists.newArrayList(PROGRESS.getCode(), FINISH.getCode(), EVALUATED.getCode()));
        return activityInfoRepository.searchByParam(query);
    }

    private ExhibitionAppTabAuthorityVO initExhibitionAppTabAuthority() {
        ExhibitionAppTabAuthorityVO exhibitionAppTabAuthority = new ExhibitionAppTabAuthorityVO();
        exhibitionAppTabAuthority.setExhibitionScheduleTab(false);
        exhibitionAppTabAuthority.setExhibitionAnalysisTab(false);
        return exhibitionAppTabAuthority;
    }


    @Override
    public ExhibitionAnalysisVO exhibitionAnalysisList(BizRequest<ExhibitionAnalysisQueryParam> bizRequestParam) throws Exception {
        ExhibitionAnalysisQueryParam param = bizRequestParam.getParam();
        if(StringUtils.isBlank(param.getExhibitionId())){
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ErrorMsgConstant.EXHIBITION_ID);
        }

        ExhibitionAnalysisVO exhibitionAnalysisVO = new ExhibitionAnalysisVO();
        // 1. 判断当前登录人的权限
        boolean flag = this.checkUserExhibitionAnalysisTabAuth(bizRequestParam.getEmpNo(), param.getExhibitionId());
        if(!flag){
            exhibitionAnalysisVO.setAuthFlag(false);
            return exhibitionAnalysisVO;
        }

        // 2.获取展会分析数据
        exhibitionAnalysisVO.setAuthFlag(true);
        List<ExhibitionAnalysisDTO> exhibitionAnalysisInfoList = accountAdapter.getExhibitionAnalysisInfoList(param);
        if(CollectionUtils.isEmpty(exhibitionAnalysisInfoList)){
            return exhibitionAnalysisVO;
        }

        // 3.总体数据
        ExhibitionAnalysisDTO exhibitionAnalysisDTO = exhibitionAnalysisInfoList.stream()
                .filter(item -> ExhibitionAnalysisDimEnum.ONE.isMe(item.getDimensionType())).findFirst().orElse(null);
        exhibitionAnalysisVO.setAllExhibitionAnalysisDTO(exhibitionAnalysisDTO);

        // 4.营销数据
        List<ExhibitionAnalysisDTO> marketExhibitionAnalysisList = exhibitionAnalysisInfoList.stream()
                .filter(item -> ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())).collect(Collectors.toList());
        List<ExhibitionAnalysisDTO> filterMarketExhibitionAnalysisList= marketExhibitionAnalysisList.stream().filter(item -> ExhibitionAnalysisMarketEnum.in(item.getDimensionId()
                , ExhibitionAnalysisMarketEnum.values())).sorted(Comparator.comparing(ExhibitionAnalysisDTO::getDimensionId, ExhibitionAnalysisMarketEnum::compare)).collect(Collectors.toList());
        Map<String, FastLookupDto> lookUpMap = lookUpExtService.getLookUpDtoMapByType(LookupConstant.LOOKUP_TYPE_EXHIBITION_DIVISION);

        LinkedHashMap<String, ExhibitionAnalysisDTO> marketExhibitionAnalysisMap = new LinkedHashMap<>(filterMarketExhibitionAnalysisList.size());
        filterMarketExhibitionAnalysisList.forEach(e ->{
            marketExhibitionAnalysisMap.put(e.getDimensionId(),e);
            //补充事业部名称与是否下钻
            fillDimensionNameAndIsDrillDown(e, lookUpMap);
            dealSpecificFieldForMarketData(e);
        });
        exhibitionAnalysisVO.setMarketingExhibitionAnalysisDTOMap(marketExhibitionAnalysisMap);

        // 5.产品数据
        List<ExhibitionAnalysisDTO> productExhibitionAnalysisList = exhibitionAnalysisInfoList.stream()
                .filter(item -> ExhibitionAnalysisDimEnum.THREE.isMe(item.getDimensionType())).collect(Collectors.toList());
        List<ExhibitionAnalysisDTO> filterProductExhibitionAnalysisList= productExhibitionAnalysisList.stream().filter(item -> ExhibitionAnalysisProductEnum.in(item.getDimensionId()
                , ExhibitionAnalysisProductEnum.values())).sorted(Comparator.comparing(ExhibitionAnalysisDTO::getDimensionId, ExhibitionAnalysisProductEnum::compare)).collect(Collectors.toList());
        LinkedHashMap<String, ExhibitionAnalysisDTO> productExhibitionAnalysisMap = new LinkedHashMap<>(filterProductExhibitionAnalysisList.size());
        filterProductExhibitionAnalysisList.forEach(e ->{
            fillDimensionNameForProductInfo(e);
            productExhibitionAnalysisMap.put(e.getDimensionId(), e);
        });
        exhibitionAnalysisVO.setProductExhibitionAnalysisDTOMap(productExhibitionAnalysisMap);

        //下钻数据
        exhibitionAnalysisVO.setMarketingExhibitionAnalysisDrillDownDTOMap(this.queryExhibitionAnalysisDrillDown(param.getExhibitionId()));

        return exhibitionAnalysisVO;
    }

    /**
     * 编辑展会议程
     * @param bizRequest
     * @return
    /**
     * 活动相关表：activity_relation_zte_people
     * 日程相关表：activity_schedule_item、activity_schedule_item_people
     * 发布相关表：activity_schedule_orchestration_version、activity_schedule_orchestration_detail、
     * activity_schedule_orchestration_version、activity_schedule_item_origin_detail
     */
    @Override
    public String editExhibitionSchedule(BizRequest<ExhibitionScheduleParam> bizRequest) {
        log.info("request ExhibitionScheduleParam : {}", JSON.toJSONString(bizRequest.getParam()));
        String currentEmpNo = bizRequest.getEmpNo();
        ExhibitionScheduleParam param = bizRequest.getParam();
        // 获取展会负责人信息
        List<ExhibitionDirectorDO> directorDOList = exhibitionDirectorRepository.fetchDirectorByExhibitionRowId(param.getExhibitionId());
        // 获取原议程人员信息
        List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDOS = scheduleItemPeopleRepository.queryAllByScheduleItemRowId(param.getScheduleId());
        // 参数校验和设置角色信息
        checkParamAndSetRoleType(param, currentEmpNo, directorDOList, scheduleItemPeopleDOS);
        // 展会专家信息
        List<ExhibitionRelationExpertDO> expertDOList = exhibitionRelationExpertRepository.queryExhibitionRelationExpertList(param.getExhibitionId());
        Set<String> expertEmpNos = expertDOList.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toSet());
        // 展会领导信息
        List<ExhibitionRelationLeaderDO> leaderDOList = exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(
                Collections.singletonList(param.getExhibitionId())).get(param.getExhibitionId());
        Set<String> leaderEmpNos = Optional.ofNullable(leaderDOList).orElse(Collections.emptyList())
                .stream().map(ExhibitionRelationLeaderDO::getEmployeeNo).collect(Collectors.toSet());
        // 判断1、如果是总营或分营负责人则直接打包数据发布。2、如果是参与人则根据活动状态选择是否发布
        if (ScheduleOrchestrationRoleEnum.ALL_ADMIN.isMe(param.getOrgType()) ||
                ScheduleOrchestrationRoleEnum.SUB_ADMIN.isMe(param.getOrgType())) {
            // 负责人
            // 组装发布数据进行发布
            ScheduleOrchestrationParam scheduleOrchestrationParam = packPublishData(param, expertEmpNos, leaderEmpNos, ResourceOrchestrationDealStatusEnum.ACCEPT);
            log.info("admin doScheduleOrchestration scheduleOrchestrationParam : {}", JSON.toJSONString(scheduleOrchestrationParam));
            // 复用PC发布接口
            scheduleOrchestrationAppService.doScheduleOrchestration(BizRequestUtil.createWithCurrentUserSecurity(scheduleOrchestrationParam));
            // 总营负责人编辑需要重推日程
            if (ScheduleOrchestrationRoleEnum.ALL_ADMIN.isMe(param.getOrgType())) {
                synScheduleInfo(param.getExhibitionId(), param.getScheduleId());
            }
        } else {
            // 参与人
            // 原议程信息
            ActivityScheduleItemDO scheduleItemDO = scheduleItemRepository.selectByPrimaryKey(param.getScheduleId());

            // 判断   1、议程待编排状态：只需要同步议程活动数据
            if (WAIT.isMe(scheduleItemDO.getDealStatus())) {
                // 组装发布数据进行发布
                ScheduleOrchestrationParam scheduleOrchestrationParam = packPublishData(param, expertEmpNos, leaderEmpNos, WAIT);
                log.info("personal change doScheduleOrchestration scheduleOrchestrationParam : {}", JSON.toJSONString(scheduleOrchestrationParam));
                // 复用PC发布接口
                scheduleOrchestrationAppService.doScheduleOrchestration(BizRequestUtil.createWithCurrentUserSecurity(scheduleOrchestrationParam));
            }
            // 判断   2、议程已编排状态：根据修改内容是否涉及会议室、领导、专家选择是否走发布流程
            if (ResourceOrchestrationDealStatusEnum.ACCEPT.isMe(scheduleItemDO.getDealStatus())) {
                boolean isChange = checkChange(param, scheduleItemPeopleDOS, expertEmpNos, leaderEmpNos);
                // 判断：1、涉及会议室、领导、专家变更则只同步议程活动数据 2、不涉及会议室、领导、专家变更直接走发布流程
                // 组装发布数据进行发布
                ResourceOrchestrationDealStatusEnum dealStatus = isChange ? WAIT : ResourceOrchestrationDealStatusEnum.ACCEPT;
                ScheduleOrchestrationParam scheduleOrchestrationParam = packPublishData(param, expertEmpNos, leaderEmpNos, dealStatus);
                log.info("personal publish doScheduleOrchestration scheduleOrchestrationParam : {}", JSON.toJSONString(scheduleOrchestrationParam));
                scheduleOrchestrationAppService.doScheduleOrchestration(BizRequestUtil.createWithCurrentUserSecurity(scheduleOrchestrationParam));

                if (isChange) {
                    sendConfirmNotice(bizRequest);
                }
            }
        }

        return param.getScheduleId();
    }

    /**
     * 推送日程待确认工作通知
     *
     * @param bizRequest
     * <AUTHOR>
     * @date 2025/1/22 上午11:05
     */
    private void sendConfirmNotice(BizRequest<ExhibitionScheduleParam> bizRequest) {
        ExhibitionScheduleParam param = bizRequest.getParam();
        ScheduleOrchestrationNoticeParam scheduleOrchestrationNoticeParam = new ScheduleOrchestrationNoticeParam();
        scheduleOrchestrationNoticeParam.setExhibitionRowId(param.getExhibitionId());
        scheduleOrchestrationNoticeParam.setNoticeScene(ScheduleOrchestrationNoticeEnum.SEND_TO_SCHEDULE_ADMIN_CONFIRM);
        scheduleOrchestrationNoticeParam.setScheduleRowIds(Lists.newArrayList(param.getScheduleId()));
        BizRequest<ScheduleOrchestrationNoticeParam> sendEmailReq = BizRequestUtil.copyRequest(bizRequest, e -> scheduleOrchestrationNoticeParam);
        // 分营、总营发布都同步给一线
        ThreadManager.submitToAsync(() -> {
            try {
                BizResult<Void> voidBizResult = scheduleOrchestrationNoticerClient.sendNotice(sendEmailReq);
                log.info("推送展会{}日程{}待确认通知结果{}", param.getExhibitionId(), param.getScheduleId(), voidBizResult.isFlag());
            } catch (Exception e) {
                log.error("推送展会{}日程{}待确认通知异常", param.getExhibitionId(), param.getScheduleId(), e);
            }
        });
    }

    /**
     * 确认展会议程
     * @param bizRequest
     * @return
     */
    @Override
    public String confirmExhibitionSchedule(BizRequest<ExhibitionScheduleUpdateParam> bizRequest) {
        log.info("request ExhibitionScheduleUpdateParam : {}", JSON.toJSONString(bizRequest.getParam()));
        ExhibitionScheduleUpdateParam param = bizRequest.getParam();
        String currentEmpNo = bizRequest.getEmpNo();
        // 获取展会信息
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(param.getExhibitionId());
        // 获取展会负责人信息
        List<ExhibitionDirectorDO> directorDOList = exhibitionDirectorRepository.fetchDirectorByExhibitionRowId(param.getExhibitionId());
        // 获取日程信息
        ActivityScheduleItemDO activityScheduleItemDO = scheduleItemRepository.selectByPrimaryKey(param.getScheduleId());

        // 校验展会报名状态必须关闭
        if (BooleanEnum.Y.isMe(exhibitionInfoDO.getEntryOpenStatus())) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.open.forbid");
        }
        // 校验登录人是否为总营负责人
        if (StringUtils.isEmpty(getAllAdmin(currentEmpNo,directorDOList))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.no.authority");
        }

        //校验：对应状态为待编排deal_status=wait且已变更has_changed="Y"，则能操作确认或回退，否则不能
        if (!(WAIT.isMe(activityScheduleItemDO.getDealStatus()) && BooleanEnum.Y.isMe(activityScheduleItemDO.getHasChanged()))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.confirmed.forbid.rollback");
        }

        // 获取议程信息
        ActivityScheduleItemDO scheduleItemDO = scheduleItemRepository.selectByPrimaryKey(param.getScheduleId());

        // 获取参与人信息
        List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDOS = scheduleItemPeopleRepository.queryAllByScheduleItemRowId(param.getScheduleId());

        // 展会专家信息
        List<ExhibitionRelationExpertDO> expertDOList = exhibitionRelationExpertRepository.queryExhibitionRelationExpertList(param.getExhibitionId());
        Set<String> expertEmpNos = expertDOList.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toSet());

        // 展会领导信息
        List<ExhibitionRelationLeaderDO> leaderDOList = exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(
                Collections.singletonList(param.getExhibitionId())).get(param.getExhibitionId());
        Set<String> leaderEmpNos = Optional.ofNullable(leaderDOList).orElse(Collections.emptyList())
                .stream().map(ExhibitionRelationLeaderDO::getEmployeeNo).collect(Collectors.toSet());

        // 打包议程发布数据参数
        ExhibitionScheduleParam scheduleParam = buildScheduleParam(scheduleItemPeopleDOS, param, scheduleItemDO);
        // 确认只能总营负责人才能确认
        scheduleParam.setOrgType(ScheduleOrchestrationRoleEnum.ALL_ADMIN.getCode());
        scheduleParam.setHasChanged(BooleanEnum.N.getCode());
        // 打包数据进行确认发布
        ScheduleOrchestrationParam scheduleOrchestrationParam = packPublishData(scheduleParam, expertEmpNos, leaderEmpNos, ResourceOrchestrationDealStatusEnum.ACCEPT);
        log.info("doScheduleOrchestration scheduleOrchestrationParam : {}", JSON.toJSONString(scheduleOrchestrationParam));
        scheduleOrchestrationAppService.doScheduleOrchestration(BizRequestUtil.createWithCurrentUserSecurity(scheduleOrchestrationParam));
        // 总营确认需要重推日程
        synScheduleInfo(param.getExhibitionId(), param.getScheduleId());
        return param.getScheduleId();
    }

    /**
     * 回退展会议程
     *
     * @param bizRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean rollbackExhibitionSchedule(BizRequest<ExhibitionScheduleUpdateParam> bizRequest) {
        //回退版本的核心逻辑：
        //1.通过活动ID查询表：activity_schedule_item_origin_version，取：version_num排序，获取当前最大版本号的上一个版本对应的rowId
        //2.通过上步获取的rowId+日程ID查询日程备份表：activity_schedule_item_origin_detail，取source_data，即：源版本日程数据，对应封装类：ActivityScheduleItemData
        //3.解析ActivityScheduleItemData数据，封装后调用同步一线日程的接口
        ExhibitionScheduleUpdateParam param = bizRequest.getParam();
        // 获取展会信息
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(param.getExhibitionId());
        // 获取展会负责人信息
        List<ExhibitionDirectorDO> directorDOList = exhibitionDirectorRepository.fetchDirectorByExhibitionRowId(param.getExhibitionId());
        // 获取日程信息
        ActivityScheduleItemDO activityScheduleItemDO = scheduleItemRepository.selectByPrimaryKey(param.getScheduleId());

        // 校验展会报名状态必须关闭
        if (BooleanEnum.Y.isMe(exhibitionInfoDO.getEntryOpenStatus())) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.open.forbid");
        }
        // 校验登录人是否为总营负责人
        if (StringUtils.isEmpty(getAllAdmin(bizRequest.getEmpNo(), directorDOList))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.no.authority");
        }
        //校验：对应状态为待编排deal_status=wait且已变更has_changed="Y"，则能操作确认或回退，否则不能
        if (!(WAIT.isMe(activityScheduleItemDO.getDealStatus()) && BooleanEnum.Y.isMe(activityScheduleItemDO.getHasChanged()))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.confirmed.forbid.rollback");
        }

        return rollbackScheduleItemHelper.rollbackScheduleItem(param);
    }

    /**
     * 三营和政企的数据归，大国数量countryBigCnt 和 MVC数量custUMvcCnt的值改为 null
     * @param e
     */
    private void dealSpecificFieldForMarketData(ExhibitionAnalysisDTO e) {
        if(ExhibitionAnalysisMarketEnum.in(e.getDimensionId(),ExhibitionAnalysisMarketEnum.THREE,ExhibitionAnalysisMarketEnum.GOV)){
            e.setCountryBigCnt(null);
            e.setCustUMvcCnt(null);
        }
    }

    /**
     * 填充产品信息名称
     * @param e
     */
    void fillDimensionNameForProductInfo(ExhibitionAnalysisDTO e) {
        if(RiskConst.LANGUAGE_EN_US.equals(HeadersProperties.getXLangId())){
            e.setDimensionName(ExhibitionAnalysisProductEnum.getTypeEnByType(e.getDimensionId()));
        }
    }

    /**
     * 补充事业部名称与是否下钻
     * @param exhibitionAnalysisDTO
     * @param lookUpMap
     */
    private static void fillDimensionNameAndIsDrillDown(ExhibitionAnalysisDTO exhibitionAnalysisDTO, Map<String, FastLookupDto> lookUpMap) {
        //事业部名称转换
        FastLookupDto fastLookupDto = lookUpMap.get(exhibitionAnalysisDTO.getDimensionId());
        if(null!=fastLookupDto){
            exhibitionAnalysisDTO.setDimensionName(fastLookupDto.getMeaning());
            exhibitionAnalysisDTO.setIsDrillDown(fastLookupDto.getMemo());

        }
    }

    /**
     * 获取展会下钻数据
     * @param exhibitionId
     * @return
     * @throws Exception
     */
    Map<String, List<ExhibitionAnalysisDrillDownDTO>> queryExhibitionAnalysisDrillDown(String exhibitionId) throws Exception {
        Map<String, List<ExhibitionAnalysisDrillDownDTO>> marketingExhibitionAnalysisDrillDownDTOMap = Maps.newHashMap();

        //1.获取展会分下下钻数据
        ExhibitionAnalysisDrillDownQueryParam bizRequestParam = new ExhibitionAnalysisDrillDownQueryParam();
        bizRequestParam.setExhibitionId(exhibitionId);
        List<ExhibitionAnalysisDrillDownDTO> exhibitionAnalysisInfoList = accountAdapter.queryExhibitionAnalysisDrillDown(bizRequestParam);
        if(CollectionUtils.isEmpty(exhibitionAnalysisInfoList)){
            return marketingExhibitionAnalysisDrillDownDTOMap;
        }
        //去除无效数据
        exhibitionAnalysisInfoList = exhibitionAnalysisInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getIndexDimName())).collect(Collectors.toList());

        // 2.1 总体数据-公司级MTO
        List<ExhibitionAnalysisDrillDownDTO> comMtoDtoList = exhibitionAnalysisInfoList.stream()
                .filter(item -> ExhibitionAnalysisDimEnum.ONE.isMe(item.getDimensionType()) && ExhibitionAnalysisDrillDownDimEnum.COM_MTO.isMe(item.getIndexDimType())).collect(Collectors.toList());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.COM_MTO.getType(), this.pageExhibitionDrillDownList(comMtoDtoList,new ExhibitionMtoComparator()));

        // 2.2 总体数据-公司级大国
        List<ExhibitionAnalysisDrillDownDTO> bigCountryDtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item -> ExhibitionAnalysisDimEnum.ONE.isMe(item.getDimensionType()) && ExhibitionAnalysisDrillDownDimEnum.BIG_COUNTRY.isMe(item.getIndexDimType())).collect(Collectors.toList());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BIG_COUNTRY.getType(), this.pageExhibitionDrillDownList(bigCountryDtoList,new ExhibitionBigCountryComparator()));

        // 2.3 1营事业部
        List<ExhibitionAnalysisDrillDownDTO> bu1DtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item ->
	        		ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())
	        		&& ExhibitionAnalysisDrillDownDimEnum.BU_125.isMe(item.getIndexDimType())
	        		&& ExhibitionAnalysisMarketEnum.ONE.isMe(item.getDimensionId())
        		).collect(Collectors.toList());
        List<ExhibitionAnalysisDrillDownDTO> bu1ExhibitionAnalysisTree = this.pageExhibitionDrillDownList(bu1DtoList,new ExhibitionOverseasBuComparator());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BU_1.getType(), bu1ExhibitionAnalysisTree);

        // 2.4 2营事业部
        List<ExhibitionAnalysisDrillDownDTO> bu2DtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item ->
        		ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())
        		&& ExhibitionAnalysisDrillDownDimEnum.BU_125.isMe(item.getIndexDimType())
        		&& ExhibitionAnalysisMarketEnum.TWO.isMe(item.getDimensionId())
        				).collect(Collectors.toList());
        List<ExhibitionAnalysisDrillDownDTO> bu2ExhibitionAnalysisTree = this.pageExhibitionDrillDownList(bu2DtoList,new ExhibitionOverseasBuComparator());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BU_2.getType(), bu2ExhibitionAnalysisTree);

        // 2.5 5营事业部
        List<ExhibitionAnalysisDrillDownDTO> bu5DtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item ->
        		ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())
        		&& ExhibitionAnalysisDrillDownDimEnum.BU_125.isMe(item.getIndexDimType())
        		&& ExhibitionAnalysisMarketEnum.FIVE.isMe(item.getDimensionId())
        				).collect(Collectors.toList());
        List<ExhibitionAnalysisDrillDownDTO> bu5ExhibitionAnalysisTree = this.pageExhibitionDrillDownList(bu5DtoList,new ExhibitionOverseasBuComparator());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BU_5.getType(), bu5ExhibitionAnalysisTree);

        // 2.6 营销维度MTO事业部
        List<ExhibitionAnalysisDrillDownDTO> buMtoDtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item -> ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())
        				&& ExhibitionAnalysisDrillDownDimEnum.BU_MTO.isMe(item.getIndexDimType())).collect(Collectors.toList());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BU_MTO.getType(), this.pageExhibitionDrillDownList(buMtoDtoList,new ExhibitionMtoComparator()));

        // 2.7 营销维度3营事业部
        List<ExhibitionAnalysisDrillDownDTO> bu3DtoList = exhibitionAnalysisInfoList.stream()
        		.filter(item -> ExhibitionAnalysisDimEnum.TWO.isMe(item.getDimensionType())
        				&& ExhibitionAnalysisDrillDownDimEnum.BU_3.isMe(item.getIndexDimType())).collect(Collectors.toList());
        marketingExhibitionAnalysisDrillDownDTOMap.put(ExhibitionAnalysisDrillDownDimEnum.BU_3.getType(), this.pageExhibitionDrillDownList(bu3DtoList,new ExhibitionBaseComparator()));

        return marketingExhibitionAnalysisDrillDownDTOMap;
    }

	/**
	 * 处理数据
     * @param list
     * @param exhibitionMtoComparator
	 * @return
	 */
	private List<ExhibitionAnalysisDrillDownDTO> pageExhibitionDrillDownList(
			List<ExhibitionAnalysisDrillDownDTO> list, ExhibitionBaseComparator exhibitionMtoComparator) {
        List<ExhibitionAnalysisDrillDownDTO> rootList = list.stream()
                .filter(exhibitionDrillDown ->StringUtils.isBlank(StringUtils.replaceIgnoreCase(exhibitionDrillDown.getPIndexDimId(),"null","")))
                .sorted(exhibitionMtoComparator).collect(Collectors.toList());
        list.removeAll(rootList);
        for (ExhibitionAnalysisDrillDownDTO root: rootList) {
            root.setChildren(this.getChildren(root,list,exhibitionMtoComparator));
        }

        return rootList;
	}

	/**
	 * 递归获取数据
	 * @param root
	 * @param all
	 * @param exhibitionMtoComparator
	 * @return
	 */
	private List<ExhibitionAnalysisDrillDownDTO> getChildren(ExhibitionAnalysisDrillDownDTO root,
			List<ExhibitionAnalysisDrillDownDTO> all, ExhibitionBaseComparator exhibitionMtoComparator) {
        List<ExhibitionAnalysisDrillDownDTO> itemList = all.stream().filter(menu -> StringUtils.equalsIgnoreCase(root.getIndexDimId(),menu.getPIndexDimId())).sorted(exhibitionMtoComparator).collect(Collectors.toList());
        all.removeAll(itemList);
        for (ExhibitionAnalysisDrillDownDTO item: itemList) {
            item.setChildren(this.getChildren(item,all,exhibitionMtoComparator));
        }
        return itemList;
	}

    /**
     * 获取所有活动下所有已编排的日程信息列表
     *
     * @param acceptedScheduleItemIdSet     已编排的日程
     * @param activityId2EntityMaps         活动关联的客户信息
     * @param filterOrchestrationDealStatus 查询指定编排状态内的日程
     * @return 所有活动下所有已编排的日程信息列表
     */
    private List<ActivityScheduleItemDO> getAcceptedScheduleItemList(Set<String> acceptedScheduleItemIdSet,
                                                                     Map<String, ActivityInfoDO> activityId2EntityMaps,
                                                                     List<String> filterOrchestrationDealStatus) {
        return scheduleItemRepository.getRelationScheduleInfoList(Lists.newArrayList(activityId2EntityMaps.keySet()))
                .stream().filter(item -> {
                    if (CollectionUtils.isEmpty(filterOrchestrationDealStatus)) {
                        return ACCEPT.isMe(item.getDealStatus());
                    }
                    return filterOrchestrationDealStatus.contains(item.getDealStatus());
                }).peek(item -> acceptedScheduleItemIdSet.add(item.getRowId())).collect(Collectors.toList());
    }

    /**
     * 过滤出所有用户有权限的日程 + 用户是参与人的日程并汇总并按照日程时间排序
     *
     * @param acceptedScheduleItemList          已编排的日程
     * @param acceptScheduleItem2PeopleCodeMaps 已编排的日程与人员的信息
     * @param authorizedActivityRowIdSet        已谈参授权的活动id
     * @param allValidScheduleItemRowIds        所有有权限的日程id
     * @param activityId2ScheduleItemListMap    活动关联的日程列表
     * @return 所有用户有权限的日程 + 用户是参与人的日程并汇总并按照日程时间排序
     */
    private List<ActivityScheduleItemDO> getValidScheduleItemList(List<ActivityScheduleItemDO> acceptedScheduleItemList,
                                                                  Map<String, Set<String>> acceptScheduleItem2PeopleCodeMaps,
                                                                  Set<String> authorizedActivityRowIdSet,
                                                                  Set<String> allValidScheduleItemRowIds,
                                                                  Map<String, List<ActivityScheduleItemDO>> activityId2ScheduleItemListMap) {
        String empNo = HeadersProperties.getXEmpNo();
        return acceptedScheduleItemList.stream().filter(item -> {
            String itemRowId = item.getRowId();
            String activityRowId = item.getActivityRowId();
            Set<String> scheduleItemPeopleSet = Optional.ofNullable(acceptScheduleItem2PeopleCodeMaps.get(itemRowId)).orElse(new HashSet<>());
            return authorizedActivityRowIdSet.contains(activityRowId) || scheduleItemPeopleSet.contains(empNo);
        }).sorted(new Comparator<ActivityScheduleItemDO>() {
            @Override
            public int compare(ActivityScheduleItemDO o1, ActivityScheduleItemDO o2) {
                return ExhibitionBoardDataSource.getOrderOfActivityScheduleItems(o1, o2);
            }
        }).peek(item -> {
            allValidScheduleItemRowIds.add(item.getRowId());
            String activityRowId = item.getActivityRowId();
            List<ActivityScheduleItemDO> activityScheduleItems = Optional.ofNullable(activityId2ScheduleItemListMap.get(activityRowId)).orElse(new ArrayList<>());
            activityScheduleItems.add(item);
            activityId2ScheduleItemListMap.putIfAbsent(activityRowId, activityScheduleItems);
        }).collect(Collectors.toList());
    }

    /* Started by AICoder, pid:lcceei72f9uc3da146810a8af0911802fb5457fc */
    /**
     * 校验活动是否谈参授权
     *
     * @param scheduleDo        活动日程信息
     * @param authActivityRowIdMap 已授权的活动id
     * @param acceptScheduleItem2PeopleCodeMaps 日程参与人
     * @return 活动授权校验结果
     */
    private boolean checkTalkAuthority(ActivityScheduleItemDO scheduleDo, Map<Integer, Set<String>> authActivityRowIdMap, Map<String,
            Set<String>> acceptScheduleItem2PeopleCodeMaps) {
        String empNo = HeadersProperties.getXEmpNo();
        Set<String> scheduleItemPeopleSet = Optional
                .ofNullable(acceptScheduleItem2PeopleCodeMaps.get(scheduleDo.getRowId()))
                .orElse(new HashSet<>());
        if (scheduleItemPeopleSet.contains(empNo)) {
            return true;
        }
        return authActivityRowIdMap.entrySet()
                .stream()
                .anyMatch(entry -> entry.getValue().contains(scheduleDo.getActivityRowId())
                        && Objects.equals(entry.getKey(), NumberConstant.THREE));

    }
    /* Ended by AICoder, pid:lcceei72f9uc3da146810a8af0911802fb5457fc */

    /**
     * 组装展会日程信息
     *
     * @param scheduleItemDO 活动日程事项安排信息
     * @param ds             APP日程数据
     * @return 展会日程信息
     */
    private ExhibitionScheduleInfoVO assembleExhibitionScheduleInfoVo(ActivityScheduleItemDO scheduleItemDO, AppScheduleDataSource ds) {
        ExhibitionScheduleInfoVO exhibitionScheduleInfo = new ExhibitionScheduleInfoVO();
        String scheduleItemId = scheduleItemDO.getRowId();
        String activityRowId = scheduleItemDO.getActivityRowId();
        ActivityInfoDO activityInfo = ds.getActivityId2EntityMaps().get(activityRowId);
        String exhibitionRowId = Optional.ofNullable(activityInfo).map(ActivityInfoDO::getOriginRowId).orElse(Strings.EMPTY);
        exhibitionScheduleInfo.setExhibitionRowId(exhibitionRowId);
        exhibitionScheduleInfo.setActivityRowId(scheduleItemDO.getActivityRowId());
        exhibitionScheduleInfo.setActivityStartTime(DateUtils.getYmdHmDate(activityInfo.getStartTime()));
        exhibitionScheduleInfo.setActivityEndTime(DateUtils.getYmdHmDate(activityInfo.getEndTime()));
        exhibitionScheduleInfo.setScheduleItemRowId(scheduleItemId);
        exhibitionScheduleInfo.setScheduleItemType(scheduleItemDO.getScheduleItemType());
        exhibitionScheduleInfo.setScheduleItemName(scheduleItemDO.getScheduleItemName());
        exhibitionScheduleInfo.setScheduleDate(DateUtils.getDateFormatDay(scheduleItemDO.getScheduleDate()));
        exhibitionScheduleInfo.setDealStatus(scheduleItemDO.getDealStatus());
        exhibitionScheduleInfo.setDealStatusName(ResourceOrchestrationDealStatusEnum.getDescByType(scheduleItemDO.getDealStatus()));
        exhibitionScheduleInfo.setRemark(scheduleItemDO.getRemark());
        String scheduleTime = scheduleItemDO.getScheduleTime();
        exhibitionScheduleInfo.setScheduleTime(scheduleTime);
        if (!StringUtils.isBlank(scheduleTime)) {
            List<String> scheduleTimeArr = Lists.newArrayList(scheduleTime.split(WAVE));
            exhibitionScheduleInfo.setScheduleStartTime(scheduleTimeArr.get(ZERO));
            exhibitionScheduleInfo.setScheduleEndTime(scheduleTimeArr.get(ONE));
        }
        if (Objects.nonNull(ds.getVersion())) {
            exhibitionScheduleInfo.setPublishTime(DateUtils.getYmdHmDate(ds.getVersion().getPublishTime()));
        }
        exhibitionScheduleInfo.setPlaceType(scheduleItemDO.getPlaceType());
        exhibitionScheduleInfo.setPlaceName(scheduleItemDO.getPlaceName());
        ActivityCustomerInfoDO mainCustomerInfo = ds.getActivityId2MainCustomerInfoMap().get(activityRowId);
        if (!Objects.isNull(mainCustomerInfo)) {
            exhibitionScheduleInfo.setCustomerCode(mainCustomerInfo.getCustomerCode());
            exhibitionScheduleInfo.setCustomerName(mainCustomerInfo.getCustomerName());
            exhibitionScheduleInfo.setMktCode(mainCustomerInfo.getMktCode());
            exhibitionScheduleInfo.setMktName(mainCustomerInfo.getMktName());
            exhibitionScheduleInfo.setMtoCode(mainCustomerInfo.getMtoCode());
            exhibitionScheduleInfo.setMtoName(mainCustomerInfo.getMtoName());
            exhibitionScheduleInfo.setLocalCode(mainCustomerInfo.getLocalCode());
            exhibitionScheduleInfo.setLocalName(mainCustomerInfo.getLocalName());
            exhibitionScheduleInfo.setSanctionedPatryCode(mainCustomerInfo.getSanctionedPatryCode());
        }
        return exhibitionScheduleInfo;
    }

    /**
     * 组装客户参与人信息
     *
     * @param ds                     APP日程数据
     * @param scheduleItemDO         活动日程事项安排信息
     * @param exhibitionScheduleInfo 展会日程信息
     */
    private void assembleCustPeopleList(AppScheduleDataSource ds, ActivityScheduleItemDO scheduleItemDO,
                                        ExhibitionScheduleInfoVO exhibitionScheduleInfo) {
        Map<String, Map<String, ActivityRelationCustPeopleDO>> activityId2CustPeopleMaps = ds.getActivityId2CustPeopleMaps();
        Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemCustomerPeopleMap = ds.getScheduleItemCustomerPeopleMap();
        Map<String, ActivityRelationCustPeopleDO> custPeopleMap = Optional.ofNullable(activityId2CustPeopleMaps.get(scheduleItemDO.getActivityRowId())).orElse(new HashMap<>());
        List<ExhibitionScheduleCustPeopleInfoVO> custPeopleList = Optional.ofNullable(scheduleItemCustomerPeopleMap.get(scheduleItemDO.getRowId())).orElse(new ArrayList<>())
                .stream().map(itemPeopleDO -> {
                    ExhibitionScheduleCustPeopleInfoVO custPeopleInfo = new ExhibitionScheduleCustPeopleInfoVO();
                    custPeopleInfo.setContactNo(itemPeopleDO.getPeopleNo());
                    custPeopleInfo.setCustomerCode(itemPeopleDO.getCustomerCode());
                    custPeopleInfo.setContactName(itemPeopleDO.getPeopleName());
                    custPeopleInfo.setPositionName(itemPeopleDO.getPosition());
                    ActivityRelationCustPeopleDO custPeopleDo = custPeopleMap.get(itemPeopleDO.getPeopleNo());
                    if (!Objects.isNull(custPeopleDo)) {
                        custPeopleInfo.setContactLevel(custPeopleDo.getContactLevel());
                        custPeopleInfo.setSanctionedPatryCode(custPeopleDo.getSanctionedPatryCode());
                        custPeopleInfo.setContactLevel(custPeopleDo.getContactLevel());
                    }
                    return custPeopleInfo;
                }).collect(Collectors.toList());
        exhibitionScheduleInfo.setCustPeopleList(custPeopleList);
    }

    /**
     * 组装ZTE参与人信息
     *
     * @param ds                     APP日程数据
     * @param scheduleItemDO         活动日程事项安排信息
     * @param exhibitionScheduleInfo 展会日程信息
     */
    private void assembleZtePeopleList(AppScheduleDataSource ds, ActivityScheduleItemDO scheduleItemDO,
                                       ExhibitionScheduleInfoVO exhibitionScheduleInfo) {
        Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemZtePeopleMap = ds.getScheduleItemZtePeopleMap();
        List<ExhibitionScheduleZtePeopleInfoVO> ztePeopleList = Optional.ofNullable(scheduleItemZtePeopleMap.get(scheduleItemDO.getRowId()))
                .orElse(new ArrayList<>())
                .stream().map(itemPeopleDo -> {
                    ExhibitionScheduleZtePeopleInfoVO ztePeople = new ExhibitionScheduleZtePeopleInfoVO();
                    ztePeople.setPeopleCode(itemPeopleDo.getPeopleNo());
                    ztePeople.setPeopleName(itemPeopleDo.getPeopleName());
                    ztePeople.setPositionName(itemPeopleDo.getPosition());
                    ztePeople.setPeopleType(itemPeopleDo.getPeopleType());
                    ztePeople.setPeopleLabel(itemPeopleDo.getPeopleLabel());
                    return ztePeople;
                }).collect(Collectors.toList());
        exhibitionScheduleInfo.setZtePeopleList(ztePeopleList);
    }

    /**
     * 组装日程附件信息
     *
     * @param ds                     APP日程数据
     * @param scheduleItemDO         活动日程事项安排信息
     * @param exhibitionScheduleInfo 展会日程信息
     * @param authActivityRowIdMap   已谈参授权的活动id
     */
    private void assembleScheduleAttachmentInfoList(AppScheduleDataSource ds, ActivityScheduleItemDO scheduleItemDO,
                                                    ExhibitionScheduleInfoVO exhibitionScheduleInfo, Map<Integer, Set<String>> authActivityRowIdMap,
                                                    Map<String, Set<String>> acceptScheduleItem2PeopleCodeMaps) {
        Map<String, List<ActivityRelationAttachmentDO>> activityId2AttachmentListMap = ds.getActivityId2AttachmentListMap();
        List<ExhibitionAttachmentDetailsVO> scheduleAttachmentInfoList = Optional
                .ofNullable(activityId2AttachmentListMap.get(scheduleItemDO.getRowId()))
                .orElse(new ArrayList<>())
                .stream().map(attachmentDo -> {
                    boolean talkAuthorized = checkTalkAuthority(scheduleItemDO, authActivityRowIdMap, acceptScheduleItem2PeopleCodeMaps);
                    ExhibitionAttachmentDetailsVO attachmentDetail = new ExhibitionAttachmentDetailsVO();
                    attachmentDetail.setFileName(attachmentDo.getFileName());
                    attachmentDetail.setFileSize(attachmentDo.getFileSize());
                    attachmentDetail.setFileToken(talkAuthorized ? attachmentDo.getFileToken() : null);
                    return attachmentDetail;
                }).collect(Collectors.toList());
        exhibitionScheduleInfo.setScheduleAttachmentInfoList(scheduleAttachmentInfoList);
    }

    @Override
    public List<ExhibitionRoomOccupyInfoVO> queryExhibitionRoomOccupyInfo(BizRequest<ExhibitionRoomOccupyQueryParam> scheduleQueryParamBizRequest) {

        List<ExhibitionRoomOccupyInfoVO> resList = Lists.newArrayList();
        //1、查询会议室基本信息
        ExhibitionRoomOccupyQueryParam param = scheduleQueryParamBizRequest.getParam();
        String exhibitionRowId = param.getExhibitionRowId();
        BizRequest<String> exhibitionRoomRequest = BizRequestUtil.createWithCurrentUser(exhibitionRowId);
        List<ExhibitionRelationRoomVO> exhibitionRelationRoomVOList = exhibitionQueryService.getExhibitionRooms(exhibitionRoomRequest);
        if(CollectionUtils.isEmpty(exhibitionRelationRoomVOList)){
            return resList;
        }
        //2、查询有效的活动
        List<ActivityInfoDO> activityInfoDOS = this.listValidActivityInfo(exhibitionRowId);
        //3、开始组装入参查询日程
        List<String> activityIdList = activityInfoDOS.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());
        List<String> scheduleStatusList = Lists.newArrayList(ACCEPT.getCode(), WAIT.getCode());
        Date scheduleDate = param.getScheduleDate();
        List<ActivityScheduleItemDO> activityScheduleItemList = this.scheduleItemRepository.getScheduleListByActivityIdListAndStatusListAndScheduleDate(
                activityIdList,scheduleStatusList,scheduleDate);
        //4、过滤待编排、已编排的日程（排除掉当前日程）
        String excludeScheduleItemId = param.getExcludeScheduleItemId();
        Map<String,List<String>> scheduleItemMap = activityScheduleItemList.stream().filter(item-> !StringUtils.equals(item.getRowId(),excludeScheduleItemId))
                .collect(Collectors.groupingBy(ActivityScheduleItemDO::getPlaceName,Collectors.mapping(ActivityScheduleItemDO::getScheduleTime,Collectors.toList())));
        //5、组转数据（会议室占用情况）
        for (ExhibitionRelationRoomVO roomVO : exhibitionRelationRoomVOList) {
            String roomName = roomVO.getRoomName();
            List<String> schduleDateList = scheduleItemMap.getOrDefault(roomName, Lists.newArrayList());
            schduleDateList.sort(Comparator.naturalOrder());
            ExhibitionRoomOccupyInfoVO exhibitionRoomOccupyInfoVO = new ExhibitionRoomOccupyInfoVO();
            BeanUtils.copyProperties(roomVO,exhibitionRoomOccupyInfoVO);
            exhibitionRoomOccupyInfoVO.setScheduleItem(schduleDateList);
            resList.add(exhibitionRoomOccupyInfoVO);
        }
        //6、相似的会议室放在一起排序
        resList.sort(Comparator.comparing(ExhibitionRoomOccupyInfoVO::getRoomName));
        return resList;
    }

    void assembleScheduleOperationAuth(AppScheduleDataSource ds, ActivityScheduleItemDO scheduleItemDO,
                                       ExhibitionScheduleInfoVO exhibitionScheduleInfo, Map<Integer, Set<String>> authActivityRowIdMap) {
        ExhibitionInfoDO exhibitionInfoDO = ds.getExhibitionInfoDO();
        // 1. 我的日程
        boolean isAllSchedule = ds.isAllSchedule();
        boolean entryOpenStatus = BooleanEnum.Y.isMe(exhibitionInfoDO.getEntryOpenStatus());
        exhibitionScheduleInfo.initScheduleOperationAuth();
        if (!isAllSchedule) {
            // 1.1 展会报名已关闭，不可以再编辑日程
            if (!entryOpenStatus) {
                return;
            }
            // 1.2 只有报名开启时 活动为进行中的日程为可编辑状态
            Map<String, ActivityInfoDO> activityId2EntityMaps = ds.getActivityId2EntityMaps();
            ActivityInfoDO activityInfoDO = Optional.ofNullable(activityId2EntityMaps.get(scheduleItemDO.getActivityRowId())).orElse(new ActivityInfoDO());
            if (PROGRESS.isMe(activityInfoDO.getActivityStatus())) {
                exhibitionScheduleInfo.setEditable(true);
            }
            return;
        }

        // 2. 所有日程
        Set<String> headQuartersAuthIds = authActivityRowIdMap.getOrDefault(NumberConstant.ELEVEN, new HashSet<>());
        Set<String> subQuartersAuthIds = authActivityRowIdMap.getOrDefault(NumberConstant.TWELVE, new HashSet<>());
        OrchestrationControlEnum controlEnum = OrchestrationControlEnum.getByType(exhibitionInfoDO.getOrchestrationControl());
        boolean isSubControl = controlEnum == OrchestrationControlEnum.SUB;
        // 2.1 展会报名关闭 + 分营编排打开 + 当前用户为分营编排角色 + 当前日程为该分营编排角色所授予的权限范围内
        if (BooleanUtils.and(new Boolean[]{!entryOpenStatus, isSubControl, CollectionUtils.isNotEmpty(subQuartersAuthIds), subQuartersAuthIds.contains(scheduleItemDO.getActivityRowId())})) {
            exhibitionScheduleInfo.setEditable(true);
        }

        // 2.2 总营编排人员
        // 展会报名已关闭 + 总营编排已打开
        if (BooleanUtils.and(new Boolean[]{CollectionUtils.isNotEmpty(headQuartersAuthIds), !entryOpenStatus, !isSubControl})) {
            exhibitionScheduleInfo.setEditable(true);
            Map<String, Set<String>> scheduleItem2OrchIdsMap = ds.getScheduleItem2OrchIdsMap();
            if (BooleanEnum.Y.isMe(scheduleItemDO.getHasChanged())) {
                // 如果当前日程发生过变更 则该总营编排人员有确定的权限
                exhibitionScheduleInfo.setConfirmable(true);
                // 如果当前日程发生过变更 且该日程存在历史版本 才存在回退的权限
                exhibitionScheduleInfo.setFallbackable(scheduleItem2OrchIdsMap.containsKey(scheduleItemDO.getRowId()));
            }
        }
    }

    /**
     * 参数校验并设置发布角色
     * @param currentEmpNo
     * @param directorDOList
     */
    private void checkParamAndSetRoleType(ExhibitionScheduleParam param, String currentEmpNo, List<ExhibitionDirectorDO> directorDOList,
                            List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDOS) {
        // 获取展会信息
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(param.getExhibitionId());
        // 获取原有议程我司参与人和我司陪同人员列表员编号列表
        List<String> scheduleItemPeopleList = scheduleItemPeopleDOS.stream()
                .filter(item -> ZTE_PEOPLE.isMe(item.getPeopleType()) || ZTE_INTERFACE_PEOPLE.isMe(item.getPeopleType()))
                .map(ActivityScheduleItemPeopleDO::getPeopleNo)
                .collect(Collectors.toList());
        // 判断 展会开启角色校验
        if (BooleanEnum.Y.isMe(exhibitionInfoDO.getEntryOpenStatus())) {
            openExhibitionCheck(currentEmpNo, directorDOList, scheduleItemPeopleList);
            param.setOrgType(ScheduleOrchestrationRoleEnum.PERSONAL.getCode());
        }
        // 判断 展会关闭角色校验
        if (BooleanEnum.N.isMe(exhibitionInfoDO.getEntryOpenStatus())) {
            closeExhibitionCheck(currentEmpNo, directorDOList, scheduleItemPeopleList, exhibitionInfoDO);
            if (StringUtils.isNotEmpty(getAllAdmin(currentEmpNo, directorDOList))) {
                param.setOrgType(ScheduleOrchestrationRoleEnum.ALL_ADMIN.getCode());
            } else {
                param.setOrgType(ScheduleOrchestrationRoleEnum.SUB_ADMIN.getCode());
            }
        }
    }

    /**
     * 获取总营角色
     */
    private String getAllAdmin(String empNo, List<ExhibitionDirectorDO> directorDOList) {
        // 总营负责人
        String resourceAdminEmpNoStr = directorDOList.stream()
                .filter(item -> RESOURCE_ADMIN.getType().equals(item.getRoleType()))
                .map(ExhibitionDirectorDO::getEmployeeNo).collect(Collectors.joining(","));
        // 总营角色
        if (resourceAdminEmpNoStr.contains(empNo)) {
            return ScheduleOrchestrationRoleEnum.ALL_ADMIN.getCode();
        }
        return null;
    }

    /**
     * 获取分营角色
     */
    private String getSubAdmin(String empNo, List<ExhibitionDirectorDO> directorDOList) {
        // 分营负责人
        String resourcePointsAdminEmpNoStr = directorDOList.stream()
                .filter(item -> RESOURCE_POINTS_ADMIN.getType().equals(item.getRoleType()))
                .map(ExhibitionDirectorDO::getEmployeeNo).collect(Collectors.joining(","));
        // 分营角色
        if (resourcePointsAdminEmpNoStr.contains(empNo)) {
            return ScheduleOrchestrationRoleEnum.SUB_ADMIN.getCode();
        }
        return null;
    }

    /**
     * 打包日程发布数据
     * @param param
     * @param expertEmpNos
     * @return
     */
    private ScheduleOrchestrationParam packPublishData(ExhibitionScheduleParam param, Set<String> expertEmpNos, Set<String> leaderEmpNos, ResourceOrchestrationDealStatusEnum dealStatus) {
        // 初始化日程编排参数对象
        ScheduleOrchestrationParam scheduleOrchestrationParam = new ScheduleOrchestrationParam();
        scheduleOrchestrationParam.setPublish(TRUE);
        scheduleOrchestrationParam.setOrgType(param.getOrgType());
        scheduleOrchestrationParam.setExhibitionRowId(param.getExhibitionId());

        // 初始化日程详细信息对象
        ScheduleOrchestrationParam.OrchestrationDealDetail orchestrationDealDetail = new ScheduleOrchestrationParam.OrchestrationDealDetail();
        orchestrationDealDetail.setScheduleItemRowId(param.getScheduleId());
        orchestrationDealDetail.setActivityRowId(param.getActivityId());
        orchestrationDealDetail.setScheduleDate(param.getScheduleDate());
        orchestrationDealDetail.setScheduleTimeStart(param.getScheduleTimeStart());
        orchestrationDealDetail.setScheduleTimeEnd(param.getScheduleTimeEnd());
        orchestrationDealDetail.setPlaceType(param.getPlaceType());
        orchestrationDealDetail.setPlaceName(param.getPlaceName());
        orchestrationDealDetail.setDealStatus(dealStatus.getCode());

        // 组装领导数据
        String leaderStr = Optional.ofNullable(param.getListZtePeople()).orElse(Collections.emptyList()).stream()
                .map(ActivityScheduleItemPeopleVO::getPeopleNo)
                .filter(leaderEmpNos::contains)
                .collect(Collectors.joining(","));

        // 组装专家数据
        String expertStr = Optional.ofNullable(param.getListZtePeople()).orElse(Collections.emptyList()).stream()
                .map(ActivityScheduleItemPeopleVO::getPeopleNo)
                .filter(expertEmpNos::contains)
                .collect(Collectors.joining(","));

        ArrayList<ScheduleOrchestrationParam.OrchestrationScheduleItemPeople> orchestrationScheduleItemPeople = new ArrayList<>();

        // 组装客户参与人数据
        List<ActivityScheduleItemPeopleVO> listClientParticipant = param.getListClientParticipant();
        if (CollectionUtils.isNotEmpty(listClientParticipant)) {
            for (ActivityScheduleItemPeopleVO activityScheduleItemPeopleVO : listClientParticipant) {
                ScheduleOrchestrationParam.OrchestrationScheduleItemPeople scheduleItemPeople = new ScheduleOrchestrationParam.OrchestrationScheduleItemPeople();
                BeanUtils.copyProperties(activityScheduleItemPeopleVO, scheduleItemPeople);
                orchestrationScheduleItemPeople.add(scheduleItemPeople);
            }
        }

        // 组装我司参与人数据
        List<ActivityScheduleItemPeopleVO> listZtePeople = param.getListZtePeople();
        if (CollectionUtils.isNotEmpty(listZtePeople)) {
            for (ActivityScheduleItemPeopleVO listZtePerson : listZtePeople) {
                ScheduleOrchestrationParam.OrchestrationScheduleItemPeople scheduleItemPeople = new ScheduleOrchestrationParam.OrchestrationScheduleItemPeople();
                BeanUtils.copyProperties(listZtePerson, scheduleItemPeople);
                orchestrationScheduleItemPeople.add(scheduleItemPeople);
            }
        }

        // 设置领导、专家信息、参与人信息（客户参与人、我司参与人）
        orchestrationDealDetail.setZteLeader(leaderStr);
        orchestrationDealDetail.setZteExpert(expertStr);
        orchestrationDealDetail.setItemPeopleList(orchestrationScheduleItemPeople);

        // 将日程详细信息添加到日程编排对象中
        scheduleOrchestrationParam.setDetailList(Collections.singletonList(orchestrationDealDetail));

        return scheduleOrchestrationParam;
    }

    /**
     * 处理参与人VO转换
     */
    private List<ActivityScheduleItemPeopleVO> convertActivityScheduleItemPeopleDO(List<ActivityScheduleItemPeopleDO> activityScheduleItemPeopleDOList) {
        ArrayList<ActivityScheduleItemPeopleVO> itemPeopleVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityScheduleItemPeopleDOList)) {
            for (ActivityScheduleItemPeopleDO scheduleItemPeopleDO : activityScheduleItemPeopleDOList) {
                ActivityScheduleItemPeopleVO scheduleItemPeopleVO = new ActivityScheduleItemPeopleVO();
                scheduleItemPeopleVO.setPeopleType(scheduleItemPeopleDO.getPeopleType());
                scheduleItemPeopleVO.setPeopleNo(scheduleItemPeopleDO.getPeopleNo());
                scheduleItemPeopleVO.setPeopleName(scheduleItemPeopleDO.getPeopleName());
                scheduleItemPeopleVO.setPeopleLabel(scheduleItemPeopleDO.getPeopleLabel());
                scheduleItemPeopleVO.setPhoneNum(scheduleItemPeopleDO.getPhoneNum());
                scheduleItemPeopleVO.setPosition(scheduleItemPeopleDO.getPosition());
                scheduleItemPeopleVO.setCustomerCode(scheduleItemPeopleDO.getCustomerCode());
                itemPeopleVOS.add(scheduleItemPeopleVO);
            }
        }
        return itemPeopleVOS;
    }

    /**
     * 校验会议室、领导、专家是否有涉及变更
     * @param param
     * @param expertEmpNos
     * @return
     */

    private boolean checkChange(ExhibitionScheduleParam param, List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDOS, Set<String> expertEmpNos, Set<String> leaderEmpNos) {
        boolean isChange = false;
        // 是否使用到会议室
        boolean isPlaceChange = ScheduleItemPlaceTypeEnum.ROOM.isMe(param.getPlaceType()) && StringUtils.isNotEmpty(param.getPlaceName());

        // 原有议程所有人员信息
        Set<String> oldAllPeople = scheduleItemPeopleDOS.stream().map(ActivityScheduleItemPeopleDO::getPeopleNo)
                .collect(Collectors.toSet());
        // 我司参与人新增人员信息
        List<ActivityScheduleItemPeopleVO> ztePeople = param.getListZtePeople();

        // 是否涉及领导
        boolean isLeaderChange = ztePeople.stream().map(ActivityScheduleItemPeopleVO::getPeopleNo)
                .anyMatch(leaderEmpNos::contains) || oldAllPeople.stream().anyMatch(leaderEmpNos::contains);

        // 是否涉及专家
        boolean isExpertChange = ztePeople.stream().map(ActivityScheduleItemPeopleVO::getPeopleNo)
                .anyMatch(expertEmpNos::contains) || oldAllPeople.stream().anyMatch(expertEmpNos::contains);

        // 已编排的状态只要涉及到会议室、领导、专家其中任何一项则只需要更新活动数据，否则直接发布
        if (isPlaceChange || isLeaderChange || isExpertChange) {
            isChange = true;
        }
        return isChange;
    }

    /**
     * 补齐确认发布参数
     * @param scheduleItemPeopleDOS
     * @param param
     * @param scheduleItemDO
     */
    private ExhibitionScheduleParam buildScheduleParam(List<ActivityScheduleItemPeopleDO> scheduleItemPeopleDOS, ExhibitionScheduleUpdateParam param, ActivityScheduleItemDO scheduleItemDO) {
        ExhibitionScheduleParam scheduleParam = new ExhibitionScheduleParam();
        // 获取我司参与人
        List<ActivityScheduleItemPeopleDO> listZtePeople = scheduleItemPeopleDOS.stream().filter(itemPeopleDO ->
                ZTE_PEOPLE.isMe(itemPeopleDO.getPeopleType())).collect(Collectors.toList());
        // 获取客户参与人
        List<ActivityScheduleItemPeopleDO> listClientParticipant = scheduleItemPeopleDOS.stream().filter(itemPeopleDO ->
                CLIENT_PARTICIPANT.isMe(itemPeopleDO.getPeopleType())).collect(Collectors.toList());
        scheduleParam.setExhibitionId(param.getExhibitionId());
        scheduleParam.setActivityId(param.getActivityId());
        scheduleParam.setScheduleId(param.getScheduleId());
        scheduleParam.setListZtePeople(convertActivityScheduleItemPeopleDO(listZtePeople));
        scheduleParam.setListClientParticipant(convertActivityScheduleItemPeopleDO(listClientParticipant));
        scheduleParam.setPlaceType(scheduleItemDO.getPlaceType());
        scheduleParam.setPlaceName(scheduleItemDO.getPlaceName());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        scheduleParam.setScheduleDate(dateFormat.format(scheduleItemDO.getScheduleDate()));
        scheduleParam.setScheduleTimeStart(scheduleItemDO.getScheduleTime().split(WAVE)[ZERO]);
        scheduleParam.setScheduleTimeEnd(scheduleItemDO.getScheduleTime().split(WAVE)[ONE]);
        return scheduleParam;
    }

    /**
     * 展会开启参数校验
     * @param currentEmpNo
     * @param directorDOList
     * @param scheduleItemPeopleList
     */
    private void openExhibitionCheck(String currentEmpNo, List<ExhibitionDirectorDO> directorDOList, List<String> scheduleItemPeopleList) {
        // 是负责人但不是参与人的情况，优化提示
        if ((StringUtils.isNotEmpty(getAllAdmin(currentEmpNo, directorDOList)) ||
                StringUtils.isNotEmpty(getSubAdmin(currentEmpNo, directorDOList)))
                && !scheduleItemPeopleList.contains(currentEmpNo)) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.open.forbid");
        }
        // 检查当前登录人是否在参与人员列表中
        if (!scheduleItemPeopleList.contains(currentEmpNo)) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.item.no.permission");
        }
    }

    /**
     * 展会关闭参数校验
     * @param currentEmpNo
     * @param directorDOList
     * @param scheduleItemPeopleList
     * @param exhibitionInfoDO
     */
    private void closeExhibitionCheck(String currentEmpNo, List<ExhibitionDirectorDO> directorDOList, List<String> scheduleItemPeopleList, ExhibitionInfoDO exhibitionInfoDO) {
        // 是参与人但不是负责人 优化提示
        if ((StringUtils.isEmpty(getAllAdmin(currentEmpNo, directorDOList)) &&
                StringUtils.isEmpty(getSubAdmin(currentEmpNo, directorDOList))) && scheduleItemPeopleList.contains(currentEmpNo)) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.close.forbid");
        }
        // 分营开关开启，必须是有分营编排人角色才能编辑
        if (OrchestrationControlEnum.SUB.isMe(exhibitionInfoDO.getOrchestrationControl()) && StringUtils.isEmpty(getSubAdmin(currentEmpNo, directorDOList))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.forbid.admin");
        }
        // 分营开关关闭，必须是有总营编排人角色才能编辑
        if (OrchestrationControlEnum.ALL.isMe(exhibitionInfoDO.getOrchestrationControl()) && StringUtils.isEmpty(getAllAdmin(currentEmpNo, directorDOList))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.forbid.sub");
        }
    }

    /**
     * 推送日程
     * @param exhibitionId
     * @param scheduleId
     */
    private void synScheduleInfo(String exhibitionId, String scheduleId) {
        Map<String, ActivityResourceReservationScheduleDO> ori2Schedule = activityResourceReservationScheduleRepository.queryScheduleWithOriRowIds(
                Collections.singletonList(scheduleId), ReserveScheduleOriTypeEnum.ACTIVITY_SCHEDULE);
        // 已推送过的日程才重推日程
        if (Objects.nonNull(ori2Schedule.get(scheduleId))) {
            ScheduleOrchestrationSynNoticeParam scheduleParam = new ScheduleOrchestrationSynNoticeParam();
            scheduleParam.setExhibitionRowId(exhibitionId);
            scheduleParam.setScheduleItemRowIdList(Collections.singletonList(scheduleId));
            scheduleParam.setOrgType(ScheduleOrchestrationRoleEnum.ALL_ADMIN.getCode());
            scheduleOrchestrationAppService.doSynScheduleOrchestrationAndNotice(BizRequestUtil.createWithCurrentUserSecurity(scheduleParam));
        }
    }

}
