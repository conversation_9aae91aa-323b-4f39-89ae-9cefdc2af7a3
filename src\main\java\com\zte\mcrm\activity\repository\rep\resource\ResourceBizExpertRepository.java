package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.service.resource.model.ExpertQuery;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ExpertSearchParam;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResourceBizExpertRepository {

    /**
     * 添加专家资源信息（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ResourceBizExpertDO> recordList);

    /**
     * 删除专家资源信息
     *
     * @param expertIds 专家表id
     */
    int deleteSelective(List<String> expertIds);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ResourceBizExpertDO record);

    /**
     * 修改产品线专家启用状态
     * @param expertIds 专家表id
     * @param enableStatus 启用状态 Y-启用；N-不启用；
     * @return
     */
    int updateEnableStatusByPrimaryKeySelective(List<String> expertIds, String enableStatus);

    /**
     * 根据主键列表查询产品线专家
     * @param rowIds 主键
     * @return
     */
    List<ResourceBizExpertDO> selectByPrimaryKeys(List<String> rowIds);

    /**
     * 根据交流方向统计专家数
     * @return key:direction,num
     */
    List<HashMap<String, Object>> summaryExpertWithDirection();

    /**
     * 列表查询
     *
     * @param record 参数内容
     * @return
     */
    List<ResourceBizExpertDO> getList(ResourceBizExpertDO record);

    /**
     * 分页查询
     *
     * @param record   参数内容
     * @param pageNo   页码
     * @param pageSize 页面数据量
     * @return
     */
    List<ResourceBizExpertDO> getPage(ResourceBizExpertDO record, int pageNo, int pageSize);

    /**
     * 根据专家ID批量查询
     *
     * @param employeeNos 专家员工编号
     * @return {@link List< ResourceBizExpertDO>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:04
     */
    List<ResourceBizExpertDO> selectByEmployeeNos(@Param("employeeNos") List<String> employeeNos);

    /**
     * 分页查询
     *
     * @param param
     * @return {@link List< ResourceBizExpertDO>}
     * <AUTHOR>
     * @date 2023/5/19 下午5:42
     */
    PageRows<ResourceBizExpertDO> selectParticipantsPage(PageQuery<ExpertSearchParam> param);

    long selectParticipantsCount(ExpertSearchParam param);

    List<ResourceBizExpertDO> getExpertPageByConditions(ExpertQuery expertQuery);

    PageRows<ResourceBizExpertDO> getExpertPageByConditions(PageQuery<ExpertQuery> pageQuery);

    /**
     * 获取非领导身份的专家工号，用于数据迁移
     * @return
     */
    List<String> getNonLeaderExpertCodes();
}
