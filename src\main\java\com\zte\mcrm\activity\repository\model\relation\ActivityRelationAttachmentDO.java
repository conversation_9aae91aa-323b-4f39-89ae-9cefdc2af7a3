package com.zte.mcrm.activity.repository.model.relation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * table:activity_relation_attachment -- 
 */
@Setter
@Getter
@ToString
public class ActivityRelationAttachmentDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 附件所属场景。如：客户成员黑名单扫描结果附件；参谈；方案；纪要等等。枚举：AttachmentSceneTypeEnum */
    private String attachmentSceneType;

    /** 所属场景，关联的源row_id，如：方案表的row_id */
    private String sceneOriginRowId;

    /** 文件所在的协议类型。如：文档云、http等。枚举：FileProtocolEnum */
    private String fileProtocol;

    /** 文件token。如果是云文档这是对应key，如果是http则是对应附件url地址…… */
    private String fileToken;

    /** 文件名 */
    private String fileName;

    /** 附件额外地址信息补充说明 */
    private String additionalAddress;

    /** 备注 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 附件大小byte */
    private Long fileSize;
    
    /** 中文摘要 */
    private String abstractZh;
    
    /** 英文摘要 */
    private String abstractEn;

    /** 关联的ID */
    private String releatedId;
}