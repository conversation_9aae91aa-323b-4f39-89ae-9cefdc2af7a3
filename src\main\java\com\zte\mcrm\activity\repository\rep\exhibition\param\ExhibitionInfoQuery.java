package com.zte.mcrm.activity.repository.rep.exhibition.param;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.model.PageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: ExhibitionInfoQuery
 * @projectName zte-crm-custinfo-custvisit
 * @description: TODO
 * @date 2023/9/619:52
 */
@Getter
@Setter
@ToString
public class ExhibitionInfoQuery extends PageQuery {
    /**
     * 展会编号
     */
    private String exhibitionNo;

    /**
     * 展会ID
     */
    private String exhibitionRowId;

    /**
     * 展会名称
     */
    private String exhibitionName;

    /**
     * 员工编号
     */
    private String employeeNo;

    /**
     * 是否为管理员 1-是，其他值不是的
     */
    private int admin;

    /**
     * 是否为领导 1-是，其他值不是的
     */
    private int leader;

    /**
     * 展会报名开启状态。Y-开启，N-未开启
     */
    private String entryOpenStatus;

    /**
     * 判断展会是否在有效时间内。Y-判断，N-不判断。
     */
    private String validExhibitionTime;

    /**
     * 创建时间-开始
     */
    private Date createdStartDate;
    /**
     * 创建时间-结束
     */
    private Date createdEndDate;

    private String enabledFlag = BooleanEnum.Y.getCode();

    /**
     * 场所资源类型
     */
    private String placeResourceType;

    public void initCreateExhibitDateRange(){
        LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
        Date createdStartDate = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        this.createdStartDate = createdStartDate;
        this.createdEndDate = new Date();
        this.enabledFlag = null;
    }

}
