package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;

public interface ExhibitionInfoMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionInfoDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionInfoDO record);

    /**
     * query by primary key
     */
    ExhibitionInfoDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionInfoDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionInfoDO record);
}