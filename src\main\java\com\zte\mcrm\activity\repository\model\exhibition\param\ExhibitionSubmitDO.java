package com.zte.mcrm.activity.repository.model.exhibition.param;

import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.repository.model.exhibition.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ExhibitionSubmitDO
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会新增数据库存储对象
 * @date 2023/9/710:51
 */
@Getter
@Setter
@ToString
public class ExhibitionSubmitDO {
    /**
     * 操作人
     */
    private String optionEmpNo;
    /**
     * 操作类型:ExhibitionConstant:insert：新增/update:更新
     */
    private String infoOptionType;
    /**
     * 基本信息
     */
    private ExhibitionInfoDO infoDO;
    /**
     * 待新增展会工作通知定时任务
     */
    private CommonTaskEventDO needAddExhibitionTaskEvent;
    /**
     * 待修改工作通知定时任务列表
     */
    private List<CommonTaskEventDO> needModifyExhibitionTaskEvent;
    /**
     * 日程安排附件
     */
    private List<ExhibitionRelationAttachmentDO> attachmentDOList;
    /**
     * 展会负责人
     */
    private List<ExhibitionDirectorDO> directorDOList;
    /**
     * 领导资源
     */
    private List<ExhibitionRelationLeaderDO> leaderDOList;
    /**
     * 专家资源
     */
    private List<ExhibitionRelationExpertDO> expertDOList;
    /**
     * 会议室资源
     */
    private List<ExhibitionRelationRoomDO> roomDOList;
    /**
     * 酒店资源
     */
    private List<ExhibitionRelationHotelDO> hotelDOList;
    /**
     * 车辆资源
     */
    private List<ExhibitionRelationCarDO> carDOList;

}
