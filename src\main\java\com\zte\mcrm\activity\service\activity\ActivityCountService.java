package com.zte.mcrm.activity.service.activity;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.controller.activity.param.ContactVisitsDetailsParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ContactVisitsDetailsVO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryRequestVO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryResponseVO;
import com.zte.mcrm.isearch.service.impl.CustomerPortraitServiceImpl;

import java.util.List;

/**
 * 活动信息统计
 *
 * <AUTHOR>
 * @date 2024/2/2 下午3:03
 */
public interface ActivityCountService {

    /**
     * 统计融合活动信息
     * 与 {@link CustomerPortraitServiceImpl#homePageQuery(HomePageQueryRequestVO)} 保持一致
     *
     * @param bizRequest
     * @return {@link ServiceData<HomePageQueryResponseVO>}
     * <AUTHOR>
     * @date 2024/2/2 下午3:03
     */
    ServiceData<HomePageQueryResponseVO> queryIntegrationActivityCountInfo(BizRequest<HomePageQueryRequestVO> bizRequest);

    /**
     * 查询联系人拜访明细
     *
     * @param param 联系人拜访明细入参
     * @return ContactVisitsDetailsVO
     */
    List<ContactVisitsDetailsVO> recentContactVisitsDetails(ContactVisitsDetailsParam param);
}
