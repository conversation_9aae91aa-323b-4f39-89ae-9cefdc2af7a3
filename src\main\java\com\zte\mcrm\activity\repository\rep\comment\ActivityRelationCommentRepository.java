package com.zte.mcrm.activity.repository.rep.comment;

import com.zte.mcrm.activity.repository.model.comment.ActivityRelationCommentDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动沟通交流关联icenter评论信息
 * @createTime 2023年05月13日 16:03:00
 */
public interface ActivityRelationCommentRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective( ActivityRelationCommentDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective( ActivityRelationCommentDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityRelationCommentDO> queryAllByActivityRowId(String activityRowId);

    /**
     *  批量插入
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<ActivityRelationCommentDO> recordList);

    /**
     * 根据主键查询
     *
     * @param rowId
     * @return: ActivityRelationCommentDO
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 14:42
     */
    ActivityRelationCommentDO selectByPrimaryKey(String rowId);

    /**
     * 根据主键逻辑删除
     *
     * @param rowId
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 14:54
     */
    int deleteByPrimaryKey(String rowId);
}
