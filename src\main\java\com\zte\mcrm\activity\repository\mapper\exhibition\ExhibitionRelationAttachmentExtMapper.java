package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExhibitionRelationAttachmentExtMapper extends ExhibitionRelationAttachmentMapper {
    /**
     * 根据展会Id获取展会日程附件
     * @param exhibitionRowIds
     * @return
     * <AUTHOR>
     * @date 2023/9/14
     */
    List<ExhibitionRelationAttachmentDO> getAttachmentByExhibitionRowIds(@Param("exhibitionRowIds") List<String> exhibitionRowIds);
}
