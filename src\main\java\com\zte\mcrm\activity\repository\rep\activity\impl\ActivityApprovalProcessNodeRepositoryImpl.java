package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityApprovalProcessNodeExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ActivityApprovalProcessNodeRepositoryImpl implements ActivityApprovalProcessNodeRepository {
    @Autowired
    ActivityApprovalProcessNodeExtMapper extMapper;

    @Autowired
    IKeyIdService idService;

    @Override
    public int insertSelective(ActivityApprovalProcessNodeDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(idService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());

        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityApprovalProcessNodeDO record) {
        record.setLastUpdateDate(new Date());

        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityApprovalProcessNodeDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public Map<String, List<ActivityApprovalProcessNodeDO>> queryAllByActivityRowIds(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap()
                : extMapper.queryAllByActivityRowIds(activityRowIds)
                .stream().collect(Collectors.groupingBy(ActivityApprovalProcessNodeDO::getActivityRowId));
    }

    @Override
    public List<ActivityApprovalProcessNodeDO> queryByProcessRowId(String approvalProcessRowId) {
        AssertUtil.assertNotNull(approvalProcessRowId);
        return extMapper.queryByProcessRowId(approvalProcessRowId);
    }

    @Override
    public ActivityApprovalProcessNodeDO queryByActIdAndApprovalFlowNo(String activityRowId, String approvalFlowNo) {
        AssertUtil.assertNotNull(activityRowId);
        AssertUtil.assertNotNull(approvalFlowNo);
        return extMapper.queryByActIdAndApprovalFlowNo(activityRowId,approvalFlowNo);
    }

    @Override
    public List<ActivityApprovalProcessNodeDO> queryByActIdAndNodeType(String activityRowId, String nodeType) {
        AssertUtil.assertNotNull(activityRowId);
        AssertUtil.assertNotNull(nodeType);
        return extMapper.queryByActIdAndNodeType(activityRowId, nodeType);
    }

    /**
     * @param rowIds
     * @return
     */
    @Override
    public int deleteByRowIds(List<String> rowIds) {
        return extMapper.deleteByRowIds(rowIds);
    }
}
