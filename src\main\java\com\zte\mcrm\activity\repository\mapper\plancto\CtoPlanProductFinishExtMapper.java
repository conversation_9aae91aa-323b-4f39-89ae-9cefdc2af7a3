package com.zte.mcrm.activity.repository.mapper.plancto;

/**
 * <AUTHOR>
 * @date 2024年12月09日15:47
 */
/* Started by AICoder, pid:6e95b6f913l1fa9142a9092e80146803e9344966 */
import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CtoPlanProductFinishExtMapper extends CtoPlanProductFinishMapper {

    /**
     * 数据看板-查询产品维度
     *
     * @param planInfoId
     * @param employeeType
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinish(@Param("planInfoId") String planInfoId, @Param("employeeType") String employeeType);

    /**
     * 查询产品数据-排序是核心领导
     * @param planInfoId
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinishSorted(@Param("planInfoId") String planInfoId);

    /**
     * 更新有效状态为N
     * @param planInfoId
     * @return
     */
    int updateByPlanId(@Param("planInfoId") String planInfoId);

    /**
     * 处理所有未处理的任务
     *
     * @return
     */
    List<String> listByUndoProcess();

    /**
     * 查询 客户-参与人 活动信息
     *
     * @param param
     * @return
     */
    List<SelectCtoActivityInfoResDTO>  selectCtoActivityInfo(@Param("param") SelectCtoActivityParamDTO param);

    /**
     * 查询产品详情
     * @param rowId
     * @return
     */
    CtoPlanDetailDTO queryProductDetail(@Param("rowId") String rowId);

    /**
     * 根据计划Id查询产品数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinishByPlanId(@Param("planInfoId") String planInfoId);
}
/* Ended by AICoder, pid:6e95b6f913l1fa9142a9092e80146803e9344966 */
