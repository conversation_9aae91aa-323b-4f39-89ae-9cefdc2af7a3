package com.zte.mcrm.activity.common.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 加解密配置
 *
 * <AUTHOR>
 * @date 2024/5/14 下午8:32
 */
@Component
@Getter
public class EncryptConfig {

    /**
     * 加解密通用key，如果没有特殊需要，都可以使用此key进行加密
     */
    public static String commonEncryptKey;

    @Value("${common.encrypt.key: }")
    public void setCommonEncryptKey(String commonEncryptKey) {
        setStaticCommonEncryptKey(commonEncryptKey);
    }

    private static void setStaticCommonEncryptKey(String commonEncryptKey) {
        EncryptConfig.commonEncryptKey = commonEncryptKey;
    }

}
