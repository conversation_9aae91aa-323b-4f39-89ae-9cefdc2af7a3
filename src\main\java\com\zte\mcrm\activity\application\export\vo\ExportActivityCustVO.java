package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户活动-客户信息（客户、客户联系人）
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Getter
@Setter
public class ExportActivityCustVO {
    @Excel(name = "活动编号", orderNum = "1")
    private String activityRequestNo;
    @Excel(name = "议题", orderNum = "2")
    private String activityTitle;
    @Excel(name = "客户集团客户简称", orderNum = "3")
    private String mtoName;
    @Excel(name = "客户Account客户名称", orderNum = "4")
    private String mktName;
    @Excel(name = "客户单位", orderNum = "5")
    private String customerName;
    @Excel(name = "客户主管部门编号", orderNum = "6")
    private String belongBuId;
    @Excel(name = "客户主管部门名称", orderNum = "7")
    private String belongBuName;
    @Excel(name = "客户级别", orderNum = "8")
    private String custLevel;
    @Excel(name = "客户参与人", orderNum = "9")
    private String contactName;
    @Excel(name = "客户参与人所在部门", orderNum = "10")
    private String customerDepartment;
    @Excel(name = "客户参与人职务/岗位", orderNum = "11")
    private String positionName;
    @Excel(name = "客户参与人受限制主体", orderNum = "12")
    private String sanctionedPatryCode;
    @Excel(name = "客户参与人级别", orderNum = "13")
    private String contactLevel;
    @Excel(name = "是否主客户", orderNum = "14")
    private String mainCust;
}
