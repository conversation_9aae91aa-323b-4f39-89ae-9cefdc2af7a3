package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;

public interface ActivityScheduleItemMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleItemDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleItemDO record);

    /**
     * query by primary key
     */
    ActivityScheduleItemDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleItemDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleItemDO record);
}