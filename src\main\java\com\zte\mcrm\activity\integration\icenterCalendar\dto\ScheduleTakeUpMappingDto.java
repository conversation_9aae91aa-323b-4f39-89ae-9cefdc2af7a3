package com.zte.mcrm.activity.integration.icenterCalendar.dto;

import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日程占用时间查询结果映射对象
 * <pre>
 *     这里主要将日程时间占用接口中，我们需要的字段拿出来映射。更多字段以及结构可尝试test环境的接口调用查看
 *     http://calendar.test.zte.com.cn/zte-icenter-calendar-biz/v1/calendar/take_up
 * </pre>
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ScheduleTakeUpMappingDto {
    /**
     * 日程外部关联ID
     */
    private String outSystemId;

    /**
     * 占用开始时间
     */
    private String startDate;
    /**
     * 占用结束时间
     */
    private String endDate;
    /**
     * 时间对应时区。如：8
     */
    private String timeZone;
    /**
     * 时间对应时区编码。如：Asia/Shanghai
     */
    private String timeZoneCode;
    private EmpInfo userNo;

    /**
     * 获取员工编号
     *
     * @return
     */
    public String fetchEmpNo() {
        return userNo != null ? userNo.getUserNo() : null;
    }

    /**
     * 获取开始日期
     *
     * @return
     */
    public Date fetchStartDate() {
        return parseDate(startDate);
    }

    /**
     * 获取截止日期
     *
     * @return
     */
    public Date fetchEndDate() {
        return parseDate(endDate);
    }

    private Date parseDate(String dateStr) {
        Date date = null;
        SimpleDateFormat format = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        try {
//            format.setTimeZone(TimeZone.getTimeZone("GMT+" + timeZoneCode));
            date = format.parse(dateStr);
        } catch (ParseException e) {
            date = DateUtils.convertStringToDate(dateStr, DateUtils.YYYY_MM_DD_HH_MM_SS);
        }

        return date;
    }

    public static void main(String[] args) {
        ScheduleTakeUpMappingDto t = new ScheduleTakeUpMappingDto();
        t.setStartDate("2023-05-23 00:00:00");

        System.out.println(DateUtils.getDateString(t.fetchStartDate()));

    }

    @Setter
    @Getter
    public static class EmpInfo {
        @Setter
        @Getter
        String userNo;
    }

}
