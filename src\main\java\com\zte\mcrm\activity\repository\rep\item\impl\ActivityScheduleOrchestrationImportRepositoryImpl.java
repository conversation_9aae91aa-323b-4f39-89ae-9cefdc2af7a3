package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleOrchestrationImportExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationImportDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationImportRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 资源编排导入就
 *
 * <AUTHOR>
 */
@Component
public class ActivityScheduleOrchestrationImportRepositoryImpl implements ActivityScheduleOrchestrationImportRepository {
    @Resource
    private ActivityScheduleOrchestrationImportExtMapper activityScheduleOrchestrationImportExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityScheduleOrchestrationImportDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }

        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return activityScheduleOrchestrationImportExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityScheduleOrchestrationImportDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityScheduleOrchestrationImportExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public ActivityScheduleOrchestrationImportDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : activityScheduleOrchestrationImportExtMapper.selectByPrimaryKey(rowId);
    }
}
