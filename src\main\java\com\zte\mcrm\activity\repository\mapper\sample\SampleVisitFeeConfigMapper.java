package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;

public interface SampleVisitFeeConfigMapper {
    /**
     * all field insert
     */
    int insert(SampleVisitFeeConfigDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(SampleVisitFeeConfigDO record);

    /**
     * query by primary key
     */
    SampleVisitFeeConfigDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(SampleVisitFeeConfigDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(SampleVisitFeeConfigDO record);
}