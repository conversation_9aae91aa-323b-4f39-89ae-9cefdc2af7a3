package com.zte.mcrm.activity.application.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.dataservice.dto.ActivityAnalysisOfficePersonDTO;
import com.zte.mcrm.dataservice.dto.ActivityDivisionSummaryDTO;
import com.zte.mcrm.dataservice.dto.ActivityVisitAccountDTO;
import com.zte.mcrm.dataservice.dto.ActivityWeeklySummaryDTO;
import com.zte.mcrm.dataservice.model.ActivityAnalysisOfficePersonQuery;
import com.zte.mcrm.dataservice.model.ActivityApDataQuery;
import com.zte.mcrm.dataservice.model.ActivityVisitAccountQuery;
import com.zte.mcrm.dataservice.model.ActivityWeeklySummaryQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @projectName zte-crm-custinfo-custvisit
 * @date 2024/11/08 14:12
 */
public interface ActivityAnalysisService {
    /**
     * 客户画像-活动AP列表
     * @param bizRequest
     * @return pageRow
     * <AUTHOR>
     * @date 2024/6/18
     */
    List<ActivityAnalysisOfficePersonDTO> queryActivityOfficePersonDate(BizRequest<ActivityAnalysisOfficePersonQuery> bizRequest);

    /**
     *
     * @param visitAccountQuery
     * @return
     */
    List<ActivityVisitAccountDTO> listActivityVisitAccount(BizRequest<ActivityVisitAccountQuery> visitAccountQuery);

    /**
     * 查询活动周总结数据
     *
     * @param query 查询参数
     * @return 活动周总结
     */
    ActivityWeeklySummaryDTO activityWeeklySummary(BizRequest<ActivityWeeklySummaryQuery> query);

    /**
     * 查询管理干部参与活动情况汇总
     *
     * @param query 查询参数
     * @return 管理干部参与活动情况汇总
     */
    List<ActivityDivisionSummaryDTO> listActivityVisitManager(BizRequest<ActivityWeeklySummaryQuery> query);
}
