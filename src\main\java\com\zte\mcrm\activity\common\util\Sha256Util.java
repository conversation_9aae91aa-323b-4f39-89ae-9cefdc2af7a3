package com.zte.mcrm.activity.common.util;


import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;


/**
 * <AUTHOR> 10344346
 * @date 2023-12-08 13:51
 **/
public class Sha256Util {
    public  static String generateSha256Sign(String content, String secret){
        String sign = "";
        try{
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            byte[] hmacBytes = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
            sign = Base64.getEncoder().encodeToString(hmacBytes);
        }catch (Exception e) {
            ExceptionUtils.getStackTrace(e);
        }

        return sign;
    }

    public static String generateSignature(String tenantId, String secretKey, Long timestamp) {
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, secretKey).hmacHex(tenantId + timestamp);
    }



}
