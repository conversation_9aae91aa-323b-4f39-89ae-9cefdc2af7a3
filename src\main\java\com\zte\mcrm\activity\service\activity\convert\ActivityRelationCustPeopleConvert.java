package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityCustomerPeopleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户活动客户联系人转换
 *
 * <AUTHOR>
 * @date 2023/5/17 下午4:29
 */
public class ActivityRelationCustPeopleConvert {

    private ActivityRelationCustPeopleConvert() {
    }

    /**
     * 转换成VO
     *
     * @param custPeopleDOList
     * @return {@link List< ActivityCustomerPeopleVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午4:31
     */
    public static List<ActivityCustomerPeopleVO> convert2VO(List<ActivityRelationCustPeopleDO> custPeopleDOList) {
        if (CollectionUtils.isEmpty(custPeopleDOList)) {
            return Collections.emptyList();
        }
        return custPeopleDOList.stream().map(item -> {
            ActivityCustomerPeopleVO vo = new ActivityCustomerPeopleVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(ActivityCustomerPeopleVO::getContactNo))), ArrayList::new));
    }
}
