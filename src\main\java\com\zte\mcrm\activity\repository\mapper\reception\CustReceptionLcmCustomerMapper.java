package com.zte.mcrm.activity.repository.mapper.reception;

import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmCustomerDO;

public interface CustReceptionLcmCustomerMapper {
    /**
     * all field insert
     */
    int insert(CustReceptionLcmCustomerDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(CustReceptionLcmCustomerDO record);

    /**
     * query by primary key
     */
    CustReceptionLcmCustomerDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(CustReceptionLcmCustomerDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(CustReceptionLcmCustomerDO record);
}