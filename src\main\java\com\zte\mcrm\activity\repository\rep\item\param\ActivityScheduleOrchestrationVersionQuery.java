package com.zte.mcrm.activity.repository.rep.item.param;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-17 10:08
 **/
@Getter
@Setter
public class ActivityScheduleOrchestrationVersionQuery {
    /** 展会RowId */
    private String exhibitionRowId;

    /** 创建人，编排人员 */
    private String createdBy;

    /** 版本状态，草稿-draft，publish-已发布 */
    private String versionStatus;
    /**
     * publish_time > publishTimeAfter
     */
    private Date publishTimeAfter;

    /** Y-总营，N-分营。见：BooleanEnum */
    private String orgType;

}
