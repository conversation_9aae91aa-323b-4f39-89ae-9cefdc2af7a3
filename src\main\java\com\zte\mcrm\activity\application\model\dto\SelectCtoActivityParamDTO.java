package com.zte.mcrm.activity.application.model.dto;

import lombok.*;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectCtoActivityParamDTO {

    private Date scopeStart;
    private Date scopeEnd;
    // 参与人集合
    private Set<String> participants;
    // account客户
    private Set<String> accountCodes;

    public static SelectCtoActivityParamDTO buildByCount(Date scopeStart, Date scopeEnd, Set<String> accountCodeList, Set<String> participantList) {
        SelectCtoActivityParamDTO obj = new SelectCtoActivityParamDTO();
        obj.setScopeStart(scopeStart);
        obj.setScopeEnd(scopeEnd);
        obj.setAccountCodes(accountCodeList);
        obj.setParticipants(participantList);
        return obj;
    }
}
