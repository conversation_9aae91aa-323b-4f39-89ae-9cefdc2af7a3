package com.zte.mcrm.activity.integration.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.config.IcenterConfig;
import com.zte.mcrm.activity.integration.ai.dto.ContactDTO;
import com.zte.mcrm.activity.integration.ai.dto.ContactParamDTO;
import com.zte.mcrm.activity.integration.ai.dto.ContactQueryDTO;
import com.zte.mcrm.activity.integration.ai.dto.ContactResultDTO;
import com.zte.mcrm.activity.web.controller.ai.vo.IcenterDataVO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.common.ServiceDataUtil;
import com.zte.mcrm.common.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

import static com.zte.mcrm.activity.common.constant.AiConstant.CONTACT;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.VERTICAL;

@Component
@Slf4j
public class IcenterService {
    @Autowired
    private IcenterConfig config;

    // 用于生成消息ID的日期时间格式化模式
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    // 生成随机数的Random对象
    private static final Random RANDOM = new Random();
    // 消息组成的常量
    private static final String MSG_PREFIX = "AS";
    private static final String AT_PREFIX = " ";
    private static final String AT = "@";
    private static final int RANDOM_BOUND = 1000;


    // 根据当前时间戳、随机数和系统缩写生成唯一的消息ID
    private static String generateMsgId() {
        String timestamp = LocalDateTime.now().format(DATE_FORMAT);
        int randomNum = RANDOM.nextInt(RANDOM_BOUND);
        String formattedRandomNum = String.format("%03d", randomNum);
        return MSG_PREFIX + timestamp + formattedRandomNum + "iCRM";
    }

    // 构建发送消息的URL
    String buildSendMsgUrl(String uri, String sendTo) {
        return config.getSendMsgUrl()
                + uri
                + "?to=" + sendTo
                + "&msgid=" + generateMsgId()
                + "&msgtype=text"
                + "&sessionType=200";
    }

    // 基于多个参数和配置设置创建验证码
    public String createVerifyCode(String text, String uri, String timeStamp) {
        return DigestUtils.sha256Hex(String.join(VERTICAL,
                uri,
                config.getSpeechAuthKey(),
                config.getSpeechTerminalType(),
                config.getSpeechAuthCode(),
                timeStamp,
                String.valueOf(text.getBytes().length)));
    }

    // 使用提供的数据和参数向ICenter发送消息
    public boolean sendICenterMsg(IcenterDataVO msgData, String answerText) {
        try {
            String body = Base64.encodeBase64String((AT + msgData.getSenderCName() + StringUtils.SPACE + AT_PREFIX + answerText).getBytes());
            String msgProperty = buildMsgProperty(msgData);
            String uri = msgData.getRobot();
            String sendTo = msgData.getGroupID();
            String timeStamp = String.valueOf(System.currentTimeMillis());
            String verifyCode = createVerifyCode(body, uri, timeStamp);
            String senderName = Base64.encodeBase64String(("zh="+msgData.getRobotCName() + VERTICAL + "en="+msgData.getRobotEName()).getBytes());
            Map<String, String> headers = Maps.newHashMap();
            headers.put("uri", uri);
            headers.put("Terminal-Type", config.getSpeechTerminalType());
            headers.put("authKey", config.getSpeechAuthKey());
            headers.put("Sender-Name", senderName);
            headers.put("verfiyCode", verifyCode);
            headers.put("msgProperty", msgProperty);
            headers.put("Timestamp", timeStamp);

            String url = buildSendMsgUrl(uri, sendTo);

            log.info("正在发送ICenter消息: URL={}, Headers={}, Body={}.", url, headers, body);
            String result = HttpClientUtil.httpPostWithJSON(url, body, headers);
            log.info("ICenter消息发送并返回结果: {}", result);
        } catch (Exception e) {
            log.error("发送ICenter消息时发生错误", e);
            return false;
        }
        return true;
    }

    private String buildMsgProperty(IcenterDataVO msgData) {
        String format = "sysCode={0};serviceid={1};at=\"{2}#1\";dispMode=0;stype=1;repMsgId={3};channel=\"{4}\";repUid=\"{5}\";repType=1;repTxt=\"{6}\";";
        String msgProperty = MessageFormat.format(format, 2, msgData.getRobot(), msgData.getSender(), msgData.getMessageID(),
                msgData.getMessageID(), "sip:"+msgData.getSender()+"@zte.com.cn", Base64.encodeBase64String(msgData.getMsgBody().getBytes()));
        return msgProperty;
    }


    public List<ContactDTO> getZteContact(List<String> listName) {
        if (CollectionUtils.isEmpty(listName)) {
            return Lists.newArrayList();
        }
        List<ContactDTO> resultList = Lists.newArrayList();
        for(String name : listName){
            List<ContactDTO> list = listZteContact(name);
            resultList.addAll(list);
        }
        return resultList;
    }
    public List<ContactDTO> listZteContact(String listName) {
        ContactQueryDTO queryDTO = new ContactQueryDTO();
        queryDTO.setKeyword(listName);
        ContactParamDTO paramDTO = new ContactParamDTO();
        paramDTO.setName(CONTACT);
        paramDTO.setPage(1);
        paramDTO.setRows(100);
        queryDTO.setApps(Lists.newArrayList(paramDTO));
        Map<String, String> headerParamsMap = Maps.newHashMap();
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, HeadersProperties.getXEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, HeadersProperties.getXAuthValue());
        // 请求
        try {
            log.info("查询中兴人员入参:{}", JSON.toJSONString(paramDTO));
            String result = HttpClientUtil.httpPostWithJSON(config.getIcenterContactUrl(), JSON.toJSONString(queryDTO), headerParamsMap);
            log.info("查询中兴人员入参的返回结果:{}", result);
            result = JsonSanitizer.sanitize(result);
            ServiceData<Object> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<Object>>() {
            }.getType());
            if (ServiceDataUtil.validSuccess(serviceData) && Objects.nonNull(serviceData.getBo())) {
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(serviceData.getBo()));
                String contactString = jsonObject.getString("contact");
                ServiceData<ContactResultDTO> serviceDataContact = JSON.parseObject(contactString, new TypeReference<ServiceData<ContactResultDTO>>() {
                }.getType());
                if (ServiceDataUtil.validSuccess(serviceDataContact) && Objects.nonNull(serviceDataContact.getBo())) {
                   return  serviceDataContact.getBo().getRows();
                }
            }
        } catch (Exception e) {
            log.error("Error occurs when queryUserInfo", e);
        }
        return Lists.newArrayList();
    }
}
