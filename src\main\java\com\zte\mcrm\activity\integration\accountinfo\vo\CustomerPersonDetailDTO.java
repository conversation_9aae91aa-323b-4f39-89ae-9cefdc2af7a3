package com.zte.mcrm.activity.integration.accountinfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户联系人详细信息
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Getter
@Setter
public class CustomerPersonDetailDTO {
    @ApiModelProperty("联系人编号")
    private String contactNo;
    @ApiModelProperty("联系人基本信息")
    private CustomerPersonBaseDTO baseInfo;

    @ApiModelProperty("联系人部门信息")
    private CustomerPersonDeptDTO deptInfo;

    //todo: 合规信息VO

    public CustomerPersonDetailDTO() {
        baseInfo = new CustomerPersonBaseDTO();
        deptInfo = new CustomerPersonDeptDTO();
    }
}
