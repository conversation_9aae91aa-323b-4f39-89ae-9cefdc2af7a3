package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import java.util.List;

public interface ExhibitionRelationLeaderMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationLeaderDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationLeaderDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationLeaderDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationLeaderDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationLeaderDO record);
}