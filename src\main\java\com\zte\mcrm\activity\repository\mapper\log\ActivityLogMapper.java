package com.zte.mcrm.activity.repository.mapper.log;

import com.zte.mcrm.activity.repository.model.log.ActivityLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动日志
 *
 * @author: 汤踊10285568
 * @date: 2023/9/2 13:24
 */
@Mapper
public interface ActivityLogMapper {
    /**
     * 根据主键查询
     *
     * @param rowId 主键
     * @return 实体
     * <AUTHOR>
     * @date 2023/09/02
     */
    ActivityLogDO get(@Param("rowId") String rowId);

    /**
     * 查询列表
     *
     * @param entity 查询条件
     * @return 实体集合
     * <AUTHOR>
     * @date 2023/09/02
     */
    List<ActivityLogDO> getList(ActivityLogDO entity);

    /**
     * 删除
     *
     * @param rowId 主键
     * @return 删除总数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int delete(@Param("rowId") String rowId);

    /**
     * 动态新增
     *
     * @param entity 新增实体
     * @return 新增总数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int insert(ActivityLogDO entity);

    /**
     * 批量新增
     *
     * @param list 新增实体集合
     * @return 新增总数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int insertByBatch(List<ActivityLogDO> list);

    /**
     * 更新
     *
     * @param entity 更新条件
     * @return 更新影响总数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int update(ActivityLogDO entity);
}
