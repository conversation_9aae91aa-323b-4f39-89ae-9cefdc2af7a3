package com.zte.mcrm.activity.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * RDC相关配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
public class RdcConfig {
    /**
     * RDC原始需求信息查询地址
     */
    @Value("${rdc.rr.url:https://inone.test.zte.com.cn/ZXRDCloud/RDCloud/WIC/rest/workspaces/WANT/queries/query_work_items}")
    private String rrUrl;
    /**
     * 租户ID。如何外部没有传入，则默认使用这个
     */
    @Value("${rdc.tenant.id:ZTE}")
    private String tenantId;


}
