package com.zte.mcrm.activity.repository.mapper.plancto;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日16:13
 */
/* Started by AICoder, pid:m98164555155cbf14ff609ac9080ac05c013fff1 */
@Mapper
public interface CtoPlanExeReportExtMapper extends CtoPlanExeReportMapper {
    // 自定义扩展方法可以在这里添加

    /**
     * 根据CTO握手计划ID获取执行报表数据
     *
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoPlanExeReportDO> getByCtoPlanInfoId(@Param("ctoPlanInfoId") String ctoPlanInfoId);

    /**
     * 更新有效状态为N
     * @param planInfoId
     * @return
     */
    int updateByPlanId(@Param("planInfoId") String planInfoId);

    /**
     * 查询执行报表中待执行的数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanExeReportDO> listExeReportWait(@Param("ctoPlanInfoId") String planInfoId);
}
/* Ended by AICoder, pid:m98164555155cbf14ff609ac9080ac05c013fff1 */
