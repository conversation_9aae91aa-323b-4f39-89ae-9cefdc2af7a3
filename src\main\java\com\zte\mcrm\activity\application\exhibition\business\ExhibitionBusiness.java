package com.zte.mcrm.activity.application.exhibition.business;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.web.controller.exhibition.param.*;
import com.zte.mcrm.customermgr.util.LocalMessageUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.enums.resource.RequiredResourceTypeEnum.*;

/**
 * <AUTHOR>
 * @title: ExhibitionBusiness
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会业务相关
 * @date 2023/9/1510:28
 */
public class ExhibitionBusiness {

    /**
     * 展会创建入参校验
     * @param request 入参业务对象
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    public static void validatorExhibitionSubmitParam(BizRequest<ExhibitionSubmitParam> request) {
        ExhibitionInfoParam infoParam = request.getParam().getInfoParam();
        ValidationUtils.validateMessage(infoParam);
        infoParam.validatorExhibitionTime();

        isNotBlankCheckExhibitionOrConference(infoParam);
        List<ExhibitionResourcesParam> resourcesList = infoParam.getResourcesList();
        List<ExhibitionDirectorParam> directorParams = request.getParam().getDirectorParams();
        ValidationUtils.validateListMessage(directorParams);

        if (CollectionUtils.isNotEmpty(directorParams)){
            List<ExhibitionDirectorParam> pointList = directorParams.stream().filter(d -> StringUtils.equalsAny(d.getRoleType(),
                    ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN.getType(), ExhibitionDirectorRoleTypeEnum.REFERENCE_ADMIN.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pointList)){
                List<ExhibitionDirectorParam> pointAuthList = pointList.stream().filter(x -> CollectionUtils.isEmpty(x.getOrgAuthList())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pointAuthList)){
                    throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("exhibition.administrator.source.is.null"));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(resourcesList)){
            resourcesList.forEach(exhibitionResourcesParam -> {
                validateExhibitionParam(request, exhibitionResourcesParam);
            });
        }
    }

    /**
     * 展会、大会 的官网信息是否必填校验；大会的大会类型必填校验
     * @param infoParam
     */
    public static void isNotBlankCheckExhibitionOrConference(ExhibitionInfoParam infoParam) {
        if (PlaceResourceTypeEnum.EXHIBITION.isMe(infoParam.getPlaceResourceType())) {
            if (StringUtils.isBlank(infoParam.getOfficialWebsite())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("exhibition.official.website.can.not.be.null"));
            }
        } else if (PlaceResourceTypeEnum.CONFERENCE.isMe(infoParam.getPlaceResourceType())) {
            if (StringUtils.isBlank(infoParam.getConferenceType())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("conference.conference.type.can.not.be.null"));
            }
        }
    }

    /**
     * 校验入参
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateExhibitionParam(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        validateLeader(request, exhibitionResourcesParam);
        validateExpert(request, exhibitionResourcesParam);
        validateMeetingRoom(request, exhibitionResourcesParam);
        validateHotel(request, exhibitionResourcesParam);
        validateCar(request, exhibitionResourcesParam);
    }

    /**
     * 专家校验
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateExpert(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        if (EXPERT.isMe(exhibitionResourcesParam.getResourceType())){
            ValidationUtils.validateListMessage(request.getParam().getExpertParams());
        }
    }

    /**
     * 领导校验
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateLeader(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        if (LEADER.isMe(exhibitionResourcesParam.getResourceType())){
            ValidationUtils.validateListMessage(request.getParam().getLeaderParams());
        }
    }

    /**
     * 车辆校验
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateCar(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        if (CAR.isMe(exhibitionResourcesParam.getResourceType())){
            if (CollectionUtils.isNotEmpty(request.getParam().getCarParams())){
                ValidationUtils.validateListMessage(request.getParam().getCarParams());
            }
        }
    }

    /**
     * 酒店校验
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateHotel(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        if (HOTEL.isMe(exhibitionResourcesParam.getResourceType())){
            Map<String, List<ExhibitionRelationHotelParam>> hotelParamsMap = request.getParam().getHotelParams();
            if (null != hotelParamsMap && hotelParamsMap.size() != NumberConstant.ZERO){
                for(String key : hotelParamsMap.keySet()){
                    List<ExhibitionRelationHotelParam> hotelParamList = hotelParamsMap.get(key);
                    ValidationUtils.validateListMessage(hotelParamList);
                }
            }
        }
    }

    /**
     * 会议室校验
     * @param request
     * @param exhibitionResourcesParam
     * @return
     * <AUTHOR>
     * @date 2023/10/19
     */
    public static void validateMeetingRoom(BizRequest<ExhibitionSubmitParam> request, ExhibitionResourcesParam exhibitionResourcesParam) {
        if (MEETING_ROOM.isMe(exhibitionResourcesParam.getResourceType())){
            if (CollectionUtils.isNotEmpty(request.getParam().getRoomParams())){
                ValidationUtils.validateListMessage(request.getParam().getRoomParams());
                request.getParam().getRoomParams().forEach(room ->{
                    room.validateRoomFiled();
                });
            }
        }
    }

    /**
     * 获取月份
     * @param monthTemp
     * @return
     */
    public static String getMonth(int monthTemp) {
        if(monthTemp < 10) {
            return CharacterConstant.ZERO + monthTemp;
        }
        return String.valueOf(monthTemp);
    }
}
