package com.zte.mcrm.activity.repository.model.summary;

import java.util.Date;

/**
 * table:activity_summary_ap -- 
 */
public class ActivitySummaryApDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String activityRowId;

    /** 活动纪要row_id */
    private String activitySummaryRowId;

    /** 和ap进行关联的id */
    private String eventId;

    /** AP任务ID */
    private String taskId;

    /** 关联AP单号 */
    private String apNo;

    /** AP名称 */
    private String apName;

    /** AP亮灯状态 1-绿，2-黄，3-红。枚举：ApLightStatusEnum */
    private String apLightStatus;

    /** AP状态。枚举：ApStatusEnum */
    private String apStatus;

    /** 责任人 */
    private String apResponsible;

    /** ap任务类型。apTask-AP   coreTask-核心任务 */
    private String apType;

    /** 开始时间 */
    private Date beginTime;

    /** 截止时间 */
    private Date endTime;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getActivitySummaryRowId() {
        return activitySummaryRowId;
    }

    public void setActivitySummaryRowId(String activitySummaryRowId) {
        this.activitySummaryRowId = activitySummaryRowId == null ? null : activitySummaryRowId.trim();
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId == null ? null : eventId.trim();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    public String getApNo() {
        return apNo;
    }

    public void setApNo(String apNo) {
        this.apNo = apNo == null ? null : apNo.trim();
    }

    public String getApName() {
        return apName;
    }

    public void setApName(String apName) {
        this.apName = apName == null ? null : apName.trim();
    }

    public String getApLightStatus() {
        return apLightStatus;
    }

    public void setApLightStatus(String apLightStatus) {
        this.apLightStatus = apLightStatus == null ? null : apLightStatus.trim();
    }

    public String getApStatus() {
        return apStatus;
    }

    public void setApStatus(String apStatus) {
        this.apStatus = apStatus == null ? null : apStatus.trim();
    }

    public String getApResponsible() {
        return apResponsible;
    }

    public void setApResponsible(String apResponsible) {
        this.apResponsible = apResponsible == null ? null : apResponsible.trim();
    }

    public String getApType() {
        return apType;
    }

    public void setApType(String apType) {
        this.apType = apType == null ? null : apType.trim();
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}