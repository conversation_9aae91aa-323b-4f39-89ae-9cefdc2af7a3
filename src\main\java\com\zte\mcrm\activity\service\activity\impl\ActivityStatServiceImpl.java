package com.zte.mcrm.activity.service.activity.impl;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.DateFormatUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.service.activity.ActivityStatService;
import com.zte.mcrm.activity.service.activity.param.ActivityStatDataSource;
import com.zte.mcrm.activity.service.activity.support.IActivityStatDataSourceSupport;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityStatParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityRelationSamplePointVO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityVisitingSamplePointStatVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityStatServiceImpl implements ActivityStatService {

    @Autowired
    private IActivityStatDataSourceSupport activityStatDataSourceSupport;


    @Override
    public PageRows<ActivityRelationSamplePointVO> getActivityRelationSamplePointList(BizRequest<PageQuery<ActivityStatParam>> bizRequest) {
        PageQuery<ActivityStatParam> requestParam = bizRequest.getParam();
        ActivityStatParam activityStatParam = requestParam.getParam();
        if (!activityStatParam.validate() || !requestParam.validatePage()) {
            return PageRowsUtil.buildEmptyPage(requestParam.getPageNo(), requestParam.getPageSize());
        }
        ActivityStatDataSource activityStatDataSource = activityStatDataSourceSupport.getActivityStatDataSourceByOriginRowId(bizRequest);
        List<ActivityInfoDO> activityInfoDOList = activityStatDataSource.getActivityInfoDOList();
        if (CollectionUtils.isEmpty(activityInfoDOList)) {
            return PageRowsUtil.buildEmptyPage(requestParam.getPageNo(), requestParam.getPageSize());
        }
        return PageRowsUtil.singleBuildPageRow(activityStatDataSource.getActivityInfoDOPageRows(), item -> convertActivityInfo2ActivityRelationSamplePointVO(item, activityStatDataSource));

    }

    @Override
    public ActivityVisitingSamplePointStatVO getVisitingSamplePointStat(BizRequest<ActivityStatParam> bizRequest) {
        ActivityVisitingSamplePointStatVO activityVisitingSamplePointStatVO = new ActivityVisitingSamplePointStatVO();
        activityVisitingSamplePointStatVO.init();
        ActivityStatParam activityStatParam = bizRequest.getParam();
        if (!activityStatParam.validate()) {
            return activityVisitingSamplePointStatVO;
        }
        ActivityStatDataSource visitingSamplePointStatDataSource = activityStatDataSourceSupport.getVisitingSamplePointStatDataSource(bizRequest);
        activityVisitingSamplePointStatVO.setVisitCount(visitingSamplePointStatDataSource.getVisitCount());
        activityVisitingSamplePointStatVO.setLatestVisitTime(DateFormatUtil.format(visitingSamplePointStatDataSource.getLatestVisitTime(), DateFormatUtil.DATE_YYYY_MM_DD_PATTERN));
        return activityVisitingSamplePointStatVO;
    }

    private static ActivityRelationSamplePointVO convertActivityInfo2ActivityRelationSamplePointVO(ActivityInfoDO activityInfoDO, ActivityStatDataSource activityStatDataSource) {
        ActivityRelationSamplePointVO activityRelationSamplePointVO = new ActivityRelationSamplePointVO();
        String activityId = activityInfoDO.getRowId();
        activityRelationSamplePointVO.setActivityRowId(activityId);
        activityRelationSamplePointVO.setActivityRequestNo(activityInfoDO.getActivityRequestNo());
        activityRelationSamplePointVO.setActivityTitle(activityInfoDO.getActivityTitle());
        activityRelationSamplePointVO.setActivityStatus(activityInfoDO.getActivityStatus());
        activityRelationSamplePointVO.setVisitTime(DateFormatUtil.format(activityInfoDO.getStartTime(), DateFormatUtil.DATE_YYYY_MM_DD_HH_MM_SS_PATTERN));
        activityRelationSamplePointVO.setActivityType(activityInfoDO.getActivityType());
        activityRelationSamplePointVO.setOriginRowId(activityInfoDO.getOriginRowId());
        activityRelationSamplePointVO.setApplyPeopleNameNo(activityStatDataSource.getEmployeeName(activityInfoDO.getApplyPeopleNo()) + activityInfoDO.getApplyPeopleNo());
        activityRelationSamplePointVO.setDeptFullName(activityStatDataSource.getOrgInfo(activityInfoDO.getApplyDepartmentNo()).getHrOrgNamePath());
        ActivityCustomerInfoDO activityMainCustomerInfo = activityStatDataSource.getActivityMainCustomerInfo(activityId);
        activityRelationSamplePointVO.setMktName(activityMainCustomerInfo.getMktName());
        activityRelationSamplePointVO.setMtoName(activityMainCustomerInfo.getMtoName());
        activityRelationSamplePointVO.setCustomerName(activityMainCustomerInfo.getCustomerName());
        activityRelationSamplePointVO.setCusContactPeople(activityStatDataSource.getActivityRelationMainCustPeople(activityId));
        activityRelationSamplePointVO.setZteContactPeople(activityStatDataSource.getActivityRelationZtePeople(activityId));
        activityRelationSamplePointVO.setCanViewActivityDetail(activityStatDataSource.getHasCurrSamplePointManageAuth()
                                                               || activityStatDataSource.getHasLeaderAuth()
                                                               || activityStatDataSource.getHasSamplePointAdminAuth());
        return activityRelationSamplePointVO;
    }


}
