package com.zte.mcrm.activity.common.auth;

import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.enums.AuthConstraintFieldEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

import static com.zte.mcrm.activity.common.constant.RoleConstant.AUTHORITY_ROLE_ACTIVITY_ADMIN;

/**
 * 客户融合活动角色约束模型
 *
 * <AUTHOR>
 * @date 2023/8/18 下午5:35
 */
@Getter
@Setter
@ToString
public class ActivityRoleConstraintModel {

    /**
     * 角色编号
     */
    private String roleCode;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 是否可删除
     */
    private Boolean deletable;

    /**
     * 是否可作废
     */
    private Boolean voidable;

    /**
     * 是否可撤回
     */
    private Boolean cancelable;

    /**
     * 是否可变更
     */
    private Boolean changeable;

    /**
     * 申请人部门
     */
    private Set<String> applyDepartmentNoSet;

    /**
     * 主客户的Group和Account
     */
    private Set<String> customerGroupAccountSet;

    /**
     * 交流方向
     */
    private Set<String> communicationDirectionSet;

    /**
     * 活动类型
     */
    private Set<String> activityTypeSet;

    /**
     * 客户的主管部门
     */
    private Set<String> customerBelongBuIdSet;

    /**
     * 是否可编辑
     *
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:35
     */
    public boolean editable() {
        return Objects.nonNull(this.editable) && this.editable;
    }

    /**
     * 是否可删除
     *
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:35
     */
    public boolean deletable() {
        return Objects.nonNull(this.deletable) && this.deletable;
    }

    /**
     * 是否可作废
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/30 13:49
     */
    public boolean voidable() {
        return Objects.nonNull(this.voidable) && this.voidable;
    }

    /**
     * 是否可撤回
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/30 13:49
     */
    public boolean cancelable() {
        return Objects.nonNull(this.cancelable) && this.cancelable;
    }

    /**
     * 是否可编辑
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/30 13:49
     */
    public boolean changeable() {
        return Objects.nonNull(this.changeable) && this.changeable;
    }

    /**
     * 添加约束
     *
     * @param constraints       约束值
     * @param constraintEnum    约束类型
     * <AUTHOR>
     * @date 2023/8/18 下午5:36
     */
    public void addConstraints(Collection<String> constraints, AuthConstraintFieldEnum constraintEnum) {
        if (CollectionUtils.isEmpty(constraints) || Objects.isNull(constraintEnum)) {
            return;
        }
        switch (constraintEnum) {
            case APPLY_DEPARTMENT:
                if (Objects.isNull(this.applyDepartmentNoSet)) {
                    this.applyDepartmentNoSet = Sets.newHashSet();
                }
                this.applyDepartmentNoSet.addAll(constraints);
                break;
            case CUSTOMER_GROUP_ACCOUNT:
                if (Objects.isNull(this.customerGroupAccountSet)) {
                    this.customerGroupAccountSet = Sets.newHashSet();
                }
                this.customerGroupAccountSet.addAll(constraints);
                break;
            case COMMUNICATION_DIRECTION:
                if (Objects.isNull(this.communicationDirectionSet)) {
                    this.communicationDirectionSet = Sets.newHashSet();
                }
                this.communicationDirectionSet.addAll(constraints);
                break;
            case ACTIVITY_TYPE:
                if (Objects.isNull(this.activityTypeSet)) {
                    this.activityTypeSet = Sets.newHashSet();
                }
                this.activityTypeSet.addAll(constraints);
                break;
            case CUSTOMER_BELONG:
                if (Objects.isNull(this.customerBelongBuIdSet)) {
                    this.customerBelongBuIdSet = Sets.newHashSet();
                }
                this.customerBelongBuIdSet.addAll(constraints);
                break;
            default:
                break;
        }
    }
}
