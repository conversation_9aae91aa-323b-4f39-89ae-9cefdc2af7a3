package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitySummaryApExtMapper extends ActivitySummaryApMapper {

    /**
     * 查询活动关联的所有AP
     *
     * @param activityRowId 活动RowId
     * @return
     */
    @Deprecated
    List<ActivitySummaryApDO> queryAllApForActivity(String activityRowId);

    /**
     * 查询活动相关AP信息
     * @param activityIds
     * @return
     */
    List<ActivitySummaryApDO> queryAllByActivityRowId(@Param("activityIds") List<String> activityIds);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 通过活动Id批量删除
     * @param operator  操作者
     * @param activityIds    活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:53
     */
    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list")List<ActivitySummaryApDO> list);

    List<ActivitySummaryApDO> queryEmpNoTransList(DataTransParam searchParam);
}