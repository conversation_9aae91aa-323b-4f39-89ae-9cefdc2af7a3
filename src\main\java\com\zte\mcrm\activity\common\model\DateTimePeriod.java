package com.zte.mcrm.activity.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 时间区间模型
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class DateTimePeriod {

    /**
     * 区间开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 区间截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    public DateTimePeriod() {
    }

    public DateTimePeriod(Date startTime, Date endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    /**
     * 输入的时间是否在该日期时间区间范围内（时间范围都不为空）
     *
     * @param time 时间
     * @return true-在范围内；false-不在范围内，任意区间空，或者不在范围内
     */
    public boolean in(Date time) {
        return startTime != null && endTime != null && time != null
                && time.compareTo(startTime) > 0 && time.compareTo(endTime) < 0;
    }

    /**
     * 有效的时间范围
     * @return false-无效（starTime > endTime），true-其他情况
     */
    public boolean validRange() {
        return !(startTime != null && endTime != null && startTime.after(endTime));
    }

    /**
     * 输入的时间是否在该日期时间区间范围内（时间范围为空的一边表示无限小 或无限大）
     * <pre>
     *     true: time=2023-05-19 [null, 2023-05-20];
     *     true: time=2023-05-19 [2023-05-18, null];
     *     true: time=2023-05-19 [2023-05-18, 2023-05-20];
     *     true: time=2023-05-19 [null, null];
     *     false: time=2023-05-19 [2023-06-18, 2023-06-20];
     *     false: time=2023-05-19 [null, 2023-04-20];
     *     false: time=2023-05-19 [2023-06-20, null];
     *     false: time=null
     * </pre>
     *
     * @param time 时间
     * @return true-在范围内；false-不在范围内，任意区间空，或者不在范围内
     */
    public boolean inIgnorePeriodNull(Date time) {
        if (time == null) {
            return false;
        }

        boolean left = true;
        boolean right = true;

        if (startTime != null) {
            left = time.compareTo(startTime) >= 0;
        }
        if (endTime != null) {
            right = time.compareTo(endTime) <= 0;
        }

        return left && right;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DateTimePeriod period = (DateTimePeriod) o;
        boolean left = (this.startTime == null && period.startTime == null) || (this.startTime != null && period.startTime != null && this.startTime.equals(period.startTime));
        boolean right = (this.endTime == null && period.endTime == null) || (this.endTime != null && period.endTime != null && this.endTime.equals(period.endTime));

        return left && right;
    }

    /**
     * 校验时间范围
     *
     * @param
     * @return {@link boolean} true 合法 false 不合法
     * <AUTHOR>
     * @date 2023/5/23 下午10:16
     */
    public boolean checkPeriod(int period, int unit) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return false;
        }
        switch (unit) {
            case Calendar.DAY_OF_MONTH:
                return !DateUtils.addDays(startTime, period).after(endTime);
            case Calendar.WEEK_OF_YEAR:
                return !DateUtils.addWeeks(startTime, period).after(endTime);
            case Calendar.MONTH:
                return !DateUtils.addMonths(startTime, period).after(endTime);
            case Calendar.YEAR:
                return !DateUtils.addYears(startTime, period).after(endTime);
            case Calendar.HOUR_OF_DAY:
                return !DateUtils.addHours(startTime, period).after(endTime);
            case Calendar.MINUTE:
                return !DateUtils.addMinutes(startTime, period).after(endTime);
            case Calendar.SECOND:
                return !DateUtils.addSeconds(startTime, period).after(endTime);
            case Calendar.MILLISECOND:
                return !DateUtils.addMilliseconds(startTime, period).after(endTime);
            default:
                return false;
        }
    }
}
