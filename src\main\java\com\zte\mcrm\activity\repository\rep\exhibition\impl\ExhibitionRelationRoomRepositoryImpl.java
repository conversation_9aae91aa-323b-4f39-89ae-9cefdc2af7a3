package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationRoomExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationRoomRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午3:19
 **/
@Repository
public class ExhibitionRelationRoomRepositoryImpl implements ExhibitionRelationRoomRepository {

    @Autowired
    private ExhibitionRelationRoomExtMapper roomExtMapper;

    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ExhibitionRelationRoomDO record) {
        setDefaultValue(record);
        return roomExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationRoomDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }

        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return roomExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ExhibitionRelationRoomDO> queryAllByRoomRowIds(List<String> roomRowIds) {
        return CollectionUtils.isEmpty(roomRowIds) ? Collections.emptyList()
                : roomExtMapper.queryAllByRoomRowIds(roomRowIds);
    }

    @Override
    public Map<String, List<ExhibitionRelationRoomDO>> getRelationRoomListByExhibitionRowIds(Set<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return Collections.emptyMap();
        }

        return roomExtMapper.queryAllByExhibitionIds(Lists.newArrayList(exhibitionRowIds))
                .stream().collect(Collectors.groupingBy(ExhibitionRelationRoomDO::getExhibitionRowId));
    }

    @Override
    public int batchInsert(List<ExhibitionRelationRoomDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        return roomExtMapper.batchInsert(records.stream().map(record -> {
            this.setDefaultValue(record);
            return record;
        }).collect(Collectors.toList()));
    }

    @Override
    public int batchUpdate(List<ExhibitionRelationRoomDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        String empNo = HeadersProperties.getXEmpNo();
        Date updatedDate = new Date();
        List<ExhibitionRelationRoomDO> needUpdateRecords = records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getRowId()))
                .map(record -> {
                    record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(empNo));
                    record.setLastUpdateDate(updatedDate);
                    return record;
                }).collect(Collectors.toList());

        return CollectionUtils.isEmpty(needUpdateRecords) ? ZERO : roomExtMapper.batchUpdate(needUpdateRecords);
    }

    @Override
    public int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return ZERO;
        }

        return roomExtMapper.softDeleteByExhibitionRowIds(operator, exhibitionRowIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return ZERO;
        }

        List<ExhibitionRelationRoomDO> needDeletedRecords = rowIds.stream().map(rowId -> {
            ExhibitionRelationRoomDO record = new ExhibitionRelationRoomDO();
            record.setRowId(rowId);
            record.setLastUpdatedBy(operator);
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.N.getCode());
            return record;
        }).collect(Collectors.toList());
        return roomExtMapper.batchUpdate(needDeletedRecords);
    }

    private void setDefaultValue(ExhibitionRelationRoomDO exhibitionRelationRoomDO) {
        exhibitionRelationRoomDO.setRowId(Optional.ofNullable(exhibitionRelationRoomDO.getRowId()).orElse(keyIdService.getKeyId()));
        exhibitionRelationRoomDO.setCreatedBy(Optional.ofNullable(exhibitionRelationRoomDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationRoomDO.setLastUpdatedBy(Optional.ofNullable(exhibitionRelationRoomDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationRoomDO.setCreationDate(new Date());
        exhibitionRelationRoomDO.setLastUpdateDate(new Date());
        exhibitionRelationRoomDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }
}
