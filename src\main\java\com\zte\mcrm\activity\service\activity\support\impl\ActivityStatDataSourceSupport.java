package com.zte.mcrm.activity.service.activity.support.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.RoleConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.authorityclient.VisitUppService;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.service.activity.param.ActivityStatDataSource;
import com.zte.mcrm.activity.service.activity.support.IActivityStatDataSourceSupport;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityStatParam;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.adapter.bo.EmployeeBO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ActivityStatDataSourceSupport implements IActivityStatDataSourceSupport {

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ActivityCustomerInfoRepository customerInfoRepository;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Autowired
    private HrmOrgInfoSearchService hrmOrgInfoSearchService;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    @Autowired
    private VisitUppService uppService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private SamplePointInfoRepository samplePointInfoRepository;

    /**
     * 分页查找活动信息
     * @param bizRequest
     * @return
     */
    @Override
    public ActivityStatDataSource getActivityStatDataSourceByOriginRowId(BizRequest<PageQuery<ActivityStatParam>> bizRequest) {
        ActivityStatDataSource activityStatDataSource = new ActivityStatDataSource();
        PageQuery<ActivityStatParam> requestParam = bizRequest.getParam();
        PageRows<ActivityInfoDO> activityInfoDOPageRows = getActivityInfoDOList(requestParam);
        activityStatDataSource.setActivityInfoDOPageRows(activityInfoDOPageRows);
        if (activityInfoDOPageRows.getTotal() < NumberConstant.ONE) {
            return activityStatDataSource;
        }
        List<ActivityInfoDO> activityInfoDOList = activityInfoDOPageRows.getRows();
        activityStatDataSource.setActivityInfoDOList(activityInfoDOList);
        List<String> activityRowIds = activityInfoDOList.stream().map(e -> e.getRowId()).distinct().collect(Collectors.toList());
        Map<String, ActivityCustomerInfoDO> activityIdAndMainCustomerInfoMap = getActivityMainCustomerInfoDOMap(activityRowIds);
        Map<String, List<ActivityRelationCustPeopleDO>> activityIdAndMainCustPeopleMap = getActivityMainCustPeopleMap(activityRowIds);
        Map<String, List<ActivityRelationZtePeopleDO>> activityRowId2ZtePeoplesMap = getActivityRelationZtePeopleMap(activityRowIds);
        Map<String, OrgInfoVO> applyDepartmentInfoMap = getApplyDepartmentInfoMap(activityInfoDOList);
        Map<String, EmployeeBO> allPeopleNoMap = getActivityApplyPeopleInfoMap(activityInfoDOList);
        activityStatDataSource.setActivityRowIds(activityRowIds);
        activityStatDataSource.setActivityIdAndMainCustomerInfoMap(activityIdAndMainCustomerInfoMap);
        activityStatDataSource.setActivityIdAndMainCustPeopleMap(activityIdAndMainCustPeopleMap);
        activityStatDataSource.setActivityRowId2ZtePeoplesMap(activityRowId2ZtePeoplesMap);
        activityStatDataSource.setApplyDepartmentInfoMap(applyDepartmentInfoMap);
        activityStatDataSource.setApplyPeopleMap(allPeopleNoMap);
        activityStatDataSource.setHasLeaderAuth(hasLeaderAuth(bizRequest.getEmpNo()));
        activityStatDataSource.setHasSamplePointAdminAuth(hasSamplePointAdminAuth());
        activityStatDataSource.setHasCurrSamplePointManageAuth(hasCurrSamplePointManageAuth(requestParam.getParam(), bizRequest.getEmpNo()));
        return activityStatDataSource;
    }

    @Override
    public ActivityStatDataSource getVisitingSamplePointStatDataSource(BizRequest<ActivityStatParam> bizRequest) {
        ActivityStatDataSource activityStatDataSource = new ActivityStatDataSource();
        ActivityInfoQuery query = new ActivityInfoQuery();
        ActivityStatParam activityStatParam = bizRequest.getParam();
        query.setOriginRowIdList(Lists.newArrayList(activityStatParam.getOriginRowId()));
        query.setActivityType(activityStatParam.getActivityType());
        query.setEndTime(new Date());
        Integer total = activityInfoRepository.countListForSpecialStatus(query);
        activityStatDataSource.setVisitCount(total);
        if (total < NumberConstant.ONE) {
            return activityStatDataSource;
        }
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectLatestVisitingActivity(query);
        activityStatDataSource.setLatestVisitTime(activityInfoDO.getStartTime());
        return activityStatDataSource;
    }

    /**
     * 查询活动申请人员信息
     * @param activityInfoDOList
     * @return
     */
    private Map<String, EmployeeBO> getActivityApplyPeopleInfoMap(List<ActivityInfoDO> activityInfoDOList) {
        List<String> applyPeopleList = activityInfoDOList.stream().map(e -> e.getApplyPeopleNo()).distinct().collect(Collectors.toList());
        Map<String, EmployeeBO> allPeopleNoMap = employeeAdapter.getEmpInfosByShortNo(applyPeopleList);
        return allPeopleNoMap;
    }

    private Map<String, OrgInfoVO> getApplyDepartmentInfoMap(List<ActivityInfoDO> activityInfoDOList) {
        List<String> applyDepartmentNoList = activityInfoDOList.stream().map(e -> e.getApplyDepartmentNo()).distinct().collect(Collectors.toList());
        Map<String, OrgInfoVO> applyDepartmentInfoMap = hrmOrgInfoSearchService.getOrgInfoByOrgIds(applyDepartmentNoList);
        return applyDepartmentInfoMap;
    }

    private Map<String, List<ActivityRelationZtePeopleDO>> getActivityRelationZtePeopleMap(List<String> activityRowIds) {
        Map<String, List<ActivityRelationZtePeopleDO>> activityRowId2ZtePeoplesMap = activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(Sets.newHashSet(activityRowIds));
        return activityRowId2ZtePeoplesMap;
    }

    /**
     * 获取活动主客户参与人
     * @param activityRowIds
     * @return
     */
    private Map<String, List<ActivityRelationCustPeopleDO>> getActivityMainCustPeopleMap(List<String> activityRowIds) {
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleList = activityRelationCustPeopleRepository.queryAllCustPeopleForActivity(activityRowIds);
        Map<String, List<ActivityRelationCustPeopleDO>> activityIdAndMainCustPeopleMap = activityRelationCustPeopleList.stream().filter(e -> BooleanEnum.Y.isMe(e.getMainCust())).collect(Collectors.groupingBy(e -> e.getActivityRowId()));
        return activityIdAndMainCustPeopleMap;
    }

    /**
     * 获取活动主客户
     * @param activityRowIds
     * @return
     */
    private Map<String, ActivityCustomerInfoDO> getActivityMainCustomerInfoDOMap(List<String> activityRowIds) {
        List<ActivityCustomerInfoDO> activityCustomerList = customerInfoRepository.getActivityCustomerListByActivityRowIdSet(Sets.newHashSet(activityRowIds));
        Map<String, ActivityCustomerInfoDO> activityIdAndCustomerInfoMap = activityCustomerList.stream().filter(e -> BooleanEnum.Y.isMe(e.getMainCust()))
                .collect(Collectors.toMap(ActivityCustomerInfoDO::getActivityRowId, e -> e, (k1, k2) -> k1));
        return activityIdAndCustomerInfoMap;
    }

    /**
     * 根据活动类型和源活动id查询活动列表
     * @param requestParam
     * @return
     */
    private PageRows<ActivityInfoDO> getActivityInfoDOList(PageQuery<ActivityStatParam> requestParam) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        ActivityStatParam activityStatParam = requestParam.getParam();
        query.setOriginRowIdList(Lists.newArrayList(activityStatParam.getOriginRowId()));
        query.setActivityType(activityStatParam.getActivityType());
        query.setEndTime(new Date());
        query.setStartRow((requestParam.getPageNo() - 1) * requestParam.getPageSize());
        query.setRowSize(requestParam.getPageSize());
        Integer count = activityInfoRepository.countListForSpecialStatus(query);
        if (count < NumberConstant.ONE) {
            return PageRowsUtil.buildEmptyPage(requestParam.getPageNo(), requestParam.getPageSize());
        }
        List<ActivityInfoDO> activityInfoDOList = activityInfoRepository.getListForSpecialStatus(query);
        return PageRowsUtil.buildPageRow(requestParam.getPageNo(), requestParam.getPageSize(), count, activityInfoDOList);
    }

    /**
     * 是否领导
     * @param currUser
     * @return
     */
    public Boolean hasLeaderAuth(String currUser) {
        Map<String, Boolean> leaderMap = userCenterService.isLeaderByShortNo(Lists.newArrayList(currUser));
        if (Objects.equals(Boolean.TRUE, leaderMap.get(currUser))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 当前用户是否样板点管理员
     * @return
     */
    public Boolean hasSamplePointAdminAuth() {
        if (uppService.hasRoleAuthority(MsaRpcRequestUtil.createWithCurrentUser(RoleConstant.AUTHORITY_ROLE_SAMPLE_POINT_ADMIN))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 是否当前样板点管理员
     * @param activityStatParam
     * @param currUser
     * @return
     */
    public Boolean hasCurrSamplePointManageAuth(ActivityStatParam activityStatParam, String currUser) {
        if (!ActivityTypeEnum.in(activityStatParam.getActivityType(), ActivityTypeEnum.VISITING_SAMPLE)) {
            return Boolean.FALSE;
        }
        SamplePointInfoDO samplePointInfoDO = samplePointInfoRepository.selectByPrimaryKey(activityStatParam.getOriginRowId());
        if (Objects.isNull(samplePointInfoDO)) {
            return Boolean.FALSE;
        }
        if (Objects.equals(currUser, samplePointInfoDO.getAdminEmpNo())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
