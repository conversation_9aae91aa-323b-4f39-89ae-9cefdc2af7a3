package com.zte.mcrm.activity.repository.mapper.plancto;
/* Started by AICoder, pid:i8f6b15a3bg963f14c4f08f9200a0b266b735ff5 */

import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CtoPlanOrgFinishExtMapper extends CtoPlanOrgFinishMapper {

    /**
     * 查询事业部维度数据
     * @param planInfoId
     */
    List<CtoPlanOrgFinishDO> listOrgFinish(@Param("planInfoId") String planInfoId);

    /**
     * 查询事业部维度数据-按事业部排序
     * @param planInfoId
     */
    List<CtoPlanOrgFinishDO> listOrgFinishSorted(@Param("planInfoId") String planInfoId);

    /**
     * 获取所有待处理的营销部分数据
     *
     */
    List<String> listByUndoProcess();

    /**
     * 计算达成场次
     *
     * @param countParam
     * @return
     */
    Integer countFinishByAccountAndParticipant(@Param("param") SelectCtoActivityParamDTO countParam );

    /**
     * 更新所有执行总次数
     */
    void updateAllFinishCount(@Param("ctoPlanInfoId") String ctoPlanInfoId);

    /**
     * 根据计划id获取列表数据
     *
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoPlanOrgFinishDO> selectByPlanId(@Param("ctoPlanInfoId") String ctoPlanInfoId);
    /**
     * 更新有效状态为N
     * @param planInfoId
     * @return
     */
    int updateByPlanId(@Param("planInfoId") String planInfoId);

    /**
     * 计算AP完成数
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoReportApIndicatorDO> listOrgApIndicatorByPlanInfoId(@Param("planInfoId") String ctoPlanInfoId);


    List<CtoReportApIndicatorDO> listProductApIndicatorByPlanInfoId(@Param("planInfoId") String ctoPlanInfoId);

    /**
     * 查询事业部详情
     * @param rowId
     * @return
     */
    CtoPlanDetailDTO queryOrgDetail(@Param("rowId") String rowId);
}

/* Ended by AICoder, pid:i8f6b15a3bg963f14c4f08f9200a0b266b735ff5 */
