package com.zte.mcrm.activity.repository.model.item;

import java.util.Date;

/**
 * table:activity_schedule_item_people -- 
 */
public class ActivityScheduleItemPeopleDO {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 活动日程事项RowId */
    private String activityScheduleItemRowId;

    /** 人员类型ScheduleItemPeopleTypeEnum。我司参与人，我司现场接口人，非我司现场接口人，客户参与人 */
    private String peopleType;

    /** 人员编号（员工编号、客户参与人编号） */
    private String peopleNo;

    /** 人员名称 */
    private String peopleName;

    /** 人员标签。见：PeopleRoleLabelEnum */
    private String peopleLabel;

    /** 电话 */
    private String phoneNum;

    /** 岗位名称。因为管理联系人岗位最大256长度 */
    private String position;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    /** 对应客户编码，如果是客户参与人这个是有值的 */
    private String customerCode;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getActivityScheduleItemRowId() {
        return activityScheduleItemRowId;
    }

    public void setActivityScheduleItemRowId(String activityScheduleItemRowId) {
        this.activityScheduleItemRowId = activityScheduleItemRowId == null ? null : activityScheduleItemRowId.trim();
    }

    public String getPeopleType() {
        return peopleType;
    }

    public void setPeopleType(String peopleType) {
        this.peopleType = peopleType == null ? null : peopleType.trim();
    }

    public String getPeopleNo() {
        return peopleNo;
    }

    public void setPeopleNo(String peopleNo) {
        this.peopleNo = peopleNo == null ? null : peopleNo.trim();
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName == null ? null : peopleName.trim();
    }

    public String getPeopleLabel() {
        return peopleLabel;
    }

    public void setPeopleLabel(String peopleLabel) {
        this.peopleLabel = peopleLabel == null ? null : peopleLabel.trim();
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum == null ? null : phoneNum.trim();
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }
}