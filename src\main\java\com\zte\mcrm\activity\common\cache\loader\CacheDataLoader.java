package com.zte.mcrm.activity.common.cache.loader;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存数据加载器
 *
 * @param <V>
 * <AUTHOR>
 * @date 2022-09-19
 */
public interface CacheDataLoader<K, V> {

    /**
     * 加载数据
     *
     * @param key
     * @return
     */
    V load(K key);

    /**
     * 加载所有数据
     *
     * @param keys
     * @return
     */
    default Map<K, V> loadAll(Set<K> keys) {
        Map<K, V> res = new HashMap<>();
        if (keys != null) {
            for (K key : keys) {
                V val = load(key);
                if (val != null) {
                    res.put(key, val);
                }
            }
        }

        return res;
    }

}
