package com.zte.mcrm.activity.service.activity.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.integration.igpt.config.IGPTConfig;
import com.zte.mcrm.activity.integration.igpt.dto.IGPTPushInfoDTO;
import com.zte.mcrm.activity.integration.igpt.dto.PushParamDTO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationLeaderRepository;
import com.zte.mcrm.activity.service.activity.ActivityAndExhibitionService;
import com.zte.mcrm.activity.service.activity.ActivityToIGPTService;
import com.zte.mcrm.activity.service.activity.convert.ExhibitionSummaryPushInfoConvert;
import com.zte.mcrm.activity.service.summary.dto.SummaryPushContentDTO;
import com.zte.mcrm.common.util.HttpClientUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * 展会活动推送IGPT服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/5 16:53
 */
@Log4j2
@Service
public class ActivityToIGPTServiceImpl implements ActivityToIGPTService {

    @Value("${igpt.Authorization}")
    private String igptAuthorization;

    @Value("${igpt.assistant.url}")
    private String igptAssistantUrl;

    @Autowired
    private IGPTConfig igptConfig;

    @Autowired
    private ExhibitionRelationLeaderRepository exhibitionRelationLeaderRepository;

    @Autowired
    private ActivityAndExhibitionService activityAndExhibitionService;

    @Override
    public ServiceData sendIGPT(SummaryPushContentDTO info) {
        return Optional.ofNullable(info)
                .map(summaryInfo -> ExhibitionSummaryPushInfoConvert.toIGPTPushInfo(igptConfig, summaryInfo))
                .map(this::igptAssistantProcess)
                .orElse(null);
    }

    @Override
    public ServiceData pushData(PushParamDTO param) {
        // 1. 获取筛选后的领导Map
        Map<String, ExhibitionRelationLeaderDO> leaderMap = getFilteredLeaderMap(param);

        // 2. 获取摘要数据并处理目标用户
        List<SummaryPushContentDTO> summaryList = prepareSummaryList(param.getExtId(), leaderMap.keySet(), param.getTargetList());

        // 3. 批量推送
        List<ServiceData> resultList = summaryList.stream()
                .map(this::sendIGPT)
                .collect(Collectors.toList());

        return ServiceDataUtils.success(resultList);
    }

    /**
     * 获取筛选后的领导Map
     */
    protected Map<String, ExhibitionRelationLeaderDO> getFilteredLeaderMap(PushParamDTO param) {
        // 获取所有领导
        Map<String, ExhibitionRelationLeaderDO> allLeaders =
                exhibitionRelationLeaderRepository.getLeaderNoMapWithExhibitionRowId(param.getExtId());

        // 筛选指定领导
        if (CollectionUtils.isNotEmpty(param.getLeaderList())) {
            return allLeaders.entrySet().stream()
                    .filter(entry -> param.getLeaderList().contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        return allLeaders;
    }

    /**
     * 准备摘要列表，包括获取摘要和处理目标用户
     */
    protected List<SummaryPushContentDTO> prepareSummaryList(String extId, Iterable<String> leaderNos, List<String> targetList) {
        // 获取摘要数据流
        List<SummaryPushContentDTO> summaryList = getSummaries(extId, leaderNos);

        // 如果没有指定目标用户，直接返回摘要列表
        if (CollectionUtils.isEmpty(targetList)) {
            return summaryList;
        }

        // 如果指定了目标用户，扩展摘要列表
        return summaryList.stream()
                .flatMap(summary -> targetList.stream()
                        .map(targetNo -> {
                            SummaryPushContentDTO copy = new SummaryPushContentDTO();
                            BeanUtils.copyProperties(summary, copy);
                            copy.setTargetNo(targetNo);
                            return copy;
                        }))
                .collect(Collectors.toList());
    }

    /**
     * 获取摘要数据列表
     */
    private List<SummaryPushContentDTO> getSummaries(String extId, Iterable<String> leaderNos) {
        return StreamSupport.stream(leaderNos.spliterator(), false)
                .map(leaderNo -> activityAndExhibitionService.getSummaryData(extId, leaderNo))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 调用IGPT助手接口处理请求
     */
    protected ServiceData igptAssistantProcess(IGPTPushInfoDTO pushInfo) {
        try {
            // 构建请求
            String content = JSON.toJSONString(pushInfo);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Authorization", igptAuthorization);
            headers.put("Content-Type", "application/json");

            // 发送请求并记录日志
            log.info("IGPT input param: {}", content);
            String result = HttpClientUtil.httpPostWithJSON(igptAssistantUrl, content, headers);
            log.info("IGPT out response: {}", result);

            // 解析响应
            String sanitizedResult = JsonSanitizer.sanitize(result);
            ServiceData serviceData = JSON.parseObject(sanitizedResult, new TypeReference<ServiceData>() {}.getType());
            serviceData.setBo(pushInfo.getUserId());
            return serviceData;
        } catch (Exception e) {
            log.error("call igptAssistantProcess error!", e);
            return ServiceDataUtils.buildServiceData(RetCode.BUSINESSERROR_CODE, "call igptAssistantProcess error!");
        }
    }
}