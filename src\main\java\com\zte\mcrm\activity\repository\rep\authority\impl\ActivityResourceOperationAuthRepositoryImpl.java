package com.zte.mcrm.activity.repository.rep.authority.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.authority.ActivityResourceOperationAuthExtMapper;
import com.zte.mcrm.activity.repository.model.authority.ActivityResourceOperationAuthDO;
import com.zte.mcrm.activity.repository.rep.authority.ActivityResourceOperationAuthRepository;
import com.zte.mcrm.activity.repository.rep.authority.param.ActivityResourceOperationAuthQueryParam;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户活动资源操作授权DAO
 *
 * <AUTHOR>
 * @date 2024/11/13 上午10:35
 */
@Repository
public class ActivityResourceOperationAuthRepositoryImpl implements ActivityResourceOperationAuthRepository {

    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private ActivityResourceOperationAuthExtMapper mapper;

    /* Started by AICoder, pid:dbb5dj7400ucf271463e0a01e09a6a21aa130cd3 */
    /**
     * 根据业务id和业务类型批量查询权限数据
     *
     * @param bizRelatedIdList 业务id列表
     * @param bizType          业务类型
     * @param peopleNo         被授权人工号
     * @return Map<String, List<ActivityResourceOperationAuthDO>> 权限数据映射，键为业务id，值为对应的权限数据列表
     * <AUTHOR>
     * @date 2024/11/13 下午2:36
     */
    @Override
    public Map<String, List<ActivityResourceOperationAuthDO>> selectByBizRelatedIdList(List<String> bizRelatedIdList, String bizType, String peopleNo) {
        if (CollectionUtils.isEmpty(bizRelatedIdList)) {
            return Collections.emptyMap();
        }

        ActivityResourceOperationAuthQueryParam param = new ActivityResourceOperationAuthQueryParam();
        param.setBizRelatedIdList(bizRelatedIdList);
        param.setBizType(bizType);
        param.setPeopleNo(peopleNo);

        // 直接在流操作中进行分组，避免不必要的中间集合创建
        return mapper.selectList(param).stream()
                .collect(Collectors.groupingBy(ActivityResourceOperationAuthDO::getBizRelatedId));
    }

    /* Ended by AICoder, pid:dbb5dj7400ucf271463e0a01e09a6a21aa130cd3 */

    /* Started by AICoder, pid:zc6f4j28304546514b430978706edc2e7733bdc2 */
    /**
     * 根据活动id和业务类型批量查询权限数据
     *
     * @param activityIdList 活动id列表
     * @param bizType        业务类型
     * @param peopleNo       被授权人工号
     * @return Map<String, List<ActivityResourceOperationAuthDO>> - 以活动行ID为键的权限数据映射
     * <AUTHOR>
     * @date 2024/11/13 下午2:40
     */
    @Override
    public Map<String, List<ActivityResourceOperationAuthDO>> selectByActivityIdList(List<String> activityIdList, String bizType, String peopleNo) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return Collections.emptyMap();
        }

        ActivityResourceOperationAuthQueryParam param = new ActivityResourceOperationAuthQueryParam();
        param.setActivityIdList(activityIdList);
        param.setBizType(bizType);
        param.setPeopleNo(peopleNo);

        // 直接使用mapper返回的可迭代对象，避免不必要的流转换
        return mapper.selectList(param).stream()
                .collect(Collectors.groupingBy(ActivityResourceOperationAuthDO::getActivityRowId));
    }

    /* Started by AICoder, pid:x2ca04e513d40ae1444a0a26b011f029d6964395 */
    /**
     * 批量新增
     *
     * @param list 待插入的列表
     * @return 影响的行数
     * <AUTHOR>
     * @date 2024/11/13 上午10:31
     */
    @Override
    public int batchInsert(List<ActivityResourceOperationAuthDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        Date now = new Date();
        for (ActivityResourceOperationAuthDO operationAuthDO : list) {
            if (StringUtils.isBlank(operationAuthDO.getRowId())) {
                operationAuthDO.setRowId(keyIdService.getKeyId());
            }
            operationAuthDO.setEnabledFlag(BooleanEnum.Y.getCode());
            operationAuthDO.setCreationDate(now);
            operationAuthDO.setLastUpdateDate(now);
        }

        return mapper.batchInsert(list);
    }
    /* Ended by AICoder, pid:x2ca04e513d40ae1444a0a26b011f029d6964395 */

    /* Started by AICoder, pid:p7b2275f30gf2a51412808f510be07272321de9f */
    /**
     * 批量更新
     *
     * @param list 待更新的列表
     * @return 更新的记录数
     * <AUTHOR>
     * @date 2024/11/13 上午10:31
     */
    @Override
    public int batchUpdate(List<ActivityResourceOperationAuthDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        Date now = new Date();
        for (ActivityResourceOperationAuthDO operationAuthDO : list) {
            operationAuthDO.setLastUpdateDate(now);
        }

        return mapper.batchUpdate(list);
    }
    /* Ended by AICoder, pid:p7b2275f30gf2a51412808f510be07272321de9f */

    /* Ended by AICoder, pid:zc6f4j28304546514b430978706edc2e7733bdc2 */

    /**
     * 根据id批量删除（软删除）
     *
     * @param ids id
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/25 上午10:33
     */
    @Override
    public int deleteByIdList(List<String> ids, String operator) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        Date now = new Date();
        List<ActivityResourceOperationAuthDO> updateList = ids.stream().map(id -> {
            ActivityResourceOperationAuthDO update = new ActivityResourceOperationAuthDO();
            update.setRowId(id);
            update.setEnabledFlag(BooleanEnum.N.getCode());
            update.setLastUpdatedBy(operator);
            update.setLastUpdateDate(now);
            return update;
        }).collect(Collectors.toList());
        return mapper.batchUpdate(updateList);
    }
}
