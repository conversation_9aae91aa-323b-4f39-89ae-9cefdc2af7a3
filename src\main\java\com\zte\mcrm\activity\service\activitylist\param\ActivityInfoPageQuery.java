package com.zte.mcrm.activity.service.activitylist.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@ToString
public class ActivityInfoPageQuery {

    /**
     * 活动编号
     */
    private String activityRequestNo;
    /**
     * 活动议题
     */
    private String activityTitle;
    /**
     * 活动主要客户单位名称
     */
    private String customerName;
    /**
     * 操作人
     */
    private String operater;
    /**
     * 拓展活动状态。枚举：ActivityStatusEnum
     */
    private List<String> activityStatusList;
    /**
     * 活动起始时间。显示按yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date startDate;
    /**
     * 活动截止时间。显示按yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 页码（默认1）
     */
    private Integer pageNo = 1;
    /**
     * 分页大小（默认10）
     */
    private Integer pageSize = 10;
    /**
     * 开始行数
     */
    private Integer startRow;

}
