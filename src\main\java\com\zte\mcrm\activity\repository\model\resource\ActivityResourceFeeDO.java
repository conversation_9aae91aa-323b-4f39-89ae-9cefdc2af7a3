package com.zte.mcrm.activity.repository.model.resource;

import java.math.BigDecimal;
import java.util.Date;

/**
 * table:activity_resource_fee -- 
 */
public class ActivityResourceFeeDO {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 币种 */
    private String currency;

    /** 我司-zte,客户侧-cust */
    private String feeSource;

    /** 机票费 */
    private BigDecimal airTicketFee;

    /** 签证费 */
    private BigDecimal visaFee;

    /** 业务招待费 */
    private BigDecimal bizReceptionFee;

    /** 酒店费用 */
    private BigDecimal hotelFee;

    /** 车辆费用 */
    private BigDecimal carFee;

    /** 其他费用 */
    private BigDecimal otherFee;

    /** 合计费用，所有费用加起来 */
    private BigDecimal sumFee;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getFeeSource() {
        return feeSource;
    }

    public void setFeeSource(String feeSource) {
        this.feeSource = feeSource == null ? null : feeSource.trim();
    }

    public BigDecimal getAirTicketFee() {
        return airTicketFee;
    }

    public void setAirTicketFee(BigDecimal airTicketFee) {
        this.airTicketFee = airTicketFee;
    }

    public BigDecimal getVisaFee() {
        return visaFee;
    }

    public void setVisaFee(BigDecimal visaFee) {
        this.visaFee = visaFee;
    }

    public BigDecimal getBizReceptionFee() {
        return bizReceptionFee;
    }

    public void setBizReceptionFee(BigDecimal bizReceptionFee) {
        this.bizReceptionFee = bizReceptionFee;
    }

    public BigDecimal getHotelFee() {
        return hotelFee;
    }

    public void setHotelFee(BigDecimal hotelFee) {
        this.hotelFee = hotelFee;
    }

    public BigDecimal getCarFee() {
        return carFee;
    }

    public void setCarFee(BigDecimal carFee) {
        this.carFee = carFee;
    }

    public BigDecimal getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        this.otherFee = otherFee;
    }

    public BigDecimal getSumFee() {
        return sumFee;
    }

    public void setSumFee(BigDecimal sumFee) {
        this.sumFee = sumFee;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}