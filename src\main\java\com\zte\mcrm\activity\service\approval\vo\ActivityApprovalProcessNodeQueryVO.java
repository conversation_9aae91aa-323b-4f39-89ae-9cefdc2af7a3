package com.zte.mcrm.activity.service.approval.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.activity.repository.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 审批节点详情对象
 *
 * <AUTHOR> 10317843
 * @date 2023/05/22
 */
@Getter
@Setter
@ToString
public class ActivityApprovalProcessNodeQueryVO extends BaseEntity {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String rowId;

    /**
     * 拓展活动id
     */
    @ApiModelProperty("拓展活动id")
    private String activityRowId;

    /**
     * 活动审批流程信息row_id
     */
    @ApiModelProperty("活动审批流程信息row_id")
    private String approvalProcessRowId;

    /**
     * 审批单号-对应审批中心 taskId
     */
    @ApiModelProperty("审批单号-对应审批中心 taskId")
    private String approvalFlowNo;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String approveBy;

    /**
     * 审批人姓名
     */
    @ApiModelProperty("审批人姓名")
    private String approverName;

    @ApiModelProperty("审批级别。2，3，4")
    private int level;
    @ApiModelProperty("节点类型")
    private String nodeType;
    @ApiModelProperty("节点状态")
    private String nodeStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date approveTime;

    /**
     * 审批结果Y-同意，N-拒绝，T-转交。枚举：ApproveResultEnum
     */
    @ApiModelProperty("审批结果Y-同意，N-拒绝，T-转交，W-待审批。枚举：ApproveResultEnum")
    private String approveResult;

    /**
     * 审批结果Y-同意，N-拒绝，T-转交。枚举：ApproveResultEnum
     */
    @ApiModelProperty("审批结果Y-同意，N-拒绝，T-转交，W-待审批。枚举：ApproveResultEnum")
    private String approveResultName;

    /**
     * 如果是转交过来的，则该有值，转交来源id（本表的row_id）
     */
    @ApiModelProperty("如果是转交过来的，则该有值，转交来源id（本表的row_id）")
    private String transFrom;

    /**
     * 审批意见备注
     */
    @ApiModelProperty("审批意见备注")
    private String remark;



}