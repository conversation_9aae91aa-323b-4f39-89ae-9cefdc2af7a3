package com.zte.mcrm.activity.application.activity.impl;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.application.activity.ActivityQueryAppService;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.service.activity.ActivityCountService;
import com.zte.mcrm.activity.service.activity.convert.HomePageQueryResponseConvert;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.vo.SysGlobalConstVo;
import com.zte.mcrm.isearch.model.vo.HomePageQueryRequestVO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryResponseVO;
import com.zte.mcrm.isearch.service.CustomerPortraitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 活动查询
 *
 * <AUTHOR>
 * @date 2024/2/2 上午11:26
 */
@Slf4j
@Service
public class ActivityQueryAppServiceImpl implements ActivityQueryAppService {

    @Autowired
    private ActivityCountService activityCountService;

    @Autowired
    private CustomerPortraitService customerPortraitService;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 活动统计信息查询
     *
     * @param bizRequest
     * @return {@link ServiceData < HomePageQueryResponseVO >}
     * <AUTHOR>
     * @date 2024/2/2 上午11:25
     */
    @Override
    public ServiceData<HomePageQueryResponseVO> activityCountInfo(BizRequest<HomePageQueryRequestVO> bizRequest) {

        ExecutorService executorService = taskExecutor.getThreadPoolExecutor();

        HomePageQueryRequestVO param = bizRequest.getParam();
        HomePageQueryRequestVO integrationActivityParam = new HomePageQueryRequestVO();
        BeanUtils.copyProperties(param, integrationActivityParam);
        HomePageQueryRequestVO oldActivityParam = new HomePageQueryRequestVO();
        BeanUtils.copyProperties(param, oldActivityParam);

        //主线程本地变量
        SysGlobalConstVo parentThreadLocalVo = HeadersProperties.getSysGlobalInfo();

        Future<ServiceData<HomePageQueryResponseVO>> integrationActivityFuture = executorService.submit(
                createTask(activityCountService, param, parentThreadLocalVo));

        Future<ServiceData<HomePageQueryResponseVO>> oldActivityFuture = executorService.submit(
                createTask(customerPortraitService, param, parentThreadLocalVo));

        try {
            HomePageQueryResponseVO integrationActivityResult = integrationActivityFuture.get().getBo();
            HomePageQueryResponseVO oldActivityResult = oldActivityFuture.get().getBo();
            HomePageQueryResponseVO responseVO
                    = HomePageQueryResponseConvert.merge(oldActivityResult, integrationActivityResult);
            return ServiceDataUtils.success(responseVO);
        } catch (Exception e) {
            log.error("活动统计数据查询异常", e);
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.CUSTOMER_PORTRAIT_COUNT_QUERY_ERROR);
        }
    }


    private Callable<ServiceData<HomePageQueryResponseVO>> createTask(Object service, HomePageQueryRequestVO param, SysGlobalConstVo context) {
        return () -> {
            try {
                // 设置传入的上下文
                HeadersProperties.setSysGlobalInfo(context);
                if (service instanceof ActivityCountService) {
                    log.info("开始统计融合活动信息 empNo:{}",context.getXEmpNo());
                    return ((ActivityCountService)service).queryIntegrationActivityCountInfo(BizRequestUtil.createWithCurrentUser(param));
                } else {
                    log.info("根据参与人级别查询具体参与人活动次数 empNo:{}",context.getXEmpNo());
                    return ((CustomerPortraitService)service).homePageQuery(param);
                }
            } finally {
                // 清空上下文
                HeadersProperties.removeSysGlobalInfo();
                log.info("活动数据统计完成");
            }
        };
    }
}
