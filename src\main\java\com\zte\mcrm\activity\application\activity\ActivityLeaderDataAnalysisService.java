package com.zte.mcrm.activity.application.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.dataservice.model.ActivityLeaderInfoQueryDTO;
import com.zte.mcrm.dataservice.vo.ActivityMtoInfoVO;

import java.util.List;


public interface ActivityLeaderDataAnalysisService {

    /**
     * 获取公司级MTO活动数据
     *
     * @param param
     * @return
     */
    List<ActivityMtoInfoVO> listMtoActivityList(BizRequest<ActivityLeaderInfoQueryDTO> param);
}
