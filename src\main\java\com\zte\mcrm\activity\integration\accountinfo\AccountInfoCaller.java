package com.zte.mcrm.activity.integration.accountinfo;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.exception.RpcRuntimeException;
import com.zte.mcrm.activity.integration.accountinfo.dto.AccountInfoDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerDetailDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.ContactDetailParam;
import com.zte.mcrm.activity.integration.accountinfo.param.CustomerDetailQueryParamDTO;
import com.zte.mcrm.activity.integration.accountinfo.vo.CustomerPersonDetailDTO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.common.ServiceDataUtil;
import com.zte.mcrm.adapter.vo.AccountVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * zte-crm-account-info 客户管理服务端代理调用器
 *
 * <AUTHOR>
 * @date 2024-04-18 15:45:00
 */
@Slf4j
@Component
public class AccountInfoCaller {

    @Autowired
    private AccountInfoClient accountInfoClient;

    /**
     * 获取登录人作为A/B角色，所关联的客户联系人
     *
     * @return List<AccountInfoDTO>
     */
    public List<AccountInfoDTO> queryABAccountContacts(String managerType) {
        ServiceData<List<AccountInfoDTO>> serviceData = accountInfoClient.queryABAccountContacts(
                HeadersProperties.getXEmpNo(),
                HeadersProperties.getXAuthValue(),
                HeadersProperties.getXLangId(),
                managerType
        );
        if (serviceData == null || serviceData.getCode() == null || !RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            log.error("/zte-crm-account-info/api/person/myServeList调用失败， requestDTO={}，serviceData={}", null, serviceData);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        }
        return Optional.ofNullable(serviceData.getBo()).orElse(Lists.newArrayList());
    }


    /**
     * 根据客户编码获取客户联系人详细信息-基础/部门/合规
     *
     * @return List<CustomerPersonDetailVO>
     */
    public List<CustomerPersonDetailDTO> queryContactDetailList(ContactDetailParam contactDetailParam) {
        ServiceData<List<CustomerPersonDetailDTO>> serviceData = accountInfoClient.queryContactDetailList(
                HeadersProperties.getXEmpNo(),
                HeadersProperties.getXAuthValue(),
                HeadersProperties.getXLangId(),
                contactDetailParam);
        if (serviceData == null || serviceData.getCode() == null || !RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
            log.error("/zte-crm-account-info/api/person/contactDetailList调用失败， requestDTO={}，serviceData={}", contactDetailParam, serviceData);
                throw new RpcRuntimeException();
        }
        return Optional.ofNullable(serviceData.getBo()).orElse(Lists.newArrayList());
    }


    /**
     * 客户多法人接口
     */
    public List<OutCustomerDetailDTO> batchCustomerQueryV2(CustomerDetailQueryParamDTO customerDetailQueryParam) {
        ServiceData<List<OutCustomerDetailDTO>> serviceData = accountInfoClient.batchCustomerQueryV2(customerDetailQueryParam);
        if (ServiceDataUtil.isFailure(serviceData)) {
            log.error("/zte-crm-account-info/api/customer/batchDetail/v2调用失败， requestDTO={}，serviceData={}", customerDetailQueryParam, serviceData);
            throw new RpcRuntimeException();
        }
        return Optional.ofNullable(serviceData.getBo()).orElse(Lists.newArrayList());
    }
    /**
     * 客户多法人接口
     */
    public List<AccountVO> getAccountByCodeListV2(String accountCode,String filterHistoryName,String filterFrozenCustonmer,String corporateNo) {
        ServiceData<List<AccountVO>> serviceData = accountInfoClient.getAccountByCodeListV2(accountCode,filterHistoryName,filterFrozenCustonmer,corporateNo);
        if (ServiceDataUtil.isFailure(serviceData)) {
            log.error("/zte-crm-account-info//noPermisonAccountBatch/v2调用失败， requestDTO={}，serviceData={}", accountCode, serviceData);
            throw new RpcRuntimeException();
        }
        return Optional.ofNullable(serviceData.getBo()).orElse(Lists.newArrayList());
    }
}
