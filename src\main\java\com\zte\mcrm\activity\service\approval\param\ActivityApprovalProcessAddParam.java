package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.activity.common.enums.activity.ProcessStatusEnum;
import com.zte.mcrm.expansion.access.vo.ApprovalNodeCreatedInfoVO;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ActivityApprovalProcessAddParam {

    /**
     * 拓展活动id
     */
    @NotNull
    private String activityRowId;

    /**
     * 审批过程类型。业务审批、合规审批。枚举：ProcessTypeEnum
     */
    @NotNull
    private String processType;
    /**
     * 审批过程状态，枚举：ProcessStatusEnum
     */
    private String processStatus;

    /**
     * 审批备注
     */
    private String remark;

    /** 记录创建人 */
    @NotNull
    private String createdBy;

    /** 记录最近修改人 */
    @NotNull
    private String lastUpdatedBy;
    /**
     * 审批详情
     */
    @NotNull
    private ActivityApprovalProcessNodeAddParam processNodeAddParam;


    public void buildAddOf(ApprovalNodeCreatedInfoVO param) {
        this.activityRowId = param.getBusinessId();
        this.processType = param.getExtendedCode();
        this.processStatus = ProcessStatusEnum.ACTIVE.getCode();
        this.createdBy=param.getApproverMap().keySet().iterator().next();
        this.lastUpdatedBy = this.createdBy;
        this.processNodeAddParam= new ActivityApprovalProcessNodeAddParam();
        this.processNodeAddParam.buildOfAdd(param);
    }


}