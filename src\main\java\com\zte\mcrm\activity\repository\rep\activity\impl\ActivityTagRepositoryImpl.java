package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityTagExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityTagDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityTagRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ActivityTagRepositoryImpl implements ActivityTagRepository {
    @Resource
    private ActivityTagExtMapper activityTagExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivityTagDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityTagDO tag : recordList) {
            if (StringUtils.isBlank(tag.getRowId())) {
                tag.setRowId(keyIdService.getKeyId());
            }

            activityTagExtMapper.insertSelective(tag);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityTagDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityTagExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityTagDO> queryTagForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityTagExtMapper.queryTagForActivity(activityRowId);
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return activityTagExtMapper.softDeleteByActivityIds(operator, activityIds);
    }
}
