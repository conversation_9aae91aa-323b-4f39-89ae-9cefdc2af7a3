package com.zte.mcrm.activity.service.approval.event;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingBizTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingNoticeStatusEnum;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityStatusLifecycleRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityStatusLifecycleConverter;
import com.zte.mcrm.activity.service.converter.notice.ActivityPendingNoticeConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @ClassName ApprovalComplianceNodeService
 * @description:
 * @author: 李龙10317843
 * @create: 2023-05-19 16:36
 * @Version 1.0
 **/
@Service
public class ApprovalComplianceNodeService extends AbstractApprovalNodeService {
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;


    @Override
    String getCurrentActivityStatus() {
        return ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode();
    }

    @Override
    String getNodeType() {
        return PendingBizTypeEnum.APPROVAL_COMPLIANCE.getType();
    }

    @Override
    String getApprovalCompleteStatus(String approvalResult) {
        return BooleanEnum.Y.getCode().equals(approvalResult) ? ActivityStatusEnum.BUSINESS_APPROVAL.getCode()
                : ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS.getCode();
    }

}