package com.zte.mcrm.activity.common.auth;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.dto.auth.UserAuthQueryReqDTO;
import com.zte.itp.authorityclient.dto.auth.UserDataAuthQueryRespDTO;
import com.zte.itp.authorityclient.entity.input.FormData;
import com.zte.itp.authorityclient.entity.output.PageRows;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.activity.common.config.ActivityAuthConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.AuthConstraintFieldEnum;
import com.zte.mcrm.authority.bo.HeadersProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 客户活动融合权限工具
 *
 * <AUTHOR>
 * @date 2023/8/18 下午4:10
 */
public class ActivityAuthUtil {
    public ActivityAuthUtil() {
    }

    /**
     * 获取用户UPP角色约束，并解析成客户融合权限模型
     *
     * @param empNo 用户工号
     * @return {@link List<ActivityRoleConstraintModel>}
     * <AUTHOR>
     * @date 2023/8/18 下午4:13
     */
    public static List<ActivityRoleConstraintModel> analysisConstraint(String empNo) {
        Map<String, ActivityRoleAuthConfigDTO> authConfigMap = ActivityAuthConfig.getRoleAuthConfigMap();
        Map<String, List<UserDataAuthQueryRespDTO>> userAuthDataMap = fetchUserRoleConstraint(empNo, authConfigMap.keySet());
        List<ActivityRoleConstraintModel> constraintModelList = Lists.newArrayListWithCapacity(userAuthDataMap.size());
        for (Map.Entry<String, List<UserDataAuthQueryRespDTO>> entry : userAuthDataMap.entrySet()) {
            String key = entry.getKey();
            List<UserDataAuthQueryRespDTO> value = entry.getValue();
            if (CollectionUtils.isEmpty(value) || !authConfigMap.containsKey(key)) {
                continue;
            }
            ActivityRoleAuthConfigDTO config = authConfigMap.get(key);
            ActivityRoleConstraintModel constraintModel = new ActivityRoleConstraintModel();
            constraintModel.setRoleCode(key);
            constraintModel.setEditable(config.getEditable());
            constraintModel.setDeletable(config.getDeletable());
            constraintModel.setVoidable(config.getVoidable());
            constraintModel.setCancelable(config.getCancelable());
            constraintModel.setChangeable(config.getChangeable());
            buildConstraints(value, config, constraintModel);
            constraintModelList.add(constraintModel);
        }
        return constraintModelList;
    }

    /**
     * 组装角色约束
     *
     * @param roleConstraintList UPP角色约束
     * @param config             角色配置
     * @param constraintModel    需要构建的角色模型
     * <AUTHOR>
     * @date 2023/8/18 下午4:29
     */
    private static void buildConstraints(List<UserDataAuthQueryRespDTO> roleConstraintList,
                                         ActivityRoleAuthConfigDTO config,
                                         ActivityRoleConstraintModel constraintModel) {
        for (UserDataAuthQueryRespDTO authDto : roleConstraintList) {
            Map<String, List<String>> constraintMapping = config.getConstraintMapping();
            if (MapUtils.isNotEmpty(constraintMapping)) {
                List<String> constraintCodes = constraintMapping.get(Objects.nonNull(authDto.getConstraintId()) ?
                        authDto.getConstraintId().toString() : StringUtils.EMPTY);
                if (CollectionUtils.isNotEmpty(constraintCodes)) {
                    for (String constraintCode : constraintCodes) {
                        AuthConstraintFieldEnum constraintEnum = AuthConstraintFieldEnum.getEnum(constraintCode);
                        String constraintValue = authDto.getConstraintValue();
                        List<String> constraintList = Arrays.asList(constraintValue.split(CharacterConstant.COMMA));
                        constraintModel.addConstraints(constraintList, constraintEnum);
                    }
                }
            }
        }
    }

    /**
     * 获取UPP角色约束
     *
     * @param empNo   用户工号
     * @param roleSet 需要获取的角色
     * @return {@link Map<String, List<UserDataAuthQueryRespDTO>>}
     * <AUTHOR>
     * @date 2023/8/18 下午4:31
     */
    private static Map<String, List<UserDataAuthQueryRespDTO>> fetchUserRoleConstraint(String empNo, Set<String> roleSet) {
        if (StringUtils.isBlank(empNo)) {
            return Maps.newHashMap();
        }
        // uac接口建议50
        final int pageSize = NumberConstant.FIFTY;
        int pageNo = 1;
        FormData<UserAuthQueryReqDTO> formData = new FormData<>();
        formData.setPage(pageNo);
        formData.setRows(pageSize);
        formData.setEmpidui(HeadersProperties.getXEmpNo());
        formData.setToken(HeadersProperties.getXAuthValue());
        UserAuthQueryReqDTO reqDTO = new UserAuthQueryReqDTO();
        reqDTO.setUserNo(empNo);
        formData.setBo(reqDTO);

        Map<String, List<UserDataAuthQueryRespDTO>> roleMap = Maps.newHashMapWithExpectedSize(roleSet.size());
        while (pageNo < NumberConstant.FIFTY) {
            formData.setPage(pageNo);
            ServiceData<PageRows<UserDataAuthQueryRespDTO>> serviceData = AuthorityClient.queryUserPowerByUserId(formData);
            boolean isRespEmpty = Objects.isNull(serviceData) || Objects.isNull(serviceData.getBo()) || CollectionUtils.isEmpty(serviceData.getBo().getRows());
            if (!isRespEmpty) {
                add2RoleMap(roleSet, serviceData.getBo().getRows(), roleMap);
            }
            if (isRespEmpty || serviceData.getBo().getRows().size() < pageSize) {
                break;
            }
            pageNo++;
        }
        return roleMap;
    }

    /**
     * 把UPP角色约束添加到角色Map
     *
     * @param roleSet     需要获取约束的角色
     * @param respDTOList UPP角色约束
     * @param roleMap     角色Map
     * <AUTHOR>
     * @date 2023/8/18 下午4:42
     */
    private static void add2RoleMap(Set<String> roleSet, List<UserDataAuthQueryRespDTO> respDTOList,
                                    Map<String, List<UserDataAuthQueryRespDTO>> roleMap) {
        for (UserDataAuthQueryRespDTO respDTO : respDTOList) {
            if (roleSet.contains(respDTO.getRoleCode()) && StringUtils.isNotBlank(respDTO.getConstraintValue())) {
                roleMap.computeIfAbsent(respDTO.getRoleCode(), v -> Lists.newArrayList()).add(respDTO);
            }
        }
    }
}
