package com.zte.mcrm.activity.repository.rep.exhibition;


import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;

import java.util.List;
import java.util.Map;

public interface ExhibitionRelationExpertRepository {

    /**
     * 插入数据
     *
     * @param record
     * @return
     */
    int insertSelective(ExhibitionRelationExpertDO record);

    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ExhibitionRelationExpertDO record);


    /**
     * 通过展会id 查询对应专家信息
     *
     * @param exhibitionRowIds
     * @return 《展会ID，专家列表》
     */
    Map<String, List<ExhibitionRelationExpertDO>> queryExpertsWithExhibitionRowId(List<String> exhibitionRowIds);

    /***
     * <p>
     * 获取展会id 关联的 专家工号实体对应Map
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 上午10:26
     * @param exhibitionRowId 展会Id
     * @return java.util.Map<java.lang.String,com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO>
     */
    Map<String, ExhibitionRelationExpertDO> getExpertNoMapWithExhibitionRowId(String exhibitionRowId);

    /**
     * 根据展会ID查询专家信息
     * @param exhibitionRowId
     * @return
     */
    List<ExhibitionRelationExpertDO> queryExhibitionRelationExpertList(String exhibitionRowId);

    /**
     * 查询常用专家信息
     *
     * @param size 返回的数据个数
     * @return
     */
    List<ExhibitionRelationExpertDO> queryCommonExperts(int size);

}
