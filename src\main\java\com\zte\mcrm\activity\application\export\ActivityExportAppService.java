package com.zte.mcrm.activity.application.export;

import com.zte.mcrm.activity.application.export.param.ExportActivityParam;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import org.apache.http.entity.mime.content.ByteArrayBody;

/**
 * 活动导出
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
public interface ActivityExportAppService {

    /**
     * 活动导出
     *
     * @param req 参数
     * @return null-生成数据异常
     */
    ByteArrayBody exportActivity(BizRequest<ExportActivityParam> req);

    /**
     * 导出活动列表数
     *
     * @param req
     * <AUTHOR>
     * @date 2024-01-25
     */
    void exportActivityList(BizRequest<ActivityInfoQuery> req);

}
