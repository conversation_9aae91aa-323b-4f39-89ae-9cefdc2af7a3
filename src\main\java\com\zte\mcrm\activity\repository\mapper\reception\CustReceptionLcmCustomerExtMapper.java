package com.zte.mcrm.activity.repository.mapper.reception;

import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmCustomerDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface CustReceptionLcmCustomerExtMapper extends CustReceptionLcmCustomerMapper {

    /**
     * 根据客户接待拓展活动ID获取对应客户LCM扫描信息
     *
     * @param list
     * @return
     */
    List<CustReceptionLcmCustomerDO> getByHeaderIds(List<String> list);

    /**
     * 批量插入客户LCM扫描信息
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    int batchInsert(@Param("list")List<CustReceptionLcmCustomerDO> list);

    /**
     * 通过头Id选择性更新字段
     * @param contactDO 联系人DO
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 16:01
     */
    int updateByHeaderIdSelective(CustReceptionLcmCustomerDO contactDO);

}