package com.zte.mcrm.activity.service.activity.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.*;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceFeeRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryApRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryIssueRepository;
import com.zte.mcrm.activity.service.activity.ActivityInfoService;
import com.zte.mcrm.activity.service.activity.model.ActivityCompareModel;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.service.model.activity.ActivityBO;
import com.zte.mcrm.activity.service.people.ActivityZtePeopleService;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityApprovalParam;
import com.zte.mcrm.common.util.CollectionExtUtils;
import org.springframework.cglib.beans.BeanMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.COMMA;
import static com.zte.mcrm.activity.common.constant.DictConstant.*;
import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.common.constant.NumberConstant.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ApprovalTypeEnum.LEADER_AUDITOR;
import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;

/**
 * 活动信息服务类
 *
 * <AUTHOR> 10333830
 * @date 2023-08-30 21:04
 */
@Service
public class ActivityInfoServiceImpl implements ActivityInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityInfoServiceImpl.class);

    @Autowired
    private DictService dictService;
    @Autowired
    private ActivityInfoRepository infoRepository;
    @Autowired
    private ActivityCustomerInfoRepository customerInfoRepository;
    @Autowired
    private ActivityRelationZtePeopleRepository ztePeopleRepository;
    @Autowired
    private ActivityResourceFeeRepository activityResourceFeeRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private ActivityZtePeopleService ztePeopleService;
    @Autowired
    private ActivityCommunicationDirectionRepository communicationDirectionRepository;
    @Autowired
    private ActivityRelationProjectRepository projectRepository;
    @Autowired
    private ActivityRelationSolutionRepository solutionRepository;
    @Autowired
    private ActivityRelationAttachmentRepository attachmentRepository;
    @Autowired
    private ActivityRelationTalkRepository talkRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;
    @Autowired
    private ActivitySummaryApRepository activitySummaryApRepository;
    @Autowired
    private ActivitySummaryIssueRepository activitySummaryIssueRepository;

    /**
     * 对比活动信息
     *
     * @param newActivityBO 新活动信息
     * @param oldActivityBO 旧活动信息
     * <AUTHOR>
     * date: 2023/8/30 21:03
     */
    @Override
    public void compareActivityInfo(ActivityBO newActivityBO, ActivityBO oldActivityBO) {
        // 查数据字典，核对哪些字段，字段建议配置为可以修改-然后对比-然后存表
        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(ACTIVITY_FIELD_NOT_MODIFY_TYPE);
        // 活动信息对比
        ActivityInfoDO newActivityInfoDo = newActivityBO.getActivityInfoDO();
        ActivityInfoDO oldActivityInfoDo = oldActivityBO.getActivityInfoDO();
        this.compareValue(new ActivityCompareModel<>(newActivityInfoDo, oldActivityInfoDo, ActivityInfoDO.class), getNotModifyConfig(fieldDictMap.get(ACCOUNT_INFO_KEY)));
        /*
         * 比较展会、大会活动变更前后字段修改信息
         *      展会信息不可修改、(申请人、业务归属部门)不可修改、业务审批人不可修改
         */
        if (ActivityTypeEnum.in(oldActivityInfoDo.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE, VISITING_SAMPLE)) {
            // 活动关联展会信息字段校验
            this.compareValue(new ActivityCompareModel<>(newActivityInfoDo, oldActivityInfoDo, ActivityInfoDO.class), getNotModifyConfig(fieldDictMap.get(ORIGIN_INFO)));
            // 业务审批人变更校验
//            this.compareActivityLeaderAuditorNode(newActivityInfoDo.getApprovalText(), oldActivityInfoDo.getApprovalText());
            // 申请人及业务归属部门字段校验
            this.compareValue(new ActivityCompareModel<>(newActivityInfoDo, oldActivityInfoDo, ActivityInfoDO.class), getNotModifyConfig(fieldDictMap.get(APPLICATION_INFO)));
        }

        // 活动客户信息对比-客户不可变更,判断客户编码是否变更即可
        this.compareCustInfo(newActivityBO,oldActivityBO,getNotModifyConfig(fieldDictMap.get(CUSTOMER_INFO_KEY)));
    }

    /***
     * <p>
     * 比较展会、大会活动变更前后字审批人不可修改
     *
     * </p>
     * <AUTHOR>
     * @since  2023/11/17 上午10:56
     * @param newApprovalText 变更后活动审批人
     * @param oldApprovalText 变更前活动审批人
     */
   /* private void compareActivityLeaderAuditorNode(String newApprovalText, String oldApprovalText) {
        Set<String> newActivityLeaderAuditorSet = Optional.ofNullable(JSON.parseArray(newApprovalText, ActivityApprovalParam.class))
                .orElse(new ArrayList<>()).stream().filter(e -> LEADER_AUDITOR.isMe(e.getApprovalType()))
                .map(ActivityApprovalParam::getEmpNo).collect(Collectors.toSet());
        Set<String> oldActivityLeaderAuditorSet = Optional.ofNullable(JSON.parseArray(oldApprovalText, ActivityApprovalParam.class))
                .orElse(new ArrayList<>()).stream().filter(e -> LEADER_AUDITOR.isMe(e.getApprovalType()))
                .map(ActivityApprovalParam::getEmpNo).collect(Collectors.toSet());

        if (newActivityLeaderAuditorSet.size() != oldActivityLeaderAuditorSet.size()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CAN_NOT_CHANGE_LEADER_AUDITOR_INFO);
        }

        if (!newActivityLeaderAuditorSet.containsAll(oldActivityLeaderAuditorSet)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, CAN_NOT_CHANGE_LEADER_AUDITOR_INFO);
        }
    }*/

    /**
     * 获取活动信息和客户信息
     *
     * @param activityRowId 活动Id
     * @return com.zte.mcrm.activity.service.model.activity.ActivityBO
     * <AUTHOR>
     * date: 2023/8/31 21:30
     */
    @Override
    public ActivityBO getActivityBaseInfoById(String activityRowId) {
        ActivityBO activityBO = new ActivityBO();
        // 查询各项数据
        activityBO.setActivityInfoDO(infoRepository.selectByPrimaryKey(activityRowId));
        activityBO.setListCustInfo(CollectionExtUtils.getListOrDefaultEmpty(customerInfoRepository.queryAllByActivityRowId(activityRowId)));
        activityBO.setListCustPeopleInfo(CollectionExtUtils.getListOrDefaultEmpty(activityRelationCustPeopleRepository.queryAllByActivityRowId(activityRowId)));
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = CollectionExtUtils.getListOrDefaultEmpty(ztePeopleRepository.queryZtePeopleByActivityRowId(activityRowId, NEGATIVE_ONE));
        ztePeopleDOList = ztePeopleDOList.stream().filter(e -> !ActivityPeopleTypeEnum.in(e.getPeopleType(), ActivityPeopleTypeEnum.CONTACTS))
                                .collect(Collectors.toList());
        activityBO.setListZtePeopleInfo(ztePeopleDOList);
        activityBO.setListActivityResourceFee(activityResourceFeeRepository.queryActivityResourceFeesByActivityRowIds(Collections.singletonList(activityRowId)).get(activityRowId));

        return activityBO;
    }

    /**
     * 获取ISearch推送数据
     *
     * @param activityRowId 活动Id
     * @return com.zte.mcrm.activity.service.model.activity.ActivityBO
     * <AUTHOR>
     * date: 2023/8/31 21:30
     */
    @Override
    public ActivityBO getActivitySearchInfoById(String activityRowId) {
        ActivityBO activityBO = new ActivityBO();
        // 查询各项数据
        List<ActivityRelationProjectDO> projectList = projectRepository.queryAllProjectForActivity(activityRowId);
        ActivityRelationProjectDO projectDO = CollectionUtils.isEmpty(projectList) ? new ActivityRelationProjectDO() : projectList.get(ZERO);
        activityBO.setActivityInfoDO(infoRepository.selectByPrimaryKey(activityRowId));
        activityBO.setListCustInfo(CollectionExtUtils.getListOrDefaultEmpty(customerInfoRepository.queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setListZtePeopleInfo(CollectionExtUtils.getListOrDefaultEmpty(ztePeopleService.getActivityZtePeopleAndSupplementName(activityRowId)));
        activityBO.setListCustPeopleInfo(CollectionExtUtils.getListOrDefaultEmpty(activityRelationCustPeopleRepository.queryAllActivityWithNotEnable(activityRowId)));

        activityBO.setRelationProject(projectDO);
        activityBO.setListSolution(CollectionExtUtils.getListOrDefaultEmpty(solutionRepository.queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setListAttachment(CollectionExtUtils.getListOrDefaultEmpty(attachmentRepository.queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setData(CollectionExtUtils.getListOrDefaultEmpty(talkRepository.queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setListPendingNoticeInfo(CollectionExtUtils.getListOrDefaultEmpty(pendingNoticeRepository.queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setListCommunicateDirection(CollectionExtUtils.getListOrDefaultEmpty(communicationDirectionRepository
                .queryAllActivityWithNotEnable(activityRowId)));
        activityBO.setListAp(CollectionExtUtils.getListOrDefaultEmpty(activitySummaryApRepository.queryAllApForActivity(activityRowId)));
        activityBO.setListIssue(CollectionExtUtils.getListOrDefaultEmpty(activitySummaryIssueRepository.queryAllSummaryIssueForActivity(activityRowId)));
        return activityBO;
    }

    /**
     * 比较客户信息 如果不一致需要启动审批
     * @param newActivityBO   新客户信息
     * @param oldActivityBO   老客户信息
     * @param configMap 配置信息
     * @return java.util.List<com.zte.mcrm.activity.repository.model.activity.ActivityKeyModifyLogDO>
     * <AUTHOR>
     * date: 2023/8/31 16:21
     */
    private void compareCustInfo(ActivityBO newActivityBO, ActivityBO oldActivityBO,Map<String, String> configMap) {
        List<ActivityCustomerInfoDO> newListCustInfo = newActivityBO.getListCustInfo();
        List<ActivityCustomerInfoDO> oldListCustInfo = oldActivityBO.getListCustInfo();

        Map<String, ActivityCustomerInfoDO> oldCustMap = oldListCustInfo.stream()
                .collect(Collectors.toMap(ActivityCustomerInfoDO::getCustomerCode, Function.identity(), (v1, v2) -> v1));
        Set<String> oldCustSet = oldCustMap.keySet();
        Set<String> newCustSet = newListCustInfo.stream().map(ActivityCustomerInfoDO::getCustomerCode)
                .collect(Collectors.toSet());
        List<String> difCustList = newCustSet.stream().filter(e -> !oldCustSet.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(difCustList) || oldCustSet.size() != newCustSet.size()) {
            newActivityBO.setChangeCustomerFlag(true);
        }
        for (ActivityCustomerInfoDO newCustInfo : newListCustInfo) {
            ActivityCustomerInfoDO oldCustInfo = oldCustMap.get(newCustInfo.getCustomerCode());
            if (oldCustInfo == null) {
                newActivityBO.setChangeCustomerFlag(true);
            }
            compareValue(new ActivityCompareModel<>(newCustInfo, oldCustInfo, ActivityCustomerInfoDO.class), configMap);
        }
    }

    /**
     * 字段值对比
     *
     * @param compareModel  对比工具
     * @param notModifyFieldMap 不可变更字段配置
     * @return void
     * <AUTHOR>
     * date: 2023/8/31 9:45
     */
    private <T> void compareValue(ActivityCompareModel<T> compareModel, Map<String, String> notModifyFieldMap) {
        if (MapUtils.isEmpty(notModifyFieldMap)) {
            return;
        }
        T newBean = compareModel.getNewBean();
        T oldBean = compareModel.getOldBean();
        Class clazz = compareModel.getClazz();
        BeanMap newValueMap = null;
        BeanMap oldValueMap = null;
        try {
            if (newBean == null && oldBean == null) {
                return;
            }
            newValueMap = newBean == null ? BeanMap.create(clazz.newInstance()) : BeanMap.create(newBean);
            oldValueMap = oldBean == null ? BeanMap.create(clazz.newInstance()) : BeanMap.create(oldBean);

        } catch (InstantiationException | IllegalAccessException e) {
            logger.error("compareValue：系统异常，对比失败，新数据：{}，旧数据：{}", newBean, oldBean);
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID);
        }
        for (Object key : oldValueMap.keySet()) {
            compareFieldValue(notModifyFieldMap, newValueMap, oldValueMap, key);
        }
    }

    /**
     * 比较字段的Value
     * @param notModifyFieldMap 不可变更的字段Map
     * @param newValueMap   新值
     * @param oldValueMap   旧值
     * @param key
     * @return void
     * <AUTHOR>
     * date: 2023/9/4 15:34
     */
    private static void compareFieldValue(Map<String, String> notModifyFieldMap, BeanMap newValueMap, BeanMap oldValueMap, Object key) {
        String keyStr = key.toString();
        String keyDesc = notModifyFieldMap.get(keyStr);
        if (StringUtils.isBlank(keyDesc)) {
            return;
        }
        Object oldValue = oldValueMap.get(key);
        Object newValue = newValueMap.get(key);
        String oldValueStr = oldValue == null ? null : oldValue.toString();
        String newValueStr = newValue == null ? null : newValue.toString();
        if (StringUtils.equals(oldValueStr, newValueStr)) {
            return;
        }
        String errorDesc = StringUtils.equals(BizRequestUtil.createWithCurrentUser().getLangId(), LANGUAGE_ZH_CN) ? keyStr : keyDesc;
        throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, errorDesc + CAN_NOT_CHANGE);
    }

    /**
     * 获取不可编辑字段配置
     * @param notModifyConfig   不可编辑字段配置
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * date: 2023/8/31 9:51
     */
    private Map<String, String> getNotModifyConfig(DictLanguageDTO notModifyConfig) {
        if (notModifyConfig == null) {
            return Maps.newHashMap();
        }
        Set<String> configSet = JSON.parseObject(notModifyConfig.getDictValue(), new TypeReference<Set<String>>(){});
        Map<String, String> configMap = Maps.newHashMap();
        configSet.forEach(e -> {
            // 中英文 字段编码,字段中文名称
            String[] configAry = e.split(COMMA);
            String fieldCode = configAry[ZERO];
            if (configAry.length < TWO) {
                configMap.put(fieldCode, fieldCode);
            } else {
                configMap.put(fieldCode, configAry[ONE]);
            }
        });
        return configMap;
    }
}
