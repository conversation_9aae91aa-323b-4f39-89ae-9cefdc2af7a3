package com.zte.mcrm.activity.repository.rep.sample.impl;

import com.zte.mcrm.activity.repository.mapper.sample.SampleVisitFeeConfigExtMapper;
import com.zte.mcrm.activity.repository.mapper.sample.SampleVisitFeeConfigMapper;
import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;
import com.zte.mcrm.activity.repository.rep.sample.SampleVisitFeeConfigRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 样板参观费用配置 服务类 
 * <AUTHOR>
 * @date 2024/01/30
 */
@Repository
public class SampleVisitFeeConfigRepositoryImpl implements SampleVisitFeeConfigRepository {
	@Autowired
	private IKeyIdService iKeyIdService;

	@Autowired
	private SampleVisitFeeConfigExtMapper sampleVisitFeeConfigExtMapper;

	/**
	 * 获取所有有效实体列表,按指定属性排序
	 *
	 * @return 实体集合
	 * <AUTHOR>
	 * @date 2024/01/30
	 */
	@Override
	public List<SampleVisitFeeConfigDO> queryFeeConfigAll() {
		List<SampleVisitFeeConfigDO> sampleVisitFeeConfigDOS = sampleVisitFeeConfigExtMapper.querySampleVisitFeeConfigAll();
		return sampleVisitFeeConfigDOS;
	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @return 实体集合
	 * <AUTHOR>
	 * @date 2024/01/30
	 */
	@Override
	public List<SampleVisitFeeConfigDO> queryFeeConfigSelectiveList(Map<String, Object> map) {
		return sampleVisitFeeConfigExtMapper.getSelectiveList(map);
	}

	/**
	 * 新增指定记录
	 * @param sampleVisitFeeConfigList 实体对象
	 * @return 新增的记录对象, 注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 *
	 *
	 *
	 * @date 2024/01/30
	 */
	@Override
	public int insert(SampleVisitFeeConfigDO sampleVisitFeeConfigList) {
		return sampleVisitFeeConfigExtMapper.insertSelective(sampleVisitFeeConfigList);
	}

	/**
	 * 修改指定记录
	 *
	 * @param sampleVisitFeeConfigDO 实体对象
	 * @return 修改的记录对象, 注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2024/01/30
	 */
	@Override
	public int update(SampleVisitFeeConfigDO sampleVisitFeeConfigDO) {
		return sampleVisitFeeConfigExtMapper.updateByPrimaryKeySelective(sampleVisitFeeConfigDO);
	}

	@Override
	public int softDeleteByBatch(List<String> rowIds,String empNo){
		for (String rowId : rowIds) {
			sampleVisitFeeConfigExtMapper.softDelete(rowId,empNo);
		}
		return rowIds.size();
	}

}
