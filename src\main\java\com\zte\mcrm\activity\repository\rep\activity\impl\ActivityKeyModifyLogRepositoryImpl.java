package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityKeyModifyLogExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityKeyModifyLogDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityKeyModifyLogRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
@Service
public class ActivityKeyModifyLogRepositoryImpl implements ActivityKeyModifyLogRepository {
    @Autowired
    private ActivityKeyModifyLogExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityKeyModifyLogDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityKeyModifyLogDO record) {
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityKeyModifyLogDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }
}