package com.zte.mcrm.activity.repository.rep.reception.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.reception.TCustReceptionLcmTempResExtMapper;
import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResDO;
import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResWithOldPairRowIdDO;
import com.zte.mcrm.activity.repository.rep.reception.CustReceptionLcmTempResRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * 客户接待-LCM扫描结果暂存信息
 * <AUTHOR> 10333830
 * @date 2024-01-05 14:22
 */
@Component
public class CustReceptionLcmTempResRepositoryImpl implements CustReceptionLcmTempResRepository {

    @Autowired
    private IKeyIdService iKeyIdService;
    @Autowired
    private TCustReceptionLcmTempResExtMapper extMapper;

    /**
     * 批量插入客户LCM扫描信息
     *
     * @param list 列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    @Override
    public int batchInsert(List<TCustReceptionLcmTempResDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ZERO;
        }
        list.forEach(this::setDefaultValue);
        return extMapper.batchInsert(list);
    }

    /**
     * 批量更新客户LCM扫描信息
     *
     * @param list 列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    @Override
    public int batchUpdateByPairRowId(List<TCustReceptionLcmTempResWithOldPairRowIdDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ZERO;
        }
        String updateBy = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (TCustReceptionLcmTempResWithOldPairRowIdDO entity : list) {
            entity.setLastUpdatedBy(updateBy);
            entity.setLastUpdateDate(new Date());
        }
        return extMapper.batchUpdateByPairRowId(list);
    }

    /**
     * 根据客户接待拓展活动ID获取对应客户LCM扫描暂存信息
     *
     * @param headerId  活动头Id
     * @return
     */
    @Override
    public List<TCustReceptionLcmTempResDO> getByHeaderId(String headerId) {
        return StringUtils.isBlank(headerId) ? Collections.emptyList() : extMapper.getByHeaderIds(Collections.singletonList(headerId.trim()));
    }

    /**
     * 批量更新-根据主键和赋值字段选择性更新
     *
     * @param updateList 更新列表
     * @return int
     * <AUTHOR>
     * date: 2024/1/8 14:30
     */
    @Override
    public int batchUpdateByPrimaryKey(List<TCustReceptionLcmTempResDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        String updateBy = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (TCustReceptionLcmTempResDO entity : updateList) {
            entity.setLastUpdatedBy(updateBy);
            entity.setLastUpdateDate(new Date());
        }
        return extMapper.batchUpdateByPrimaryKey(updateList);
    }

    /**
     * 设置默认值
     * @param entity 实例
     * @return void
     * <AUTHOR>
     * date: 2023/12/20 16:03
     */
    private void setDefaultValue(TCustReceptionLcmTempResDO entity) {
        entity.setRowId(Optional.ofNullable(entity.getRowId()).orElse(iKeyIdService.getKeyId()));
        entity.setCreatedBy(Optional.ofNullable(entity.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        entity.setLastUpdatedBy(Optional.ofNullable(entity.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        entity.setCreationDate(new Date());
        entity.setLastUpdateDate(new Date());
        entity.setEnabledFlag(BooleanEnum.Y.getCode());
    }
}
