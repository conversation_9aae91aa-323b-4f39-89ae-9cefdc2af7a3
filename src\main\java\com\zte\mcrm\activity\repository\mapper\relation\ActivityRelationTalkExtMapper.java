package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationTalkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityRelationTalkExtMapper extends ActivityRelationTalkMapper {

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    /**
     * 查询一个活动下的所有谈参记录
     *
     * @param activityRowId 活动Id
     * @return List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/6/8 9:30
     */
    List<ActivityRelationTalkDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);


    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效谈参
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationTalkDO> queryAllActivityWithNotEnable(String activityRowId);
}