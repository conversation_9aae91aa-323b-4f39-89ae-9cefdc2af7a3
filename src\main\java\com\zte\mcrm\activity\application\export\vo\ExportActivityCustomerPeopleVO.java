package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ExportActivityCustomerPeopleVO {

    @Excel(name = "序号", orderNum = "1")
    private String index;
    @Excel(name = "提单人/申请人", orderNum = "2")
    private String applicantDesc;
    @Excel(name = "事业部", orderNum = "3")
    private String orgName2;
    @Excel(name = "片区", orderNum = "4")
    private String orgName3;
    @Excel(name = "代表处", orderNum = "5")
    private String orgName4;
    @Excel(name = "国家", orderNum = "6")
    private String orgName5;
    @Excel(name = "现场陪同人员及联系电话", orderNum = "7")
    private String interfacePeopleAndPhone;
    @Excel(name = "客户单位", orderNum = "8")
    private String customerName;
    @Excel(name = "客户单位级别", orderNum = "9")
    private String custUnionLevel;
    @Excel(name = "客户单位受限制主体", orderNum = "10")
    private String custUnionSanctionedPatry;
    @Excel(name = "客户姓名/我司参与人姓名", orderNum = "11")
    private String contactName;
    @Excel(name = "客户受限制主体", orderNum = "12")
    private String custSanctionedPatry;
    @Excel(name = "客户级别", orderNum = "13")
    private String custLevel;
    @Excel(name = "客户职务/现场陪同人员职务", orderNum = "14")
    private String position;
    @Excel(name = "所在部门", orderNum = "15")
    private String contactBuName;
    @Excel(name = "性别", orderNum = "16")
    private String gender;
    @Excel(name = "是否住宿", orderNum = "17")
    private String useHotel;
    @Excel(name = "酒店星级", orderNum = "18")
    private String hotelStar;
    @Excel(name = "酒店房型", orderNum = "19")
    private String hotelType;
    @Excel(name = "房间系数", orderNum = "20")
    private String roomCoefficient;
    @Excel(name = "入住日期", orderNum = "21")
    private String checkInTime;
    @Excel(name = "退房日期", orderNum = "22")
    private String checkOutTime;
    @Excel(name = "是否自付", orderNum = "23")
    private String paySelf;
    @Excel(name = "合规编号", orderNum = "24")
    private String complianceNo;
    @Excel(name = "备注", orderNum = "25")
    private String memo;
    @Excel(name = "活动编号", orderNum = "26")
    private String activityRequestNo;
    @Excel(name = "单据状态", orderNum = "27")
    private String activityStatus;
    @Excel(name = "最后更新日期", orderNum = "28")
    private String lastUpdatedDate;
    @Excel(name = "客户参与人id", orderNum = "29")
    private String contactNo;
    @Excel(name = "我司参与人", orderNum = "29")
    private String zetPeopleName;
    @Excel(name = "手机号码", orderNum = "30")
    private String phoneNumber;
    @Excel(name = "角色", orderNum = "31")
    private String peopleType;
    @Excel(name = "标签", orderNum = "32")
    private String peopleLabel;

}
