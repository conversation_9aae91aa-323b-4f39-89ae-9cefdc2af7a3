package com.zte.mcrm.activity.repository.rep.event;


import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventQuery;

import java.util.List;

/**
 * 客户活动关联事件 服务接口类
 * <AUTHOR>
 * @date 2023/06/29
 */
public interface ActivityRelationEventExtRepository {
    /**
     * 批量更新
     *
     * @param updateList
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    int batchUpdateByPrimaryKey(List<ActivityRelationEventDO> updateList);

    /**
     * 按条件查询
     *
     * @param query 按条件查询
     * @return: List<ActivityRelationEventDO>
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    List<ActivityRelationEventDO> getListByQuery(ActivityRelationEventQuery query);

    /**
     * 批量删除
     * @param operator  操作者
     * @param activityIds   活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:52
     */
    int deleteByActivityIds(String operator, List<String> activityIds);

    /**
     * 动态更新
     * @param record
     * @return
     * <AUTHOR>
     * @date 2024/2/23
     */
    int updateByPrimaryKeySelective(ActivityRelationEventDO record);
}
