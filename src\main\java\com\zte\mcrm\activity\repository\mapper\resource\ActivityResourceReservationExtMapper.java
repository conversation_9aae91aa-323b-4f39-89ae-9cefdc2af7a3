package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationDO;
import com.zte.mcrm.activity.web.controller.reservation.param.ActivityResourceReservationParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nonnull;
import java.util.Date;
import java.util.List;

@Mapper
public interface ActivityResourceReservationExtMapper extends ActivityResourceReservationMapper {

    /**
     * 获取在某个时间过期的资源申请单
     *
     * @param expiredTime 过期时间
     * @param size 条数
     * @return
     */
    List<ActivityResourceReservationDO> fetchExpiredReservation(@Param("expiredTime") Date expiredTime, @Param("size") int size);

    /**
     * 查询活动所有资源预约订单
     *
     * @param param
     * @return
     */
    List<ActivityResourceReservationDO> queryAllReservationForActivity(@Param("param") ActivityResourceReservationParam param);

    /***
     * <p>
     * 逻辑删除指定活动id下资源预约情况
     *
     * </p>
     * <AUTHOR>
     * @since 2024/4/28 下午4:49
     * @param operator 操作人
     * @param activityRowId 活动id
     * @return int
     */
    int softDeleteByActivityRowIds(@Param("operator") String operator,@Nonnull @Param("activityRowId") String activityRowId);
}