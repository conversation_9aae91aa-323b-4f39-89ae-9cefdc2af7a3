package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 导出活动参数
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Getter
@Setter
public class ExportActivityInfoVO {
    @Excel(name = "活动编号", orderNum = "1")
    private String activityRequestNo;
    @Excel(name = "活动状态", orderNum = "2")
    private String activityStatusName;
    @Excel(name = "活动类型", orderNum = "3")
    private String activityTypeName;
    @Excel(name = "议题", orderNum = "4")
    private String activityTitle;
    @Excel(name = "活动开始时间", orderNum = "5")
    private String startTime;
    @Excel(name = "活动结束时间", orderNum = "6")
    private String endTime;
    @Excel(name = "交流地点", orderNum = "7")
    private String activityPlace;
    @Excel(name = "交流方式", orderNum = "8")
    private String communicationWayName;
    @Excel(name = "交流方向", orderNum = "9")
    private String direction;
    @Excel(name = "关联项目", orderNum = "10")
    private String relationProject;
    @Excel(name = "交流层级", orderNum = "11")
    private String communicationLevelName;
    @Excel(name = "申请人", orderNum = "12")
    private String applyPeopleDesc;
    @Excel(name = "申请人部门编号", orderNum = "13")
    private String applyDepartmentNo;
    @Excel(name = "申请人部门名称", orderNum = "14")
    private String applyDepartmentName;
    @Excel(name = "创建人", orderNum = "15")
    private String createdBy;
    @Excel(name = "大项目接口人", orderNum = "16")
    private String bigProjectContractPeople;
    @Excel(name = "知会人", orderNum = "17")
    private String informedPeople;
    @Excel(name = "是否有会议纪要", orderNum = "18")
    private String hasSummary;
    @Excel(name = "遗留问题数量", orderNum = "19")
    private String issueNum;
    @Excel(name = "AP总数", orderNum = "20")
    private String apNum;
}
