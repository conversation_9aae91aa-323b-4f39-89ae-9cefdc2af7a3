package com.zte.mcrm.activity.repository.rep.sample;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.sample.param.SamplePointInfoQuery;

import java.util.List;
import java.util.Map;

/**
 * 样板点信息
 *
 * <AUTHOR>
 */
public interface SamplePointInfoRepository {
    /**
     * 新增样板点信息
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<SamplePointInfoDO> recordList);

    /**
     * 更新样板点信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SamplePointInfoDO record);

    /**
     * 获取当月样板点信息数
     *
     * @param queryParams
     * @return
     */
    int getMonthCount(SamplePointInfoQuery queryParams);

    /**
     * description 查询样板点列表
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2024/2/7 下午4:16
     */
    PageRows<SamplePointInfoDO> querySamplePointInfoList(SamplePointInfoQuery samplePointInfoQuery);

    /**
     * 查询有效的样板点
     * @return
     */
    List<SamplePointInfoDO> queryEffectiveSamplePointInfoList();

    /**
     * 查询样板点
     * @param rowId
     * @return
     */
    SamplePointInfoDO selectByPrimaryKey(String rowId);

    /**
     * 根据id列表查询样板点
     * @param rowIds
     * @return
     */
    Map<String, SamplePointInfoDO> querySamplePointInfoByRowIds(List<String> rowIds);
}