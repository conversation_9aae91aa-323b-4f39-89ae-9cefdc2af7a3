package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.EsQueryResponse;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.convert.EsQueryParamConvert;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityInfoExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityEsSearchRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityConvergenceInfoVO;
import com.zte.mcrm.activity.web.controller.search.bo.ActivitySearchQueryBuilder;
import com.zte.mcrm.activity.web.controller.search.vo.ActivitySearchInfoVO;
import com.zte.mcrm.isearch.constants.AppSearchConstant;
import com.zte.mcrm.isearch.model.dto.dsl.CommonQueryParamsDslDTO;
import com.zte.mcrm.isearch.service.ISearchService;
import com.zte.mcrm.isearch.units.EsResConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ES 查询客户融合活动
 *
 * <AUTHOR>
 * @date 2023/12/23 下午11:51
 */
@Slf4j
@Component
public class ActivityEsSearchRepositoryImpl implements ActivityEsSearchRepository {

    @Autowired
    private ISearchService searchService;

    @Autowired
    private ActivityInfoExtMapper extMapper;

    @Value("${cust.activity.expansion.search.sync:}")
    private String activityAppName;
    /**
     * 查询活动列表分页
     *
     * @param query
     * @return {@link PageRows <  ActivityInfoDO >}
     * <AUTHOR>
     * @date 2023/12/20 下午2:24
     */
    @Override
    public PageRows<ActivityInfoDO> searchPage(ActivityInfoQuery query) {
        int pageNo = Optional.ofNullable(query.getPageNo()).orElse(NumberConstant.ONE);
        int pageSize = Optional.ofNullable(query.getPageSize()).orElse(NumberConstant.TEN);
        // 解决ES深度分页问题，ES最大查询数量(目前设置是10000)
        final int maxRowSize = NumberConstant.TEN_THOUSANDS;
        pageSize = Math.min(pageSize, maxRowSize);
        long total = NumberConstant.ZERO;
        while (pageNo * pageSize > maxRowSize && pageNo > 1) {
            int currentPageNo = Math.min(pageNo - 1, maxRowSize / pageSize);
            ActivitySearchQueryBuilder builder = EsQueryParamConvert.generateDslBuild(query, activityAppName);
            builder.setPageNo(currentPageNo * pageSize);
            // 滚动时只需要查最后一条即可
            builder.setPageSize(1);
            ServiceData serviceData = searchService.commonQueryByDsl(builder.build(Boolean.FALSE));
            EsQueryResponse.HitDTO<ActivitySearchInfoVO> hitDTO = EsResConverterUtil.analysisHits(serviceData, new TypeReference<EsQueryResponse<ActivitySearchInfoVO>>() {});
            if (Objects.isNull(hitDTO) || CollectionUtils.isEmpty(hitDTO.getHits())) {
                log.warn("ES查询返回数据异常, {}", serviceData);
                return PageRowsUtil.buildEmptyPage(query.getPageNo(), query.getPageSize());
            }
            String uniqueSortKey = Optional.ofNullable(hitDTO.getHits().get(0).getSource()).map(ActivitySearchInfoVO::getUniqueSortKey).orElse(StringUtils.EMPTY);
            if (StringUtils.isBlank(uniqueSortKey)) {
                return PageRowsUtil.buildEmptyPage(query.getPageNo(), query.getPageSize());
            }
            if (StringUtils.isBlank(query.getUniqueSortKey())) {
                total = hitDTO.getTotal();
            }
            query.setUniqueSortKey(uniqueSortKey);
            pageNo = pageNo - currentPageNo;
        }

        ActivitySearchQueryBuilder builder = EsQueryParamConvert.generateDslBuild(query, activityAppName);
        builder.setPageNo(pageNo);
        builder.setPageSize(pageSize);
        ServiceData serviceData = searchService.commonQueryByDsl(builder.build(Boolean.FALSE));
        EsQueryResponse.HitDTO<ActivitySearchInfoVO> hitDTO = EsResConverterUtil.analysisHits(serviceData, new TypeReference<EsQueryResponse<ActivitySearchInfoVO>>() {});
        if (Objects.isNull(hitDTO) || CollectionUtils.isEmpty(hitDTO.getHits())) {
            log.warn("ES查询返回数据异常, {}", serviceData);
            return PageRowsUtil.buildEmptyPage(query.getPageNo(), query.getPageSize());
        }
        List<EsQueryResponse.HitDetailDTO<ActivitySearchInfoVO>> hits = hitDTO.getHits();
        List<String> ids = hits.stream().map(EsQueryResponse.HitDetailDTO::getId).collect(Collectors.toList());
        ActivityInfoQuery idQuery = new ActivityInfoQuery();
        idQuery.setActivityRowIdList(ids);
        idQuery.setUniqueSortKey(query.getUniqueSortKey());
        List<ActivityInfoDO> activityInfoDOList = extMapper.getList(idQuery);
        PageRows<ActivityInfoDO> pageRows = new PageRows<>();
        pageRows.setRows(activityInfoDOList);
        pageRows.setTotal(hitDTO.getTotal());
        pageRows.setCurrent(query.getPageNo());
        pageRows.setPageSize(query.getPageSize());
        return pageRows;
    }

    @Override
    public List<ActivityConvergenceInfoVO> searchConvergeInfo(ActivityInfoQuery query) {
        List<ActivityConvergenceInfoVO> activityConvergenceInfoVOList = new ArrayList<>();
        ActivitySearchQueryBuilder builder = EsQueryParamConvert.generateGroupByDslBuild(query, activityAppName);
        ServiceData serviceData = searchService.commonQueryByDsl(builder.buildBody());
        log.info("ES查询返回数据, {}", serviceData);
        if (Objects.isNull(serviceData) || !RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode()) || Objects.isNull(serviceData.getBo())) {
            return activityConvergenceInfoVOList;
        }

        JSONObject bo = JSONObject.parseObject(serviceData.getBo().toString());
        if (!bo.containsKey(AppSearchConstant.ES_DSL_KEYWORD_AGGREGATIONS)) {
            return activityConvergenceInfoVOList;
        }

        JSONObject aggregations = bo.getJSONObject(AppSearchConstant.ES_DSL_KEYWORD_AGGREGATIONS);
        if (!aggregations.containsKey(AppSearchConstant.ES_DSL_KEYWORD_GROUP_BY_KEY)) {
            return activityConvergenceInfoVOList;
        }

        JSONObject groupByKeyMap = aggregations.getJSONObject(AppSearchConstant.ES_DSL_KEYWORD_GROUP_BY_KEY);
        if (!groupByKeyMap.containsKey(AppSearchConstant.ES_DSL_KEYWORD_BUCKETS)) {
            return activityConvergenceInfoVOList;
        }

        JSONArray buckets = groupByKeyMap.getJSONArray(AppSearchConstant.ES_DSL_KEYWORD_BUCKETS);
        buckets.forEach(item -> {
            JSONObject bucket = JSONObject.parseObject(item.toString());
            ActivityConvergenceInfoVO convergenceInfoVO = new ActivityConvergenceInfoVO();
            convergenceInfoVO.setActivityType(bucket.getString(AppSearchConstant.ES_DSL_KEYWORD_KEY));
            convergenceInfoVO.setCount(Integer.valueOf(bucket.getString(AppSearchConstant.ES_DSL_KEYWORD_DOC_COUNT)));
            activityConvergenceInfoVOList.add(convergenceInfoVO);
        });

        return activityConvergenceInfoVOList;
    }


    /**
     * 查询活动列表分页
     *
     * @param query
     * @return {@link PageRows <  ActivityInfoDO >}
     * <AUTHOR>
     * @date 2023/12/20 下午2:24
     */
    @Override
    public PageRows<ActivityInfoDO> searchPageDownLoad(ActivityInfoQuery query) {
        CommonQueryParamsDslDTO dslQueryInfo = EsQueryParamConvert.convertActivityDownLoadParam(query, activityAppName);
        ServiceData serviceData = searchService.commonQueryByDsl(dslQueryInfo);
        EsQueryResponse.HitDTO<ActivitySearchInfoVO> hitDTO = EsResConverterUtil.analysisHits(serviceData, new TypeReference<EsQueryResponse<ActivitySearchInfoVO>>() {});
        if (Objects.isNull(hitDTO) || CollectionUtils.isEmpty(hitDTO.getHits())) {
            log.warn("客户融合活动列表ES查询异常, {}", serviceData);
            return PageRowsUtil.buildEmptyPage(query.getPageNo(), query.getPageSize());
        }
        List<EsQueryResponse.HitDetailDTO<ActivitySearchInfoVO>> hits = hitDTO.getHits();
        Map<String, String> uniqueSortKeyMap = Maps.newHashMap();
        for(EsQueryResponse.HitDetailDTO<ActivitySearchInfoVO> searchInfoVO : hits){
            uniqueSortKeyMap.put(searchInfoVO.getId(),Optional.ofNullable(searchInfoVO.getSource()).orElse(new ActivitySearchInfoVO()).getUniqueSortKey());
        }
        List<String> ids = hits.stream().map(EsQueryResponse.HitDetailDTO::getId).collect(Collectors.toList());
        List<ActivityInfoDO> activityInfoDOList = extMapper.selectByIds(ids);
        for (ActivityInfoDO activityInfoDO : activityInfoDOList) {
            activityInfoDO.setUniqueSortKeyDownId(uniqueSortKeyMap.get(activityInfoDO.getRowId()));
        }
        PageRows<ActivityInfoDO> pageRows = new PageRows<>();
        pageRows.setRows(activityInfoDOList);
        pageRows.setTotal(hitDTO.getTotal());
        pageRows.setCurrent(query.getPageNo());
        pageRows.setPageSize(query.getPageSize());
        return pageRows;
    }
}
