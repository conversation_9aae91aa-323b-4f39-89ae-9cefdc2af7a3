package com.zte.mcrm.activity.repository.rep.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;

import java.util.List;

/**
 * 活动关联的解决方案信息
 *
 * <AUTHOR>
 */
public interface ActivityRelationSolutionRepository {

    /**
     * 添加关联的方案信息（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivityRelationSolutionDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityRelationSolutionDO record);

    /**
     * 查询活动关联的所有方案信息
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivityRelationSolutionDO> queryAllSolutionForActivity(String activityRowId);

    /**
     * 批量插入数据
     *
     * @param list 列表数据
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityRelationSolutionDO> list);

    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量修改数据
     *
     * @param list 列表数据
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchUpdate(List<ActivityRelationSolutionDO> list);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationSolutionDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationSolutionDO> queryAllActivityWithNotEnable(String activityRowId);
}
