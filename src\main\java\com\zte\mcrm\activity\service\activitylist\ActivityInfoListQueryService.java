package com.zte.mcrm.activity.service.activitylist;

import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.service.activitylist.param.*;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityConvergenceInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 14:06
 */
public interface ActivityInfoListQueryService {

    /**
     * 获取活动列表信息
     * @param bizRequest
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageRows<ActivityInfoVO> getActivityInfoList(BizRequest<ActivityInfoSearchParamVO> bizRequest, Integer pageNo, Integer pageSize);

    /**
     * 查询汇聚信息
     * @param req 查询条件
     * @return
     */
    List<ActivityConvergenceInfoVO> queryActivityConvergenceInfo(BizRequest<ActivityInfoSearchParamVO> req);

    /**
     * 批量获取活动权限信息
     * @param param 查询参数
     * @return java.util.List<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO>
     * <AUTHOR>
     * date: 2023/8/29 15:05
     */
    List<ActivityInfoVO> getActivityAuthInfo(BizRequest<List<String>> param);

    /**
     * 获取数据源
     * @param activityInfoQuery 活动信息查询条件
     * @return com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource
     * <AUTHOR>
     * date: 2023/8/29 10:24
     */
    StandardActivityDetailDataSource getStandardActivityDetailDataSource(ActivityInfoQuery activityInfoQuery);

    /**
     * 根据条件按需获取活动的信息（这个作为基础方法，所有地方都可以使用）
     *
     * @param query
     * @return
     */
    StandardActivityDetailDataSource getStandardActivityDetailDataSource(ActivityDataSourceQuery query);

    /**
     * 获取数据源
     * @param activityRowId 活动ID
     * @return com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource
     * <AUTHOR>
     * date: 2024/2/20 09:00
     */
    List<ActivityInfoVO> getActivityDetailDataSource(String activityRowId);

    /**
     * 关注任务
     * @param activityRowId 活动id
     * @return
     */
    int followActivityTask(String activityRowId);

    /**
     * 活动流程图
     * @param acRowId
     * @return
     */
    List<ActivityFlowInfoVO> activityFlow(String acRowId);

    /**
     * FOL客户扩展活动分页
     *
     * @param queryParam
     * @return com.zte.mcrm.activity.common.model.PageRows<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO>
     * @date 2024/6/3
     */
    PageRows<ActivityInfoFolVO> queryPage4Fol(ActivityInfoPageQuery queryParam);

    /**
     * FOL客户扩展活动列表
     *
     * @param queryParam
     * @return java.util.List<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO>
     * @date 2024/6/4
     */
    List<ActivityInfoFolVO> queryList4Fol(ActivityInfoListQuery queryParam);

}
