package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleItemPeopleExtMapper extends ActivityScheduleItemPeopleMapper {
    /**
     * 批量查询数据
     * @param activityScheduleItemRowIds
     * @return
     */
    List<ActivityScheduleItemPeopleDO> queryAllSchedulePeopleByScheduleItemRowIds(@Param("activityScheduleItemRowIds") List<String> activityScheduleItemRowIds);

    /**
     * 批量更新数据
     * @param records
     * @return
     */
    int batchUpdate(@Param("records") List<ActivityScheduleItemPeopleDO> records);

    /**
     * 根据activityScheduleItemRowId查询人员列表
     * @param activityScheduleItemRowId
     * @return
     */
    List<ActivityScheduleItemPeopleDO> queryAllByScheduleItemRowId(@Param("activityScheduleItemRowId") String activityScheduleItemRowId);

    /**
     * 根据scheduleItemRowIds,批量软删除日程参与人
     * @param operator
     * @param scheduleItemRowIds
     * @return
     */
    int deleteByScheduleItemRowIds(@Param("operator") String operator, @Param("scheduleItemRowIds") List<String> scheduleItemRowIds);

    /**
     * 根据scheduleItemRowIds,批量软删除日程参与人,指定类型的人员不删除
     *
     * @param operator
     * @param scheduleItemRowIds
     * @param peopleTypes
     * @return {@link int}
     * <AUTHOR>
     * @date 2025/2/28 下午7:20
     */
    int deleteByScheduleItemRowIdsWithoutPeopleTypes(@Param("operator") String operator,
                                                     @Param("scheduleItemRowIds") List<String> scheduleItemRowIds,
                                                     @Param("peopleTypes") List<String> peopleTypes);

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<ActivityScheduleItemPeopleDO> records);


    /**
     * 根据活动ID批量查询数据
     * @param activityRowIds
     * @return
     */
    List<ActivityScheduleItemPeopleDO> queryAllSchedulePeopleByActivityRowIds(@Param("activityRowIds") List<String> activityRowIds);

    List<ActivityScheduleItemPeopleDO> queryEmpNoTransList(DataTransParam searchParam);
}