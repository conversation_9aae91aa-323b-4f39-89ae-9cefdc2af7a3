package com.zte.mcrm.activity.application.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.application.exhibition.ScheduleOrchestrationQueryAppService;
import com.zte.mcrm.activity.application.exhibition.convert.ScheduleOrchestrationQueryConvertor;
import com.zte.mcrm.activity.application.model.ScheduleOrchestrationDataSource;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.export.ExcelExportHelper;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationDetailRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationVersionRepository;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.schedule.impl.support.ScheduleOrchestrationConstants;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationExportParam;
import com.zte.mcrm.activity.web.controller.schedule.vo.LastScheduleOrchestrationVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationVO;
import com.zte.mcrm.expansion.common.constant.CustExpansionConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展会资源编排列表查询服务
 *
 * <AUTHOR>
 */
@Service
public class ScheduleOrchestrationQueryAppServiceImpl implements ScheduleOrchestrationQueryAppService {

    // 传入的onlyPassActivity为true，资源编排导出导出进行中、已完成、已评价的活动，为false，导出审批不通过、进行中的数据
    @Value("${orchestration.export.onlyPassActivity:false}")
    private boolean onlyPassActivity;
    @Autowired
    private ExhibitionDirectorRepository exhibitionDirectorRepository;
    @Autowired
    private ActivityScheduleItemRepository activityScheduleItemRepository;
    @Autowired
    private ActivityScheduleItemPeopleRepository activityScheduleItemPeopleRepository;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;
    @Autowired
    private ActivityCommunicationDirectionRepository activityCommunicationDirectionRepository;
    @Autowired
    private ActivityScheduleOrchestrationVersionRepository activityScheduleOrchestrationVersionRepository;
    @Autowired
    private ActivityScheduleOrchestrationDetailRepository activityScheduleOrchestrationDetailRepository;
    @Autowired
    private ScheduleOrchestrationQueryConvertor scheduleOrchestrationQueryConvertor;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private UserCenterService userCenterService;


    @Override
    public BizResult<ScheduleOrchestrationVO> scheduleOrchestrationEditList(BizRequest<ScheduleOrchestrationExportParam> request) {
        // 编排模式，所有数据取最新/暂存的
        ScheduleOrchestrationDataSource ds = buildDataSource(request, true, request.getParam().isOnlyPassActivity());
        ds.setNeedOrchestrationFiler(false);

        return BizResult.buildSuccessRes(scheduleOrchestrationQueryConvertor.toScheduleOrchestrationDataSource(request, ds));
    }

    @Override
    public BizResult<LastScheduleOrchestrationVO> lastPublishVersion(BizRequest<ScheduleOrchestrationExportParam> request) {
        // 非编排模式，取最新一线数据
        ScheduleOrchestrationDataSource ds = buildDataSource(request, false, true);
        ds.setNeedOrchestrationFiler(false);

        return BizResult.buildSuccessRes(scheduleOrchestrationQueryConvertor.toLastScheduleOrchestrationVO(request, ds));
    }

    @Override
    public ByteArrayBody exportScheduleOrchestrationEditList(BizRequest<ScheduleOrchestrationExportParam> request) throws IOException {
        ScheduleOrchestrationExportParam param = request.getParam();

        // 非编排模式，取最新一线数据；编排模式会获取。如果传入的last是Y则使用非编排模式
        // 传入的onlyPassActivity为true，资源编排导出进行中、已完成、已评价的活动，为false，导出审批不通过、进行中、已完成、已评价的数据
        ScheduleOrchestrationDataSource ds = buildDataSource(request, param.isEditModel(), onlyPassActivity);
        ds.setNeedOrchestrationFiler(false);

        ScheduleOrchestrationVO vo = scheduleOrchestrationQueryConvertor.toScheduleOrchestrationDataSource(request, ds);

        // 年份展会名称-高层会见汇总 如：2024年巴塞展-高层会见汇总
        String fileName = vo.getExhibitionYear() + ScheduleOrchestrationConstants.YEAR_CN + vo.getExhibitionName()
                + CharacterConstant.SHORT_BAR_EN + ScheduleOrchestrationConstants.HIGH_MEETING_SUMMARY;

        return ExcelExportHelper.simpleExportExcel(scheduleOrchestrationQueryConvertor.exportScheduleOrchestrationData(vo), fileName);
    }

    /**
     * 构建资源编排数据源
     *
     * @param request   展会ID请求
     * @param editModel true-编排模式，false-查看模式
     * @return
     */
    ScheduleOrchestrationDataSource buildDataSource(BizRequest<ScheduleOrchestrationExportParam> request, boolean editModel, boolean onlyPassActivity) {
        ScheduleOrchestrationDataSource ds = new ScheduleOrchestrationDataSource();
        ds.setEditModel(editModel);

        List<ExhibitionDirectorDO> directorList = exhibitionDirectorRepository.queryDirectorByExhibitionRowId(Collections.singletonList(request.getParam().getExhibitionRowId()))
                .get(request.getParam().getExhibitionRowId());
        ds.setDirectorList(directorList);

        boolean isLeader = currentLeader(request);
        if (!isLeader && StringUtils.isBlank(ds.fetchScheduleOrchestrationType(request.getEmpNo()))) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.orchestration.no.authority");
        }

        packScheduleOrchestrationDataSource(ds, request, isLeader, onlyPassActivity);
        return ds;
    }


    /**
     * 打包资源编排数据
     *
     * @param request
     * @return
     */
    void packScheduleOrchestrationDataSource(ScheduleOrchestrationDataSource ds, BizRequest<ScheduleOrchestrationExportParam> request, boolean isLeader, boolean onlyPassActivity) {
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(request.getParam().getExhibitionRowId());
        ds.setExhibitionInfo(exhibitionInfo);
        if (exhibitionInfo == null) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.info.not.exist");
        }
        Set<String> orgAuth = ds.fetchScheduleOrchestrationOrgAuth(request.getEmpNo());
        // 判断是否即是分营有是总营，如果是分营还是按照分营的角色过滤数据
        orgAuth = dealOrgAuth(ds, request, orgAuth);
        List<ActivityInfoDO> activityInfoDOList = fetchActivityInfo(request, orgAuth, isLeader, onlyPassActivity);
        Set<String> activityRowIds = activityInfoDOList.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toSet());
        // 《活动，交流方向》
        Map<String, List<ActivityCommunicationDirectionDO>> activity2direction = activityCommunicationDirectionRepository.queryAllByActivityRowId(new ArrayList<>(activityRowIds));
        // 《活动，查询客户信息》
        Map<String, List<ActivityCustomerInfoDO>> activity2Customer = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(activityRowIds);
        // 《活动，日程》
        Map<String, List<ActivityScheduleItemDO>> activity2ScheduleItem = activityScheduleItemRepository.getRelationScheduleInfoIds(new ArrayList<>(activityRowIds));
        // 《日程，人员》
        Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItem2People = activityScheduleItemPeopleRepository.getRelationSchedulePeopleInfoIds(
                activity2ScheduleItem.values().stream().flatMap(Collection::stream).map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList()));
        //日程，客户参与人
        Map<String, Map<String, ActivityRelationCustPeopleDO>> relationCustPeopleMap = activityRelationCustPeopleRepository.getRelationCustPeopleMap(activityRowIds);

        ds.setActivityInfoList(activityInfoDOList);
        ds.setCustomerInfoMap(activity2Customer);
        ds.setDirectionMap(activity2direction);
        ds.setActivity2ScheduleItem(activity2ScheduleItem);
        ds.setScheduleItem2People(scheduleItem2People);
        ds.setExhibitionInfo(exhibitionInfo);
        ds.setRelationCustPeople(relationCustPeopleMap);
        packScheduleOrchestrationVersionInfo(ds, request);
    }

    /**
     * 处理分营总营代表处，方便后面做数据过滤
     * @param ds
     * @param request
     * @param orgAuth
     * @return
     */
    public Set<String> dealOrgAuth(ScheduleOrchestrationDataSource ds, BizRequest<ScheduleOrchestrationExportParam> request, Set<String> orgAuth) {
        // 总营
        if (orgAuth == null) {
            ExhibitionDirectorDO subDirector = ds.filterDirector(ds.getDirectorList(), ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN, request.getEmpNo());
            // 即是总营又是分营
            if (subDirector != null) {
                ds.setRoleTypeAll(BooleanEnum.Y.getCode());

                // 分营编排开关是否开启
                if (CustExpansionConstant.RESOURCE_POINTS_ADMIN_SUB.equals(ds.getExhibitionInfo().getOrchestrationControl())) {
                    orgAuth = ds.getOrgAuth(request.getEmpNo());
                }
            } else {
                ds.setRoleTypeAll(BooleanEnum.N.getCode());
            }
        } else {
            ds.setRoleTypeAll(BooleanEnum.N.getCode());
        }
        return orgAuth;
    }

    /**
     * 打包资源编排版本信息
     *
     * @param ds
     * @param request
     */
    void packScheduleOrchestrationVersionInfo(ScheduleOrchestrationDataSource ds, BizRequest<ScheduleOrchestrationExportParam> request) {
        // 【1】 查询当前登录人最新的编排版本记录
        ActivityScheduleOrchestrationVersionQuery lastVersionQuery = new ActivityScheduleOrchestrationVersionQuery();
        lastVersionQuery.setExhibitionRowId(request.getParam().getExhibitionRowId());
        lastVersionQuery.setCreatedBy(request.getEmpNo());
        ActivityScheduleOrchestrationVersionDO lastScheduleOrchestrationVersion = activityScheduleOrchestrationVersionRepository.getLastScheduleOrchestrationVersion(lastVersionQuery);

        List<ActivityScheduleOrchestrationVersionDO> orchestrationVersionList = new ArrayList<>(NumberConstant.THIRTY_TWO);
        Date publishTime = null;

        if (lastScheduleOrchestrationVersion != null) {
            orchestrationVersionList.add(lastScheduleOrchestrationVersion);

            if (ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH.equalsIgnoreCase(lastScheduleOrchestrationVersion.getVersionStatus())) {
                // 如果最后一个版本是【已发布】
                publishTime = lastScheduleOrchestrationVersion.getPublishTime();
            } else {
                // 如果最后一个版本是【草稿】，则尝试查询最新发布版本
                lastVersionQuery.setVersionStatus(ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH);
                lastScheduleOrchestrationVersion = activityScheduleOrchestrationVersionRepository.getLastScheduleOrchestrationVersion(lastVersionQuery);

                if (lastScheduleOrchestrationVersion != null) {
                    publishTime = lastScheduleOrchestrationVersion.getPublishTime();
                }
            }
        }

        // 【2】查询在某个时间点后展会所有人发布的最新版本《员工编号，资源编排版本》
        Map<String, ActivityScheduleOrchestrationVersionDO> afterVersionMap = activityScheduleOrchestrationVersionRepository.queryAfterPublishTimeVersion(
                request.getParam().getExhibitionRowId(), publishTime);

        orchestrationVersionList.addAll(afterVersionMap.values());

        // 【3】查询展会中所有人最新版本对应的资源编排明细
        Map<String, List<ActivityScheduleOrchestrationDetailDO>> lastScheduleOrchestrationMap = activityScheduleOrchestrationDetailRepository
                .queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(
                        orchestrationVersionList.stream().map(ActivityScheduleOrchestrationVersionDO::getRowId).collect(Collectors.toList()));


        // 将当前登录人和展会其他在本人之后发布的【资源编排版本+ 对应明细】
        ds.setScheduleOrchestrationVersionDetailMap(lastScheduleOrchestrationMap);
        // 确保每个人对应一条最新的版本数据
        ds.setScheduleOrchestrationVersionList(orchestrationVersionList);
    }

    /**
     * 获取权限过滤后的活动数据
     *
     * @param request  请求
     * @param orgAuths 拥有的数据权限
     * @param isLeader 是否领导
     * @return 具有权限的活动
     */
    List<ActivityInfoDO> fetchActivityInfo(BizRequest<ScheduleOrchestrationExportParam> request, Set<String> orgAuths, boolean isLeader, boolean onlyPassActivity) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        query.setOriginRowIdList(Collections.singletonList(request.getParam().getExhibitionRowId()));
        query.setActivityStatus(onlyPassActivity ? Lists.newArrayList(ActivityStatusEnum.PROGRESS.getCode(),
                ActivityStatusEnum.FINISH.getCode(), ActivityStatusEnum.EVALUATED.getCode())
                : Lists.newArrayList(ActivityStatusEnum.PROGRESS.getCode(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS.getCode(),
                ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS.getCode(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(),
                ActivityStatusEnum.BUSINESS_APPROVAL.getCode(),
                ActivityStatusEnum.FINISH.getCode(),
                ActivityStatusEnum.EVALUATED.getCode()));

        List<ActivityInfoDO> activityInfoDOList = activityInfoRepository.searchByParam(query);
        // 如果是分营(非领导)的传入进来，需要进行数据权限的过滤
        if (!isLeader && orgAuths != null && !request.getParam().isPreviewFlag()) {
            return activityInfoDOList.stream().filter(e -> {
                for (String org : orgAuths) {
                    if (StringUtils.isNotBlank(e.getApplyFullDepartmentNo()) && e.getApplyFullDepartmentNo().contains(org)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }

        return activityInfoDOList;
    }

    /**
     * 当前登录用户是否为领导
     *
     * @param request
     * @return
     */
    boolean currentLeader(BizRequest<ScheduleOrchestrationExportParam> request) {
        Map<String, Boolean> map = userCenterService.isLeaderByShortNo(Collections.singletonList(request.getEmpNo()));
        Boolean isLeader = map.get(request.getEmpNo());
        return isLeader != null && isLeader;
    }
}
