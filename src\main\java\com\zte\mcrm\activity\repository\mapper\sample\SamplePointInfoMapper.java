package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;

public interface SamplePointInfoMapper {
    /**
     * all field insert
     */
    int insert(SamplePointInfoDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(SamplePointInfoDO record);

    /**
     * query by primary key
     */
    SamplePointInfoDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(SamplePointInfoDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(SamplePointInfoDO record);
}