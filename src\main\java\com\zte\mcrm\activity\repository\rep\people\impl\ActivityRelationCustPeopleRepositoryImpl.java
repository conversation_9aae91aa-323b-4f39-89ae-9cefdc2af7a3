package com.zte.mcrm.activity.repository.rep.people.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.people.ActivityRelationCustPeopleExtMapper;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationContactDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ActivityRelationCustPeopleRepositoryImpl implements ActivityRelationCustPeopleRepository {
    @Autowired
    private ActivityRelationCustPeopleExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public ActivityRelationCustPeopleDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : extMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public int insertSelective(ActivityRelationCustPeopleDO record) {
        setDefaultValue(record);
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationCustPeopleDO record) {
        if(StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationCustPeopleDO> queryErrContractNos(int index, int size) {
        return extMapper.queryErrContractNos(index, size);
    }

    @Override
    public List<ActivityRelationCustPeopleDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public int insertSelective(List<ActivityRelationCustPeopleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityRelationCustPeopleDO record : recordList) {
            this.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public Map<String, List<ActivityRelationCustPeopleDO>> getCustPeopleListByActivityRowIds(Set<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                extMapper.getCustPeopleListByActivityRowIds(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityRelationCustPeopleDO::getActivityRowId));
    }

    @Override
    public Map<String, Map<String, ActivityRelationCustPeopleDO>> getRelationCustPeopleMap(Set<String> activityRowIds) {
        Map<String, Map<String, ActivityRelationCustPeopleDO>> relationCustPeopleMap = new HashMap<>();
        Map<String, List<ActivityRelationCustPeopleDO>> activityId2CustPeopleListMap = getCustPeopleListByActivityRowIds(activityRowIds);
        activityId2CustPeopleListMap.forEach((activityRowId, custPeopleList) -> {
            Map<String, ActivityRelationCustPeopleDO> custPeopleDoMap = custPeopleList.stream()
                    .collect(Collectors.toMap(ActivityRelationCustPeopleDO::getContactNo, i -> i, (u, v) -> u));
            relationCustPeopleMap.put(activityRowId, custPeopleDoMap);
        });
        return relationCustPeopleMap;
    }

    /**
     * 查找用户最近创建的活动中使用的客户联系人
     *
     * @param pageQuery
     * @return {@link List< ActivityRelationCustPeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:56
     */
    @Override
    public List<ActivityRelationCustPeopleDO> selectRecentlyCustomerPeopleByUser(PageQuery<ActivityRecentlySearchParam> pageQuery) {
        if (!pageQuery.validatePage()
                || StringUtils.isBlank(pageQuery.getParam().getCustomerCode())
                || StringUtils.isBlank(pageQuery.getParam().getEmpNo())) {
            return Collections.emptyList();
        }
        PageInfo<ActivityRelationCustPeopleDO> pageInfo
                = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> extMapper.selectRecentlyCustomerPeopleByUser(pageQuery.getParam()));
        return pageInfo.getList();
    }

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchInsert(List<ActivityRelationCustPeopleDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        list.forEach(this::setDefaultValue);
        extMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 批量修改数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public void batchUpdate(List<ActivityRelationCustPeopleDO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(ActivityRelationCustPeopleDO custPeopleDO : list){
            updateByPrimaryKeySelective(custPeopleDO);
        }
    }

    /**
     * 设置默认值
     *
     * @param customerPeopleDO 实体类
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    private void setDefaultValue(ActivityRelationCustPeopleDO customerPeopleDO) {
        customerPeopleDO.setRowId(Optional.ofNullable(customerPeopleDO.getRowId()).orElse(keyIdService.getKeyId()));
        customerPeopleDO.setCreatedBy(Optional.ofNullable(customerPeopleDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        customerPeopleDO.setLastUpdatedBy(Optional.ofNullable(customerPeopleDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        customerPeopleDO.setCreationDate(new Date());
        customerPeopleDO.setLastUpdateDate(new Date());
        customerPeopleDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }


    @Override
    public List<ActivityRelationCustPeopleDO> queryAllCustPeopleForActivity(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : extMapper.queryAllCustPeopleForActivity(activityRowIds);
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.deleteByRowIds(operator, rowIds);
    }

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效客户联系人
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationCustPeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityRelationCustPeopleDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllActivityWithNotEnable(activityRowId);
    }

    @Override
    public List<ActivityRelationContactDO> queryAllContactVisits(List<String> contactNos, String startTime, String endTime) {
        return CollectionUtils.isEmpty(contactNos) ? Collections.emptyList()
                : extMapper.queryAllContactVisits(contactNos, startTime, endTime);
    }

    @Override
    public List<ActivityRelationContactDO> queryContactVisits(List<String> contactNos) {
        return CollectionUtils.isEmpty(contactNos) ? Collections.emptyList()
                : extMapper.queryAllContactVisits(contactNos, null, null);
    }
}
