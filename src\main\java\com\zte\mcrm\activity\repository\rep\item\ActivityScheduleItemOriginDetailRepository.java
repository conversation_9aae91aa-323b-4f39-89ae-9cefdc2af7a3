package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginDetailDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivityScheduleItemOriginDetailRepository {

    /**
     * 批量插入日程安排信息
     *
     * @param scheduleItemList
     * @return
     */
    int batchInsert(List<ActivityScheduleItemOriginDetailDO> scheduleItemList);

    /**
     * 根据源数据版本RowId获取信息
     *
     * @param scheduleItemOriginVersionRowIds 源数据版本RowId
     * @return
     */
    Map<String, List<ActivityScheduleItemOriginDetailDO>> getRelationScheduleItemOriginDetails(List<String> scheduleItemOriginVersionRowIds);

}
