package com.zte.mcrm.activity.common.cache.client;

import com.zte.mcrm.activity.common.cache.cache.Cache;
import com.zte.mcrm.activity.common.cache.cache.CacheConfig;
import com.zte.mcrm.activity.common.cache.cache.LocalCache;
import com.zte.mcrm.activity.common.cache.loader.CacheDataLoader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 缓存使用客户端基类
 * <pre>
 *     基类中的方法中的参数都是对应缓存key
 *     对于各个业务场景的缓存客户端，当业务字段作为key时，只要直接使用即可；如果业务字段并不直接作为key，可自行处理业务字段然后调用基类方法。（如：BasLookupCacheClient）
 *     各个业务缓存客户端应该以业务为主，不应该关注具体key规则
 *     所有缓存客户端的自定义场景方法都是基于基类中已有接口的扩展封装
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-09-20
 */
public abstract class BaseCacheClient<V> implements CacheClient<V> {
    /**
     * 缓存
     */
    private Cache<String, V> cache;
    /**
     * 数据加载器
     */
    private CacheDataLoader<String, V> loader;

    /**
     * @param loader 缓存数据加载器
     * @param config 缓存配置
     */
    public BaseCacheClient(CacheDataLoader<String, V> loader, CacheConfig config) {
        init(loader, config);
        this.loader = loader;
    }

    @Override
    public V fetchCache(String key) {
        return StringUtils.isBlank(key) ? null : cache.fetchCache(key);
    }

    @Override
    public Map<String, V> fetchAllCache(Set<String> keys) {
        Map<String, V> res = new HashMap<>();

        Set<String> param = new HashSet<>();
        if (!CollectionUtils.isEmpty(keys)) {
            param.addAll(keys.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        }

        if (!param.isEmpty()) {
            Map<String, V> existValMap = cache.fetchCachesOnlyExist(param);
            param.removeAll(existValMap.keySet());

            Map<String, V> notExistValMap = loader.loadAll(param);

            notExistValMap.forEach(this::addCache);

            res.putAll(existValMap);
            res.putAll(notExistValMap);
        }

        return res;
    }

    @Override
    public void addCache(String key, V val) {
        if (StringUtils.isNotBlank(key) && val != null) {
            cache.addCache(key, val);
        }
    }

    @Override
    public void remove(String key) {
        if (StringUtils.isNotBlank(key)) {
            cache.invalidate(key);
        }
    }

    @Override
    public boolean removeAll() {
        cache.invalidateAll();
        // 为了解决Test中的mock返回值
        return true;
    }

    /**
     * 初始化
     *
     * @param loader
     * @param config
     */
    private void init(CacheDataLoader<String, V> loader, CacheConfig config) {
        // 可以在配置中增加《缓存策略》参数，这样可以根据策略来构建：本地缓存、分布式缓存等缓存实现方式。以后真有redis分布式缓存再优化
        cache = new LocalCache<>(loader, config);
    }
}
