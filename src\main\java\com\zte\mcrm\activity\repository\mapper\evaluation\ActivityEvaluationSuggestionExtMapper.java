package com.zte.mcrm.activity.repository.mapper.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationSuggestionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityEvaluationSuggestionExtMapper extends ActivityEvaluationSuggestionMapper {
    List<ActivityEvaluationSuggestionDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);
}