package com.zte.mcrm.activity.application.cto.helper;

import com.zte.mcrm.activity.common.config.CloudDiskConfig;
import com.zte.mcrm.activity.common.constant.ActivityConstant;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.integration.zteKmCloudUdmCloudDisk.CloudDiskDownloadService;
import com.zte.mcrm.activity.service.file.UploadService;
import com.zte.mcrm.activity.service.model.ByteUploadModel;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import com.zte.mcrm.activity.web.controller.file.vo.FileInfoVO;
import com.zte.mcrm.customvisit.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.Date;

import static com.zte.mcrm.activity.service.schedule.param.ScheduleOrchestrationMailParam.CLOUD_DISK_DOWNLOAD_URL;

/**
 * {@code @description 文件上传到文档云辅助类}
 *
 * <AUTHOR>
 * @date 2024/12/23 下午6:46
 */
@Component
public class CtoPlanUploadFileHelper {
    @Autowired
    private UploadService uploadService;
    @Autowired
    private CloudDiskDownloadService cloudDiskDownloadService;
    @Autowired
    private CloudDiskConfig cloudDiskConfig;

    public FileInfoVO doUpload(BizRequest<CtoPlanExportParam> req, Date scopeEnd, ByteArrayOutputStream fileDataByte)  {
        String month = DateUtils.convertDateToString(scopeEnd, "MM");
        // 将文件上传到文档云
        ByteUploadModel uploadModel = new ByteUploadModel();
        uploadModel.setFileName(month + ActivityConstant.CTO_MONTH_REPORT_FILE_NAME);
        uploadModel.setData(fileDataByte.toByteArray());

        BizRequest<ByteUploadModel> uploadRequest = BizRequestUtil.copyRequest(req, c -> uploadModel);
        return uploadService.upload(uploadRequest);
    }

    public String getDownloadUrl(String empNo,FileInfoVO fileInfoVO){
        // 获取文档云下载链接
        String downloadToken = cloudDiskDownloadService.getDownloadFileKey(fileInfoVO.getFileToken(), fileInfoVO.getFileName(), empNo);
        return String.format("%s%s%s/%s",
                cloudDiskConfig.getHost(),
                CLOUD_DISK_DOWNLOAD_URL,
                cloudDiskConfig.getXOriginServiceName(),
                downloadToken);
    }
}
