package com.zte.mcrm.activity.service.activitylist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 14:08
 */
@Setter
@Getter
@ToString
public class ActivityInfoFolVO {

    /**
     * 活动id
     */
    private String rowId;
    /**
     * 拓展活动申请单号
     */
    private String activityRequestNo;
    /**
     * 拓展活动议题
     */
    private String activityTitle;
    /**
     * 拓展活动状态。枚举：ActivityStatusEnum
     */
    private String activityStatus;
    /**
     * 拓展活动状态名称
     */
    private String activityStatusName;
    /**
     * 活动截止时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 活动主要客户单位编码
     */
    private String customerCode;
    /**
     * 活动主要客户单位名称
     */
    private String customerName;
    /**
     * 是否在角色中（0否，1是）
     */
    private byte hit;

    public void setActivityStatusName(String activityStatusName) {
        this.activityStatusName = activityStatusName;
    }

}
