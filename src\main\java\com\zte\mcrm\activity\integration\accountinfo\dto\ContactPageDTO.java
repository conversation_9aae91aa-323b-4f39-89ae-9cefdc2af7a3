package com.zte.mcrm.activity.integration.accountinfo.dto;


import lombok.Getter;
import lombok.Setter;

/**
 * 联系人列表
 * <AUTHOR>
 */
@Getter
@Setter
public class ContactPageDTO {
    /** 客户编码 */
    private String customerCode;
    /** 联系人级别 */
    private String personLevel;
    /** 联系人id */
    private String contactRowId;
    /** 联系人编码 */
    private String contactNo;
    /** 联系人名称 */
    private String name;
    /** 联系人名称拼音1 */
    private String pinyinName1;
    /** 联系人拼音2 */
    private String pinyinName2;
    /** 联系人名称-英文 */
    private String nameEn;
    /** 联系人状态。枚举：ContactPersonStatusEnum */
    private String status;
    /** 联系人办公邮箱 */
    private String officeMail;
    /** 联系人办公电话 */
    private String officePhone;
    /** 国家/地区id */
    private String countryId;
    /** 城市id */
    private String cityId;
    /** 工作地址 */
    private String workAddress;
    /** 邮编 */
    private String postCode;
    /** 照片链接 */
    private String photoLink;
    /** 性别 */
    private String sex;
    /** 语言 */
    private String language;
    /** 手机1 */
    private String phone1;
    /** 手机2 */
    private String phone2;

    /** 决策产品 */
    private String decisionProducts;
    /** 客户经理A角 */
    private String managerA;
    /** 客户经理B角 */
    private String managerB;
    /** 公网信息链接 */
    private String pubIntnetInforLink;
    /** 备注 */
    private String remarks;

    /** 所属客户组织部门 */
    private String belongDept;
    /** 岗位 */
    private String job;

    /** 当前审批人 */
    private String currentApprover;
}
