package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityCommunicationDirectionExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ActivityCommunicationDirectionRepositoryImpl implements ActivityCommunicationDirectionRepository {
    @Autowired
    private ActivityCommunicationDirectionExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;
    @Override
    public int insertSelective(ActivityCommunicationDirectionDO record) {
        setDefaultValue(record);
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityCommunicationDirectionDO record) {
        if(StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityCommunicationDirectionDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public List<ActivityCommunicationDirectionDO> queryAllByActivityRowIds(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowIds(activityRowIds);
    }

    @Override
    public Map<String, List<ActivityCommunicationDirectionDO>> queryAllByActivityRowId(List<String> activityRowIds) {
        List<ActivityCommunicationDirectionDO> list = CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowIds(activityRowIds);

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream()
                .collect(Collectors.groupingBy(ActivityCommunicationDirectionDO::getActivityRowId));
    }

    @Override
    public int insertSelective(List<ActivityCommunicationDirectionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityCommunicationDirectionDO record : recordList) {
            this.insertSelective(record);
        }

        return recordList.size();
    }
    /**
     * 添加活动关联交流方向信息
     *
     * @param list
     */
    @Override
    public int batchInsert(List<ActivityCommunicationDirectionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        list.forEach(this::setDefaultValue);
        extMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 添加活动关联交流方向信息（如果没有主键，自动生成）
     *
     * @param list
     */
    @Override
    public void batchUpdateCommunication(List<ActivityCommunicationDirectionDO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(ActivityCommunicationDirectionDO directionDO : list){
            updateByPrimaryKeySelective(directionDO);
        }
    }

    /**
     * 设置默认值
     *
     * @param directionDO
     */
    private void setDefaultValue(ActivityCommunicationDirectionDO directionDO) {
        directionDO.setRowId(Optional.ofNullable(directionDO.getRowId()).orElse(keyIdService.getKeyId()));
        directionDO.setCreatedBy(Optional.ofNullable(directionDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        directionDO.setLastUpdatedBy(Optional.ofNullable(directionDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        directionDO.setCreationDate(new Date());
        directionDO.setLastUpdateDate(new Date());
        directionDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.deleteByRowIds(operator, rowIds);
    }

    /**
     * 查询所有包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效交流方向
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityCommunicationDirectionDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityCommunicationDirectionDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllActivityWithNotEnable(activityRowId);
    }
}