package com.zte.mcrm.activity.repository.model.authority;

/* Started by AICoder, pid:94f0f1490ac201014a8b0bf7700a586caef4f906 */

import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationBizTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ActivityResourceOperationAuthDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * 活动id
     */
    private String activityRowId;

    /**
     * 被授权人工号
     */
    private String peopleNo;

    /**
     * 业务类型
     * {@link ActivityResourceOperationBizTypeEnum}
     */
    private String bizType;

    /**
     * 业务对象id
     */
    private String bizRelatedId;

    /**
     * 权限值
     */
    private Integer operationAuthValue;

    /**
     * 有效标识，Y有效，N失效
     */
    private String enabledFlag;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date creationDate;

    /**
     * 更新人
     */
    private String lastUpdatedBy;

    /**
     * 更新时间
     */
    private Date lastUpdateDate;
}

/* Ended by AICoder, pid:94f0f1490ac201014a8b0bf7700a586caef4f906 */