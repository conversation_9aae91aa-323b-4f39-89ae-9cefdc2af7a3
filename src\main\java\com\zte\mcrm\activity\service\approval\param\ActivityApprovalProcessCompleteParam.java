package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.expansion.access.vo.ApprovalFlowCompletedInfoVO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 审批完成入参
 */
@Data
public class ActivityApprovalProcessCompleteParam {

    /**
     * 拓展活动id
     */
    @NotNull
    private String activityRowId;

    /**
     * 审批人
     */
    @NotNull
    private String approveBy;

    /**
     * 审批结果Y-同意，N-拒绝，T-转交。枚举：ApproveResultEnum
     */
    @NotNull
    private String approveResult;



    /** 记录最近修改人 */
    @NotNull
    private String lastUpdatedBy;

    /** 扩展编码 */
    private String extendedCode;

    public void buildCompleteOf(ApprovalFlowCompletedInfoVO param) {
        this.activityRowId = param.getBusinessId();
        this.approveBy = param.getApprover();
        this.approveResult = param.getApprovalResult();
        this.lastUpdatedBy = param.getApprover();
        this.extendedCode = param.getExtendedCode();
    }

}