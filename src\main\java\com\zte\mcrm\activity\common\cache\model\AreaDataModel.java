package com.zte.mcrm.activity.common.cache.model;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * 地区数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AreaDataModel {
    /**
     * 区域类型。见：AreaTypeEnum
     */
    private String areaType;
    /**
     * 区域码
     */
    private String areaCode;
    /**
     * 区域中文名简称
     */
    private String areaNameZh;
    /**
     * 区域英文名简称
     */
    private String areaNameEn;
    /**
     * 区域中文名全称
     */
    private String areaFullNameZh;
    /**
     * 区域英文名全称
     */
    private String areaFullNameEn;
    /**
     * 国家区域二位码
     */
    private String areaTwoCode;
    /**
     * 国家区域三位码
     */
    private String areaThreeCode;
    /**
     * 中文或者英文查询
     */
    private String desc50;
    /**
     * BG001的code
     */
    private String desc60;

    public AreaDataModel() {
    }

    public AreaDataModel(MdmAreaDTO area) {
        this.areaType = area.getDesc2();
        this.areaCode = area.getCode();
        this.areaNameZh = area.getDesc3();
        this.areaNameEn = area.getDesc5();
        this.areaFullNameZh = area.getDesc1();
        this.areaFullNameEn = area.getDesc4();
        this.areaTwoCode = area.getDesc7();
        this.areaThreeCode = area.getDesc8();
        this.desc50 = area.getDesc50();
        this.desc60 = area.getDesc60();
    }

    /**
     * 转为老的对象（为了兼容老代码地方的使用）
     *
     * @return
     */
    public MdmAreaDTO toMdmAreaDTO() {
        MdmAreaDTO area = new MdmAreaDTO();

        area.setDesc2(this.areaType);
        area.setCode(this.areaCode);
        area.setDesc3(this.areaNameZh);
        area.setDesc5(this.areaNameEn);
        area.setDesc1(this.areaFullNameZh);
        area.setDesc4(this.areaFullNameEn);
        area.setDesc7(this.areaTwoCode);
        area.setDesc8(this.areaThreeCode);
        area.setDesc50(this.desc50);
        area.setDesc60(this.desc60);

        return area;
    }

    /**
     * 区域信息key
     *
     * @return
     */
    public String identityKey() {
        return this.areaType + CharacterConstant.PAUSE_MARK + this.areaCode;
    }

    /**
     * 区域信息key
     *
     * @param model
     * @return
     */
    public static String identityKey(AreaDataModel model) {
        return model.identityKey();
    }

    /**
     * 提取区域类型
     *
     * @param identityKey
     * @return
     */
    public static String fetchAreaType(String identityKey) {
        if (StringUtils.isBlank(identityKey)) {
            return null;
        }

        return identityKey.split(CharacterConstant.PAUSE_MARK)[0];
    }

    /**
     * 获取区域码
     *
     * @param identityKey
     * @return
     */
    public static String fetchAreaCode(String identityKey) {
        if (StringUtils.isBlank(identityKey)) {
            return null;
        }


        String[] arr = identityKey.split(CharacterConstant.PAUSE_MARK);
        if (arr.length == NumberConstant.TWO) {
            return arr[1];
        }
        return null;
    }

}
