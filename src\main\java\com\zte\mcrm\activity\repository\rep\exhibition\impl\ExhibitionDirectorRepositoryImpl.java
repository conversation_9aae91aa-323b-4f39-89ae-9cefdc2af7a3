package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionDirectorExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ExhibitionDirectorRepositoryImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会负责人业务层接口
 * @date 2023/9/614:56
 */
@Component
public class ExhibitionDirectorRepositoryImpl implements ExhibitionDirectorRepository {
    @Resource
    private ExhibitionDirectorExtMapper exhibitionDirectorExtMapper;

    @Override
    public int insertSelective(ExhibitionDirectorDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        return exhibitionDirectorExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionDirectorDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        return exhibitionDirectorExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String,List<ExhibitionDirectorDO>> queryDirectorByExhibitionRowId(List<String> exhibitionRowIds) {
       return CollectionUtils.isEmpty(exhibitionRowIds) ? Collections.emptyMap() :
            exhibitionDirectorExtMapper.queryDirectorByExhibitionRowId(exhibitionRowIds).stream().collect(
                Collectors.groupingBy(ExhibitionDirectorDO::getExhibitionRowId));
    }

    @Override
    public List<ExhibitionDirectorDO> queryDirectorsByEmpNo(List<String> empNoList) {
        return CollectionUtils.isEmpty(empNoList) ? Collections.emptyList() : exhibitionDirectorExtMapper.queryDirectorByEmployNo(empNoList);
    }

    @Override
    public List<ExhibitionDirectorDO> queryDirectorByExhibitionRowIdAndEmpNo(String exhibitionRowId, String empNo) {
        if (StringUtils.isBlank(exhibitionRowId) || StringUtils.isBlank(empNo)) {
            return new ArrayList<>();
        }

        return exhibitionDirectorExtMapper.queryDirectorByExhibitionRowIdAndEmpNo(exhibitionRowId, empNo);
    }

    /**
     * 根据展会ID获取负责人信息
     * @param exhibitionRowId
     * @return
     */
    @Override
    public List<ExhibitionDirectorDO> fetchDirectorByExhibitionRowId(String exhibitionRowId) {
        return exhibitionDirectorExtMapper.queryDirectorByExhibitionRowId(Collections.singletonList(exhibitionRowId));
    }

}
