package com.zte.mcrm.activity.repository.mapper.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityEvaluationInfoMapper {
    /**
     * all field insert
     */
    int insert(ActivityEvaluationInfoDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityEvaluationInfoDO record);

    /**
     * query by primary key
     */
    ActivityEvaluationInfoDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityEvaluationInfoDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityEvaluationInfoDO record);

    /**
     * 新工号切换
     * @param searchParam
     * @return
     */
    List<ActivityEvaluationInfoDO> queryEmpNoTransList(DataTransParam searchParam);
}