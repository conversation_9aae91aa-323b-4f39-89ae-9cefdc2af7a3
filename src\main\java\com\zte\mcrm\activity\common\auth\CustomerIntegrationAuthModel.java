package com.zte.mcrm.activity.common.auth;

import com.google.common.collect.Sets;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.TalkOperatorVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.enums.BooleanEnum.Y;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.JOIN_CONFERENCE;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.JOIN_EXHIBITION;
import static com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE;
import static com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum.ZTE_PEOPLE;
import static com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum.REFERENCE_ADMIN;

/**
 * 客户融合活动权限模型
 *
 * <AUTHOR>
 * @date 2023/8/18 下午5:47
 */
@Getter
@Setter
public class CustomerIntegrationAuthModel {

    /**
     * 操作人工号
     */
    private String operatorNo;

    /**
     * 是否公司领导
     */
    private boolean leader;

    /**
     * 约束查询条件
     */
    private List<ActivityRoleConstraintModel> constraintModelList;

    /**
     * 是否可编辑
     *
     * @param source
     * @param rowId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:47
     */
    public boolean editable(StandardActivityDetailDataSource source, String rowId) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = source.getZtePeopleMap().get(rowId);
        if (isTargetPeopleType(ztePeopleDOList, ORGANIZER, CREATE_BY, APPLICANT)) {
            return true;
        }
        List<ActivityRoleConstraintModel> editableRoleList = CollectionUtils.isEmpty(constraintModelList) ?
                Collections.emptyList() : constraintModelList.stream().filter(ActivityRoleConstraintModel::editable).collect(Collectors.toList());
        return roleConstraintCheck(editableRoleList, source, rowId);
    }

    /**
     * 是否可删除
     *
     * @param source
     * @param rowId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:47
     */
    public boolean deletable(StandardActivityDetailDataSource source, String rowId) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = source.getZtePeopleMap().get(rowId);
        if (isTargetPeopleType(ztePeopleDOList, CREATE_BY)) {
            return true;
        }
        List<ActivityRoleConstraintModel> editableRoleList = CollectionUtils.isEmpty(constraintModelList) ?
                Collections.emptyList() : constraintModelList.stream().filter(ActivityRoleConstraintModel::deletable).collect(Collectors.toList());
        return roleConstraintCheck(editableRoleList, source, rowId);
    }

    /**
     * 是否可作废
     * @param source    数据源
     * @param rowId     活动Id
     * @return java.lang.Boolean
     * <AUTHOR>
     * date: 2023/8/29 13:34
     */
    public Boolean voidable(StandardActivityDetailDataSource source, String rowId) {
        List<ActivityRoleConstraintModel> voidableRoleList = CollectionUtils.isEmpty(constraintModelList) ?
                Collections.emptyList() : constraintModelList.stream().filter(ActivityRoleConstraintModel::voidable).collect(Collectors.toList());
        return roleConstraintCheck(voidableRoleList, source, rowId);
    }

    /**
     * 可取消判断
     * @param source    数据源
     * @param rowId     活动Id
     * @return java.lang.Boolean
     * <AUTHOR>
     * date: 2023/8/29 16:43
     */
    public Boolean cancelable(StandardActivityDetailDataSource source, String rowId) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = source.getZtePeopleMap().get(rowId);
        if (isTargetPeopleType(ztePeopleDOList, CREATE_BY, APPLICANT)) {
            return true;
        }
        List<ActivityRoleConstraintModel> cancelableRoleList = CollectionUtils.isEmpty(constraintModelList) ?
                Collections.emptyList() : constraintModelList.stream().filter(ActivityRoleConstraintModel::cancelable).collect(Collectors.toList());
        return roleConstraintCheck(cancelableRoleList, source, rowId);
    }

    /**
     * 判断是否可变更
     * @param source    数据源
     * @param rowId     活动Id
     * @return java.lang.Boolean
     * <AUTHOR>
     * date: 2023/8/29 16:53
     */
    public boolean changeable(StandardActivityDetailDataSource source, String rowId) {
        ActivityInfoDO activityInfo = source.fetchActivityInfo(rowId);
        if (activityInfo == null) {
            return false;
        }
        ExhibitionInfoDO exhibitionInfo = source.fetchExhibitionInfo(rowId);
        SamplePointInfoDO samplePointInfoDO = source.fetchSamplePointInfo(rowId);
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = source.getZtePeopleMap().get(rowId);
        if (this.checkTargetPeopleTypeByActivityType(activityInfo, exhibitionInfo, samplePointInfoDO, ztePeopleDOList)) {
            return true;
        }
        List<ActivityRoleConstraintModel> changeableRoleList = CollectionUtils.isEmpty(constraintModelList) ?
                Collections.emptyList() : constraintModelList.stream().filter(ActivityRoleConstraintModel::changeable).collect(Collectors.toList());
        return roleConstraintCheck(changeableRoleList, source, rowId);
    }

    /**
     * 是否可以查看
     *
     * @param source
     * @param rowId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/2/21 下午4:12
     */
    public boolean viewable(StandardActivityDetailDataSource source, String rowId) {
        ActivityInfoDO activityInfo = source.fetchActivityInfo(rowId);
        if (Objects.isNull(activityInfo)) {
            return Boolean.FALSE;
        }
        boolean isDraft = ActivityStatusEnum.DRAFT.isMe(activityInfo.getActivityStatus());
        if (isDraft) {
            return StringUtils.equals(this.operatorNo, activityInfo.getCreatedBy());
        }
        return this.isLeader() || isRelated(source, rowId) || roleConstraintCheck(constraintModelList, source, rowId);
    }

    /**
     * 判断是否活动关联人员
     *
     * @param source
     * @param rowId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/2/21 下午4:12
     */
    public boolean isRelated(StandardActivityDetailDataSource source, String rowId) {
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = source.getZtePeopleMap().getOrDefault(rowId, Collections.emptyList());
        return ztePeopleDOList.stream()
                .anyMatch(item -> StringUtils.equals(item.getPeopleCode(), this.operatorNo)
                        && Objects.nonNull(item.getAuthCount())
                        && item.getAuthCount() > 0);
    }

    /***
     * <p>
     * 根据活动类型判断当前人员是否符合允许的ztePeople范围
     *      如果为展会或大会 需要改展会或大会的报名状态为开启
     *      如果为样板点，样板点状态需要为有效
     *
     * </p>
     * <AUTHOR>
     * @since  2023/11/16 下午5:25
     * @param activityInfo 活动信息
     * @param ztePeopleList 活动关联人员
     * @return boolean
     */
    private boolean checkTargetPeopleTypeByActivityType(ActivityInfoDO activityInfo, ExhibitionInfoDO exhibitionInfo,
                                                        SamplePointInfoDO samplePointInfoDO, List<ActivityRelationZtePeopleDO> ztePeopleList) {
        if (ActivityTypeEnum.in(activityInfo.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE)) {
            return isTargetPeopleType(ztePeopleList, CREATE_BY, APPLICANT)
                    && Objects.nonNull(exhibitionInfo)
                    && Y.getCode().equalsIgnoreCase(exhibitionInfo.getEntryOpenStatus());
        }
        if (ActivityTypeEnum.in(activityInfo.getActivityType(), VISITING_SAMPLE)) {
            return isTargetPeopleType(ztePeopleList, CREATE_BY, APPLICANT)
                    && Objects.nonNull(samplePointInfoDO)
                    && Y.getCode().equalsIgnoreCase(samplePointInfoDO.getSamplePointStatus());
        }

        return isTargetPeopleType(ztePeopleList, ORGANIZER, CREATE_BY, APPLICANT);
    }

    /**
     * 约束校验
     *
     * @param constraintModelList
     * @param source
     * @param rowId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:47
     */
    private boolean roleConstraintCheck(List<ActivityRoleConstraintModel> constraintModelList, StandardActivityDetailDataSource source, String rowId) {
        if (CollectionUtils.isEmpty(constraintModelList)) {
            return false;
        }
        Optional<ActivityInfoDO> optional = source.getActivityInfoList().stream().filter(item -> StringUtils.equals(rowId, item.getRowId())).findAny();
        if (!optional.isPresent()) {
            return false;
        }
        ActivityInfoDO activityInfoDO = optional.get();
        List<ActivityCommunicationDirectionDO> directionDOList = source.fetchCommDirection(rowId);
        List<ActivityCustomerInfoDO> customerInfoDOList = source.fetchCustomerList(rowId);
        for (ActivityRoleConstraintModel constraintModel : constraintModelList) {
            boolean isActivityTypeMatched = isMatched(constraintModel.getActivityTypeSet(), activityInfoDO.getActivityType());
            boolean isCommunicationDirectionMatched = isMatched(constraintModel.getCommunicationDirectionSet(), directionDOList,
                    item -> constraintModel.getCommunicationDirectionSet().contains(item.getCommunicationDirection()));
            boolean isCustomerGroupAccountMatched = isMatched(constraintModel.getCustomerGroupAccountSet(), customerInfoDOList,
                    item -> constraintModel.getCustomerGroupAccountSet().contains(item.getMtoCode())
                            && (StringUtils.isEmpty(item.getMktCode())
                                || constraintModel.getCustomerGroupAccountSet().contains(item.getMktCode())));
            if (departmentMatched(constraintModel, activityInfoDO, customerInfoDOList)
                    && isActivityTypeMatched
                    && isCommunicationDirectionMatched
                    && isCustomerGroupAccountMatched) {
                return true;
            }
        }
        return false;
    }

    /**
     * 暂时未考虑到一个约束多个条件的配置场景，先做并集处理
     *
     * @param constraintModel
     * @param activityInfoDO
     * @param customerInfoDOList
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/5/6 下午11:20
     */
    private boolean departmentMatched(ActivityRoleConstraintModel constraintModel, ActivityInfoDO activityInfoDO, List<ActivityCustomerInfoDO> customerInfoDOList) {
        boolean isApplyDepartmentMatched = isMatched(constraintModel.getApplyDepartmentNoSet(), activityInfoDO.getApplyDepartmentNo());
        boolean isCustomerBelongMatched = isMatched(constraintModel.getCustomerBelongBuIdSet(), customerInfoDOList,
                item -> constraintModel.getCustomerBelongBuIdSet().contains(item.getBelongBuId()));
        if (CollectionUtils.isNotEmpty(constraintModel.getApplyDepartmentNoSet()) && CollectionUtils.isNotEmpty(constraintModel.getCustomerBelongBuIdSet())) {
            return isApplyDepartmentMatched || isCustomerBelongMatched;
        } else {
            return isApplyDepartmentMatched && isCustomerBelongMatched;
        }
    }

    /**
     * 单个约束条件校验
     *
     * @param constraintSet
     * @param match
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:48
     */
    private boolean isMatched(Set<String> constraintSet, String match) {
        return CollectionUtils.isEmpty(constraintSet) || constraintSet.contains(match);
    }

    /**
     * 多个约束条件校验
     *
     * @param constraintSet
     * @param matches
     * @param predicate
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/18 下午5:48
     */
    private <T> boolean isMatched(Set<String> constraintSet, Collection<T> matches, Predicate<? super T> predicate) {
        if (CollectionUtils.isEmpty(constraintSet)) {
            return true;
        }
        if (CollectionUtils.isEmpty(matches)) {
            return false;
        }
        return matches.stream().anyMatch(predicate);
    }

    /**
     * 是否为固定身份
     * @param ztePeopleDOList   中兴参与人列表
     * @param enums             身份枚举列表
     * @return boolean
     * <AUTHOR>
     * date: 2023/9/7 18:32
     */
    private boolean isTargetPeopleType(List<ActivityRelationZtePeopleDO> ztePeopleDOList ,ActivityPeopleTypeEnum... enums ) {
        if (CollectionUtils.isEmpty(ztePeopleDOList)) {
            return false;
        }
        return ztePeopleDOList.stream()
                .anyMatch(item -> ActivityPeopleTypeEnum.in(item.getPeopleType(), enums)
                        && StringUtils.equals(this.operatorNo, item.getPeopleCode()));
    }

    /**
     * 获取拓展活动谈参操作权限
     * @param source 数据源
     * @param activityInfoVO 活动信息
     * @return 返回结果
     * <AUTHOR>
     * @date: 2024/2/18 17:09
     */
    public TalkOperatorVO talkOperateable(StandardActivityDetailDataSource source, ActivityInfoVO activityInfoVO){
        TalkOperatorVO talkOperatorVO = new TalkOperatorVO();
        if(!JOIN_EXHIBITION.isMe(activityInfoVO.getActivityType())){
            talkOperatorVO.setTalkOperate(Boolean.TRUE);
            talkOperatorVO.setTalkManager(Boolean.FALSE);
            return talkOperatorVO;
        }
        Set<String> opeartorSet = Sets.newHashSet();
        Set<String> talkManagerSet = Sets.newHashSet();
        ActivityInfoDO activityInfoDO = source.fetchActivityInfo(activityInfoVO.getRowId());
        if (activityInfoDO == null) {
            talkOperatorVO.setTalkOperate(Boolean.FALSE);
            talkOperatorVO.setTalkManager(Boolean.FALSE);
            return talkOperatorVO;
        }
        List<ActivityScheduleItemPeopleDO> schedulePeoples =  source.fetchSchedulePeopleByActivityRowId(activityInfoVO.getRowId());
        List<ExhibitionDirectorDO> exhibitionDirectors = source.fetchExhibitionDirection(activityInfoDO.getOriginRowId());
        //获取申请人
        opeartorSet.add(activityInfoDO.getApplyPeopleNo()) ;
        //获取日程我司参与人
        for(ActivityScheduleItemPeopleDO scheduleItemPeople : schedulePeoples){
            if(ScheduleItemPeopleTypeEnum.in(scheduleItemPeople.getPeopleType(), ZTE_PEOPLE,ZTE_INTERFACE_PEOPLE)){
                opeartorSet.add(scheduleItemPeople.getPeopleNo());
            }
        }
        //获取谈参管理员
        for(ExhibitionDirectorDO exhibitionDirector : exhibitionDirectors){
            if(ExhibitionDirectorRoleTypeEnum.in(exhibitionDirector.getRoleType(), REFERENCE_ADMIN)){
                opeartorSet.add(exhibitionDirector.getEmployeeNo());
                talkManagerSet.add(exhibitionDirector.getEmployeeNo());
            }
        }
        talkOperatorVO.setTalkOperate(opeartorSet.contains(this.operatorNo));
        talkOperatorVO.setTalkManager(talkManagerSet.contains(this.operatorNo));
        return talkOperatorVO;
    }

}
