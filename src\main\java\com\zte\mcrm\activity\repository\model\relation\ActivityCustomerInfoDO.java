package com.zte.mcrm.activity.repository.model.relation;

import java.util.Date;

/**
 * table:activity_customer_info -- 
 */
public class ActivityCustomerInfoDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 是否主嘉宾。一次活动一个主嘉宾，也就是对应活动信息表中的关联客户。Y-主，N-非主。枚举BooleanEnum */
    private String mainCust;

    /** 客户受限制主体编码 */
    private String sanctionedPatryCode;

    /** 客户编码(entity) */
    private String customerCode;

    /** 客户名称(entity) */
    private String customerName;

    /** mkt编码（account） */
    private String mktCode;

    /** mkt名称（account） */
    private String mktName;

    /** 客户集团编码（group） */
    private String mtoCode;

    /** 客户集团名称（group） */
    private String mtoName;

    /** 国家/地区编码 */
    private String localCode;

    /** 国家/地区名称 */
    private String localName;

    /** 国家/地区的省州编码 */
    private String provinceCode;

    /** 国家/地区的省州名称 */
    private String provinceName;

    /**  城市编码 */
    private String cityCode;

    /**  城市名称 */
    private String cityName;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** Y-国际，N-国内。对应客户s_org_ext_x的attrib_39 */
    private String international;

    /** 客户类型。对应客户表字段accnt_type_cd */
    private String custType;

    /** 客户级别 */
    private String custLevel;

    /** 运营商类型。对应客户s_org_ext_x的operator_type */
    private String operatorType;

    /** 客户所属主管部门 */
    private String belongBuId;

    /** 地市信息 */
    private String accountLocalInfo;

    /** 客户简称。字段长度500和客户信息系统保持一致 */
    private String aliasName;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getMainCust() {
        return mainCust;
    }

    public void setMainCust(String mainCust) {
        this.mainCust = mainCust == null ? null : mainCust.trim();
    }

    public String getSanctionedPatryCode() {
        return sanctionedPatryCode;
    }

    public void setSanctionedPatryCode(String sanctionedPatryCode) {
        this.sanctionedPatryCode = sanctionedPatryCode == null ? null : sanctionedPatryCode.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getMktCode() {
        return mktCode;
    }

    public void setMktCode(String mktCode) {
        this.mktCode = mktCode == null ? null : mktCode.trim();
    }

    public String getMktName() {
        return mktName;
    }

    public void setMktName(String mktName) {
        this.mktName = mktName == null ? null : mktName.trim();
    }

    public String getMtoCode() {
        return mtoCode;
    }

    public void setMtoCode(String mtoCode) {
        this.mtoCode = mtoCode == null ? null : mtoCode.trim();
    }

    public String getMtoName() {
        return mtoName;
    }

    public void setMtoName(String mtoName) {
        this.mtoName = mtoName == null ? null : mtoName.trim();
    }

    public String getLocalCode() {
        return localCode;
    }

    public void setLocalCode(String localCode) {
        this.localCode = localCode == null ? null : localCode.trim();
    }

    public String getLocalName() {
        return localName;
    }

    public void setLocalName(String localName) {
        this.localName = localName == null ? null : localName.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getInternational() {
        return international;
    }

    public void setInternational(String international) {
        this.international = international == null ? null : international.trim();
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType == null ? null : custType.trim();
    }

    public String getCustLevel() {
        return custLevel;
    }

    public void setCustLevel(String custLevel) {
        this.custLevel = custLevel == null ? null : custLevel.trim();
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType == null ? null : operatorType.trim();
    }

    public String getBelongBuId() {
        return belongBuId;
    }

    public void setBelongBuId(String belongBuId) {
        this.belongBuId = belongBuId == null ? null : belongBuId.trim();
    }

    public String getAccountLocalInfo() {
        return accountLocalInfo;
    }

    public void setAccountLocalInfo(String accountLocalInfo) {
        this.accountLocalInfo = accountLocalInfo == null ? null : accountLocalInfo.trim();
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName == null ? null : aliasName.trim();
    }
}