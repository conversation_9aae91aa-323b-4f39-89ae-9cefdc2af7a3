package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginVersionDO;

public interface ActivityScheduleItemOriginVersionMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleItemOriginVersionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleItemOriginVersionDO record);

    /**
     * query by primary key
     */
    ActivityScheduleItemOriginVersionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleItemOriginVersionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleItemOriginVersionDO record);
}