package com.zte.mcrm.activity.service.activity.convert;

import com.alibaba.fastjson.JSONObject;
import com.zte.mcrm.activity.integration.igpt.config.IGPTConfig;
import com.zte.mcrm.activity.integration.igpt.dto.IGPTPushInfoDTO;
import com.zte.mcrm.activity.integration.igpt.dto.RNConfigurationDTO;
import com.zte.mcrm.activity.service.summary.dto.SummaryAbstractInfoDTO;
import com.zte.mcrm.activity.service.summary.dto.SummaryPushContentDTO;
import com.zte.mcrm.activity.service.summary.dto.SummaryPushContentDataDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zte.mcrm.activity.common.constant.ActiviyIgptConstant.*;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.COMMA_ZH;

/**
 * 展会摘要推送信息转换器
 *
 * <AUTHOR>
 * @date 2025/3/5 17:16
 */
public class ExhibitionSummaryPushInfoConvert {


    /**
     * 将摘要推送内容转换为IGPT推送信息
     */
    public static IGPTPushInfoDTO toIGPTPushInfo(IGPTConfig igptConfig, SummaryPushContentDTO info) {
        IGPTPushInfoDTO result = new IGPTPushInfoDTO();
        // 设置基本信息
        result.setSceneCode(igptConfig.getSceneCode());
        result.setTitle(String.format(MESSAGE_TITLE_TEMPLATE, info.getExhibitionName()));
        result.setUserId(info.getTargetNo());
        result.setType(IGPTPushInfoDTO.TYPE_CONTENT_SUMMARY);
        result.setSceneType(1);
        result.setStatus(0);

        // 设置内容列表
        List<IGPTPushInfoDTO.Content> contents = new ArrayList<>();
        contents.add(buildSummaryContent(info));

        // 添加每日会议内容
        info.getSummaryDateMap().forEach((date, dateData) ->
                contents.addAll(buildDailyMeetingContent(igptConfig, date, dateData))
        );

        contents.add(buildFootFlag(info));
        result.setContents(contents);
        return result;
    }

    private static IGPTPushInfoDTO.Content buildFootFlag(SummaryPushContentDTO info) {
        // 创建末尾
        IGPTPushInfoDTO.Content content = new IGPTPushInfoDTO.Content();
        content.setType("placeholder");
        content.setContent("");
        content.setStatus(0);
        return content;
    }

    /**
     * 构建每日会议内容
     */
    private static List<IGPTPushInfoDTO.Content> buildDailyMeetingContent(
            IGPTConfig igptConfig, String date, SummaryPushContentDataDTO datePushData) {

        // 初始化内容列表
        List<IGPTPushInfoDTO.Content> dailyContents = new ArrayList<>();

        // 添加日期标题
        String dailyTitle = CollectionUtils.isEmpty(datePushData.getAbstractInfoList())
                ? String.format(DAILY_MEETING_TITLE_NO_RECORD_TEMPLATE, date, datePushData.getScheduleCount())
                : String.format(DAILY_MEETING_TITLE_HAS_RECORD_TEMPLATE, date, datePushData.getScheduleCount());
        dailyContents.add(createMarkdownContent(dailyTitle, 1));

        // 如果有会议记录，添加会议内容
        if (CollectionUtils.isEmpty(datePushData.getAbstractInfoList())) {
            return dailyContents;
        }
        // 使用IntStream处理每个会议摘要
        dailyContents.addAll(
                IntStream.range(0, datePushData.getAbstractInfoList().size())
                        .mapToObj(i -> {
                            SummaryAbstractInfoDTO meeting = datePushData.getAbstractInfoList().get(i);
                            List<IGPTPushInfoDTO.Content> meetingContents = new ArrayList<>();

                            // 添加会议标题
                            // 在调用 createH1Content 的地方修改逻辑
                            String customerPeople = meeting.getCustomerPeople();
                            String suffix = customerPeople.contains(COMMA_ZH) ? MEETING_TITLE_MORE_TEMPLATE : ""; // 如果包含 "，"，则加上 "等"
                            meetingContents.add(createH1Content(
                                    String.format(MEETING_TITLE_TEMPLATE, i + 1, meeting.getCustomerName(), customerPeople, suffix),
                                    2
                            ));

                            // 添加详情链接
                            meetingContents.add(createDetailLink(igptConfig, meeting));

                            // 添加会议详情内容
                            meetingContents.add(createMeetingDetailContent(meeting));

                            return meetingContents;
                        })
                        .flatMap(List::stream)
                        .collect(Collectors.toList())
        );

        return dailyContents;
    }

    /**
     * 创建详情链接内容
     */
    public static IGPTPushInfoDTO.Content createDetailLink(IGPTConfig igptConfig, SummaryAbstractInfoDTO meeting) {
        // 构建RN配置
        RNConfigurationDTO rnConfig = buildRNConfigurationDTO(igptConfig, meeting);

        // 创建详情链接内容
        IGPTPushInfoDTO.Content content = new IGPTPushInfoDTO.Content();
        content.setType("h1");
        content.setContent(CONTENT_DETAIL_LINK);
        content.setRedirect(JSONObject.toJSONString(rnConfig));
        content.setStatus(2);

        return content;
    }

    /**
     * 构建RN配置
     */
    private static RNConfigurationDTO buildRNConfigurationDTO(IGPTConfig igptConfig, SummaryAbstractInfoDTO meeting) {
        // 创建配置对象
        RNConfigurationDTO rnConfig = new RNConfigurationDTO();
        RNConfigurationDTO.RN rnParam = new RNConfigurationDTO.RN();

        // 设置启动参数
        RNConfigurationDTO.LaunchParams launchParams = new RNConfigurationDTO.LaunchParams();
        BeanUtils.copyProperties(igptConfig, launchParams);

        // 设置数据体
        RNConfigurationDTO.DataBody dataBody = new RNConfigurationDTO.DataBody();
        dataBody.setId(meeting.getActivityId());
        dataBody.setActTitle(meeting.getActivityTitle());

        // 设置参数
        RNConfigurationDTO.Params params = new RNConfigurationDTO.Params();
        params.setType("activitySummary");
        params.setDataBody(dataBody);

        // 设置参数值
        RNConfigurationDTO.ParamValue paramValue = new RNConfigurationDTO.ParamValue();
        paramValue.setParams(params);

        // 组装RN参数
        rnParam.setMultiModule(true);
        rnParam.setParamKey("appData");
        rnParam.setParamValue(JSONObject.toJSONString(paramValue));
        rnParam.setLaunchParams(launchParams);
        rnConfig.setRn(rnParam);

        return rnConfig;
    }

    /**
     * 创建会议详情内容
     */
    static IGPTPushInfoDTO.Content createMeetingDetailContent(SummaryAbstractInfoDTO meeting) {
        // 构建详情文本
        StringBuilder detailText = new StringBuilder();

        // 添加会议概要
        if (StringUtils.isNotBlank(meeting.getSummaryAbstract())) {
            detailText.append(String.format(CONTENT_MEETING_SUMMARY_FORMAT, meeting.getSummaryAbstract()));
        }

        // 添加遗留问题
        if (StringUtils.isNotBlank(meeting.getRemainingProblems())) {
            detailText.append(String.format(CONTENT_REMAINING_PROBLEMS_FORMAT, meeting.getRemainingProblems()));
        }

        // 创建元素列表
        List<IGPTPushInfoDTO.Element> elements = new ArrayList<>();
        IGPTPushInfoDTO.Element element = new IGPTPushInfoDTO.Element();
        element.setType("markdown");
        element.setContent(detailText.toString());
        elements.add(element);

        // 创建内容对象
        IGPTPushInfoDTO.Content content = new IGPTPushInfoDTO.Content();
        content.setType("h1");
        content.setContent("）");
        content.setStatus(1);
        content.setElements(elements);

        return content;
    }

    /**
     * 创建Markdown内容
     */
    public static IGPTPushInfoDTO.Content createMarkdownContent(String text, int status) {
        IGPTPushInfoDTO.Content content = new IGPTPushInfoDTO.Content();
        content.setType("markdown");
        content.setContent(text);
        content.setStatus(status);
        return content;
    }

    /**
     * 创建H1标题内容
     */
    private static IGPTPushInfoDTO.Content createH1Content(String text, int status) {
        IGPTPushInfoDTO.Content content = new IGPTPushInfoDTO.Content();
        content.setType("h1");
        content.setContent(text);
        content.setStatus(status);
        return content;
    }

    /**
     * 构建摘要内容
     */
    static IGPTPushInfoDTO.Content buildSummaryContent(SummaryPushContentDTO info) {
        String summaryText = String.format(SUMMARY_CONTENT_TEMPLATE,
                info.getExhibitionName(),
                info.getScheduleCount(),
                info.getCustomerCount(),
                info.getCustomerContactCount()
        );

        return createMarkdownContent(summaryText, 1);
    }
}