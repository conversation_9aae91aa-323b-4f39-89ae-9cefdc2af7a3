package com.zte.mcrm.activity.common.annotation.impl;


import com.zte.mcrm.activity.common.annotation.MemberOf;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 校验字段的值需要在定义的值里面的实现类
 *
 * @author: 汤踊10285568
 * @date: 2021/9/23 9:59
 */
public class MemberOfValidator implements ConstraintValidator<MemberOf, Object> {

    /**
     * 自定义的字符串
     */
    private String[] strValues;
    /**
     * 自定义的整型值
     */
    private int[] intValues;


    /**
     * Initializes the validator in preparation for
     * {@link #isValid(Object, ConstraintValidatorContext)} calls.
     * The constraint annotation for a given constraint declaration
     * is passed.
     * <p>
     * This method is guaranteed to be called before any use of this instance for
     * validation.
     * <p>
     * The default implementation is a no-op.
     *
     * @param constraintAnnotation annotation instance for a given constraint declaration
     * @author: 汤踊10285568
     * @date: 2021/9/23 9:59
     */
    @Override
    public void initialize(MemberOf constraintAnnotation) {
        strValues = constraintAnnotation.strValues();
        intValues = constraintAnnotation.intValues();
    }

    /**
     * Implements the validation logic.
     * The state of {@code value} must not be altered.
     * <p>
     * This method can be accessed concurrently, thread-safety must be ensured
     * by the implementation.
     *
     * @param value   object to validate
     * @param context context in which the constraint is evaluated
     * @return {@code false} if {@code value} does not pass the constraint
     * @author: 汤踊10285568
     * @date: 2021/9/23 9:59
     */
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if(value == null){
            return true;
        }
        if (value instanceof String) {
            for (String s : strValues) {
                if (s.equals(value)) {
                    return true;
                }
            }
        }
        if (value instanceof Integer) {
            for (int s : intValues) {
                if (s == (Integer) value) {
                    return true;
                }
            }
        }
        return false;
    }
}
