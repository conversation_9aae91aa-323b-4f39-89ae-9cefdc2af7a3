package com.zte.mcrm.activity.repository.rep.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.activity.service.resource.vo.ActivityScoreCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动评估信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityEvaluationInfoRepository {


    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityEvaluationInfoDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityEvaluationInfoDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityEvaluationInfoDO> queryAllByActivityRowId(String activityRowId);

    /**
     * 查询活动参与人评价分数及总次数
     *
     * @param codeList              参与人编号
     * @param activityStatusList    活动状态
     * @return {@link List<  ActivityScoreCountVO >}
     * <AUTHOR>
     * @date 2023/5/22 下午3:14
     */
    List<ActivityScoreCountVO> selectParticipantsScoreCount(List<String> codeList,
                                                            List<String> activityStatusList);
}