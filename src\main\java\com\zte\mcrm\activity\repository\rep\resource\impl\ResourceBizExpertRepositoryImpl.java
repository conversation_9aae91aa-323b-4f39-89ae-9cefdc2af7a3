package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.resource.ResourceBizExpertExtMapper;
import com.zte.mcrm.activity.service.resource.model.ExpertQuery;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDO;
import com.zte.mcrm.activity.repository.rep.resource.ResourceBizExpertRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ExpertSearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ExpertPageVO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.expansion.common.constant.CustExpansionConstant;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class ResourceBizExpertRepositoryImpl implements ResourceBizExpertRepository {
    @Resource
    private ResourceBizExpertExtMapper resourceBizExpertExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<ResourceBizExpertDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ResourceBizExpertDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            record.setEnabledFlag(CharacterConstant.Y);
            record.setCreationDate(new Date());
            record.setLastUpdateDate(new Date());
            record.setCreatedBy(HeadersProperties.getXEmpNo());
            record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            resourceBizExpertExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ResourceBizExpertDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return resourceBizExpertExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteSelective(List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        Map<String, Object> map = new HashMap<>();
        map.put(CharacterConstant.ROW_IDS, rowIds);
        map.put(CharacterConstant.LAST_UPDATED_BY, HeadersProperties.getXEmpNo());
        map.put(CharacterConstant.LAST_UPDATE_DATE, new Date());
        map.put(CharacterConstant.ENABLED_FLAG, CharacterConstant.N);
        return resourceBizExpertExtMapper.batchUpdateByPrimaryKeySelective(map);
    }

    @Override
    public int updateEnableStatusByPrimaryKeySelective(List<String> rowIds, String enableStatus) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        Map<String, Object> map = new HashMap<>();
        map.put(CharacterConstant.ROW_IDS, rowIds);
        map.put(CharacterConstant.LAST_UPDATED_BY, HeadersProperties.getXEmpNo());
        map.put(CharacterConstant.LAST_UPDATE_DATE, new Date());
        map.put(CharacterConstant.ENABLED_STATUS, enableStatus);
        return resourceBizExpertExtMapper.batchUpdateByPrimaryKeySelective(map);
    }

    @Override
    public List<ResourceBizExpertDO> selectByPrimaryKeys(List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return new ArrayList<>();
        }
        List<ResourceBizExpertDO> res = new ArrayList<>();
        for (List<String> ids : Lists.partition(rowIds, NumberConstant.DEFAULT_QUERY_DATABASE_SIZE)) {
            res.addAll(resourceBizExpertExtMapper.selectByPrimaryKeys(ids));
        }
        return res;
    }

    /**
     * 根据交流方向统计专家数
     * @return key:direction,num
     */
    @Override
    public List<HashMap<String, Object>> summaryExpertWithDirection(){
        return resourceBizExpertExtMapper.summaryExpertWithDirection();
    }

    @Override
    public List<ResourceBizExpertDO> getList(ResourceBizExpertDO record) {
        String jsonString = JSON.toJSONString(record);

        Map map = JSON.parseObject(jsonString, HashMap.class);
        return resourceBizExpertExtMapper.getList(map);
    }

    @Override
    public List<ResourceBizExpertDO> getPage(ResourceBizExpertDO record, int pageNo, int pageSize) {
        String jsonString = JSON.toJSONString(record);
        Map map = JSON.parseObject(jsonString, HashMap.class);
        map.put(CharacterConstant.START_ROW, pageSize * (pageNo - 1));
        map.put(CharacterConstant.ROW_SIZE, pageSize);

        return resourceBizExpertExtMapper.getList(map);
    }
    /**
     * 根据专家ID批量查询
     *
     * @param employeeNos 专家员工编号
     * @return {@link List< ResourceBizExpertDO>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:04
     */
    @Override
    public List<ResourceBizExpertDO> selectByEmployeeNos(List<String> employeeNos) {
        if (CollectionUtils.isEmpty(employeeNos)) {
            return Collections.emptyList();
        }
        return resourceBizExpertExtMapper.selectByEmployeeNos(employeeNos);
    }

    /**
     * 分页查询
     *
     * @param param
     * @return {@link List<  ExpertPageVO >}
     * <AUTHOR>
     * @date 2023/5/19 下午5:42
     */
    @Override
    public PageRows<ResourceBizExpertDO> selectParticipantsPage(PageQuery<ExpertSearchParam> param) {
        if (!param.validatePage()) {
            return PageRowsUtil.buildEmptyPage(param.getPageNo(), param.getPageSize());
        }
        // 查询我关注的专家时，操作人不能为空
        if (StringUtils.equals(CustExpansionConstant.QUERY_TYPE_MYCARE, param.getParam().getSearchType()) && StringUtils.isBlank(param.getParam().getOperator())) {
            return PageRowsUtil.buildEmptyPage(param.getPageNo(), param.getPageSize());
        }
        PageInfo<ResourceBizExpertDO> pageInfo = PageHelper.startPage(param.getPageNo(), param.getPageSize(), param.withCount())
                .doSelectPageInfo(() -> resourceBizExpertExtMapper.selectParticipantsPage(param.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }

    @Override
    public long selectParticipantsCount(ExpertSearchParam param) {
        // 查询我关注的专家时，操作人不能为空
        if (StringUtils.equals(CustExpansionConstant.QUERY_TYPE_MYCARE, param.getSearchType()) && StringUtils.isBlank(param.getOperator())) {
            return 0;
        }
        return PageHelper.count(() -> resourceBizExpertExtMapper.selectParticipantsPage(param));
    }

    @Override
    public List<ResourceBizExpertDO> getExpertPageByConditions(ExpertQuery param) {
        return resourceBizExpertExtMapper.getExpertPageByConditions(param);
    }

    @Override
    public PageRows<ResourceBizExpertDO> getExpertPageByConditions(PageQuery<ExpertQuery> param) {
        if (!param.validatePage()) {
            return PageRowsUtil.buildEmptyPage(param.getPageNo(), param.getPageSize());
        }

        PageInfo<ResourceBizExpertDO> pageInfo = PageHelper.startPage(param.getPageNo(), param.getPageSize(), param.withCount())
                .doSelectPageInfo(() -> resourceBizExpertExtMapper.getExpertPageByConditions(param.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }
    
    @Override
    public List<String> getNonLeaderExpertCodes(){
        return resourceBizExpertExtMapper.getNonLeaderExpertCodes();
    }
}