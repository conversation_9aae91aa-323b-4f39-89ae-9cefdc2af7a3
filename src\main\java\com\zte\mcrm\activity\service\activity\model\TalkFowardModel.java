package com.zte.mcrm.activity.service.activity.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.ForwardConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationBizTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.DateFormatUtil;
import com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam;
import com.zte.mcrm.activity.integration.usercenter.dto.UserInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.service.activity.param.ActivityResourceOperationLogParam;
import com.zte.mcrm.activity.service.authority.param.OperationAuthGrantModel;
import com.zte.mcrm.activity.service.authority.param.OperationAuthParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityTalkMessageBodyParam;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.common.util.StringExtUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.*;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.QUERY_FROM;
import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.common.constant.PatternConstant.FORWARD_EMP_NO;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.COMMA_EN;
import static com.zte.mcrm.temp.common.constant.CommonConstant.COMMA;

/**
 * <AUTHOR> 10333830
 * @date 2023-09-07 10:19
 */
@Slf4j
@Data
public class TalkFowardModel {

    public static final Pattern FORWARD_PATTERN = Pattern.compile(FORWARD_EMP_NO);

    /**
     * 被转发人列表
     */
    private List<String> toList;
    /**
     * 转发人
     */
    private String from;
    /**
     * 涉及活动附件列表
     */
    private List<ActivityRelationAttachmentDO> attachmentDOList;
    /**
     * 涉及日程人员列表
     */
    private List<ActivityScheduleItemPeopleDO> schedulePeopleInfoList;
    /**
     * 涉及活动
     */
    private ActivityInfoDO activity;
    /**
     * 转发参数
     */
    private ActivityTalkMessageBodyParam talkMessageBodyParam;
    /**
     * 转发的日程
     */
    private ActivityScheduleItemDO scheduleItem;

    /* Started by AICoder, pid:vcd4dqa1485004c1419d0adeb019742eca163724 */
    /**
     * 提取转发人和被转发人的工号
     * <AUTHOR>
     * date: 2024/11/21 16:16
     */
    public void extractPeopleNo() {
        if (talkMessageBodyParam == null || talkMessageBodyParam.getContactsData() == null) {
            return;
        }

        JSONObject contactsJson = JSON.parseObject(JSON.toJSONString(talkMessageBodyParam.getContactsData()));

        from = getEmpNo(contactsJson.getString(ForwardConstant.FROM));
        Object toOj = contactsJson.get(ForwardConstant.TO_LIST);

        if (toOj != null) {
            if (toOj instanceof String) {
                toList = Lists.newArrayList(getEmpNo((String) toOj));
            } else if (toOj instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) toOj;
                toList = jsonArray.stream()
                        .map(Object::toString)
                        .map(this::getEmpNo)
                        .collect(Collectors.toList());
            }
        }
    }
    /* Ended by AICoder, pid:vcd4dqa1485004c1419d0adeb019742eca163724 */

    /**
     * 构建授权模型
     * @return java.util.List<com.zte.mcrm.activity.service.authority.param.OperationAuthGrantModel>
     * <AUTHOR>
     * date: 2024/11/21 16:17
     */
    public List<OperationAuthGrantModel> buildAuthGrantModel() {
        List<OperationAuthGrantModel> authGrantModelList = Lists.newArrayList();
        if (talkMessageBodyParam == null) {
            return authGrantModelList;
        }
        CollectionExtUtils.getListOrDefaultEmpty(this.toList).forEach(e -> {
            OperationAuthGrantModel authGrantModel = new OperationAuthGrantModel();
            authGrantModel.setActivityId(talkMessageBodyParam.getId());
            authGrantModel.setBizRelatedId(talkMessageBodyParam.getActivityScheduleItemRowId());
            authGrantModel.setTargetPeopleEmpNo(e);
            authGrantModel.setBizType(ActivityResourceOperationBizTypeEnum.SCHEDULE_TALK);
            authGrantModel.setOperations(Lists.newArrayList(ActivityResourceOperationTypeEnum.VIEW));
            authGrantModelList.add(authGrantModel);
        });
        return authGrantModelList;
    }

    /* Started by AICoder, pid:b2619833e0u72d014bff09f130b587124da5776d */
    /**
     * 提取工号
     * @param sipUri    sip格式的字符串
     * @return java.lang.String
     * <AUTHOR>
     * date: 2024/11/20 15:17
     */
    private String getEmpNo(String sipUri) {
        // 定义一个正则表达式模式，用于匹配 sip: 后面的数字部分
        Matcher matcher = FORWARD_PATTERN.matcher(StringUtils.defaultIfBlank(sipUri, StringUtils.EMPTY));

        if (matcher.find()) {
            // 获取第一个匹配的组（即数字部分）
            return matcher.group(1);
        } else {
            return StringUtils.EMPTY;
        }
    }
    /* Ended by AICoder, pid:b2619833e0u72d014bff09f130b587124da5776d */

    /**
     * 获取全部需要查询的人员工号
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * date: 2024/11/21 16:17
     */
    public List<String> getAllEmpNo() {
        List<String> empNoList = Lists.newArrayList();
        empNoList.addAll(CollectionExtUtils.getListOrDefaultEmpty(toList));
        empNoList.add(from);
        empNoList.addAll(CollectionExtUtils.getListOrDefaultEmpty(schedulePeopleInfoList)
                .stream().map(ActivityScheduleItemPeopleDO::getPeopleNo).collect(Collectors.toList()));
        return empNoList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 构建邮件发送参数
     * @param userInfoList  用户信息列表
     * @return com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam
     * <AUTHOR>
     * date: 2024/11/21 16:17
     */
    public ZmailBodyParam buildMailBO(List<UserInfoDTO> userInfoList) {
        attachmentDOList = CollectionExtUtils.getListOrDefaultEmpty(attachmentDOList).stream()
                .filter(e -> AttachmentSceneTypeEnum.SCHEDULE_TALK.isMe(e.getAttachmentSceneType()))
                .collect(Collectors.toList());
        List<ActivityScheduleItemPeopleDO> peopleDOList = CollectionExtUtils.getListOrDefaultEmpty(schedulePeopleInfoList).stream()
                .filter(e -> ScheduleItemPeopleTypeEnum.REFERENCE_CONTROLLER.isMe(e.getPeopleType()))
                .collect(Collectors.toList());
        // 校验基础信息是否缺失
        checkBaseInfo(userInfoList, peopleDOList);
        String talkNames = attachmentDOList.stream().map(ActivityRelationAttachmentDO::getFileName)
                .filter(StringUtils:: isNotBlank).distinct().collect(Collectors.joining(COMMA));
        Map<String, String> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(UserInfoDTO::getEmployeeShortId, UserInfoDTO::getName, (v1, v2) -> v1));
        String scheduleName = scheduleItem.getScheduleItemName();
        String fromName = StringExtUtils.getTargetOrEmpty(userInfoMap.get(from), userInfoMap.get(from) + from);
        String toLists = toList.stream()
                .filter(e -> StringUtils.isNotBlank(userInfoMap.get(e)))
                .map(e -> userInfoMap.get(e) + e).collect(Collectors.joining(COMMA));
        // 任意信息不可缺失
        if (StringUtils.isAnyBlank(talkNames, fromName, toLists)) {
            log.error("谈参信息缺失，转发失败，talkNames：{}，fromName：{}，toLists：{}", talkNames, fromName, toLists);
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_TALK_INFO_MISS);
        }
        // 发送邮件给谈参负责人
        String mailToList = peopleDOList.stream().map(ActivityScheduleItemPeopleDO::getPeopleNo)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA_EN));
        String forwardTime = DateFormatUtil.format(new Date(), DateFormatUtil.DATE_YYYY_MM_DD_HH_MM_SS_PATTERN);

        ZmailBodyParam zmailBodyParam = new ZmailBodyParam();
        zmailBodyParam.setTemplateName(MAIL_FORWARD_TALK_NOTICE);
        zmailBodyParam.setReceiveEmpNo(mailToList);
        zmailBodyParam.setSendEmpNo(BizRequestUtil.createWithCurrentUser().getEmpNo());
        Map<String, Object> templateParamMap = new HashMap<>(8);
        templateParamMap.put(ACTIVITY_NO, activity.getActivityRequestNo());
        templateParamMap.put(ACTIVITY_TITLE, activity.getActivityTitle());
        templateParamMap.put(SCHEDULE_ITEM_NAME, scheduleName);
        templateParamMap.put(TALK_NAMES, talkNames);
        templateParamMap.put(TO_LISTS, toLists);
        templateParamMap.put(QUERY_FROM, fromName);
        templateParamMap.put(FORWARD_TIME, forwardTime);

        zmailBodyParam.setTemplateParamMap(templateParamMap);
        return zmailBodyParam;
    }

    /**
     * 校验基础信息
     * @param userInfoList  用户信息
     * @param peopleDOList      日程参与人信息
     * @return void
     * <AUTHOR>
     * date: 2024/11/21 14:26
     */
    private void checkBaseInfo(List<UserInfoDTO> userInfoList, List<ActivityScheduleItemPeopleDO> peopleDOList) {
        if (null == this.activity) {
           throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_INVALID_ACTIVITY);
        }
        if (null == scheduleItem) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_INVALID_SCHEDULE);
        }
        if (CollectionUtils.isEmpty(attachmentDOList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_NO_TALK);
        }
        if (CollectionUtils.isEmpty(peopleDOList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_NO_REFERENCE_CONTROLLER);
        }
        if (CollectionUtils.isEmpty(userInfoList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_INVALID_PEOPLE);
        }
    }

    /**
     * 构建查询权限参数
     * @return com.zte.mcrm.activity.service.authority.param.OperationAuthParam
     * <AUTHOR>
     * date: 2024/11/21 16:18
     */
    public OperationAuthParam buildAuthParam() {
        OperationAuthParam authParam = new OperationAuthParam();
        authParam.setBizType(ActivityResourceOperationBizTypeEnum.SCHEDULE_TALK.getCode());
        authParam.setActivityId(talkMessageBodyParam.getId());
        authParam.setBizRelatedIdList(Lists.newArrayList(talkMessageBodyParam.getActivityScheduleItemRowId()));
        return authParam;
    }

    /**
     * 构建操作日志
     * @param userInfoList  用户列表
     * @return java.util.List<com.zte.mcrm.activity.service.activity.param.ActivityResourceOperationLogParam>
     * <AUTHOR>
     * date: 2024/11/22 10:29
     */
    public List<ActivityResourceOperationLogParam> buildOperationLog(List<UserInfoDTO> userInfoList) {
        List<ActivityResourceOperationLogParam> logList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userInfoList)) {
            return logList;
        }
        Map<String, String> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(UserInfoDTO::getEmployeeShortId, UserInfoDTO::getName, (v1, v2) -> v1));

        ActivityRelationAttachmentDO attachmentDO = Optional.ofNullable(attachmentDOList).orElse(Collections.emptyList()).stream()
                .filter(e -> AttachmentSceneTypeEnum.SCHEDULE_TALK.isMe(e.getAttachmentSceneType()))
                .findAny().orElse(new ActivityRelationAttachmentDO());

        CollectionExtUtils.getListOrDefaultEmpty(toList).forEach(e -> {
            ActivityResourceOperationLogParam log = new ActivityResourceOperationLogParam();
            log.setActivityId(talkMessageBodyParam.getId());
            log.setOperationType(ActivityResourceOperationTypeEnum.FORWARD.getCode());
            log.setBizRelatedId(talkMessageBodyParam.getActivityScheduleItemRowId());
            log.setBizType(ActivityResourceOperationBizTypeEnum.SCHEDULE_TALK.getCode());
            log.setOperationBefore(attachmentDO.getRowId());
            log.setOperationBeforeDesc(attachmentDO.getFileName());
            log.setOperationAfter(e);
            log.setOperationAfterDesc(StringExtUtils.getTargetOrEmpty(userInfoMap.get(e), userInfoMap.get(e) + e));
            logList.add(log);
        });
        return logList.stream().filter(e -> StringUtils.isNoneBlank(e.getOperationAfterDesc(), e.getOperationBeforeDesc()))
                .collect(Collectors.toList());
    }

}
