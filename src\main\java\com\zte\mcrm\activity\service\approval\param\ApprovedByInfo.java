package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.activity.common.enums.activity.ApprovalTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 评审人信息
 * <AUTHOR>
 */
@Getter
@Setter
public class ApprovedByInfo {

    /**
     * 审批类型
     * {@link ApprovalTypeEnum}
     */
    private String approvalType;

    /**
     * 工号
     */
    private String empNo;

    /**
     * 层级
     */
    private int level;

    /**
     * 节点类型
     */
    private String approvalNodeType;

    /**
     * 节点名称
     */
    private String approvalNodeName;
}
