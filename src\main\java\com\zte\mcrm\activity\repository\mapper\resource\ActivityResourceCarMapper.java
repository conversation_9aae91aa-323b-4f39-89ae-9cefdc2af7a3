package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityResourceCarMapper {
    /**
     * all field insert
     */
    int insert(ActivityResourceCarDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityResourceCarDO record);

    /**
     * query by primary key
     */
    ActivityResourceCarDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityResourceCarDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceCarDO record);

    /**
     * 新工号切换
     */
    List<ActivityResourceCarDO> queryEmpNoTransList(DataTransParam searchParam);
}