package com.zte.mcrm.activity.application.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zte.mcrm.activity.common.constant.CTOReportHeaderConstants;
import lombok.*;


/* Started by AICoder, pid:sb49arc871x5ac814fd908d74022042212c28d91 */
/**
 * CTO报表AP指标
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CtoReportApIndicatorVO {

    /**
     * 全年AP目标数
     */
    @ExcelProperty(CTOReportHeaderConstants.YEAR_AP_TARGET)
    private String yearAPTarget;

    /**
     * 全年已完成的AP数
     */
    @ExcelProperty(CTOReportHeaderConstants.YEAR_AP_FINISH)
    private String yearAPFinish;

    /**
     * 全年已完成AP率
     */
    @ExcelProperty(CTOReportHeaderConstants.YEAR_AP_FINISH_RATE)
    private String yearAPFinishRate;
}

/* Ended by AICoder, pid:sb49arc871x5ac814fd908d74022042212c28d91 */


