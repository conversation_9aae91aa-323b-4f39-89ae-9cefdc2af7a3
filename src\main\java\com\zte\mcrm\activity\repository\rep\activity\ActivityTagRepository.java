package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityTagDO;

import java.util.List;

/**
 * 活动标签
 *
 * <AUTHOR>
 */
public interface ActivityTagRepository {

    /**
     * 添加活动标签（如果没有主键，自动生成）
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<ActivityTagDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityTagDO record);

    /**
     * 查询所有活动的标签
     *
     * @param activityRowId 拓展活动rowId
     * @return
     */
    List<ActivityTagDO> queryTagForActivity(String activityRowId);

    int deleteByActivityIds(String operator, List<String> activityIds);
}
