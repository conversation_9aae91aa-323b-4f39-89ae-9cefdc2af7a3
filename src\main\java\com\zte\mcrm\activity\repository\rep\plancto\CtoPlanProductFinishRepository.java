package com.zte.mcrm.activity.repository.rep.plancto;
/* Started by AICoder, pid:b4751j36bfv27f014f180816f0cf00082df149a6 */

import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import com.zte.mcrm.activity.web.PageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CTO握手产品维度达成
 *
 * <AUTHOR>
 */
public interface CtoPlanProductFinishRepository {
    /**
     * 插入数据
     *
     * @param list
     */
    int batchInsert(@Param("list") List<CtoPlanProductFinishDO> list);

    /**
     * 动态更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CtoPlanProductFinishDO record);

    int batchUpdate(@Param("list") List<CtoPlanProductFinishDO> list);

    /**
     * 分页查询产品维度数据
     * @param pageQuery
     * @return
     */
    PageRows<CtoPlanProductFinishDO> pageProductFinish(PageQuery<String> pageQuery);

    /**
     * 查询产品维度数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinish(String planInfoId);
    /**
     * 数据看板-查询产品维度
     *
     * @param planInfoId
     * @param role
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinishByRole(String planInfoId, String role);
    /**
     * 处理所有未处理的任务
     *
     * @return
     */
    List<String> listByUndoProcess();
    /**
     * 查询 客户-参与人 活动信息
     *
     * @param param
     * @return
     */
    List<SelectCtoActivityInfoResDTO> selectCtoActivityInfo(SelectCtoActivityParamDTO param);

    CtoPlanDetailDTO queryProductDetail(String rowId);

    /**
     * 根据计划ID查询产品数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanProductFinishDO> listProductFinishByPlanId(String planInfoId);
}

/* Ended by AICoder, pid:b4751j36bfv27f014f180816f0cf00082df149a6 */
