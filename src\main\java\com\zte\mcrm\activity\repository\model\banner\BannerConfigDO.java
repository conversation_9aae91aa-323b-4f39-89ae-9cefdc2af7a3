package com.zte.mcrm.activity.repository.model.banner;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
* banner配置 实体类
* <AUTHOR>
* @date 2024/02/01
*/
@ApiModel(description="banner配置")
@Setter
@Getter
@ToString
public class BannerConfigDO implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String bannerConfigId;

    @ApiModelProperty(value = "bannerId")
    private String bannerId;

    @ApiModelProperty(value = "key")
    private String key;

    @ApiModelProperty(value = "中文值")
    private String valueZh;

    @ApiModelProperty(value = "英文值")
    private String valueEn;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date creationDate;

    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "逻辑有效标识，Y-有效，N-无效")
    private String enabledFlag;
}