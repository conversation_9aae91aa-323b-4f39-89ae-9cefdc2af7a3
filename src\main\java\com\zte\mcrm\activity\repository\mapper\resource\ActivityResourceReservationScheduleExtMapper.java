package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityResourceReservationScheduleExtMapper extends ActivityResourceReservationScheduleMapper {

    /**
     * 查询活动对应的日程资源信息
     *
     * @param activityList 活动RowId列表
     * @return
     */
    List<ActivityResourceReservationScheduleDO> queryScheduleWithActivityRowIds(@Param("activityList") List<String> activityList);

    /**
     * 查询活动对应的日程资源信息（包含无效数据）
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivityResourceReservationScheduleDO> queryExpiredSchedules(@Param("activityRowId") String activityRowId);

    /**
     * 根据日程来源ID查询日程信息
     *
     * @param oriRowIdList   必传
     * @param reserveOriType 非必传
     * @return
     */
    List<ActivityResourceReservationScheduleDO> queryScheduleWithOriRowIds(@Param("oriRowIdList") List<String> oriRowIdList, @Param("reserveOriType") String reserveOriType);

    /**
     * 根据主键ID列表查询日程信息
     * @param rowIdList
     * @return
     */
    List<ActivityResourceReservationScheduleDO> queryScheduleWithRowIds(@Param("rowIdList") List<String> rowIdList);

    /***
     * <p>
     * 根据主键Id列表逻辑删除记录
     *
     * </p>
     * <AUTHOR>
     * @since 2024/4/28 下午4:40
     * @param operator 操作人
     * @param rowIds 主键id列表
     * @return int
     */
    int softDeleteByRowIds(@Param("operator") String operator,@Param("rowIds") List<String> rowIds);

}
