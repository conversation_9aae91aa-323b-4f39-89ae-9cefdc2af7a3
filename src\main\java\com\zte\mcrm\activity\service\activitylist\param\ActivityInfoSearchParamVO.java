package com.zte.mcrm.activity.service.activitylist.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 14:08
 */
@Setter
@Getter
@ToString
public class ActivityInfoSearchParamVO {

    /***
     * 展会名称
     */
    private String exhibitionName;
    /**
     * 展会id
     */
    private String originRowId;
    /** 拓展活动申请单号 */
    private String activityRequestNo;

    /** 拓展活动议题 */
    private String activityTitle;

    /** 拓展活动类型。枚举：ActivityTypeEnum，快码：Activity_Type_Enum */
    private String activityType;

    /** 拓展活动类型列表。枚举：ActivityTypeEnum，快码：Activity_Type_Enum */
    private List<String> activityTypeList;

    /** 拓展活动状态。枚举：ActivityStatusEnum */
    private List<String> activityStatus;

    /** 交流层级。高端交流，地市交流，一般交流。枚举：CommunicationLevelEnum */
    private String communicationLevel;

    /** 交流层级列表。高端交流，地市交流，一般交流。枚举：CommunicationLevelEnum */
    private List<String> communicationLevelList;
    /**
     * 活动交流时间fanwei
     */
    private List<String> communicationTimeRange;

    /** 活动起始时间。显示按yyyy-MM-dd HH:mm格式 */
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 活动截止时间。显示按yyyy-MM-dd HH:mm格式 */
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 活动主要客户 */
    private String customerCode;

    /** 客户单位*/
    private String customerName;

    /** 申请人员工编号 */
    private String applyPeopleNo;

    /** 申请人部门编码 */
    private String applyDepartmentNo;

    /**讲师*/
    private List<String> instructorList;

    /**参与者**/
    private List<String> attendeeList;

    /**组织者**/
    private List<String> orgerList;

    /**我司参与人**/
    private List<String> ztePeople;

    /** 是否含AP */
    private String isAp;

    /** 是否含遗留问题 */
    private String isRemainProblem;

    /** 联系人 */
    private String contactName;

    /**
     * 交流方向
     */
    private List<String> communicationDirection;

    /**
     * 交流方案
     */
    private String solutionNameCn;

    /**
     * 交流方案
     */
    private String solutionNameEn;

    /**
     * 项目编号
     */
    private String projectName;

    /**
     * 人员类型
     */
    private String peopleType;

    /**
     * 我司参与人类型
     */
    private String ztePeopleType;

    /**
     * 客户集团编码（group）
     */
    private String mtoCode;

    /**
     * mkt编码（account）
     */
    private String mktCode;

    /**
     *  客户编码（entity）
     */
    private String custCode;
    /**
     * 排序字段
     */
    private String uniqueSortKey;
}
