package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;

public interface ActivityScheduleOrchestrationVersionMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleOrchestrationVersionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleOrchestrationVersionDO record);

    /**
     * query by primary key
     */
    ActivityScheduleOrchestrationVersionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleOrchestrationVersionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationVersionDO record);
}