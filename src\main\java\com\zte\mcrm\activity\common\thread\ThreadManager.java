package com.zte.mcrm.activity.common.thread;

import com.zte.mcrm.activity.common.constant.NumberConstant;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ThreadManager {

    /** 串行任务执行器 */
    private static BaseTaskExecutor SERIAL_EXECUTOR = new SerialTaskExecutor();
    /** 异步任务执行器 */
    private static BaseTaskExecutor ASYNC_EXECUTOR = new AsyncTaskExecutor();
    /** 并行任务执行器 */
    private static BaseTaskExecutor PARALLEL_EXECUTOR = new ParallelTaskExecutor();

    /**
     * 慢任务线程池
     */
    private static SlowTaskExecutor SLOW_EXECUTOR = new SlowTaskExecutor();

    /**
     * 提交到《串行执行器》执行任务（串行、任务排队），详细见：{@link SerialTaskExecutor}
     * @param task
     * @return
     */
    public static boolean submitToSerial(Runnable task) {
        SERIAL_EXECUTOR.addTask(task);
        return true;
    }

    /**
     * 提交《异步执行器》执行任务（异步无返回值），详细见：{@link AsyncTaskExecutor}
     * @param task
     * @return
     */
    public static boolean submitToAsync(Runnable task) {
        ASYNC_EXECUTOR.addTask(task);
        return true;
    }

    /**
     * 提交任务到慢任务线程池，用于执行一些耗时较旧，执行频率较小的任务
     *
     * @param task
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2025/3/5 下午8:29
     */
    public static boolean submitSlowTask(Runnable task) {
        SLOW_EXECUTOR.addTask(task);
        return true;
    }

    /**
     * 提交《异步执行器》执行任务（异步有返回值），详细见：{@link AsyncTaskExecutor}
     * @param task
     * @return
     */
    public static <V> Future<V> submitToAsyncWithRes(Callable<V> task) {
        return ASYNC_EXECUTOR.addTask(task);
    }

    /**
     * 提交到《并行执行器》执行任务（并行），详细见：{@link ParallelTaskExecutor}
     * @param task
     * @return
     */
    public static <V> Future<V>  submitToParallel(Callable<V> task) {
        return PARALLEL_EXECUTOR.addTask(task);
    }

    /**
     * 等待任务完成（用于各异步或并行返回Future时的等待）
     *
     * @param list
     * @return 正常执行个数（无未处理异常）
     */
    public static <T> int waitFuture(List<Future<T>> list) {
        int suc = 0;
        for (Future<?> future : list) {
            try {
                future.get();
                suc++;
            } catch (Exception e) {
                // ignore，多线程执行时异常
            }
        }
        return suc;
    }

    /**
     * 并发《处理任务》
     *
     * @param taskList    任务列表
     * @param taskHandler 任务处理器。如果结果为null，则表示可能发生了异常。建议任务处理器结果不要有null
     * @param <P>         任务参数类型
     * @param <R>         结果类型
     * @return 任务处理结果列表（null结果不会添加进来）
     */
    public static <P, R> List<R> doTaskParallel(List<P> taskList, Function<P, R> taskHandler) {
        List<Future<R>> futureList = new ArrayList<>(taskList.size());
        // 优化：如果只有一个任务，，则使用当前主线程执行即可
        if (taskList.size() == NumberConstant.ONE) {
            R r = doTask(taskList.get(0), taskHandler);
            return r == null ? Collections.emptyList() : Collections.singletonList(r);
        } else {
            for (P task : taskList) {
                futureList.add(
                        submitToParallel(() -> doTask(task, taskHandler)));
            }
        }

        List<R> resList = new ArrayList<>(futureList.size());
        for (Future<R> f : futureList) {
            try {
                R res = f.get();
                if (res != null) {
                    resList.add(res);
                }
            } catch (Exception e) {
                // ignore，正常多线程不会出现异常
            }
        }

        return resList;
    }

    /**
     * 执行任务
     * @param task
     * @param taskHandler
     * @return
     * @param <P>
     * @param <R>
     */
    static <P, R> R doTask(P task, Function<P, R> taskHandler) {
        try {
            return taskHandler.apply(task);
        } catch (Exception e) {
            // ignore
            return null;
        }
    }

    /**
     * 任务分批拆分；然后并行执行，最后返回结果【List《每次批次的返回结果》】
     * <pre>
     *     1、比如原始需要处理的数据有100个，即：oriParamList = [1, 2...100]
     *     2、把任务每12（batchNum=12）个作为一组进行拆分。即：得到List《M》（M是一个含有batchNum个任务的对象）
     *     3、List《M》:总共分了9个批次（前8 * 12 + 1 * 4，前8个批次任务每个12个子任务；第9个有4个子任务）
     *     4、并行执行，当所有批次任务都执行完后，返回结果 List《R》 R-表示每个批次执行返回的结果
     * </pre>
     *
     * @param oriParamList 原批次参数
     * @param paramConvert 按batchNum分批次后的参数 List《P》 转为 R。
     * @param batchNum     批处理条数
     * @param taskHandler  任务处理器
     * @param <P>          任务源参数
     * @param <M>          转换后的参数
     * @param <R>          表示每个批次执行返回的结果
     * @return
     */
    public static <P, M, R> List<R> doTaskSplitBatch(List<P> oriParamList, Function<List<P>, M> paramConvert, int batchNum, Function<M, R> taskHandler) {
        // 【1】对任务按批次进行拆分。
        List<M> paramList = taskSplitBatch(oriParamList, paramConvert, batchNum);
        // 【2】对并行执行任务
        return doTaskParallel(paramList, taskHandler);
    }

    /**
     * 任务分批拆分（任务进行数据分组，分组数据作为convert的入参）
     *
     * @param oriParamList 原批次参数
     * @param convert      参数转换 List《P》 参数转为 R。
     * @param batchNum     批处理条数
     * @param <P>          参数
     * @param <R>          转换后的参数
     * @return
     */
    public static <P, R> List<R> taskSplitBatch(List<P> oriParamList, Function<List<P>, R> convert, int batchNum) {
        List<List<P>> list = taskSplit(oriParamList, batchNum);

        return list.stream().map(convert).collect(Collectors.toList());
    }

    /**
     * 任务分批拆分（并提供参数转换）
     *
     * @param oriParamList 原批次参数
     * @param convert      参数转换
     * @param batchNum     批处理条数
     * @param <P>          参数
     * @param <R>          转换后的参数
     * @return
     */
    public static <P, R> List<List<R>> taskSplit(List<P> oriParamList, Function<P, R> convert, int batchNum) {
        assert batchNum > 0;

        int size = oriParamList.size();
        List<List<R>> taskList = new ArrayList<>(size / batchNum + NumberConstant.ONE);

        for (int i = 0; i < size; i = i + batchNum) {
            int end = i + batchNum;
            if (end > size) {
                end = size;
            }
            List<P> oriList = oriParamList.subList(i, end);
            taskList.add(oriList.stream().map(convert).collect(Collectors.toList()));
        }

        return taskList;
    }

    /**
     * 任务分批拆分
     *
     * @param oriParamList 原批次参数
     * @param batchNum     批处理条数
     * @param <P>          参数
     * @return
     */
    public static <P> List<List<P>> taskSplit(List<P> oriParamList,  int batchNum) {
        return taskSplit(oriParamList, Function.identity(), batchNum);
    }

}




