package com.zte.mcrm.activity.integration.dicapi.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据字典查询参数
 * <AUTHOR> 10333830
 * @date 2023-07-24 14:30
 */
@Data
public class DictLanguageQueryParam {
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    @ApiModelProperty(value = "字典key")
    private String dictKey;

    @ApiModelProperty(value = "字典value")
    private String dictValue;

    @ApiModelProperty(value = "页数")
    private String pageNo;

    @ApiModelProperty(value = "单页条数")
    private String pageSize;


    public DictLanguageQueryParam(String dictType, String dictKey) {
        this.dictType = dictType;
        this.dictKey = dictKey;
    }

    public DictLanguageQueryParam() {
    }

}
