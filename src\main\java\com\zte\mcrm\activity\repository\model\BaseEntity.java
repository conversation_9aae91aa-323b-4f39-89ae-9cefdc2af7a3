package com.zte.mcrm.activity.repository.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @ClassName BaseEntity
 * @description:
 * @author: 李龙10317843
 * @create: 2023-05-17 11:33
 * @Version 1.0
 **/
@Getter
@Setter
@ToString
public class BaseEntity {
    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;
}