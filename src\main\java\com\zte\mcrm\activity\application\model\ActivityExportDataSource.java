package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.web.controller.search.vo.*;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.cust.constants.Constants.FLAG_ENABLE;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Getter
@Setter
public class ActivityExportDataSource {
    /**
     * 《扩展活动id，活动基本信息》
     */
    private Map<String, ActivitySearchBaseConditionVO> baseConditionMap;
    /**
     * 《扩展活动id，交流方向列表》
     */
    private Map<String, List<CommunicateDirectionIsearchVO>> communicateDirectionMap;
    /**
     * 《扩展活动id，客户信息列表》
     */
    private Map<String, List<CustUnitIsearchVO>> custUnitMap;
    /**
     * 《扩展活动id，客户联系人列表》
     */
    private Map<String, List<ContactsIsearchVO>> contactMap;
    /**
     * 《扩展活动id，我司参与人列表》
     */
    private Map<String, List<ZtePeopleIsearchVO>> activityRelationZtePeopleMap;
    /**
     * 《orgId，组织信息》
     */
    private Map<String, OrgInfoVO> orgMap;

    private Map<String, List<ActivityScheduleItemDO>> scheduleInfoMap;
    /**
     * 《日程id，日程参与人信息》
     */
    Map<String, List<ActivityScheduleItemPeopleDO>> relationSchedulePeopleInfoMap;

    /**
     * 查询基本信息
     */
    public ActivitySearchBaseConditionVO fetchBaseCondition(String activityRowId) {
        if (baseConditionMap == null) {
            return new ActivitySearchBaseConditionVO();
        }
        return baseConditionMap.get(activityRowId);
    }

    /**
     * 查询交流方向
     */
    public List<CommunicateDirectionIsearchVO> fetchCommunicateDirection(String activityRowId) {
        List<CommunicateDirectionIsearchVO> communicateDirectionList = communicateDirectionMap.get(activityRowId);
        return CollectionUtils.emptyIfNull(communicateDirectionList)
                .stream()
                .filter(item -> BooleanEnum.Y.isMe(item.getEnabledFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 查询客户列表
     */
    public List<CustUnitIsearchVO> fetchCustInfoList(String activityRowId) {
        List<CustUnitIsearchVO> listCustInfo = custUnitMap.get(activityRowId);
        return CollectionUtils.emptyIfNull(listCustInfo)
                .stream()
                .filter(item -> BooleanEnum.Y.isMe(item.getEnabledFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 查询客户信息
     */
    public CustUnitIsearchVO fetchCustInfo(String activityRowId, String customerCode) {
        List<CustUnitIsearchVO> custInfoList = fetchCustInfoList(activityRowId);
        Map<String, CustUnitIsearchVO> custInfoMap = custInfoList.stream()
                .collect(Collectors.toMap(CustUnitIsearchVO::getCustomerCode, Function.identity()));
        return Optional.ofNullable(custInfoMap)
                .filter(map -> !map.isEmpty())
                .map(map -> map.get(customerCode)).orElse(null);
    }

    /**
     * 查询客户联系人列表
     */
    public List<ContactsIsearchVO> fetchContactPeopleList(String activityRowId) {
        List<ContactsIsearchVO> listContacts = contactMap.get(activityRowId);
        return CollectionUtils.emptyIfNull(listContacts)
                .stream()
                .filter(item -> BooleanEnum.Y.isMe(item.getEnabledFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 查询客户联系人
     */
    public ContactsIsearchVO fetchContactPeople(String activityRowId, String contactNo) {
        List<ContactsIsearchVO> custInfoList = fetchContactPeopleList(activityRowId);
        Map<String, ContactsIsearchVO> custInfoMap = custInfoList.stream()
                .collect(Collectors.toMap(ContactsIsearchVO::getContactNo, Function.identity()));
        return Optional.ofNullable(custInfoMap)
                .filter(map -> !map.isEmpty())
                .map(map -> map.get(contactNo))
                .orElse(null);
    }

    /**
     * 查询我司参与人列表
     */
    public List<ZtePeopleIsearchVO> fetchZtePeopleList(String activityRowId) {
        List<ZtePeopleIsearchVO> listZtePeople = activityRelationZtePeopleMap.get(activityRowId);
        return CollectionUtils.emptyIfNull(listZtePeople)
                .stream()
                .filter(e -> BooleanUtils.and(new boolean[]{ActivityPeopleTypeEnum.isActivityParticipants(e.getPeopleType()), FLAG_ENABLE.equals(e.getEnabledFlag())}))
                .collect(Collectors.toList());
    }

    /**
     * 查询我司参与人
     */
    public ZtePeopleIsearchVO fetchZtePeople(String activityRowId, String peopleCode) {
        List<ZtePeopleIsearchVO> listZtePeople = fetchZtePeopleList(activityRowId);

        Map<String, ZtePeopleIsearchVO> custInfoMap = Optional.ofNullable(listZtePeople).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(ZtePeopleIsearchVO::getPeopleCode, Function.identity(), (k1, k2) -> k1));
        return custInfoMap.get(peopleCode);
    }

    /**
     * 查询日程活动
     */
    public List<ActivityScheduleItemDO> fetchScheduleItemList(String activityRowId) {
        return Optional.ofNullable(scheduleInfoMap)
                .filter(map -> !map.isEmpty())
                .map(map -> map.get(activityRowId))
                .orElseGet(ArrayList::new);
    }

    /**
     * 查询日程参与人
     */
    public List<ActivityScheduleItemPeopleDO> fetchScheduleItemPeopleList(String activityRowId) {
        return Optional.ofNullable(relationSchedulePeopleInfoMap)
                .filter(map -> !map.isEmpty())
                .map(map -> map.get(activityRowId))
                .orElseGet(ArrayList::new);
    }
}
