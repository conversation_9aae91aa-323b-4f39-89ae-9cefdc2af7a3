package com.zte.mcrm.activity.repository.rep.authority.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ActivityResourceOperationAuthQueryParam {

    /**
     * 活动id
     */
    private List<String> activityIdList;

    /**
     * 业务id
     */
    private List<String> bizRelatedIdList;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 被授权人工号
     */
    private String peopleNo;
}
