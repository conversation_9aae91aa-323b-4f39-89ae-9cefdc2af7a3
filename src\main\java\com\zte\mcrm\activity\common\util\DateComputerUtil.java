package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.constant.NumberConstant;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间计算工具
 *
 * <AUTHOR>
 */
public class DateComputerUtil {
    /**
     * 1小时秒数
     */
    public static int SECONDS_OF_HOUR = 3600;
    /**
     * 1天小时数
     */
    public static int HOURS_OF_DAY = 24;

    /**
     * 按日期降序 （null表示最小，会排在最后）
     *
     * @param o1
     * @param o2
     * @return  1或-1。1表示第2个参数 大于 第1个参数；-1表示第1个参数 大于 第2个参数
     */
    public static int dateSortDesc(Date o1, Date o2) {
        if(o1 == null && o2 == null) {
            return NumberConstant.ZERO;
        }

        if (o1 == null) {
            return NumberConstant.ONE;
        }
        if (o2 == null) {
            return NumberConstant.NEGATIVE_ONE;
        }

        return o2.compareTo(o1);
    }

    /**
     * 按日期升序 （null表示最大，会排在最后）
     *
     * @param o1
     * @param o2
     * @return 1或-1。-1表示第2个参数 大于 第1个参数；1表示第1个参数 大于 第2个参数
     */
    public static int dateSortAsc(Date o1, Date o2) {
        if (o1 == null && o2 == null) {
            return NumberConstant.ZERO;
        }
        // 避免重复代码
        return o1 == null ? NumberConstant.ONE : (
                o2 == null ? NumberConstant.NEGATIVE_ONE : o1.compareTo(o2)
        );
    }

    /**
     * 计算两个日期相隔天数（不算时间部分）
     * @param d1
     * @param d2
     * @return
     */
    public static int diffDays(Date d1, Date d2) {
        if (d1 == null || d2 == null) {
            return 0;
        }

        SimpleDateFormat format = new SimpleDateFormat(DateFormatUtil.YYYY_MM_DD_SLASH);
        String temp1 = format.format(d1);
        String temp2 = format.format(d2);

        int days = 0;
        try {
            d1 = format.parse(temp1);
            d2 = format.parse(temp2);

            long milliseconds = d1.getTime() - d2.getTime();
            days = (int) (milliseconds / NumberConstant.ONE_THOUSANDS / SECONDS_OF_HOUR / HOURS_OF_DAY);
        } catch (ParseException e) {
            // ignore
        }

        return Math.abs(days);
    }

    /**
     * 剩余天数（date - now()）
     * @param date 日期
     * @return date - now()
     */
    public static int leaveDay(Date date) {
        if (date == null) {
            return 0;
        }
        Date now = new Date();
        int diffDay = diffDays(date, now);

        return date.after(now) ? diffDay : -diffDay;
    }


}
