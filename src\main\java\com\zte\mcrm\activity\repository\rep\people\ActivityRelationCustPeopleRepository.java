package com.zte.mcrm.activity.repository.rep.people;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationContactDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 活动关联客户人员
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityRelationCustPeopleRepository {

    /**
     * 根据主键查询数据
     * @param rowId
     * @return
     */
    ActivityRelationCustPeopleDO selectByPrimaryKey(String rowId);

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityRelationCustPeopleDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityRelationCustPeopleDO record);

    /**
     * （临时方法）为了修复数据迁移客户联系人存的是ID，应该存客户编号
     * @param index limit index, size
     * @param size  limit index, size
     * @return
     */
    List<ActivityRelationCustPeopleDO> queryErrContractNos(int index, int size);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityRelationCustPeopleDO> queryAllByActivityRowId(String activityRowId);

    /**
     *  批量插入
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<ActivityRelationCustPeopleDO> recordList);

    /**
     * 根据活动id批量获取当前数据集合
     * @param activityRowIds
     * @return
     */
    List<ActivityRelationCustPeopleDO> queryAllCustPeopleForActivity(List<String> activityRowIds);

    /**
     * 根据活动id获取当前数据集合
     * @param activityRowIds
     * @return
     */
    Map<String,List<ActivityRelationCustPeopleDO>> getCustPeopleListByActivityRowIds(Set<String> activityRowIds);

    /**
     * 获取活动对应的客户联系人
     * @param activityRowIds
     * @return
     * <AUTHOR>
     * @date 2024/1/30
     */
    Map<String, Map<String, ActivityRelationCustPeopleDO>> getRelationCustPeopleMap(Set<String> activityRowIds);

    /**
     * 查找用户最近创建的活动中使用的客户联系人
     *
     * @param pageQuery  查询参数
     * @return {@link List< ActivityRelationCustPeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:56
     */
    List<ActivityRelationCustPeopleDO> selectRecentlyCustomerPeopleByUser(PageQuery<ActivityRecentlySearchParam> pageQuery);

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityRelationCustPeopleDO> list);




    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量修改数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    void batchUpdate(List<ActivityRelationCustPeopleDO> list);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationCustPeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationCustPeopleDO> queryAllActivityWithNotEnable(String activityRowId);

    /**
     * 获取客户联系人访问记录
     *
     * @param contactNos 客户联系人编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return List<ActivityRelationCustPeopleDO>
     */
    List<ActivityRelationContactDO> queryAllContactVisits(List<String> contactNos, String startTime, String endTime);

    /**
     * 获取客户联系人访问记录-根据开始时间结束时间筛选
     *
     * @param contactNos 客户联系人编码
     * @return List<ActivityRelationCustPeopleDO>
     */
    List<ActivityRelationContactDO> queryContactVisits(List<String> contactNos);
}
