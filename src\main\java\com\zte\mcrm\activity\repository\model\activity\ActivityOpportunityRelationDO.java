package com.zte.mcrm.activity.repository.model.activity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ActivityOpportunityRelationDO {

    /**
     * 活动Id
     */
    private String activityRowId;

    /**
     * 拓展活动申请单号
     */
    private String activityRequestNo;

    /**
     * 拓展活动类型
     */
    private String activityType;

    /**
     * 拓展活动议题
     */
    private String activityTitle;

    /**
     * 拓展活动状态。枚举：ActivityStatusEnum
     */
    private String activityStatus;

    /**
     * 提单申请时间
     */
    private Date submitTime;

    /**
     * 申请人员工编号
     */
    private String applyPeopleNo;

}
