package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;

import java.util.List;
import java.util.Map;

public interface ActivityScheduleItemPeopleRepository {
    /**
     * 批量插入日程安排参加人信息
     *
     * @param records
     * @return
     */
    int batchInsert(List<ActivityScheduleItemPeopleDO> records);

    /**
     * 根据日程id,批量获取日程安排参与人信息集合
     *
     * @param activityScheduleItemRowIds
     * @return
     */
    Map<String, List<ActivityScheduleItemPeopleDO>> getRelationSchedulePeopleInfoIds(List<String> activityScheduleItemRowIds);

    /**
     * 根据日程id,批量获取日程安排参与人信息集合
     *
     * @param activityScheduleItemRowIds
     * @return
     */
    List<ActivityScheduleItemPeopleDO> getRelationSchedulePeopleInfoList(List<String> activityScheduleItemRowIds);

    /**
     * 根据活动日程安排事项rowId查询参与人集合
     * @param activityScheduleItemRowId
     * @return
     */
    List<ActivityScheduleItemPeopleDO> queryAllByScheduleItemRowId(String activityScheduleItemRowId);

    /**
     * 批量更新数据
     *
     * @param records
     * @return
     */
    int batchUpdate(List<ActivityScheduleItemPeopleDO> records);

    /**
     * 根据activity_schedule_item_row_id,批量软删除日程参与人
     * @param operator
     * @param scheduleItemRowIds
     * @return
     */
    int deleteByScheduleItemRowIds(String operator, List<String> scheduleItemRowIds);

    /**
     * 根据scheduleItemRowIds,批量软删除日程参与人,指定类型的人员不删除
     *
     * @param operator
     * @param scheduleItemRowIds
     * @param peopleTypes
     * @return {@link int}
     * <AUTHOR>
     * @date 2025/2/28 下午7:20
     */
    int deleteByScheduleItemRowIdsWithoutPeopleTypes(String operator, List<String> scheduleItemRowIds, List<String> peopleTypes);

    /**
     * 根据活动id,批量获取日程安排参与人信息集合
     *
     * @param activityRowIds
     * @return
     */
    Map<String, List<ActivityScheduleItemPeopleDO>> queryAllSchedulePeopleByActivityRowIds(List<String> activityRowIds);
}
