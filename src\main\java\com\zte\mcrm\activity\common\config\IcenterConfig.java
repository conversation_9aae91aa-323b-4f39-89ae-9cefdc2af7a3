package com.zte.mcrm.activity.common.config;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.integration.third.speech.SpeechRecognitionParam;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ICenter统一配置
 * <pre>
 *     语音方面配置：
 *     dev:  https://icentermsgori.dev.zte.com.cn:15061/services/audioHandle/users/{senduri}/audioToText}   https://**************:15061
 *     test: https://moaportal.test.zte.com.cn:15061/services/audioHandle/users/{senduri}/audioToText
 *     uat:  https://moaportal.test.zte.com.cn:15061/services/audioHandle/users/{senduri}/audioToText
 *     pro:  https://pcmoa.zte.com.cn:15061/services/audioHandle/users/{senduri}/audioToText
 *
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
public class IcenterConfig {
    /**
     * 和speechUrl的域名端口保持一致
     */
    @Value("${icenter.speech.hostUrl:icentermsgori.dev.zte.com.cn:15061}")
    private String speechHost;
    @Value("${icenter.speech.uriPath:https://icentermsgori.dev.zte.com.cn:15061/services/audioHandle/users/{senduri}/audioToText}")
    private String speechUrl;
    @Value("${icenter.sendMsg.uriPath:https://icentermsgori.dev.zte.com.cn:15061/services/sendExtmsg/users/}")
    private String sendMsgUrl;
    @Value("${icenter.speech.authKey:iCRM}")
    private String speechAuthKey;
    @Value("${icenter.speech.authCode:}")
    private String speechAuthCode;
    /**
     * 请求头：Terminal-Type调用者终端类型。对于外部系统，固定以web端身份调用，填写：Web@Desktop_999_ID1004
     */
    @Value("${icenter.speech.terminalType:Web@Desktop_999_ID1004}")
    private String speechTerminalType = "Web@Desktop_999_ID1004";

    /**
     * 请求头：Sender-Name 消息发送者中英文姓名，必填，字段值进行base64解码后如下：zh=机器人小助手|en=robot assistant，其中“机器人小助手”为中文姓名，“robot assistant”为英文姓名
     * 示例：iCRM运维助手|iCRM Operations Robot:aUNSTei/kOe7tOWKqeaJi3xpQ1JNIE9wZXJhdGlvbnMgUm9ib3Q=
     */
    @Value("${icenter.iCrmRobot.senderName:aUNSTei/kOe7tOWKqeaJi3xpQ1JNIE9wZXJhdGlvbnMgUm9ib3Q=}")
    private String iCRMRobotSenderName ;

    @Value("${icenter.contact.url:https://icenter-systest.test.zte.com.cn/zte-icenter-search-bff/combine-search}")
    private String icenterContactUrl ;

    /**
     * 获取语音请求地址
     *
     * @param param 参数
     * @return
     */
    public String fetchSpeechUrl(SpeechRecognitionParam param) {
        return (speechUrl.replace("{senduri}", param.getSendUri())) + CharacterConstant.QUESTION_EN
                + "audioFormat=" + param.getSpeechFormat()
                + "&language=" + param.getLanguage()
                + "&fromSys=" + speechAuthKey
                ;
    }


}
