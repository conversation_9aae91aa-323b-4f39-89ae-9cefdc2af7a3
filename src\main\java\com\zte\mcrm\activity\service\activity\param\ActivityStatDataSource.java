package com.zte.mcrm.activity.service.activity.param;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.adapter.bo.EmployeeBO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.PAUSE_MARK;

@Getter
@Setter
public class ActivityStatDataSource {

    /**
     * 活动列表分页数据
     */
    private PageRows<ActivityInfoDO> activityInfoDOPageRows;
    /**
     * 活动id列表
     */
    private List<String> activityRowIds;

    /**
     * 活动信息列表
     */
    private List<ActivityInfoDO> activityInfoDOList;

    /**
     * 活动，主客户
     */
    private Map<String, ActivityCustomerInfoDO> activityIdAndMainCustomerInfoMap;

    /**
     * 活动，主客户参与人
      */
    private Map<String, List<ActivityRelationCustPeopleDO>> activityIdAndMainCustPeopleMap;

    /**
     * 活动，中兴参与人
     */
    private Map<String, List<ActivityRelationZtePeopleDO>> activityRowId2ZtePeoplesMap;

    /**
     * 活动申请部门列表
     */
    private List<String> applyDepartmentNoList;

    /**
     * 部门编码，部门详细信息
     */
    private Map<String, OrgInfoVO> applyDepartmentInfoMap;

    /**
     * 申请人员信息
     */
    private Map<String, EmployeeBO> applyPeopleMap;

    /**
     * 样板点信息
     */
    private SamplePointInfoDO samplePointInfoDO;

    /**
     * 是否有领导权限
     */
    private Boolean hasLeaderAuth;

    /**
     * 是否样板点管理员
     */
    private Boolean hasSamplePointAdminAuth;

    /**
     * 是否有当前样板点管理权限
     */
    private Boolean hasCurrSamplePointManageAuth;

    /**
     * 参观次数
     */
    private Integer visitCount;

    /**
     * 最后一次参观时间
     */
    private Date latestVisitTime;



    /**
     * 获取活动参与主客户
     * @param activityId
     * @return
     */
    public ActivityCustomerInfoDO getActivityMainCustomerInfo(String activityId) {
        ActivityCustomerInfoDO emptyCustomerInfo = new ActivityCustomerInfoDO();
        return activityIdAndMainCustomerInfoMap == null ? emptyCustomerInfo : activityIdAndMainCustomerInfoMap.getOrDefault(activityId, emptyCustomerInfo);
    }

    /**
     * 获取活动主客户参与人列表
     * @param activityId
     * @return
     */
    public List<ActivityRelationCustPeopleDO> getActivityRelationMainCustPeopleList(String activityId) {
        List<ActivityRelationCustPeopleDO> emptyList = Lists.newArrayList();
        return activityIdAndMainCustPeopleMap == null ? emptyList : activityIdAndMainCustPeopleMap.getOrDefault(activityId, emptyList);
    }

    /**
     * 获取活动主客户参与人,用、连接
     * @param activityId
     * @return
     */
    public String getActivityRelationMainCustPeople(String activityId) {
        List<ActivityRelationCustPeopleDO> activityRelationMainCustPeopleList = getActivityRelationMainCustPeopleList(activityId);
        if (CollectionUtils.isEmpty(activityRelationMainCustPeopleList)) {
            return null;
        }
        return activityRelationMainCustPeopleList.stream().map(e -> e.getContactName()).distinct().collect(Collectors.joining(PAUSE_MARK));
    }

    /**
     * 获取活动中兴参与人列表
     * @param activityId
     * @return
     */
    public List<ActivityRelationZtePeopleDO> getActivityRelationZtePeopleList(String activityId) {
        List<ActivityRelationZtePeopleDO> emptyList = Lists.newArrayList();
        return activityRowId2ZtePeoplesMap == null ? emptyList : activityRowId2ZtePeoplesMap.getOrDefault(activityId, emptyList);
    }

    /**
     * 获取活动中兴参与人用、连接
     * @param activityId
     * @return
     */
    public String getActivityRelationZtePeople(String activityId) {
        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleList = getActivityRelationZtePeopleList(activityId);
        if (CollectionUtils.isEmpty(activityRelationZtePeopleList)) {
            return null;
        }
        return activityRelationZtePeopleList.stream().map(e -> e.getPeopleName() + e.getPeopleCode()).distinct()
                .collect(Collectors.joining(PAUSE_MARK));
    }

    /**
     * 获取部门编码的部门信息
     * @param deptNo
     * @return
     */
    public OrgInfoVO getOrgInfo(String deptNo) {
        OrgInfoVO orgInfoVO = new OrgInfoVO();
        return applyDepartmentInfoMap == null ? orgInfoVO : applyDepartmentInfoMap.getOrDefault(deptNo, orgInfoVO);
    }

    /**
     * 获取申请人员名称
     * @param empNo
     * @return
     */
    public EmployeeBO getEmployeeInfo(String empNo) {
        EmployeeBO employeeBO = new EmployeeBO();
        return applyPeopleMap == null ? employeeBO : applyPeopleMap.getOrDefault(empNo, employeeBO);
    }

    /**
     * 获取申请人员姓名
     * @param empNo
     * @return
     */
    public String getEmployeeName(String empNo) {
        EmployeeBO employeeInfo = getEmployeeInfo(empNo);
        return employeeInfo.getName() == null ? "" : employeeInfo.getName();
    }
}
