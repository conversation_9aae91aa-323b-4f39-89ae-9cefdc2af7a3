package com.zte.mcrm.activity.integration.accountinfo.param;

import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.web.controller.customer.vo.CustomerQueryParamDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户详情查询
 * <pre>
 *     使用客户名称 或 客户编码进行精确查询，（对外部查询，控制最大100）
 *     默认会返回客户基本信息
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CustomerDetailQueryParamDTO {

    /**
     * 客户名称
     */
    private List<String> nameList;
    /**
     * 客户编码
     */
    private List<String> customerCodeList;
    /**
     * 需要返回的客户信息。枚举：CustomerInfoTypeEnum
     */
    private List<String> contentType;

    /**
     * 过滤合并客户。true-不查询被合并客户，false-不限制。默认true
     */
    private boolean filterMerge = true;
    /**
     * 过滤冻结客户。true-不查询被合并客户，false-不限制。默认true
     */
    private boolean filterFrozen = true;
    /**
     * 仅仅只查有效的。true-不查询被合并客户，false-不限制。默认true
     */
    private boolean onlyEffect = true;
    /**
     * 法人
     */
    private String corporateNo;

    /**
     * 构建客户编码请求参数
     * @param customerQueryParamDTO
     */
    public void buildCustomerCodeParam(CustomerQueryParamDTO customerQueryParamDTO) {
        if (!StringUtils.isBlank(customerQueryParamDTO.getCustomerCode())){
            this.customerCodeList = new ArrayList<>();
            this.customerCodeList.add(customerQueryParamDTO.getCustomerCode());
        }
        this.corporateNo = customerQueryParamDTO.getCorporateNo();
    }
}
