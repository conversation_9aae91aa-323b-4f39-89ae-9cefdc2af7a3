package com.zte.mcrm.activity.common.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.auth.ActivityRoleAuthConfigDTO;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户活动角色权限配置
 *
 * <AUTHOR>
 * @date 2023/8/11 下午5:46
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "activity.role.auth")
public class ActivityAuthConfig {

    /**
     * 角色配置
     */
    private static List<String> configList;

    /**
     * 公司领导组织id
     */
    private static String leaderOrgIds;

    public void setConfigList(List<String> configList) {
        setConfig(configList);
    }

    /**
     * 用户中心领导的组织ID，为防止后续可能存在非此ID的领导，配置成多个，用逗号分隔
     */
    @Value("${user.leader.org.ids:ORG0000843}")
    public void setLeaderOrgIds(String leaderOrgIds) {
        setLeaderOrg(leaderOrgIds);
    }

    private static void setConfig(List<String> configList) {
        ActivityAuthConfig.configList = configList;
    }

    private static void setLeaderOrg(String leaderOrgIds) {
        ActivityAuthConfig.leaderOrgIds = leaderOrgIds;
    }

    /**
     * 解析json配置
     *
     * @return {@link List< ActivityRoleAuthConfigDTO>}
     * <AUTHOR>
     * @date 2023/8/11 下午5:47
     */
    public static Map<String, ActivityRoleAuthConfigDTO> getRoleAuthConfigMap() {
        if (CollectionUtils.isEmpty(configList)) {
            return Maps.newHashMap();
        }
        return configList.stream()
                .filter(StringUtils::isNotBlank)
                .map(item -> JSON.parseObject(item, ActivityRoleAuthConfigDTO.class))
                .collect(Collectors.toMap(ActivityRoleAuthConfigDTO::getRoleCode, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 获取用于判断是否领导的组织ID
     *
     * @return {@link Set < String>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:01
     */
    public static Set<String> getLeaderOrgIdSet() {
        if (StringUtils.isBlank(leaderOrgIds)) {
            return Sets.newHashSet();
        }
        return Arrays.stream(leaderOrgIds.split(CharacterConstant.COMMA)).collect(Collectors.toSet());
    }

}
