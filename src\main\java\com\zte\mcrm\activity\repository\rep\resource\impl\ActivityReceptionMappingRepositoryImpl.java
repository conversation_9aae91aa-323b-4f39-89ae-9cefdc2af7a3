package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityReceptionMappingExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityReceptionMappingDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityReceptionMappingRepository;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityReceiveInfo;
import com.zte.mcrm.adapter.common.HeadersProperties;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public class ActivityReceptionMappingRepositoryImpl implements ActivityReceptionMappingRepository {

    @Autowired
    private ActivityReceptionMappingExtMapper activityReceptionMappingExtMapper;

    @Override
    public List<ActivityReceptionMappingDO> listReceptionByActivityRowId(String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return new ArrayList<>();
        }

        return activityReceptionMappingExtMapper.getListByActivityRowId(activityRowId);
    }

    @Override
    public List<ActivityReceiveInfo> getDetailByReceiveIdList(List<String> receiveIds) {
        if (CollectionUtils.isEmpty(receiveIds)) {
            return new ArrayList<>();
        }

        return activityReceptionMappingExtMapper.getDetailByReceiveIdList(receiveIds);
    }

    @Override
    public int softDeletedByBatch(String empNo, Set<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return 0;
        }

        empNo = StringUtils.defaultString(empNo, HeadersProperties.getXEmpNo());
        return activityReceptionMappingExtMapper.softDeleteByIds(empNo, Lists.newArrayList(rowIds));
    }
}
