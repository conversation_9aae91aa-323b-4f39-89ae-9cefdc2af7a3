package com.zte.mcrm.activity.integration.accountinfo.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: SimpleCustoerPersonVO
 * @projectName zte-crm-account-info
 * @description: TODO
 * @date 2024/1/17 10:02
 */
@Getter
@Setter
public class OutCustomerPersonDTO {

    /** 主键 */
    private String rowId;

    /** 名称 */
    private String name;

    /** 英文名称 */
    private String nameEn;

    /** 联系人受限主体 */
    private String sanctionedParty;

    /** 国家编码 */
    private String countryId;

    /** 城市编码 */
    private String cityId;

    /** 性别 */
    private Integer sex;

    /** 性别描述 */
    private String sexDesc;

    /** 手机1 */
    private String phone1;

    /** 手机2 */
    private String phone2;

    /** 状态 */
    private String status;

    /** 客户编码 */
    private String customerCode;

    /** 所属部门code */
    private String belongDept;

    /** 岗位 */
    private String job;

    /** 联系人编号 */
    private String contractNo;

    /** GTS扫描结果时间 */
    private Date gtsScanDatetime;

    /** 关系评级 */
    private String relationLevel;

    /** ECSS扫描，受限制主体 */
    private String ecssSanctionedParty;

    /** ECSS扫描，禁运国 */
    private String ecssEmbargoedCountry;

    /**GTS扫描的结果 */
    private String gtsSanctionedParty;

}
