package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;
import java.util.List;

public interface ExhibitionRelationAttachmentMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationAttachmentDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationAttachmentDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationAttachmentDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationAttachmentDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationAttachmentDO record);
}