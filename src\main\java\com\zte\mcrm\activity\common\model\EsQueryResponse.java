package com.zte.mcrm.activity.common.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * ES查询返回结果
 *
 * <AUTHOR>
 * @date 2023/12/20 下午2:58
 */
@Getter
@Setter
@ToString
public class EsQueryResponse<T> {

    /**
     * 查询执行时间
     */
    private Long took;

    /**
     * 超时=true
     */
    private Boolean timedOut;

    /**
     * 查询命中的文档信息
     */
    private HitDTO<T> hits;

    /**
     * ES查询命中结果
     *
     * <AUTHOR>
     * @date 2023/12/20 下午3:07       
     */
    @Getter
    @Setter
    @ToString
    public static class HitDTO<T> {
        private Long total;
        private List<HitDetailDTO<T>> hits;
    }

    /**
     * ES查询命中结果明细
     *
     * <AUTHOR>
     * @date 2023/12/20 下午3:08       
     */
    @Getter
    @Setter
    @ToString
    public static class HitDetailDTO<T> {
        /**
         * 数据ID
         */
        @JSONField(name = "_id")
        private String id;
        /**
         * 匹配分数
         */
        @JSONField(name = "_score")
        private Double score;
        /**
         * 匹配数据
         */
        @JSONField(name = "_source")
        private T source;

        /**
         * 高亮字段
         */
        @JSONField(name = "highlight")
        private Map<String, JSONArray> highlight;
    }

}
