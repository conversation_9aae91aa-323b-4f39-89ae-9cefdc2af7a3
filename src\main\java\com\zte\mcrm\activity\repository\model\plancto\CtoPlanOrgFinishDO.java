package com.zte.mcrm.activity.repository.model.plancto;/* Started by AICoder, pid:ic86bf01347fa2a143da097021482e961302c00f */
/* Started by AICoder, pid:j275073f4c407681433f0ae1211cc0970aa691b0 */
import lombok.Data;
import java.util.Date;

@Data
public class CtoPlanOrgFinishDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * CTO拓展计划ID
     */
    private String ctoPlanInfoId;

    /**
     * CTO事业部维度明细ID
     */
    private String ctoPlanDetailOrgId;

    /**
     * 营销事业部
     */
    private String orgDivision;

    /**
     * 客户Account编码json数组
     */
    private String accountCode;

    /**
     * 客户Account名称json数组
     */
    private String accountName;

    /**
     * 国家/地区编码json数组
     */
    private String localCode;

    /**
     * RAN目标数量
     */
    private Integer ranTarget;

    /**
     * 核心领导完成数
     */
    private Integer ranLeaderFinish;

    /**
     * 非核心领导完成数
     */
    private Integer ranSvantFinish;

    /**
     * RAN完成数量
     */
    private Integer ranFinish;

    /**
     * RAN拓展活动ID
     */
    private String ranActivityId;

    /**
     * CCN目标数量
     */
    private Integer ccnTarget;

    /**
     * 核心领导完成数
     */
    private Integer ccnLeaderFinish;

    /**
     * 非核心领导完成数
     */
    private Integer ccnSvantFinish;

    /**
     * CCN完成数量
     */
    private Integer ccnFinish;

    /**
     * CCN拓展活动ID
     */
    private String ccnActivityId;

    /**
     * BN目标数量
     */
    private Integer bnTarget;

    /**
     * 核心领导完成数
     */
    private Integer bnLeaderFinish;

    /**
     * 非核心领导完成数
     */
    private Integer bnSvantFinish;

    /**
     * BN完成数量
     */
    private Integer bnFinish;

    /**
     * BN拓展活动ID
     */
    private String bnActivityId;

    /**
     * FM目标数量
     */
    private Integer fmTarget;

    /**
     * 核心领导完成数
     */
    private Integer fmLeaderFinish;

    /**
     * 非核心领导完成数
     */
    private Integer fmSvantFinish;

    /**
     * FM完成数量
     */
    private Integer fmFinish;

    /**
     * FM拓展活动ID
     */
    private String fmActivityId;

    /**
     * SN数能目标数量
     */
    private Integer snTarget;

    /**
     * 核心领导完成数
     */
    private Integer snLeaderFinish;

    /**
     * 非核心领导完成数
     */
    private Integer snSvantFinish;

    /**
     * SN数能完成数量
     */
    private Integer snFinish;

    /**
     * SN拓展活动ID
     */
    private String snActivityId;

    /**
     * 执行状态
     */
    private String exeStatus;

    /**
     * CTO周期开始时间
     */
    private Date scopeStart;

    /**
     * CTO周期截止时间
     */
    private Date scopeEnd;

    /**
     * 待执行时间
     */
    private Date exeWaitTime;

    /**
     * 执行完毕时间
     */
    private Date exeFinishTime;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;

    /**
     * 明细说明
     */
    private String remark;
    /**
     * 组织ID
     */
    private String orgId;
    /**
     * 排序
     */
    private Integer seq;
}

/* Ended by AICoder, pid:j275073f4c407681433f0ae1211cc0970aa691b0 */
