package com.zte.mcrm.activity.application.export.convert;

import com.zte.mcrm.activity.application.export.vo.ExportLeaderViewVO;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionDataBoardViewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午3:07
 **/
@Component
public class ExportExhibitionConvertor {

    @Autowired
    private DataBoardViewConvert dataBoardViewConvert;

    public SimpleExcelExportModel toLeaderViewSimpleExcelExportModel(List<ExhibitionDataBoardViewVO> exhibitionDataBoardViewList, String sheetName) {
        SimpleExcelExportModel model = new SimpleExcelExportModel();

        List<ExportLeaderViewVO> exportLeaderViewVos = dataBoardViewConvert.dataBoardViewsToExportLeaderViews(exhibitionDataBoardViewList);
        model.addSheetData(sheetName, exportLeaderViewVos, ExportLeaderViewVO.class);
        return model;
    }
}
