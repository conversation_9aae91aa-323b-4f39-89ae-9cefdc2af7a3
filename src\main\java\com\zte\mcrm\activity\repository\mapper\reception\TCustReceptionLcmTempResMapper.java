package com.zte.mcrm.activity.repository.mapper.reception;

import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResDO;

public interface TCustReceptionLcmTempResMapper {
    /**
     * all field insert
     */
    int insert(TCustReceptionLcmTempResDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(TCustReceptionLcmTempResDO record);

    /**
     * query by primary key
     */
    TCustReceptionLcmTempResDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(TCustReceptionLcmTempResDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(TCustReceptionLcmTempResDO record);
}