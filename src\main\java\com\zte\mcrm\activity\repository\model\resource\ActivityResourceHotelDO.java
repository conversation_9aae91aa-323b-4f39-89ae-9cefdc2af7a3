package com.zte.mcrm.activity.repository.model.resource;

import java.math.BigDecimal;
import java.util.Date;

/**
 * table:activity_resource_hotel -- 
 */
public class ActivityResourceHotelDO {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 住户来源：HotelPeopleSourceEnum。选中的为客户参与人为：客户。选中的为现场接口人：接口人 */
    private String peopleSource;

    /** 人员编号（员工编号、客户参与人编号） */
    private String peopleNo;

    /** 人员名称 */
    private String peopleName;

    /** 星级 */
    private String hotelStar;

    /** 房型 */
    private String hotelType;

    /** 入住时间 */
    private Date checkInTime;

    /** 退房时间 */
    private Date checkOutTime;

    /** 是否自付。Y-是/N-否，见：BooleanEnum */
    private String paySelf;

    /**
     * 费用合计
     */
    private BigDecimal amount;

    /** 合规编号 */
    private String complianceNo;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    /** 房间系数 */
    private String roomCoefficient;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getPeopleSource() {
        return peopleSource;
    }

    public void setPeopleSource(String peopleSource) {
        this.peopleSource = peopleSource == null ? null : peopleSource.trim();
    }

    public String getPeopleNo() {
        return peopleNo;
    }

    public void setPeopleNo(String peopleNo) {
        this.peopleNo = peopleNo == null ? null : peopleNo.trim();
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName == null ? null : peopleName.trim();
    }

    public String getHotelStar() {
        return hotelStar;
    }

    public void setHotelStar(String hotelStar) {
        this.hotelStar = hotelStar == null ? null : hotelStar.trim();
    }

    public String getHotelType() {
        return hotelType;
    }

    public void setHotelType(String hotelType) {
        this.hotelType = hotelType == null ? null : hotelType.trim();
    }

    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    public Date getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(Date checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public String getPaySelf() {
        return paySelf;
    }

    public void setPaySelf(String paySelf) {
        this.paySelf = paySelf == null ? null : paySelf.trim();
    }

    public String getComplianceNo() {
        return complianceNo;
    }

    public void setComplianceNo(String complianceNo) {
        this.complianceNo = complianceNo == null ? null : complianceNo.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRoomCoefficient() {
        return roomCoefficient;
    }

    public void setRoomCoefficient(String roomCoefficient) {
        this.roomCoefficient = roomCoefficient == null ? null : roomCoefficient.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}