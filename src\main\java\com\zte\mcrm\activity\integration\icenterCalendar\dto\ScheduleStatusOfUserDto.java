package com.zte.mcrm.activity.integration.icenterCalendar.dto;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.enums.activity.ReserveStatusEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static com.zte.mcrm.customermgr.common.CustomerConst.*;

/**
 * 用户日程状态
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ScheduleStatusOfUserDto {

    /**
     * 活动RowId
     */
    private String activityRowId;
    /**
     * 活动日程中的各人员接受/拒绝等状态信息
     */
    private List<StatusOfUser> userList = new ArrayList<>();

    /**
     * 获取用户的日程状态
     *
     * @param empNo 员工编号
     * @return
     */
    public ReserveStatusEnum fetchStatusOfUser(String empNo) {
        StatusOfUser user = fetchUser(empNo);
        return user == null ? null : user.getStatus();
    }

    /**
     * 获取用户的日程状态
     *
     * @param empNo 员工编号
     * @return
     */
    public StatusOfUser fetchUser(String empNo) {
        StatusOfUser ofUser = null;
        for (StatusOfUser user : userList) {
            if (StringUtils.equals(empNo, user.empNo)) {
                ofUser = user;
                break;
            }
        }
        return ofUser;
    }

    /**
     * @param empNo          员工编号
     * @param scheduleStatus 日程那边的接受/预约状态
     */
    public void addUser(String empNo, String scheduleStatus, String remark, String replyTime) {
        StatusOfUser user = new StatusOfUser();
        ReserveStatusEnum status = null;
        if (ZERO.equals(scheduleStatus)) {
            status = ReserveStatusEnum.WAIT_DEAL;
        } else if (CharacterConstant.ONE.equals(scheduleStatus)) {
            status = ReserveStatusEnum.AGREE;
        } else if (CharacterConstant.TWO.equals(scheduleStatus)) {
            status = ReserveStatusEnum.REFUSE;
        }

        user.status = status == null ? ReserveStatusEnum.WAIT_DEAL : status;
        user.empNo = empNo;
        user.remark = remark;
        user.replyTime = replyTime;

        userList.add(user);
    }

    @Getter
    @Setter
    public static class StatusOfUser {
        /**
         * 员工编号
         */
        private String empNo;
        /**
         * 日程那边只有三种状态：待处理、接受、拒绝
         */
        private ReserveStatusEnum status;
        /**
         * 日程处理备注信息
         */
        private String remark;
        /**
         * 答复时间
         */
        private String replyTime;
    }


}
