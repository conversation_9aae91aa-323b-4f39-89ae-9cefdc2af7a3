package com.zte.mcrm.activity.repository.rep.authority;

import com.zte.mcrm.activity.repository.model.authority.ActivityResourceOperationAuthDO;

import java.util.List;
import java.util.Map;

/**
 * 客户活动资源操作授权DAO
 *
 * <AUTHOR>
 * @date 2024/11/13 上午10:35
 */
public interface ActivityResourceOperationAuthRepository {

    /**
     * 根据业务id和业务类型批量查询权限数据
     *
     * @param bizRelatedIdList  业务id
     * @param bizType           业务类型
     * @return {@link List<ActivityResourceOperationAuthDO>}
     * <AUTHOR>
     * @date 2024/11/13 下午2:36
     */
    Map<String, List<ActivityResourceOperationAuthDO>> selectByBizRelatedIdList(List<String> bizRelatedIdList, String bizType, String peopleNo);

    /**
     * 根据活动id和业务类型批量查询权限数据
     *
     * @param activityIdList    活动id
     * @param bizType           业务类型
     * @return {@link Map<String, List<ActivityResourceOperationAuthDO>>}
     * <AUTHOR>
     * @date 2024/11/13 下午2:40
     */
    Map<String, List<ActivityResourceOperationAuthDO>> selectByActivityIdList(List<String> activityIdList, String bizType, String peopleNo);

    /**
     * 批量新增
     *
     * @param list
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:31
     */
    int batchInsert(List<ActivityResourceOperationAuthDO> list);

    /**
     * 批量更新
     *
     * @param list
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/13 上午10:31
     */
    int batchUpdate(List<ActivityResourceOperationAuthDO> list);

    /**
     * 根据id批量删除（软删除）
     *
     * @param ids   id
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/11/25 上午10:33
     */
    int deleteByIdList(List<String> ids, String operator);
}
