package com.zte.mcrm.activity.common.model;

import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.function.Function;

/**
 * 所有集成层（对其他微服务的接口封装的方法结果）必须是该类
 * <pre>
 *     使用我们标准的，这样减少msa对象中的各种判空，是否成功之类的
 *     备注：凡是我们这里新赠的外部微服务接口封装的方法必须使用该类作为返回结果
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MsaRpcResponse<T> {
    /**
     * 外部系统报错
     */
    public static final String OUT_SERVERERROR_CODE="1001";
    public static final String SUCCESS_CODE = "0000";
    /**
     * 服务相应code
     */
    private String code;
    /**
     * 服务相应msg描述
     */
    private String msg;
    /**
     * 服务相应msg id
     */
    private String msgId;
    /**
     * 服务相应额外map
     */
    private Map<String, Object> other;
    /**
     * 业务对象bo
     */
    private T bo;

    /**
     * 封装的请求产生的异常信息
     */
    private Throwable ex;
    /**
     * 实际响应json字符串（返回结果）
     */
    private String responseJson;
    /**
     * 发送请求json字符串（请求参数）
     */
    private String requestJson;

    public MsaRpcResponse() {
        this(null);
    }

    public MsaRpcResponse(ServiceData<T> serviceData) {
        this(serviceData, Function.identity());
    }

    /**
     * @param serviceData msa框架定义的ServiceData
     * @param convert     参数1中的数据转换为MsaRpcResponse的bo
     */
    public <F> MsaRpcResponse(ServiceData<F> serviceData, Function<F, T> convert) {
        if (serviceData != null) {
            RetCode retCode = serviceData.getCode();
            if (retCode != null) {
                this.code = retCode.getCode();
                this.msg = retCode.getMsg();
                this.msgId = retCode.getMsgId();
            }

            if (serviceData.getBo() != null && convert != null) {
                this.bo = convert.apply(serviceData.getBo());
            }
            this.other = serviceData.getOther();
            this.setResponseJson(JSON.toJSONString(serviceData));
        }
    }

    /**
     * @param code  code
     * @param msg   msg
     * @param msgId msgId
     * @param bo    bo
     */
    public MsaRpcResponse(String code, String msg, String msgId, T bo) {
        this.setCode(code);
        this.setMsg(msg);
        this.setMsgId(msgId);
        this.setBo(bo);
    }

    /**
     * 请求是否成功
     *
     * @return true-请求相应成功，false-请求不成功
     */
    public boolean success() {
        return SUCCESS_CODE.equals(this.code);
    }

    /**
     * 是否发生了异常
     *
     * @return true-调用服务发生异常，false-没有
     */
    public boolean hasException() {
        return ex != null;
    }

    /**
     * 提供该方法为了兼容其他场景
     *
     * @return
     */
    public RetCode getRetCode() {
        RetCode r = new RetCode();
        r.setCode(this.code);
        r.setMsgId(this.msgId);
        if (StringUtils.isNotBlank(msg)) {
            r.setMsg(this.msg);
        }
        return r;
    }

    /**
     * 提供转转换方法
     *
     * @return 返回com.zte.itp.msa.core.model.ServiceData对象
     */
    public ServiceData<T> toServiceData() {
        ServiceData<T> s = new ServiceData<>();
        s.setCode(getRetCode());
        s.setBo(this.bo);
        s.setOther(this.other);

        return s;
    }

    /**
     * 构建成功的结果
     *
     * @param bo
     * @param <T>
     * @return
     */
    public static <T> MsaRpcResponse<T> successRes(T bo) {
        return new MsaRpcResponse<>(SUCCESS_CODE, "", "", bo);
    }

}
