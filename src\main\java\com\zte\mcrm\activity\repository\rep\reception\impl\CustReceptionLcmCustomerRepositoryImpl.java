package com.zte.mcrm.activity.repository.rep.reception.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.reception.CustReceptionLcmCustomerExtMapper;
import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmCustomerDO;
import com.zte.mcrm.activity.repository.rep.reception.CustReceptionLcmCustomerRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * 客户接待 - 客户LCM扫描信息
 *
 * <AUTHOR>
 */
@Component
public class CustReceptionLcmCustomerRepositoryImpl implements CustReceptionLcmCustomerRepository {

    @Autowired
    private IKeyIdService iKeyIdService;
    @Autowired
    private CustReceptionLcmCustomerExtMapper extMapper;

    @Override
    public List<CustReceptionLcmCustomerDO> getByHeaderId(String headerId) {
        return StringUtils.isBlank(headerId) ? Collections.emptyList() : extMapper.getByHeaderIds(Collections.singletonList(headerId.trim()));
    }

    /**
     * 通过头Id删除
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 16:01
     */
    @Override
    public int deleteByHeaderId(String headerId) {
        if(StringUtils.isBlank(headerId)) {
            return ZERO;
        }
        CustReceptionLcmCustomerDO customerDO = new CustReceptionLcmCustomerDO();
        customerDO.setHeaderId(headerId);
        customerDO.setEnabledFlag(BooleanEnum.N.getCode());
        customerDO.setLastUpdateDate(new Date());
        customerDO.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return extMapper.updateByHeaderIdSelective(customerDO);
    }

    /**
     * 批量插入客户LCM扫描信息
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2023/12/20 15:38
     */
    @Override
    public int batchInsert(List<CustReceptionLcmCustomerDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ZERO;
        }
        list.forEach(this::setDefaultValue);
        return extMapper.batchInsert(list);
    }


    /**
     * 设置默认值
     * @param lcmCustomerDO 实例
     * @return void
     * <AUTHOR>
     * date: 2023/12/20 16:03
     */
    private void setDefaultValue(CustReceptionLcmCustomerDO lcmCustomerDO) {
        lcmCustomerDO.setRowId(Optional.ofNullable(lcmCustomerDO.getRowId()).orElse(iKeyIdService.getKeyId()));
        lcmCustomerDO.setCreatedBy(Optional.ofNullable(lcmCustomerDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        lcmCustomerDO.setLastUpdatedBy(Optional.ofNullable(lcmCustomerDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        lcmCustomerDO.setCreationDate(new Date());
        lcmCustomerDO.setLastUpdateDate(new Date());
        lcmCustomerDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

}
