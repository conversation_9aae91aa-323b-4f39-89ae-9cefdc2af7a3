package com.zte.mcrm.activity.service.activitylist.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/17 13:43
 */
@Setter
@Getter
@ToString
public class ActivityRelationZtePeopleQuery {

    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 人员类型/角色。如：创建人、责任人、讲师、专家……枚举：ActivityPeopleTypeEnum快码：Activity_People_Type_Enum */
    private String peopleType;

    /** 人员编码。如：参与人的员工编码 */
    private String peopleCode;

    /** 人员名称 */
    private String peopleName;

    /** 姓名英文 */
    private String peopleNameEn;

    /** 岗位名称 */
    private String positionName;

    /** 部门属性 */
    private String deptAttribute;

    /** 部门名称全路径 */
    private String deptFullName;

    /** 与活动授权被提及的次数，如：评论中@时，评论被删除这里减1。0则无权访问活动了 */
    private Integer authCount;

    /** 查询条件，权限最小值需大于该值,默认为0 */
    private Integer minAuthCount = ZERO;

    /** 人员简述 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;
}
