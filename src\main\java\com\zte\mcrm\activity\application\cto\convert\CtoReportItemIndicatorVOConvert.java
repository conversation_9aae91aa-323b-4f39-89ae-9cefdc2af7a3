package com.zte.mcrm.activity.application.cto.convert;


import com.zte.mcrm.activity.application.model.CtoReportItemIndicatorVO;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * 转换器类，用于构建和设置CtoReportItemIndicatorVO对象。
 */
public class CtoReportItemIndicatorVOConvert {

    // 使用静态不可变列表存储月份对应的设置方法
    private static final List<BiConsumer<CtoReportItemIndicatorVO, Integer>> MONTH_SETTERS = Collections.unmodifiableList(Arrays.asList(
            CtoReportItemIndicatorVO::setMonthFinish1,
            CtoReportItemIndicatorVO::setMonthFinish2,
            CtoReportItemIndicatorVO::setMonthFinish3,
            CtoReportItemIndicatorVO::setMonthFinish4,
            CtoReportItemIndicatorVO::setMonthFinish5,
            CtoReportItemIndicatorVO::setMonthFinish6,
            CtoReportItemIndicatorVO::setMonthFinish7,
            CtoReportItemIndicatorVO::setMonthFinish8,
            CtoReportItemIndicatorVO::setMonthFinish9,
            CtoReportItemIndicatorVO::setMonthFinish10,
            CtoReportItemIndicatorVO::setMonthFinish11,
            CtoReportItemIndicatorVO::setMonthFinish12
    ));

    /**
     * 构建初始化的 CtoReportItemIndicatorVO 对象。
     *
     * @param productName  产品名称
     * @param empName      员工姓名
     * @param empPosition  员工职位
     * @return 初始化后的 CtoReportItemIndicatorVO 对象
     */
    public static CtoReportItemIndicatorVO buildListActivationInit(String productName, String empName, String empPosition) {
        CtoReportItemIndicatorVO vo = new CtoReportItemIndicatorVO();
        vo.setProductName(productName);
        vo.setEmpName(empName);
        vo.setEmpPosition(empPosition);
        return vo;
    }

    /**
     * 指标构建-名单盘活
     *
     * @param vo          CtoReportItemIndicatorVO 对象
     * @param month       月份编号（1-12）
     * @param monthFinish 完成数量
     */
    public static void fillMonthCount(CtoReportItemIndicatorVO vo, int month, int monthFinish) {
        // 检查月份是否在有效范围内
        if (month >= 1 && month <= 12) {
            // 获取对应月份的设置方法并执行
            MONTH_SETTERS.get(month - 1).accept(vo, monthFinish);
        } else {
            throw new IllegalArgumentException("Invalid month: " + month + ". Month must be between 1 and 12.");
        }
    }
}
