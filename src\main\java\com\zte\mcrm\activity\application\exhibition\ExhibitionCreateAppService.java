package com.zte.mcrm.activity.application.exhibition;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.repository.model.exhibition.param.ExhibitionSubmitDO;
import com.zte.mcrm.activity.web.controller.exhibition.param.ExhibitionSubmitParam;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.HrConditionVO;
import com.zte.mcrm.adapter.vo.MdmAreaVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ExhibitionCreateAppService
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会创建接口类
 * @date 2023/9/619:19
 */
public interface ExhibitionCreateAppService {

    /**
     * 展会提交信息
     * @param request 新增展会业务入参
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    String exhibitionSubmit(BizRequest<ExhibitionSubmitParam> request);

    /**
     * 新增展会信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/9/15
     */
    String insertExhibition(ExhibitionSubmitDO exhibitionSubmitDO);

    /**
     * 获取展会负责人二层组织数据列表
     * @param hrConditionVO queryType 查询类型Q：0001:查询组织数据/hrLevel 组织层级/queryVer 版本
     * @throws Exception
     * @return
     * <AUTHOR>
     * @date 2023/9/8
     */
    List<OrgInfoVO>  getDirectorAuth(HrConditionVO hrConditionVO);

    /**
     * 输入城市获取国家城市组件接口
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023/9/18
     */
    List<MdmAreaDTO> queryCountryAndCity(BizRequest<MdmAreaVO> request);

    /**
     * 更新展会信息
     * @param request 更新展会业务入参
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    String exhibitionUpdate(BizRequest<ExhibitionSubmitParam> request);


    /**
     * 输入城市获取国家OR城市组件接口
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023/9/18
     */
    List<MdmAreaDTO> queryCountryOrCity(BizRequest<MdmAreaVO> request);

}
