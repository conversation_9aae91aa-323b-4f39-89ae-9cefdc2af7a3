package com.zte.mcrm.activity.common.util;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.BizResult;

import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class BizResultUtil {

    /**
     * @param res
     * @param <T>
     * @return
     */
    public static <T> ServiceData<T> toServiceData(BizResult<T> res) {

        return toServiceData(res, Function.identity());
    }

    /**
     * @param res
     * @param convert
     * @param <F>
     * @param <R>
     * @return
     */
    public static <F, R> ServiceData<R> toServiceData(BizResult<F> res, Function<F, R> convert) {
        RetCode r = new RetCode();
        r.setCode(res.getCode());
        r.setMsg(res.getMsg());

        ServiceData<R> s = new ServiceData<>();
        s.setCode(r);
        s.setOther(res.getExtMsg());

        if (convert != null && res.getData() != null) {
            s.setBo(convert.apply(res.getData()));
        }

        return s;
    }

}
