package com.zte.mcrm.activity.repository.model.item;

import java.util.Date;

/**
 * table:activity_schedule_orchestration_detail -- 
 */
public class ActivityScheduleOrchestrationDetailDO {
    /** 主键 */
    private String rowId;

    /** 活动日程事项编排版本信息rowId */
    private String orchestrationRowId;

    /** 关联日程ID */
    private String scheduleItemRowId;

    /** 活动ID */
    private String activityRowId;

    /** 会见日期 */
    private Date scheduleDate;

    /** 日程排时间。形如：10:10~10:40 */
    private String scheduleTime;

    /** 会见地点类型。见：ScheduleItemPlaceTypeEnum。room,other */
    private String placeType;

    /** 会见地点名称 */
    private String placeName;

    /** 会见我司领导。员工编号，多个英文逗号隔开 */
    private String zteLeader;

    /** 会见我司专家。员工编号，多个英文逗号隔开 */
    private String zteExpert;

    /** 资源编排处理状态。见：ResourceOrchestrationDealStatusEnum */
    private String dealStatus;

    /** 资源编排处理意见 */
    private String dealNote;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    /** 备注信息 */
    private String remark;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getOrchestrationRowId() {
        return orchestrationRowId;
    }

    public void setOrchestrationRowId(String orchestrationRowId) {
        this.orchestrationRowId = orchestrationRowId == null ? null : orchestrationRowId.trim();
    }

    public String getScheduleItemRowId() {
        return scheduleItemRowId;
    }

    public void setScheduleItemRowId(String scheduleItemRowId) {
        this.scheduleItemRowId = scheduleItemRowId == null ? null : scheduleItemRowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public Date getScheduleDate() {
        return scheduleDate;
    }

    public void setScheduleDate(Date scheduleDate) {
        this.scheduleDate = scheduleDate;
    }

    public String getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(String scheduleTime) {
        this.scheduleTime = scheduleTime == null ? null : scheduleTime.trim();
    }

    public String getPlaceType() {
        return placeType;
    }

    public void setPlaceType(String placeType) {
        this.placeType = placeType == null ? null : placeType.trim();
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName == null ? null : placeName.trim();
    }

    public String getZteLeader() {
        return zteLeader;
    }

    public void setZteLeader(String zteLeader) {
        this.zteLeader = zteLeader == null ? null : zteLeader.trim();
    }

    public String getZteExpert() {
        return zteExpert;
    }

    public void setZteExpert(String zteExpert) {
        this.zteExpert = zteExpert == null ? null : zteExpert.trim();
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus == null ? null : dealStatus.trim();
    }

    public String getDealNote() {
        return dealNote;
    }

    public void setDealNote(String dealNote) {
        this.dealNote = dealNote == null ? null : dealNote.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}