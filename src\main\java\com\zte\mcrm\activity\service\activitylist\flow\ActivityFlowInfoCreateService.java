package com.zte.mcrm.activity.service.activitylist.flow;

import com.zte.mcrm.activity.common.enums.activity.ActivityFlowNodeEnum;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;

/**
 * 活动流程节点信息创建服务
 *
 * <AUTHOR>
 */
public interface ActivityFlowInfoCreateService {


    /**
     * 获取节点类型
     *
     * @return
     */
    ActivityFlowNodeEnum getFlowNode();

    /**
     * 创建节点信息
     *
     * @param data
     * @return
     */
    ActivityFlowInfoVO createActivityFlowInfo(ActivityFlowInfoDataSource data);

}
