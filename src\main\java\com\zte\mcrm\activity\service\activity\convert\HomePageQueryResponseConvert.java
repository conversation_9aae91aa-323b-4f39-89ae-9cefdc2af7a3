package com.zte.mcrm.activity.service.activity.convert;

import com.google.common.collect.Lists;
import com.zte.mcrm.isearch.model.dto.activity.AppActivityViewNumDTO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryResponseVO;

import java.util.Optional;

/**
 * 活动信息统计转换
 *
 * <AUTHOR>
 * @date 2024/2/5 上午11:02
 */
public class HomePageQueryResponseConvert {

    /**
     * 获取初始化的返回值（与原接口返回值保持一致）
     *
     * @return {@link HomePageQueryResponseVO}
     * <AUTHOR>
     * @date 2024/2/5 下午2:27
     */
    public static HomePageQueryResponseVO initResponse() {
        HomePageQueryResponseVO responseVO = new HomePageQueryResponseVO();
        responseVO.setActivityInfo(new AppActivityViewNumDTO());
        responseVO.setAttenderInfo(Lists.newArrayList());
        responseVO.setSchemeInfo(Lists.newArrayList());
        return responseVO;
    }

    /**
     * 合并统计项，当前只合并活动数据，其他数据以第一项为准（其他数据融合活动先不统计）
     *
     * @param first     不能为空
     * @param second    不能为空
     * @return {@link HomePageQueryResponseVO}
     * <AUTHOR>
     * @date 2024/2/5 上午10:51
     */
    public static HomePageQueryResponseVO merge(HomePageQueryResponseVO first, HomePageQueryResponseVO second) {
        AppActivityViewNumDTO mergeActivityNum = mergeActivityNum(first.getActivityInfo(), second.getActivityInfo());

        HomePageQueryResponseVO mergeVO = new HomePageQueryResponseVO();
        mergeVO.setActivityInfo(mergeActivityNum);
        mergeVO.setAttenderInfo(first.getAttenderInfo());
        mergeVO.setSchemeInfo(first.getSchemeInfo());
        return mergeVO;
    }

    /**
     * 合并活动数量统计
     *
     * @param first
     * @param second
     * @return {@link AppActivityViewNumDTO}
     * <AUTHOR>
     * @date 2024/2/5 上午11:02
     */
    private static AppActivityViewNumDTO mergeActivityNum(AppActivityViewNumDTO first, AppActivityViewNumDTO second) {
        AppActivityViewNumDTO firstActivityNum = Optional.ofNullable(first).orElse(new AppActivityViewNumDTO());
        AppActivityViewNumDTO secondActivityNum = Optional.ofNullable(second).orElse(new AppActivityViewNumDTO());
        int mergeEndNum = firstActivityNum.getEndNum() + secondActivityNum.getEndNum();
        int mergeIngNum = firstActivityNum.getIngNum() + secondActivityNum.getIngNum();
        int mergePlanNum = firstActivityNum.getPlanNum() + secondActivityNum.getPlanNum();
        AppActivityViewNumDTO mergeActivityNum = new AppActivityViewNumDTO();
        mergeActivityNum.setEndNum(mergeEndNum);
        mergeActivityNum.setIngNum(mergeIngNum);
        mergeActivityNum.setPlanNum(mergePlanNum);
        return mergeActivityNum;
    }
}
