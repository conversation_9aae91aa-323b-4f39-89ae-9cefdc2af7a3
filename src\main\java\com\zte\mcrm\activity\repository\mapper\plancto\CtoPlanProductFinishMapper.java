package com.zte.mcrm.activity.repository.mapper.plancto;

/**
 * <AUTHOR>
 * @date 2024年12月09日15:46
 */
/* Started by AICoder, pid:re95bdf913a1fa9142a9092e80146823e9334966 */
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CtoPlanProductFinishMapper {

    int batchInsert(@Param("list") List<CtoPlanProductFinishDO> list);

    int insertSelective(CtoPlanProductFinishDO record);

    int batchUpdate(@Param("list") List<CtoPlanProductFinishDO> list);

    int updateByPrimaryKeySelective(CtoPlanProductFinishDO record);

    List<CtoPlanProductFinishDO> selectByPrimaries(@Param("list") List<String> primaries);
}

/* Ended by AICoder, pid:re95bdf913a1fa9142a9092e80146823e9334966 */
