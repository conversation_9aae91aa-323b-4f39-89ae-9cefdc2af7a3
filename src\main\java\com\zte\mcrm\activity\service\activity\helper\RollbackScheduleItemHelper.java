package com.zte.mcrm.activity.service.activity.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginDetailDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginVersionDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemOriginDetailRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemOriginVersionRepository;
import com.zte.mcrm.activity.service.activity.convert.ScheduleItemModelConvert;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemModel;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemPeopleModel;
import com.zte.mcrm.activity.service.activity.model.ScheduleItemModel;
import com.zte.mcrm.activity.service.schedule.model.ActivityScheduleItemData;
import com.zte.mcrm.activity.web.controller.exhibition.param.app.ExhibitionScheduleParam;
import com.zte.mcrm.activity.web.controller.exhibition.param.app.ExhibitionScheduleUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * {@code @description 回退日程辅助类}
 *
 * <AUTHOR>
 * @date 2025/1/17 下午3:34
 */
@Slf4j
@Component
public class RollbackScheduleItemHelper {
    @Autowired
    private ActivityScheduleItemOriginVersionRepository activityScheduleItemOriginVersionRepository;
    @Autowired
    private ActivityScheduleItemOriginDetailRepository activityScheduleItemOriginDetailRepository;
    @Autowired
    private SyncUpScheduleItemHelper syncUpScheduleItemHelper;

    /**
     * 回退日程版本
     *
     * @param scheduleUpdateParam
     * @return
     */
    public boolean rollbackScheduleItem(ExhibitionScheduleUpdateParam scheduleUpdateParam) {
        String activityRowId = scheduleUpdateParam.getActivityId();
        String activityScheduleItemRowId = scheduleUpdateParam.getScheduleId();

        //获取回退的日程版本信息
        Map<String, ActivityScheduleItemOriginVersionDO> relationScheduleItemOriginMap = activityScheduleItemOriginVersionRepository.getRelationScheduleItemOriginInfos(Collections.singletonList(activityRowId), 0);
        ActivityScheduleItemOriginVersionDO activityScheduleItemOriginVersionDO = relationScheduleItemOriginMap.get(activityRowId);
        if (Objects.isNull(activityScheduleItemOriginVersionDO)) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.item.no.version");
        }
        //获取版本对应的日程备份
        String versionRowId = activityScheduleItemOriginVersionDO.getRowId();
        Map<String, List<ActivityScheduleItemOriginDetailDO>> relationScheduleItemOriginDetails = activityScheduleItemOriginDetailRepository.getRelationScheduleItemOriginDetails(Collections.singletonList(versionRowId));
        List<ActivityScheduleItemOriginDetailDO> activityScheduleItemOriginDetailDOS = relationScheduleItemOriginDetails.get(versionRowId);

        if (CollectionUtils.isEmpty(activityScheduleItemOriginDetailDOS)) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.item.no.detail");
        }
        //匹配指定日程ID的备份日程数据
        Optional<ActivityScheduleItemOriginDetailDO> scheduleItemOriginDetailOpt = activityScheduleItemOriginDetailDOS.stream().filter(t -> StringUtils.equals(activityScheduleItemRowId, t.getScheduleItemRowId())).findFirst();
        if (!scheduleItemOriginDetailOpt.isPresent()) {
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "exhibition.schedule.item.no.detail");
        }
        String sourceData = scheduleItemOriginDetailOpt.get().getSourceData();

        //打包封装同步日程数据
        BizRequest<ScheduleItemModel> scheduleRequest = this.packSyncUpScheduleItemData(sourceData);
        //同步一线活动
        syncUpScheduleItemHelper.syncUpActivity(scheduleRequest);
        //同步一线日程
        syncUpScheduleItemHelper.syncUpActivityScheduleItem(scheduleRequest);
        return true;
    }

    /**
     * 打包封装同步日程数据
     *
     * @param sourceData 源日程数据
     */
    BizRequest<ScheduleItemModel> packSyncUpScheduleItemData(String sourceData) {
        ScheduleItemModel scheduleItemModel = new ScheduleItemModel();
        ActivityScheduleItemData activityScheduleItemData = JSON.parseObject(sourceData, ActivityScheduleItemData.class);
        if (Objects.isNull(activityScheduleItemData)) {
            return BizRequestUtil.createWithCurrentUser(scheduleItemModel);
        }

        ActivityScheduleItemDO oldItemDO = activityScheduleItemData.getScheduleItem();
        List<ActivityScheduleItemPeopleDO> oldItemPeopleDOList = activityScheduleItemData.getItemPeopleList();

        ActivityScheduleItemModel activityScheduleItemModel = ScheduleItemModelConvert.INSTANCE.scheduleItemDOToModel(oldItemDO);
        List<ActivityScheduleItemPeopleModel> activityScheduleItemPeopleModels = ScheduleItemModelConvert.INSTANCE.scheduleItemPeopleDOToModel(oldItemPeopleDOList);

        scheduleItemModel.setActivityScheduleItemModels(Lists.newArrayList(activityScheduleItemModel));
        activityScheduleItemModel.setScheduleItemPeopleModels(activityScheduleItemPeopleModels);

        return BizRequestUtil.createWithCurrentUser(scheduleItemModel);
    }
}
