package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityKeyModifyLogDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动关键信息修改日志
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityKeyModifyLogRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityKeyModifyLogDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityKeyModifyLogDO record);
    
    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityKeyModifyLogDO> queryAllByActivityRowId(String activityRowId);
}