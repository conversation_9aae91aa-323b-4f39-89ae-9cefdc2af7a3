package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginDetailDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleItemOriginDetailExtMapper extends ActivityScheduleItemOriginDetailMapper {

    /**
     * 获取对应版本的详细日程源信息
     *
     * @param scheduleItemOriginVersionRowIds
     * @return
     */
    List<ActivityScheduleItemOriginDetailDO> getRelationScheduleItemOriginDetails(@Param("scheduleItemOriginVersionRowIds") List<String> scheduleItemOriginVersionRowIds);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<ActivityScheduleItemOriginDetailDO> list);

}