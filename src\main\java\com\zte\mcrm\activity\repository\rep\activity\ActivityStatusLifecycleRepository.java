package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;

import java.util.List;

/**
 * 活动状态生命周期
 *
 * <AUTHOR>
 */
public interface ActivityStatusLifecycleRepository {

    /**
     * 添加活动状态记录（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivityStatusLifecycleDO> recordList);

    /**
     * 添加活动状态记录（如果没有主键，自动生成,不赋予默认值，方便数据迁移）
     * <AUTHOR>
     * @param recordList
     */
    int insertSelectiveWithoutDefaultValue(List<ActivityStatusLifecycleDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityStatusLifecycleDO record);

    /**
     * 查询所有活动状态变更记录
     *
     * @param activityRowId 拓展活动rowId
     * @return
     */
    List<ActivityStatusLifecycleDO> queryStatusForActivity(String activityRowId);

    int deleteByActivityIds(String operator, List<String> activityIds);
}
