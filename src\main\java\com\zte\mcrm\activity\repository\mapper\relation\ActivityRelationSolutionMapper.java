package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;

import java.util.List;

public interface ActivityRelationSolutionMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationSolutionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationSolutionDO record);

    /**
     * query by primary key
     */
    ActivityRelationSolutionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationSolutionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationSolutionDO record);
}