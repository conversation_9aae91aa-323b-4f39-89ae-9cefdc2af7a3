package com.zte.mcrm.activity.repository.model.activity;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * table:activity_info -- 
 */
public class ActivityInfoDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动申请单号 */
    private String activityRequestNo;

    /** 拓展活动议题 */
    private String activityTitle;

    /** 拓展活动类型。枚举：ActivityTypeEnum，快码：Activity_Type_Enum */
    private String activityType;

    /** 拓展活动状态。枚举：ActivityStatusEnum */
    private String activityStatus;

    /** 交流方式。枚举：ActivityCommunicationWayEnum */
    private String communicationWay;

    /** 活动起始时间。显示按yyyy-MM-dd HH:mm格式 */
    private Date startTime;

    /** 活动截止时间。显示按yyyy-MM-dd HH:mm格式 */
    private Date endTime;

    /** 提单申请时间 */
    private Date submitTime;

    /** 活动地点 */
    private String activityPlace;

    /** 活动主要客户 */
    private String customerCode;

    /** 申请人员工编号 */
    private String applyPeopleNo;

    /** 申请人员工姓名 */
    private String applyPeopleName;

    /** 申请人部门编码 */
    private String applyDepartmentNo;

    /** 是否代提单Y-是，N-否。枚举：BooleanEnum。（创建人不等于申请人） */
    private String agentApply;

    /** 租户ID */
    private String tenantId;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 交流层级。高端交流，地市交流，一般交流。见枚举：CommunicationLevelEnum */
    private String communicationLevel;

    /** 活动内容概要 */
    private String activityContent;

    /** 活动来源，ActivityOriginTypeEnum */
    private String originType;

    /** 活动来源关联rowId，如：关联展会信息row_id */
    private String originRowId;

    /** 申请部门全路径编码 */
    private String applyFullDepartmentNo;
    /** 交流城市 */
    private String cityCode;

    /** 交流国家 */
    private String countryCode;

    /** 活动交流城市名称 */
    private String cityCodeName;

    /** 活动交流城市所在国家 */
    private String countryCodeName;

    /** 审核人信息，用于保存活动时的审核人 */
    private String approvalText;

    @ApiModelProperty(value = "主要客户的国家简称")
    private String countryShortName;

    /** 老数据来源 */
    private String oldDataSource;

    /**
     * 是否需要客户部提供酒店，Y是，N否，BooleanEnum
     */
    private String provideHotelFlag;

    /**
     * 是否需要客户部提供车辆，Y是，N否，BooleanEnum
     */
    private String provideCarFlag;

    /** 排序键 */
    private String uniqueSortKeyDownId;

    public String getUniqueSortKeyDownId() {
        return uniqueSortKeyDownId;
    }

    public void setUniqueSortKeyDownId(String uniqueSortKeyDownId) {
        this.uniqueSortKeyDownId = uniqueSortKeyDownId;
    }

    public String getApplyPeopleName() {
        return applyPeopleName;
    }

    public void setApplyPeopleName(String applyPeopleName) {
        this.applyPeopleName = applyPeopleName;
    }

    public String getCountryShortName() {
        return countryShortName;
    }

    public void setCountryShortName(String countryShortName) {
        this.countryShortName = countryShortName;
    }

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRequestNo() {
        return activityRequestNo;
    }

    public void setActivityRequestNo(String activityRequestNo) {
        this.activityRequestNo = activityRequestNo == null ? null : activityRequestNo.trim();
    }

    public String getActivityTitle() {
        return activityTitle;
    }

    public void setActivityTitle(String activityTitle) {
        this.activityTitle = activityTitle == null ? null : activityTitle.trim();
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus == null ? null : activityStatus.trim();
    }

    public String getCommunicationWay() {
        return communicationWay;
    }

    public void setCommunicationWay(String communicationWay) {
        this.communicationWay = communicationWay == null ? null : communicationWay.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getActivityPlace() {
        return activityPlace;
    }

    public void setActivityPlace(String activityPlace) {
        this.activityPlace = activityPlace == null ? null : activityPlace.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getApplyPeopleNo() {
        return applyPeopleNo;
    }

    public void setApplyPeopleNo(String applyPeopleNo) {
        this.applyPeopleNo = applyPeopleNo == null ? null : applyPeopleNo.trim();
    }

    public String getApplyDepartmentNo() {
        return applyDepartmentNo;
    }

    public void setApplyDepartmentNo(String applyDepartmentNo) {
        this.applyDepartmentNo = applyDepartmentNo == null ? null : applyDepartmentNo.trim();
    }

    public String getAgentApply() {
        return agentApply;
    }

    public void setAgentApply(String agentApply) {
        this.agentApply = agentApply == null ? null : agentApply.trim();
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getCommunicationLevel() {
        return communicationLevel;
    }

    public void setCommunicationLevel(String communicationLevel) {
        this.communicationLevel = communicationLevel == null ? null : communicationLevel.trim();
    }

    public String getActivityContent() {
        return activityContent;
    }

    public void setActivityContent(String activityContent) {
        this.activityContent = activityContent == null ? null : activityContent.trim();
    }

    public String getOriginType() {
        return originType;
    }

    public void setOriginType(String originType) {
        this.originType = originType;
    }

    public String getOriginRowId() {
        return originRowId;
    }

    public void setOriginRowId(String originRowId) {
        this.originRowId = originRowId;
    }

    public String getApplyFullDepartmentNo() {
        return applyFullDepartmentNo;
    }

    public void setApplyFullDepartmentNo(String applyFullDepartmentNo) {
        this.applyFullDepartmentNo = applyFullDepartmentNo;
    }

    public String getApprovalText() {
        return approvalText;
    }

    public void setApprovalText(String approvalText) {
        this.approvalText = approvalText;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCityCodeName() {
        return cityCodeName;
    }

    public void setCityCodeName(String cityCodeName) {
        this.cityCodeName = cityCodeName;
    }

    public String getCountryCodeName() {
        return countryCodeName;
    }

    public void setCountryCodeName(String countryCodeName) {
        this.countryCodeName = countryCodeName;
    }

    public String getOldDataSource() {
        return oldDataSource;
    }

    public void setOldDataSource(String oldDataSource) {
        this.oldDataSource = oldDataSource == null ? null : oldDataSource.trim();
    }

    public String getProvideHotelFlag() {
        return provideHotelFlag;
    }

    public void setProvideHotelFlag(String provideHotelFlag) {
        this.provideHotelFlag = provideHotelFlag == null ? null : provideHotelFlag.trim();
    }

    public String getProvideCarFlag() {
        return provideCarFlag;
    }

    public void setProvideCarFlag(String provideCarFlag) {
        this.provideCarFlag = provideCarFlag == null ? null : provideCarFlag.trim();
    }
}