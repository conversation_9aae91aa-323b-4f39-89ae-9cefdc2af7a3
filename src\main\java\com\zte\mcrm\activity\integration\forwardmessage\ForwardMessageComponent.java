package com.zte.mcrm.activity.integration.forwardmessage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.itp.msa.message.SpringKafkaProducer;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.integration.forwardmessage.param.MessageBodyParam;
import com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam;
import com.zte.mcrm.activity.service.common.dict.EmailAddressComponent;
import com.zte.mcrm.adapter.UserCenterPgAdapter;
import com.zte.mcrm.custcomm.access.vo.Template;
import com.zte.mcrm.custcomm.business.SendMailService;
import com.zte.mcrm.custcomm.common.MonitorKafkaMessageStatus;
import com.zte.mcrm.custcomm.common.RetCode;
import com.zte.mcrm.expansion.common.util.FormatUtils;
import com.zte.mcrm.sys.access.dao.SysEmailFormatTemplateDao;
import com.zte.mcrm.sys.access.vo.SysEmailFormatTemplateVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/17 16:15
 */
@Component
public class ForwardMessageComponent {

    private final Logger logger = LoggerFactory.getLogger(ForwardMessageComponent.class);

    @Autowired
    private SpringKafkaProducer kafkaProducer;

    @Value("${icenter.imessage.topic}")
    private String icenterImessageTopic;

    @Autowired
    SendMailService sendMailService;

    @Autowired
    private MonitorKafkaMessageStatus monitorKafkaMessageStatus;

    @Autowired
    private SysEmailFormatTemplateDao sysEmailFormatTemplateDao;

    @Autowired
    private EmailAddressComponent emailAddressComponent;

    @Autowired
    UserCenterPgAdapter userCenterPgAdapter;

    public boolean forwardMessageToICenter(MessageBodyParam messageBodyParam) {
        Object contactsData = messageBodyParam.getContactsData();
        Object groupsData = messageBodyParam.getGroupsData();
        String str = "";
        boolean result = false;

        try {
            // 转发给iCenter群组
            if (null != groupsData) {
                str = JSONObject.toJSONString(groupsData);
                result = kafkaProducer.sendMessage(icenterImessageTopic, "", str, monitorKafkaMessageStatus);
            }
            // 转发给iCenter联系人
            if (null != contactsData) {
                str = JSONObject.toJSONString(contactsData);
                result = kafkaProducer.sendMessage(icenterImessageTopic, "", str, monitorKafkaMessageStatus);
            }
        } catch (Exception e) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.forward.info.fail");
        }
        return result;
    }

    public boolean forwardMessageToZMail(MsaRpcRequest<ZmailBodyParam> request) {
        ZmailBodyParam requestBody = request.getBody();
        String headerNo = request.getHeader(RequestHeaderConstant.X_EMP_NO);
        String empNo = StringUtils.isNotBlank(headerNo) ? headerNo : requestBody.getSendEmpNo();
        ZmailBodyParam zmailBodyParam = request.getBody();

        String templateName = zmailBodyParam.getTemplateName();
        if (StringUtils.isBlank(templateName) || StringUtils.isBlank(empNo)) {
            return false;
        }
        // 获取邮件模板
        SysEmailFormatTemplateVO sysEmailFormatTemplateVO = sysEmailFormatTemplateDao
                .getSysEmailFormatTemplateBySerialNumbers(templateName);

        if(null == sysEmailFormatTemplateVO){
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.email.template.is.null");
        }
        // 构造Template对象
        Template template = new Template();
        template.setImportance(0);
        template.setSubscribe(0);
        template.setLanguage(1);
        template.setPriority(30);
        template.setMailToIsOne(false);

        // 点击查看
        String[] clickLook = {sysEmailFormatTemplateVO.getClickLook()};
        template.setClickLook(clickLook);

        Map<String, Object> templateParamMap = zmailBodyParam.getTemplateParamMap();
        // 发件人
        try {
            /* Started by AICoder, pid:tf603ze18bae97b1471909d390d8781eb8f65bf0 */
            // 获取员工公司邮箱地址
            String emailFull = userCenterPgAdapter.getEmailFullByEmpNo(empNo);
            String emailAddress = StringUtils.isNotBlank(emailFull)
                    ? emailFull
                    : emailAddressComponent.fetchDefaultEmailFromAddress();
            template.setMailFrom(emailAddress);
            /* Ended by AICoder, pid:tf603ze18bae97b1471909d390d8781eb8f65bf0 */
        } catch (Exception e) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.search.user.email.fail");
        }
        // 收件人
        template.setMailTo(zmailBodyParam.getReceiveEmpNo());
        // 抄送人
        template.setMailCC(zmailBodyParam.getCcEmpNo());
        // url链接地址
        String linkUrl = zmailBodyParam.getLinkUrl();
        String[] linkAdd = {linkUrl};
        template.setLinkAdd(linkAdd);

        // 系统名称，便于以后做统计的 iSales_拓展活动
        template.setTitle(FormatUtils.getStringReplaceEl(sysEmailFormatTemplateVO.getEmailTitle(), templateParamMap));

        // 邮件内容
        String[] mainText = {FormatUtils.getStringReplaceEl(sysEmailFormatTemplateVO.getEmailBody(), templateParamMap)};
        template.setMainText(mainText);

        // 邮件中看到的系统名称
        String[] systemName = {sysEmailFormatTemplateVO.getRemark()};
        template.setMailSysName(systemName);

        logger.info("sendSysEmailBySerialNumbers template:{}", JSON.toJSONString(template));
        return sendMailService.sendMail(template);
    }
}
