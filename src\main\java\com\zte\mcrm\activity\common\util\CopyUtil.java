package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.expansion.access.external.UserInfoVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CopyUtil
 * @description: 简单对象复制工具
 * @author: 李龙10317843
 * @create: 2023-05-18 10:45
 * @Version 1.0
 **/
@Log4j2
public class CopyUtil {

    /**
     * 拷贝对象
     *
     * @param source
     * @param classType
     * @return
     */
    public static <T, E> E copy(T source, Class<E> classType) {

        if (source == null) {
            return null;
        }
        E targetInstance;
        try {
            targetInstance = classType.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        BeanUtils.copyProperties(source, targetInstance);
        return targetInstance;
    }

    /**
     * 拷贝数组对象
     *
     * @param sourceList
     * @param classType
     * @return
     */
    public static <T, E> List<E> copyList(List<T> sourceList, Class<E> classType) {
        if (sourceList == null) {
            return null;
        }
        List<E> result = new ArrayList<E>();
        for (T t : sourceList) {
            result.add(copy(t, classType));
        }
        return result;
    }

}
