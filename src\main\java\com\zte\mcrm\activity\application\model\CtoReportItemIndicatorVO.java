package com.zte.mcrm.activity.application.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zte.mcrm.activity.common.constant.CTOReportHeaderConstants;
import lombok.*;

/* Started by AICoder, pid:79b23k48a0qda8a14c6d0a1060b8d2826589713f */
/**
 * CTO报表名单盘活指标
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CtoReportItemIndicatorVO {

    /**
     * 产品名称
     */
    @ExcelProperty(CTOReportHeaderConstants.PRODUCT_NAME)
    private String productName;

    /**
     * 员工姓名
     */
    @ExcelProperty(CTOReportHeaderConstants.EMP_NAME)
    private String empName;

    /**
     * 员工职位
     */
    @ExcelProperty(CTOReportHeaderConstants.EMP_POSITION)
    private String empPosition;

    /**
     * 全年累计
     */
    @ExcelProperty(CTOReportHeaderConstants.YEAR_SUM)
    private Integer yearSum;

    /**
     * 每月完成情况
     */
    @ExcelProperty(CTOReportHeaderConstants.monthFinish1)
    private Integer monthFinish1;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish2)
    private Integer monthFinish2;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish3)
    private Integer monthFinish3;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish4)
    private Integer monthFinish4;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish5)
    private Integer monthFinish5;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish6)
    private Integer monthFinish6;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish7)
    private Integer monthFinish7;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish8)
    private Integer monthFinish8;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish9)
    private Integer monthFinish9;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish10)
    private Integer monthFinish10;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish11)
    private Integer monthFinish11;
    @ExcelProperty(CTOReportHeaderConstants.monthFinish12)
    private Integer monthFinish12;


}

/* Ended by AICoder, pid:79b23k48a0qda8a14c6d0a1060b8d2826589713f */
