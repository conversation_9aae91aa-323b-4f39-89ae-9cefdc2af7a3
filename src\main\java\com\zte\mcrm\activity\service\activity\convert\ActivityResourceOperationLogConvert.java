package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationTypeEnum;
import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityResourceOperationLogVO;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityCustomerInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/11/19 15:33
 * @description ：活动资源操作日志转换
 */
public class ActivityResourceOperationLogConvert {

    private ActivityResourceOperationLogConvert() {

    }

    public static List<ActivityResourceOperationLogVO> convert2VO(List<ActivityResourceOperationLogDO> operationLogDOList) {
        if (CollectionUtils.isEmpty(operationLogDOList)) {
            return Collections.emptyList();
        }
        return operationLogDOList.stream().map(item -> {
            ActivityResourceOperationLogVO vo = new ActivityResourceOperationLogVO();
            BeanUtils.copyProperties(item, vo);
            vo.setOperationTypeName(ActivityResourceOperationTypeEnum.getNameByCode(item.getOperationType()));
            vo.setTalkFileName(item.getOperationBeforeDesc());
            return vo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(ActivityResourceOperationLogVO::getCreationDate).reversed())), ArrayList::new));
    }
}
