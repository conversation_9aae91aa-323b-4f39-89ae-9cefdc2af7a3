package com.zte.mcrm.activity.application.exhibition.business;

import org.apache.commons.lang3.StringUtils;

import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDrillDownDTO;

/**
 * 展会分析 - 国家比较器
 * <AUTHOR>
 *
 */
public class ExhibitionOverseasBuComparator extends ExhibitionBaseComparator{

    @Override
    public int compare(ExhibitionAnalysisDrillDownDTO o1, ExhibitionAnalysisDrillDownDTO o2) {
        int compare = StringUtils.compare(o1.getCustomerLevel(), o2.getCustomerLevel());
        if(compare!=0){
            return compare;
        }
    	compare = StringUtils.compare(o1.getCountryType(),o2.getCountryType());
        return compare!=0?compare*(-1):super.compare(o1, o2);
    }
    
}
