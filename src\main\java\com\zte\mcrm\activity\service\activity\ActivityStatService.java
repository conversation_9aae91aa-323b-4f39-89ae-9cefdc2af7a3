package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityStatParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityRelationSamplePointVO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityVisitingSamplePointStatVO;

public interface ActivityStatService {

    /**
     * 参观样板点的活动列表
     * @param bizRequest
     * @return
     */
    PageRows<ActivityRelationSamplePointVO> getActivityRelationSamplePointList(BizRequest<PageQuery<ActivityStatParam>> bizRequest);

    /**
     * 参观样板点统计信息：参观次数和最后一次参观活动开始时间
     * @param bizRequest
     * @return
     */
    ActivityVisitingSamplePointStatVO getVisitingSamplePointStat(BizRequest<ActivityStatParam> bizRequest);

}
