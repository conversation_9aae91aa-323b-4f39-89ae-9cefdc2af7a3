package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;

public interface ActivityResourceHotelMapper {
    /**
     * all field insert
     */
    int insert(ActivityResourceHotelDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityResourceHotelDO record);

    /**
     * query by primary key
     */
    ActivityResourceHotelDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityResourceHotelDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceHotelDO record);
}