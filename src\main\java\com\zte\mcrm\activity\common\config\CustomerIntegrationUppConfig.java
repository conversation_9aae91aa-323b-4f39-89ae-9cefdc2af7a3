package com.zte.mcrm.activity.common.config;

import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 客户活动融合UPP相关配置<br/>
 * 部门相关配置（对于部门的信息，和zte-crm-account-info的 DepartmentConfig 保持一致）（客户融合活动上线前临时需求变更）
 *
 * <AUTHOR>
 * @date 2023/5/29 下午2:15
 */
@Component
@Getter
public class CustomerIntegrationUppConfig {

    /**
     * 约束-组织树-中兴组织_二层
     */
    @Value("${upp.constraint.id.org2:8100088}")
    private String org2ConstraintId;

    /**
     * 约束-组织树-中兴组织_四层
     */
    @Value("${upp.constraint.id.org4:1620004}")
    private String org4ConstraintId;

    /**
     * 约束-组织树-子公司
     */
    @Value("${upp.constraint.id.subsidiary:300001}")
    private String subsidiaryConstraintId;

    /**
     * 约束-交流方向
     */
    @Value("${upp.constraint.id.communication.direction:9510500}")
    private String communicationDirectionConstraintId;

    /**
     * 角色-客户融合-大项目接口人
     */
    @Value("${upp.role.code.large.team.person:CUSTOMER_INTEGRATION_LARGE_TEAM_PERSON}")
    private String customerIntegrationLargeTeamPersonRoleCode;

    /**
     * 角色-客户融合-crm主管
     */
    @Value("${upp.role.code.crm.manager:CRM_MANAGER}")
    private String customerIntegrationCrmManagerRoleCode;

    /**
     * 角色-客户融合-crm主管-非营销
     */
    @Value("${upp.role.code.crm.manager.no.mkt:CRM_MANAGER_NO_MKT}")
    private String customerIntegrationCrmManagerNoMktRoleCode;

    /**
     * 角色-客户融合-拓展活动管理员
     */
    @Value("${upp.role.code.activity.admin:activity_admin}")
    private String customerIntegrationActivityAdminRoleCode;

    /**
     * 角色-客户融合-拓展活动查询员
     */
    @Value("${upp.role.code.activity.inquirer:activity_inquirer}")
    private String customerIntegrationActivityInquirerRoleCode;

    /**
     * 角色-客户融合-crm主管-营销部门，逗号分隔，用于判断是否营销主管
     */
    @Value("${upp.role.code.crm.manager.mkt.dept:}")
    private String customerIntegrationCrmManagerMktDept;

    /**
     * 三营部门
     */
    @Value("${upp.constraint.value.sales3.dept:}")
    private String salesDivision3Dept;

    /**
     * 海外部门
     */
    @Value("${upp.constraint.value.international.dept:}")
    private String internationalDept;
    /**
     * 政企部门
     */
    @Value("${upp.constraint.value.government.dept:}")
    private String governmentEnterprise;

    /**
     * 三营+政企部门编码
     */
    @Value("${upp.constraint.value.sales3.government.deptCode:}")
    private String salesDivision3GovDeptCode;
    /**
     * 获取营销部门
     *
     * @return {@link Set< String>}
     * <AUTHOR>
     * @date 2023/6/3 下午5:47
     */
    public Set<String> getMktDeptSet() {
        if (StringUtils.isBlank(this.getCustomerIntegrationCrmManagerMktDept())) {
            return Sets.newHashSet();
        }
        return Arrays.stream(this.getCustomerIntegrationCrmManagerMktDept().split(CharacterConstant.COMMA))
                .collect(Collectors.toSet());
    }

    /**
     * 获取三营部门
     *
     * @return {@link Set< String>}
     * <AUTHOR>
     * @date 2023/6/3 下午5:48
     */
    public Set<String> getSalesDivision3DeptSet() {
        if (StringUtils.isBlank(this.getSalesDivision3Dept())) {
            return Sets.newHashSet();
        }
        return Arrays.stream(this.getSalesDivision3Dept().split(CharacterConstant.COMMA))
                .collect(Collectors.toSet());
    }

    /**
     * 获取海外营销部门
     *
     * @return {@link Set<String>}
     * <AUTHOR>
     * @date 2023/6/3 下午5:48
     */
    public Set<String> getInternationalDeptSet() {
        if (StringUtils.isBlank(this.getInternationalDept())) {
            return Sets.newHashSet();
        }
        return Arrays.stream(this.getInternationalDept().split(CharacterConstant.COMMA))
                .collect(Collectors.toSet());
    }

    /**
     * 获取政企部门
     * @return
     */
    public Set<String> getGovernmentDeptSet() {
        if (StringUtils.isBlank(this.getGovernmentEnterprise())) {
            return Sets.newHashSet();
        }
        return Arrays.stream(this.getGovernmentEnterprise().split(CharacterConstant.COMMA))
                .collect(Collectors.toSet());
    }

    /**
     * 获取三营+政企部门编码
     *
     * @return {@link Set< String>}
     * <AUTHOR>
     * @date 2024/02/26
     */
    public Set<String> getDivision3OrGovDeptCode() {
        if (StringUtils.isBlank(this.getSalesDivision3GovDeptCode())) {
            return Sets.newHashSet();
        }
        return Arrays.stream(this.getSalesDivision3GovDeptCode().split(CharacterConstant.COMMA))
                .collect(Collectors.toSet());
    }


}
