# 制品库二级/三级文件夹（产品大类/产品组）
repo_path: crm/custinfo
# 项目所属子系统编码
systemno: 100000455588

# BuildCI需要的参数
deploy_configs:
  #需要部署的所有服务列表
  services:
  - name: zte-crm-custinfo-custvisit
    health_port: 8080

# 需要忽略健康检查的服务，DeployCI需要的参数
ignore_health: zte-crm-custinfo-xxx


# 微服务名称
service_name: zte-crm-custinfo-custvisit
## 制品库属性（可选） ##
# 镜像制品库
artifactory_domain: artsz.zte.com.cn
art_server_id: art_sz_server
snap_docker_repo: it-snapshot-docker
alpha_docker_repo: it-alpha-docker
snap_gen_repo: it-snapshot-generic
alpha_gen_repo: it-alpha-generic

# 自定义区域
maven_image: docker.artsz.zte.com.cn/cci/it-zxcrm-prm100/mvn-jdk8
# pom文件目录，如果在根目录填空字符串
build_path: ""
# 镜像版本号前缀+yyyyMMddHHmmss
image_version: v1.0.0_Build

hccode:
  exclude:
  - "**/*zh_cn.js"
  - "**/Constants.java"
