package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExhibitionInfoExtMapper extends ExhibitionInfoMapper {
    /**
     * 查询展会列表
     *
     * @param query 展会信息
     * @return 展会列表
     */
    List<ExhibitionInfoDO> getList(com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery  query);

    /**
     * 查询展会，不需要权限
     *
     * @param query
     * @return {@link List<ExhibitionInfoDO>}
     * <AUTHOR>
     * @date 2023/12/28 上午10:16
     */
    List<ExhibitionInfoDO> searchListNoAuth(com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery query);

    /**
     * @param query
     * @return
     */
    int countList(com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery  query);

    /**
     * 获取展会数量，用于生成智能化编号
     * @param query
     * @return
     * <AUTHOR>
     * @date 2023/9/14
     */
    int getCountExhibitionInfo(ExhibitionInfoQuery query);

    /**
     * 根据展会Id查询展会列表
     * @param rowIds
     * @return
     * <AUTHOR>
     * @date 2023/9/14
     */
    List<ExhibitionInfoDO> queryExhibitionInfo(@Param("rowIds") List<String> rowIds);
}
