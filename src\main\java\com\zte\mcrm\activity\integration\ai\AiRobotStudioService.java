package com.zte.mcrm.activity.integration.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.ai.dto.StudioParamDTO;
import com.zte.mcrm.activity.integration.ai.dto.StudioResponseDTO;
import com.zte.mcrm.activity.web.controller.ai.vo.IcenterDataVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataQueryVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataRespVO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.common.ServiceDataUtil;
import com.zte.mcrm.common.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.zte.mcrm.activity.common.constant.AiConstant.AUTHORIZATION;
import static com.zte.mcrm.activity.common.constant.AiConstant.BEARER;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.COMMA_MINUS;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/4 16:47
 */
@Component
@Slf4j
public class AiRobotStudioService {
    @Value("${ai.studio.url}")
    private String studioUrl;
    @Value("${ai.studio.appId}")
    private String studioAppId;
    @Value("${ai.studio.apiKey}")
    private String studioApikey;

    @Value("${ai.robot.host:http://boassistant.test.zte.com.cn/zte-icrm-ai-assistant}")
    private String aiRobotHost;

    public static final String MARKET_DATA_QUERY_PATH = "/marketingDataQA";

    public String getAiRobotResult(IcenterDataVO msgData) {
        String text = msgData.getMsgBody();
        if (StringUtils.isBlank(text)) {
            return StringUtils.EMPTY;
        }
        StudioParamDTO studioDTO = new StudioParamDTO();
        String param = BEARER + studioAppId + COMMA_MINUS + studioApikey;
        Map<String, String> headerParamsMap = Maps.newHashMap();
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, HeadersProperties.getXEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, HeadersProperties.getXAuthValue());
        headerParamsMap.put(AUTHORIZATION, param);
        studioDTO.setKeep(Boolean.TRUE);
        studioDTO.setStream(Boolean.FALSE);
        studioDTO.setText(text);
        // 请求
        try {
            log.info("AI智能客服获取AI studio的入参:{}", JSON.toJSONString(studioDTO));
            String result = HttpClientUtil.httpPostWithJSON(studioUrl, JSON.toJSONString(studioDTO), headerParamsMap);
            log.info("AI智能客服获取AI studio的返回结果:{}", result);
            result = JsonSanitizer.sanitize(result);
            ServiceData<StudioResponseDTO> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<StudioResponseDTO>>() {
            }.getType());
            if (ServiceDataUtil.validSuccess(serviceData)) {
                return serviceData.getBo().getResult();
            }
        } catch (Exception e) {
            log.error("Error occurs when queryUserInfo", e);
        }
        return StringUtils.EMPTY;
    }

    public String getMarketDataInfo(MarketDataQueryVO msgData) {
        JSONObject body = new JSONObject();
        body.put("question", msgData.getQuestion());
        Map<String, String> headerParamsMap = Maps.newHashMap();
        headerParamsMap.put("Content-Type", "application/json");
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, HeadersProperties.getXEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, HeadersProperties.getXAuthValue());
        String url = aiRobotHost + MARKET_DATA_QUERY_PATH;
        String result = null;
        // 请求
        try {
            log.info("AiRobotStudioService.getMarketDataInfo.param:{}", body.toJSONString());
            result = HttpClientUtil.httpPostWithJSON(url, body.toJSONString(), headerParamsMap);
            log.info("AiRobotStudioService.getMarketDataInfo.response:{}", result);
        } catch (Exception e) {
            log.error("Error occurs when getMarketDataInfo", e);
        }
        return result;
    }

}
