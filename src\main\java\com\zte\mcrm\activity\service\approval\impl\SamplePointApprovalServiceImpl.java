package com.zte.mcrm.activity.service.approval.impl;


import com.google.common.collect.Lists;
import com.zte.iss.approval.sdk.bean.FlowStartDTO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.SamplePointConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.service.activity.impl.ActivityCustomerSearchServiceImpl;
import com.zte.mcrm.activity.service.approval.BaseApprovalBizService;
import com.zte.mcrm.activity.service.approval.param.ApprovalFlowStartParam;
import com.zte.mcrm.activity.service.approval.param.ApprovedByInfo;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.customvisit.util.DateUtils;
import com.zte.mcrm.isearch.enums.AmountUnitTypeEnum;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.COLON_EN;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.SEMICOLON;
import static com.zte.mcrm.activity.common.constant.DateConstants.YYYY_MM_DD_HH_MM_SS;
import static com.zte.mcrm.activity.common.constant.I18Constants.NOTICE_APPROVAL_RESUBMIT;
import static com.zte.mcrm.activity.common.enums.activity.ApprovalFlowTypeEnum.SAMPLE_POINT_APPROVAL;
import static com.zte.mcrm.customvisit.util.DateUtils.convertDateToString;

/**
 * <AUTHOR>
 */
@Service
public class SamplePointApprovalServiceImpl extends BaseApprovalBizService {

    @Autowired
    private ActivityApprovalInfoRepository approvalInfoRepository;
    @Autowired
    private IKeyIdService keyIdService;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private SamplePointInfoRepository samplePointInfoRepository;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ActivityApprovalProcessNodeRepository approvalProcessNodeRepository;
    @Autowired
    private ActivityUrlConfig activityUrlConfig;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private ActivityCustomerSearchServiceImpl activityCustomerSearchService;
    @Autowired
    private HrmOrgInfoSearchService hrmOrgInfoSearchService;
    @Autowired
    private ActivityApprovalProcessRepository approvalProcessRepository;
    @Autowired
    private ActivityCostBudgetRepository activityCostBudgetRepository;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Override
    protected void checkParam(ApprovalFlowStartParam approvalFlowStartParam) {
        if (StringUtils.isBlank(approvalFlowStartParam.getBizId())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity id is null");
        }
        // 已存在的未完成的审批活动不让重复启动
        List<ActivityApprovalInfoDO> approvalInfoDOList = approvalInfoRepository.queryAllByActivityRowId(approvalFlowStartParam.getBizId());
        approvalInfoDOList = approvalInfoDOList.stream().filter(e -> BooleanEnum.N.isMe(e.getInstanceStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(approvalInfoDOList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_RESUBMIT);
        }
    }

    @Override
    public FlowStartDTO buildStartDTO(ApprovalFlowStartParam approvalFlowStartParam, String empNo) {
        FlowStartDTO flowStartDTO = new FlowStartDTO();
        flowStartDTO.setFlowCode(approvalFlowStartParam.getApprovalType().getCode());
        flowStartDTO.setBusinessId(keyIdService.getKeyId());
        flowStartDTO.setHandler(empNo);
        Map<String, Object> params = new HashMap<>(16);
        // 开始组装启动参数
        // 1.查询审批信息
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(approvalFlowStartParam.getBizId());
        SamplePointInfoDO samplePointInfoDO = samplePointInfoRepository.selectByPrimaryKey(activityInfoDO.getOriginRowId());
        // 1.2 填充审批人
        List<ApprovedByInfo> approvedByInfoList = approvalFlowStartParam.getApprovedByInfoList();
        if (CollectionUtils.isEmpty(approvedByInfoList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.approver.not.empty");
        }
        // 1.1 查询合规审批
        List<ApprovedByInfo> complianceApprovedByList = approvedByInfoList.stream().filter(e -> ApprovalTypeEnum.COMPLIANCE_AUDITOR.isMe(e.getApprovalType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(complianceApprovedByList)) {
            params.put(ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.getCode(), complianceApprovedByList.get(0).getEmpNo());
            params.put(ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.getCode() + "Flag", BooleanEnum.Y.getCode());
        }
        // 1.2 查询业务审批 需要对相同审批人进行去重处理
        List<ApprovedByInfo> leaderApprovedByList = approvedByInfoList.stream()
                .filter(e -> ApprovalTypeEnum.LEADER_AUDITOR.isMe(e.getApprovalType()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ApprovedByInfo::getEmpNo))), ArrayList::new));
        for (ApprovedByInfo approvedByInfo : leaderApprovedByList) {
            params.put(approvedByInfo.getApprovalNodeType(), approvedByInfo.getEmpNo());
            params.put(approvedByInfo.getApprovalNodeType() + "Flag", BooleanEnum.Y.getCode());
        }
        // 1.3 封装通知参数
        buildInformInfo(params, activityInfoDO, samplePointInfoDO);

        flowStartDTO.setParams(params);
        return flowStartDTO;
    }

    private void buildInformInfo(Map<String, Object> params, ActivityInfoDO activityInfoDO, SamplePointInfoDO samplePointInfoDO) {
        String actLinkUrl = activityUrlConfig.fetchPendingNoticeUrl(activityInfoDO.getRowId());
        String actFlowUrl = activityUrlConfig.fetchDetailUrl(activityInfoDO.getRowId(), activityInfoDO.getActivityType(), "fifth");
        EmployeeInfoDTO approve = userCenterService.getUserInfo(activityInfoDO.getApplyPeopleNo());
        String applyPeopleName = Optional.ofNullable(approve).map(EmployeeInfoDTO::getEmpName).orElse(Strings.EMPTY);
        params.put("samplePointName", samplePointInfoDO.getSamplePointName());
        params.put("actLinkUrl", actLinkUrl);
        params.put("actNo", activityInfoDO.getActivityRequestNo());
        params.put("actTitle", activityInfoDO.getActivityTitle());
        params.put("adminEmpNo", samplePointInfoDO.getAdminEmpNo());
        params.put("submitPerson", samplePointInfoDO.getLastUpdatedBy());
        params.put("applyPeople", applyPeopleName + activityInfoDO.getApplyPeopleNo());
        params.put("applyPeopleEmpNo", activityInfoDO.getApplyPeopleNo());
        params.put("submitTime", DateUtils.convertDateToString(activityInfoDO.getSubmitTime(), YYYY_MM_DD_HH_MM_SS));
        params.put("actFlowUrl", actFlowUrl);
        params.put("activityContent", activityInfoDO.getActivityContent());
        Map<String, OrgInfoVO> orgInfoMap = hrmOrgInfoSearchService.getOrgInfoByOrgIds(Arrays.asList(activityInfoDO.getApplyDepartmentNo(), samplePointInfoDO.getAdminDepartmentNo()));
        String applyDepartment = Optional.ofNullable(orgInfoMap.get(activityInfoDO.getApplyDepartmentNo())).map(OrgInfoVO::getHrOrgName).orElse(activityInfoDO.getApplyDepartmentNo());
        String samplePointDepartment = Optional.ofNullable(orgInfoMap.get(samplePointInfoDO.getAdminDepartmentNo())).map(OrgInfoVO::getHrOrgName).orElse(samplePointInfoDO.getAdminDepartmentNo());
        params.put("applyDepartment", applyDepartment);
        params.put("samplePointDepartment", samplePointDepartment);

        String startTime = convertDateToString(activityInfoDO.getStartTime(), YYYY_MM_DD_HH_MM_SS);
        String endTime = convertDateToString(activityInfoDO.getEndTime(), YYYY_MM_DD_HH_MM_SS);
        params.put("communicationDate", startTime + "-" + endTime);

        // 查询客户联系人信息
        // 受限制主体信息
        params.put("cusLimitStatus", getCusLimitStatus(activityInfoDO.getRowId()));
        params.put("cusRelationLimitStatus", getCusRelationLimitStatus(activityInfoDO.getRowId()));
        // 参观模式
        params.put("visitModel", fetchVisitModel(activityInfoDO.getRowId()));
    }

    private String getCusLimitStatus(String activityRowId) {
        List<ActivityCustomerInfoDO> activityCustomerInfoList = activityCustomerInfoRepository.queryAllByActivityRowId(activityRowId);
        StringBuilder cusLimitStatusStr = new StringBuilder();
        activityCustomerInfoList.forEach(e -> cusLimitStatusStr.append(e.getCustomerName()).append(COLON_EN)
                .append(SanctionedPartyEnum.getDescByCode(e.getSanctionedPatryCode())).append(SEMICOLON));
        return cusLimitStatusStr.toString();
    }

    private String getCusRelationLimitStatus(String activityRowId) {
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleList = activityRelationCustPeopleRepository.queryAllByActivityRowId(activityRowId);
        StringBuilder cusRelationLimitStatusStr = new StringBuilder();
        activityRelationCustPeopleList.forEach(e -> {
            cusRelationLimitStatusStr.append(e.getContactName()).append(COLON_EN).append(SanctionedPartyEnum.getDescByCode(e.getSanctionedPatryCode())).append(SEMICOLON);
        });
        return cusRelationLimitStatusStr.toString();
    }

    private String fetchVisitModel(String activityRowId) {
        List<String> visitModelList = Lists.newArrayList();
        List<ActivityCostBudgetDO> activityCostBudgetList = activityCostBudgetRepository.queryCostBudgetByActivityRowIds(activityRowId);
        Assert.notEmpty(activityCostBudgetList, "activity cost budget is empty, please check it");

        String currency = AmountUnitTypeEnum.getDescByType(activityCostBudgetList.get(0).getCurrency());
        BigDecimal totalFeeAmount = BigDecimal.ZERO;
        for (ActivityCostBudgetDO activityCostBudgetDO : activityCostBudgetList) {
            String visitModel = activityCostBudgetDO.getFeeDesc()
                    + activityCostBudgetDO.getFeeAmount()
                    + currency + SamplePointConstant.TIMES;
            visitModelList.add(visitModel);
            totalFeeAmount = totalFeeAmount.add(activityCostBudgetDO.getFeeAmount());
        }
        visitModelList.add(SamplePointConstant.TOTAL_FEE + totalFeeAmount + currency);
        return String.join(CharacterConstant.PAUSE_MARK, visitModelList);
    }

    private String createApprovalProcess(ApprovalFlowStartParam approvalFlowStartParam, String empNo, ApprovalTypeEnum approvalTypeEnum, String approvalInfoRowId) {
        ActivityApprovalProcessDO data = new ActivityApprovalProcessDO();
        data.setActivityRowId(approvalFlowStartParam.getBizId());
        data.setRowId(keyIdService.getKeyId());
        data.setProcessType(approvalTypeEnum.getCode());
        data.setProcessStatus(ProcessStatusEnum.DEFAULT.getCode());
        data.setCreatedBy(empNo);
        data.setLastUpdatedBy(empNo);
        data.setActivityApprovalInfoRowId(approvalInfoRowId);
        approvalProcessRepository.insertSelective(data);
        return data.getRowId();
    }

    @Override
    protected String startFlow(FlowStartDTO flowStartDTO) {
        try {
            return ApprovalFlowClient.start(flowStartDTO);
        } catch (Exception e) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "tips.approval.center.callback.error");
        }
    }

    @Override
    protected String saveData(ApprovalFlowStartParam approvalFlowStartParam, FlowStartDTO flowStartDTO, String empNo) {
        ActivityApprovalInfoDO record = new ActivityApprovalInfoDO();
        record.setInstanceStatus(BooleanEnum.N.getCode());
        record.setInstanceId(StringUtils.EMPTY);
        record.setCreatedBy(empNo);
        record.setLastUpdatedBy(empNo);
        String approvalInfoRowId = keyIdService.getKeyId();
        record.setRowId(approvalInfoRowId);
        record.setApprovalNo(flowStartDTO.getBusinessId());
        record.setApprovalType(SAMPLE_POINT_APPROVAL.getCode());
        record.setActivityRowId(approvalFlowStartParam.getBizId());
        approvalInfoRepository.insertSelective(record);

        // 对应的process和code表都都先创建
        List<ApprovedByInfo> complianceAuditorList = approvalFlowStartParam.getApprovedByInfoList().stream()
                .filter(x -> StringUtils.equals(x.getApprovalType(), ApprovalTypeEnum.COMPLIANCE_AUDITOR.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(complianceAuditorList)) {
            String processRowId = createApprovalProcess(approvalFlowStartParam, empNo, ApprovalTypeEnum.COMPLIANCE_AUDITOR, approvalInfoRowId);
            saveApprovalProcessNode(approvalFlowStartParam, empNo, complianceAuditorList.get(0), processRowId);
        }

        List<ApprovedByInfo> leaderAuditorList = approvalFlowStartParam.getApprovedByInfoList().stream()
                .filter(x -> StringUtils.equals(x.getApprovalType(), ApprovalTypeEnum.LEADER_AUDITOR.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaderAuditorList)) {
            String processRowId = createApprovalProcess(approvalFlowStartParam, empNo, ApprovalTypeEnum.LEADER_AUDITOR, approvalInfoRowId);
            for (ApprovedByInfo approvedByInfo : leaderAuditorList) {
                saveApprovalProcessNode(approvalFlowStartParam, empNo, approvedByInfo, processRowId);
            }
        }
        return approvalInfoRowId;
    }

    private void saveApprovalProcessNode(ApprovalFlowStartParam approvalFlowStartParam, String empNo, ApprovedByInfo approvedByInfo, String processRowId) {
        ActivityApprovalProcessNodeDO data = new ActivityApprovalProcessNodeDO();
        data.setActivityRowId(approvalFlowStartParam.getBizId());
        data.setApprovalProcessRowId(processRowId);
        data.setNodeStatus(ApproveNodeStatusEnum.DEFAULT.getCode());
        data.setNodeType(approvedByInfo.getApprovalNodeType());
        String approvedBy = approvedByInfo.getEmpNo();
        data.setApproveBy(approvedBy);
        EmployeeInfoDTO approve = userCenterService.getUserInfo(approvedBy);
        data.setApproverName(approve.getEmpName());
        data.setCreatedBy(empNo);
        // 初始状态
        approvalProcessNodeRepository.insertSelective(data);
    }

    @Override
    protected void updateFlowInfo(String flowId, String approvalRowId) {
        ActivityApprovalInfoDO record = new ActivityApprovalInfoDO();
        record.setInstanceId(flowId);
        record.setRowId(approvalRowId);
        approvalInfoRepository.updateByActivityRowIdSelective(record);
    }

    @Override
    public boolean support(ApprovalFlowStartParam param) {
        return ApprovalFlowTypeEnum.SAMPLE_POINT_APPROVAL.isMe(param.getApprovalType().getCode());
    }
}
