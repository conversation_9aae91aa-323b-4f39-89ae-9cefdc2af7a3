package com.zte.mcrm.activity.common.cache.model;

import com.zte.mcrm.activity.common.enums.CompanyOrgSourceEnum;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import lombok.Getter;
import lombok.Setter;

/**
 * HR部门组织数据模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class HrOrgDataModel {
    /***
     * 组织编码，如：ORG2235883
     */
    private String hrOrgID;
    /**
     * 父组织编码，如：ORG2235569
     */
    private String hrOrgPID;
    // hrOldDeptNO; *********01, hrOldDeptPNO; *********,
    /**
     * 行政级别，如：5
     */
    private String hrLevel;
    /**
     * 组织名称，如：政企深圳
     */
    private String hrOrgName;
    /**
     * 组织名称全路径，如：政企深圳/广州处/广州处/国内营销_政企中国营销事业部/中兴通讯股份有限公司
     */
    private String hrOrgNamePath;
    /**
     * 组织编码全路径，如：ORG0000000－ORG0100668－ORG2235521－ORG2235569－ORG2235883
     */
    private String orgIDPath;
    /**
     * 有效标识，1-有效
     */
    private String enabled;
    /**
     * 机构层级，如;5
     */
    private String orgLevel;
    /**
     *
     */
    private String orgStatusID;
    /**
     * 是否参与业绩统计，1:业绩单位，0:非业绩单位
     */
    private String isPartakePerfStats;
    // orgEstDate; 2023-07-17T16:00:00.000+00:00,
    /**
     * 备注
     */
    private String remark;
    /**
     * 所属租户ID，如：10001
     */
    private String tenantId;
    /**
     * 所属公司ID
     */
    private String companyId;
    /**
     * 所属公司名称，如：中兴通讯股份有限公司
     */
    private String companyName;
    /**
     * 组织所属公司类型，见：{@link CompanyOrgSourceEnum}
     */
    private String type;

    /**
     * 所属来源
     *
     * @param orgSource
     * @return
     */
    public boolean belongSource(CompanyOrgSourceEnum orgSource) {
        return orgSource.isMe(type);
    }

    /**
     * 转为老的VO（用于兼容老代码地方的数据转换）
     *
     * @return
     */
    public OrgInfoVO toOrgInfoVO() {
        OrgInfoVO vo = new OrgInfoVO();
        vo.setHrOrgID(this.getHrOrgID());
        vo.setHrOrgPID(this.getHrOrgPID());
        vo.setHrLevel(this.getHrLevel());
        vo.setHrOrgName(this.getHrOrgName());
        vo.setHrOrgNamePath(this.getHrOrgNamePath());
        vo.setOrgIDPath(this.getOrgIDPath());
        vo.setEnabled(this.getEnabled());
        vo.setOrgLevel(this.getOrgLevel());
        vo.setOrgStatusID(this.getOrgStatusID());
        vo.setIsPartakePerfStats(this.getIsPartakePerfStats());
        vo.setRemark(this.getRemark());
        return vo;
    }
}
