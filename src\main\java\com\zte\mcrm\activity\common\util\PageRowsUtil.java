package com.zte.mcrm.activity.common.util;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.common.model.PageQuery;
import com.zte.mcrm.activity.common.model.PageRows;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * com.zte.erp.common.model.PageRows工具类
 *
 * <AUTHOR>
 */
public class PageRowsUtil {

    /**
     * 构建空页数据对象
     *
     * @param page 分页信息
     * @param <T>
     * @return 空数据分页结果
     */
    public static <T> PageRows<T> buildEmptyPage(PageQuery page) {
        PageRows<T> pageRows = new PageRows<>();

        pageRows.setTotal(0);
        pageRows.setCurrent(page == null || page.getPageNo() == null ? 1 : page.getPageNo());
        pageRows.setPageSize(page == null || page.getPageSize() == null ? 10 : page.getPageSize());
        pageRows.setTotalPage(0);
        pageRows.setRows(Collections.emptyList());

        return pageRows;
    }

    /**
     * 构建空页数据对象
     *
     * @param pageNo   当前页
     * @param pageSize 分页大小
     * @param <T>
     * @return 空数据分页结果
     */
    public static <T> PageRows<T> buildEmptyPage(Integer pageNo, Integer pageSize) {
        PageRows<T> pageRows = new PageRows<>();

        pageRows.setTotal(0);
        pageRows.setCurrent(pageNo == null ? 1 : pageNo);
        pageRows.setPageSize(pageSize == null ? 10 : pageSize);
        pageRows.setTotalPage(0);
        pageRows.setRows(Collections.emptyList());

        return pageRows;
    }

    /**
     * 整体构建分页信息
     *
     * @param pageNo      当前页
     * @param pageSize    页大小
     * @param totalCount  总条数
     * @param curPageData 当前页数据
     * @param <T>
     * @return
     */
    public static <T> PageRows<T> buildPageRow(long pageNo, long pageSize, long totalCount, List<T> curPageData) {
        PageRows<T> res = new PageRows<>();

        res.setCurrent(pageNo);
        res.setPageSize(pageSize);
        res.setTotal(totalCount);
        long totalPage = (totalCount / pageSize) + ((totalCount % pageSize) == 0 ? 0 : 1);
        res.setTotalPage(totalPage);
        res.setRows(curPageData);

        return res;
    }

    /**
     * 整体构建分页信息
     *
     * @param from    源数据分页
     * @param convert 数据转换器
     * @param <F>     源数据类型
     * @param <T>     转换后数据类型
     * @return 新分页数据
     */
    public static <F, T> PageRows<T> buildPageRow(PageRows<F> from, Function<List<F>, List<T>> convert) {
        PageRows<T> res = new PageRows<>();

        res.setCurrent(from.getCurrent());
        res.setPageSize(from.getPageSize());
        res.setTotal(from.getTotal());
        res.setTotalPage(from.getTotalPage());
        res.setRows(convert.apply(from.getRows()));

        return res;
    }

    /**
     * 构建分页信息
     *
     * @param from    源数据分页
     * @param convert 数据转换器
     * @param <F>     源数据类型
     * @param <T>     转换后数据类型
     * @return 新分页数据
     */
    public static <F, T> PageRows<T> singleBuildPageRow(PageRows<F> from, Function<F, T> convert) {
        PageRows<T> res = new PageRows<>();

        List<F> sourceList = from.getRows();
        List<T> targetList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sourceList)) {
            for (F f : sourceList) {
                if (f != null) {
                    targetList.add(convert.apply(f));
                }
            }
        }

        res.setPageSize(from.getPageSize());
        res.setCurrent(from.getCurrent());
        res.setTotalPage(from.getTotalPage());
        res.setTotal(from.getTotal());
        res.setRows(targetList);

        return res;
    }

    /**
     * 构建分页信息
     *
     * @param from 源数据分页
     * @param <F>  源数据类型
     * @return 新分页数据
     */
    public static <F> PageRows<F> buildPageRow(Page<F> from) {
        PageRows<F> res = new PageRows<>();
        // 可以再重载一个具有转换（Function）的方法，就和上面的一样
        res.setCurrent(from.getPageNum());
        res.setPageSize(from.getPageSize());
        res.setTotalPage(from.getPages());
        res.setTotal(from.getTotal());
        res.setRows(from);

        return res;
    }

    /**
     * 构建分页信息
     *
     * @param pageInfo 源数据分页
     * @param <F>      源数据类型
     * @return 新分页数据
     */
    public static <F> PageRows<F> buildPageRow(PageInfo<F> pageInfo) {
        PageRows<F> pageRows = new PageRows<>();
        pageRows.setRows(pageInfo.getList());
        pageRows.setTotal(pageInfo.getTotal());
        pageRows.setCurrent(pageInfo.getPageNum());
        pageRows.setPageSize(pageInfo.getPageSize());
        pageRows.setTotalPage(pageInfo.getPages());
        return pageRows;
    }

    /**
     * PageRows 转换
     *
     * @param from     待转换pageRows
     * @param function 对象转换方法
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> com.zte.itp.msa.core.model.PageRows<T> transform(
            com.zte.itp.msa.core.model.PageRows<F> from,
            Function<? super F, ? extends T> function
    ) {
        com.zte.itp.msa.core.model.PageRows<T> pageRows = new com.zte.itp.msa.core.model.PageRows();
        pageRows.setRows(org.apache.commons.collections4.CollectionUtils.isEmpty(from.getRows())
                ? Collections.emptyList() : (List) from.getRows().stream().map(function).collect(Collectors.toList()));
        pageRows.setTotal(from.getTotal());
        pageRows.setCurrent(from.getCurrent());
        return pageRows;
    }

    /**
     * 构建空pageRows
     * @return
     */
    public static com.zte.itp.msa.core.model.PageRows buildPageRows(){
        com.zte.itp.msa.core.model.PageRows recordVOPage = new com.zte.itp.msa.core.model.PageRows<>();
        recordVOPage.setRows(new ArrayList<>());
        return recordVOPage;
    }

    /***
     * <p>
     * 分页查询入参 默认参数设置
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/4 下午4:21
     * @param pageQuery 分页查询入参
     */
    public static <T> void setDefaultPageQueryParams(com.zte.mcrm.activity.web.PageQuery<T> pageQuery) {
        Integer pageNum = pageQuery.getPageNo();
        if (pageNum == null || pageNum <= 0) {
            pageQuery.setPageNo(1);
        }

        Integer pageSize = pageQuery.getPageSize();
        if (pageSize == null || pageSize <= 0) {
            pageQuery.setPageSize(10);
        }
    }

    /**
     * 构建空页数据分页对象（低代码标准分页对象）
     *
     * @param page 分页信息
     * @param <T>
     * @return 空集合数据分页结果
     */
    public static <T> LowCodePageRow<T> buildEmptyLowCodePageRow(PageQuery page) {
        return buildLowCodePageRow(page.getPageNo(), page.getPageSize(), 0, Collections.emptyList());
    }

    /**
     * 构建空页数据分页对象（低代码标准分页对象）
     *
     * @param pageNo   当前页
     * @param pageSize 分页大小
     * @param <T>
     * @return 空集合数据分页结果
     */
    public static <T> LowCodePageRow<T> buildEmptyLowCodePageRow(int pageNo, int pageSize) {
        return buildLowCodePageRow(pageNo, pageSize, 0, Collections.emptyList());
    }

    /**
     * 构建数据列表对象（低代码标准分页对象）
     *
     * @param list 记录列表
     * @param <T>
     * @return
     */
    public static <T> LowCodePageRow<T> buildLowCodePageRow(List<T> list) {
        return buildLowCodePageRow(1, list.size(), list.size(), list);
    }

    /**
     * 构建数据分页对象（低代码标准分页对象）
     *
     * @param pageNo   当前页
     * @param pageSize 分页大小
     * @param total    总数量
     * @param list     记录列表
     * @param <T>
     * @return
     */
    public static <T> LowCodePageRow<T> buildLowCodePageRow(int pageNo, int pageSize, int total, List<T> list) {
        LowCodePageRow<T> pageRows = new LowCodePageRow<>();
        pageRows.setPageNo(pageNo);
        pageRows.setPageSize(pageSize);
        pageRows.setTotal(total);
        pageRows.setRecords(list);

        return pageRows;
    }

    /**
     * 转换为标准低代码分页对象
     *
     * @param pageRows
     * @param <T>
     * @return
     */
    public static <T> LowCodePageRow<T> toLowCodePageRow(PageRows<T> pageRows) {
        LowCodePageRow<T> res = new LowCodePageRow<>();
        res.setPageSize((int) pageRows.getPageSize());
        res.setPageNo((int) pageRows.getCurrent());
        res.setTotal((int) pageRows.getTotal());
        res.setRecords(pageRows.getRows());
        return res;
    }

    /**
     * 转换为老的分页对象
     *
     * @param pageRows
     * @param <T>
     * @return
     */
    public static <T> PageRows<T> toPageRow(LowCodePageRow<T> pageRows) {
        int totalCount = pageRows.getTotal();
        int pageSize = pageRows.getPageSize();
        int totalPage = (totalCount / pageSize) + ((totalCount % pageSize) == 0 ? 0 : 1);

        PageRows<T> res = new PageRows<>();
        res.setCurrent(pageRows.getPageNo());
        res.setPageSize(pageSize);
        res.setTotal(totalCount);
        res.setTotalPage(totalPage);
        res.setRows(pageRows.getRecords());

        return res;
    }

    /***
     * <p>
     * 将分页查询结果转换为BoMap
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/4 下午3:36
     * @param pageRows 分页查询结果
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    public static <T> Map<String, Object> convertPageRowsToBoMap(PageRows<T> pageRows) {
        Map<String, Object> boMap = new HashMap<>();
        boMap.put("records", pageRows.getRows());
        boMap.put("current", pageRows.getCurrent());
        boMap.put("total", pageRows.getTotal());
        boMap.put("pageNo", pageRows.getCurrentPage());
        boMap.put("pageSize", pageRows.getPageSize());
        return boMap;
    }
}
