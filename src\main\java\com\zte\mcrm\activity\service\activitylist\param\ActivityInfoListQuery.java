package com.zte.mcrm.activity.service.activitylist.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class ActivityInfoListQuery {

    /**
     * 操作人
     */
    private String operater;
    /**
     * 拓展活动申请单号集合
     */
    private List<String> activityRequestNoList;
    /**
     * 拓展活动状态。枚举：ActivityStatusEnum
     */
    private List<String> activityStatusList;

}
