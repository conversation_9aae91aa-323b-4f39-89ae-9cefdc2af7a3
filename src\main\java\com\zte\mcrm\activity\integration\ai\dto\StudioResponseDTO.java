package com.zte.mcrm.activity.integration.ai.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/4 17:31
 */
@Setter
@Getter
@ToString
public class StudioResponseDTO implements Serializable {
    private static final long serialVersionUID = -7502456291398849898L;

    @ApiModelProperty(value = "会话唯一标识，由数字和字母组成的32位字符串，用户如果不填，则由系统自动生成")
    private String chatUuid;
    @ApiModelProperty(value = "单个文本生成")
    private String docId;
    @ApiModelProperty(value = "结束原因")
    private String finishReason;
    @ApiModelProperty(value = "返回结果")
    private String result;

}
