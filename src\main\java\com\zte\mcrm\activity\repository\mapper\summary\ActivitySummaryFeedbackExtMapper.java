package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryFeedbackDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitySummaryFeedbackExtMapper extends ActivitySummaryFeedbackMapper {
    /**
     * 查询活动相关反馈信息
     * @param activityIds
     * @return
     */
    List<ActivitySummaryFeedbackDO> queryAllFeedbackByActivityRowId(@Param("activityIds") List<String> activityIds);

}