package com.zte.mcrm.activity.service.activity;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.web.controller.activity.vo.TenantInfoVO;

/**
 * <AUTHOR>
 * @date ：2025/3/21 10:32
 * @description ：适配租户和法人
 */
public interface ActivityTenantInfoService {
    /**
     * 通过部门编码适配法人及租户信息
     *
     * @param orgNo 部门/组织编码
     * @return 法人及租户信息
     */
    TenantInfoVO fetchTenantByOrgNo(String orgNo);

    /**
     * 通过员工短工号适配法人及租户信息
     *
     * @return 法人及租户信息
     */
    TenantInfoVO fetchTenantByEmpNo();
}
