package com.zte.mcrm.activity.common.enums;

/* Started by AICoder, pid:3732ei52aa5935d148450a1690d0cf92b3051c1f */
import lombok.Getter;

/**
 * <AUTHOR>
 * @date ：2024/11/7 9:51
 * @description ：谈参授权范围枚举
 */
@Getter
public enum ActivityTalkAuthRangeEnum {

    /**
     * 日程
     */
    SCHEDULE(1, "日程"),

    /**
     * 谈参
     */
    DISCUSSION(2, "谈参"),

    /**
     * 日程&谈参
     */
    SCHEDULE_DISCUSSION(3, "日程&谈参");

    /**
     * 枚举值
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param type  枚举值
     * @param desc  描述
     */
    ActivityTalkAuthRangeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 判断当前枚举值是否与给定的字符串相等（忽略大小写）
     *
     * @param type 给定的字符串
     * @return 如果相等则返回true，否则返回false
     */
    public boolean isMe(Integer type) {
        return this.type.equals(type);
    }

    /**
     * 根据枚举值获取对应的枚举对象
     *
     * @param type 枚举值
     * @return 对应的枚举对象，如果不存在则返回null
     */
    public static ActivityTalkAuthRangeEnum getEnumByType(Integer type) {
        for (ActivityTalkAuthRangeEnum e : ActivityTalkAuthRangeEnum.values()) {
            if (e.isMe(type)) {
                return e;
            }
        }
        return SCHEDULE;
    }

    /**
     * 检查给定的枚举值是否有效
     *
     * @param type 枚举值
     * @return 如果有效则返回true，否则返回false
     */
    public static boolean valid(Integer type) {
        return getEnumByType(type) != null;
    }

    /**
     * 检查给定的枚举值是否在指定的枚举数组中
     *
     * @param type 枚举值
     * @param es   指定的枚举数组
     * @return 如果在则返回true，否则返回false
     */
    public static boolean in(Integer type, ActivityTalkAuthRangeEnum... es) {
        for (ActivityTalkAuthRangeEnum e : es) {
            if (e.isMe(type)) {
                return true;
            }
        }
        return false;
    }

    /* Started by AICoder, pid:lbf5dt5d9569ea2146fa09cd00b90e1db830abb6 */
    /**
     * 根据类型获取描述
     *
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        ActivityTalkAuthRangeEnum e = getEnumByType(type);
        return e.desc;
    }
    /* Ended by AICoder, pid:lbf5dt5d9569ea2146fa09cd00b90e1db830abb6 */
}

/* Ended by AICoder, pid:3732ei52aa5935d148450a1690d0cf92b3051c1f */
