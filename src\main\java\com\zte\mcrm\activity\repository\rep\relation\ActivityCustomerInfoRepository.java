package com.zte.mcrm.activity.repository.rep.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.relation.param.BaseActivityCustomerQuery;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 活动关联客户信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityCustomerInfoRepository {


    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityCustomerInfoDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityCustomerInfoDO record);

    /**
     * 通用基础拓展活动客户信息查询
     *
     * @param query
     * @return
     */
    List<ActivityCustomerInfoDO> baseActivityCustomerInfoQuery(BaseActivityCustomerQuery query);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityCustomerInfoDO> queryAllByActivityRowId(String activityRowId);

    /**
     * 根据活动idp批量获取当前数据集合
     * @param activityRowIds
     * @return
     */
    List<ActivityCustomerInfoDO> queryAllByActivityRowIds(List<String> activityRowIds);

    /**
     *  批量插入
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<ActivityCustomerInfoDO> recordList);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowIds
     * @return
     */
    Map<String,List<ActivityCustomerInfoDO>> getActivityCustomerListByActivityRowIds(Set<String> activityRowIds);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowIds
     * @return
     */
    List<ActivityCustomerInfoDO> getActivityCustomerListByActivityRowIdSet(Set<String> activityRowIds);
    /**
     * 查找用户最近创建的活动中使用的客户
     *
     * @param pageQuery
     * @return {@link List< ActivityCustomerInfoDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:52
     */
    List<ActivityCustomerInfoDO> selectRecentlyCustomerByUser(PageQuery<ActivityRecentlySearchParam> pageQuery);

    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityCustomerInfoDO> list);

    /**
     * 批量修改数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    void batchUpdate(List<ActivityCustomerInfoDO> list);

    /**
     * 查询所有包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityCustomerInfoDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityCustomerInfoDO> queryAllActivityWithNotEnable(String activityRowId);

    /**
     * 根据mktCode查询mktName
     * @param codes
     * @return
     */
    List<ActivityCustomerInfoDO> listMktNameByMktCode(Collection<String> codes);

    /**
     * 根据客户三级结构编码查询活动id列表
     * @param mtoCode
     * @param mktCode
     * @param customerCode
     * @return
     */
    List<String> getActivityRowIdsByCustomerInfo(String mtoCode, String mktCode, String customerCode);
}
