package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ExhibitionRelationCarExtMapper extends ExhibitionRelationCarMapper {

    /***
     * <p>
     * 根据主键Id列表获取对应记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:08
     * @param rowIds 关联车辆记录主键Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO>
     */
    List<ExhibitionRelationCarDO> queryAllByCarRowIds(List<String> rowIds);

    /***
     * <p>
     * 根据展会Id列表获取所有关联车辆记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:09
     * @param exhibitionIds 展会Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO>
     */
    List<ExhibitionRelationCarDO> queryAllByExhibitionIds(List<String> exhibitionIds);

    /***
     * <p>
     * 批量插入数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联车辆记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationCarDO> records);

    /***
     * <p>
     * 批量修改数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联车辆记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationCarDO> records);

    /***
     * <p>
     * 逻辑删除所有与展会Id关联的车辆记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:11
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id列表
     * @return int
     */
    int softDeleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);

}