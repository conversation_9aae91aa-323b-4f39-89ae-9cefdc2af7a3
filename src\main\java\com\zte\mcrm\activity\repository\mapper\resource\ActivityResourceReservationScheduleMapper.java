package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityResourceReservationScheduleMapper {
    /**
     * all field insert
     */
    int insert(ActivityResourceReservationScheduleDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityResourceReservationScheduleDO record);

    /**
     * query by primary key
     */
    ActivityResourceReservationScheduleDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityResourceReservationScheduleDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceReservationScheduleDO record);

    /**
     * 新工号切换
     */
    List<ActivityResourceReservationScheduleDO> queryEmpNoTransList(DataTransParam searchParam);
}