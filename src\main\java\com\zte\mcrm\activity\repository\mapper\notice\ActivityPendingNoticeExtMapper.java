package com.zte.mcrm.activity.repository.mapper.notice;

import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface ActivityPendingNoticeExtMapper extends ActivityPendingNoticeMapper {
    /**
     * 根据待办类型更新数据
     *
     * @param activityRowId
     * @param bizType
     */
    void updateByActivityRowIdAndBizType(@Param("activityRowId") String activityRowId, @Param("bizType") String bizType, @Param("status") String status);

    /**
     * 获取待办在某个时间之前过期的数据
     * @param expiredTime
     * @param size
     * @return
     */
    List<ActivityPendingNoticeDO> fetchExpiredNotice(@Param("expiredTime") Date expiredTime, @Param("size") int size);

    /**
     * 列表查询
     *
     * @param record    查询条件
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/5/24 14:23
     */
    List<ActivityPendingNoticeDO> getList(ActivityPendingNoticeDO record);

    /**
     *
     * @param activityRowId
     * @param pendingBizType 可空
     * @return
     */
    List<ActivityPendingNoticeDO> queryAllPending(@Param("activityRowId")String activityRowId, @Param("pendingBizType")String pendingBizType,
                                                  @Param("status")String status);
     /**
     * 批量添加待办信息（如果没有主键，自动生成）
     *
     * @param list
     */
    int batchInsert(@Param("list") List<ActivityPendingNoticeDO> list);

    /**
     * 根据业务id更新待办状态
     *
     * @param businessId
     * @param status
     */
    void updateStatusByBusinessId(@Param("businessId") String businessId, @Param("status") String status);

    /**
     * 批量更新
     *
     * @param ids       id
     * @param update    更新内容
     * @return {@link int}
     * <AUTHOR>
     * @date 2023/8/30 下午10:14
     */
    int batchUpdate(@Param("ids") List<String> ids, @Param("update") ActivityPendingNoticeDO update);


    /**
     * 更新指定活动状态
     * @param activityRowId 活动主键
     * @param beforeStatus  旧状态
     * @param afterStatus   新状态
     * @return void
     * <AUTHOR>
     * date: 2023/8/29 18:57
     */
    int updateTargetStatusByActivityRowId(String activityRowId, String beforeStatus, String afterStatus);

    /**
     * 根据活动Id查询所有待办信息
     *
     * @param activityRowIds 活动Id列表
     * @return java.util.List < ActivityPendingNoticeDO>>
     * <AUTHOR>
     * date: 2023/9/4 7:19
     */
    List<ActivityPendingNoticeDO> queryAllByActivityRowIds(@Param("activityRowIds") Set<String> activityRowIds);

    /**
     * 根据活动Id更新
     * @param record    记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/5 10:33
     */
    int updateByActivityId(ActivityPendingNoticeDO record);

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag='Y'，推送ES不需要无效待办
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityPendingNoticeDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    /**
     * 根据业务类型和状态批量查询待办，性能不好，慎用
     *
     * @param pendingBizType
     * @param pendingStatus
     * @param size
     * @return {@link List<ActivityPendingNoticeDO>}
     * <AUTHOR>
     * @date 2024/7/27 上午10:32
     */
    List<ActivityPendingNoticeDO> selectListBatch(@Param("pendingBizType") String pendingBizType,
                                                  @Param("pendingStatus") String pendingStatus,
                                                  @Param("size") int size);


    /**
     * 分页查询
     * @param startRow 开始页
     * @param pageSize 查询总页数
     * @return
     * <AUTHOR>
     * @date 2024/7/16 上午10:32
     */
    List<ActivityPendingNoticeDO> getEndingNotice(int startRow, int pageSize);
}