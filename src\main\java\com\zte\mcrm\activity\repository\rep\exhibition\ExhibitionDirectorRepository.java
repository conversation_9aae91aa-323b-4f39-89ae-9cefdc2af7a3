package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ExhibitionDirectorRepository
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会负责人业务层接口
 * @date 2023/9/614:56
 */
public interface ExhibitionDirectorRepository {
    /**
     * 新增展会负责人信息
     * @param record 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int insertSelective(ExhibitionDirectorDO record);

    /**
     * 更新展会负责人信息
     * @param record 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int updateByPrimaryKeySelective(ExhibitionDirectorDO record);

    /**
     * 根据展会Id获取展会负责人信息
     * @param exhibitionRowIds 展会Id
     * @return
     * <AUTHOR>
     * @date 2023/9/9
     */
    Map<String,List<ExhibitionDirectorDO>> queryDirectorByExhibitionRowId(List<String>  exhibitionRowIds);

    /**
     * 根据人员工号列表查询所有角色
     */
    @Deprecated
    List<ExhibitionDirectorDO> queryDirectorsByEmpNo(List<String> empNoList);

    /**
     * 根据人员工号 查询 指定展会下的角色列表信息
     */
    List<ExhibitionDirectorDO> queryDirectorByExhibitionRowIdAndEmpNo(String exhibitionRowId, String empNo);

    /**
     * 根据展会Id获取负责人信息
     * @param exhibitionRowId
     * @return
     */
    List<ExhibitionDirectorDO> fetchDirectorByExhibitionRowId(String exhibitionRowId);
}
