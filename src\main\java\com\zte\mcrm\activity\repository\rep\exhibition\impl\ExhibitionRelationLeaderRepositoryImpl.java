package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationLeaderExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationLeaderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ExhibitionRelationLeaderRepositoryImpl implements ExhibitionRelationLeaderRepository {
    @Resource
    private ExhibitionRelationLeaderExtMapper exhibitionRelationLeaderExtMapper;

    @Override
    public int insertSelective(ExhibitionRelationLeaderDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        return exhibitionRelationLeaderExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationLeaderDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return exhibitionRelationLeaderExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ExhibitionRelationLeaderDO>> queryLeadersWithExhibitionRowId(List<String> exhibitionRowIds) {
        return CollectionUtils.isEmpty(exhibitionRowIds) ? Collections.emptyMap()
                : exhibitionRelationLeaderExtMapper.queryLeadersWithExhibitionRowId(exhibitionRowIds)
                .stream().collect(
                        Collectors.groupingBy(ExhibitionRelationLeaderDO::getExhibitionRowId));

    }

    @Override
    public Map<String, ExhibitionRelationLeaderDO> getLeaderNoMapWithExhibitionRowId(String exhibitionRowId) {
        if (StringUtils.isBlank(exhibitionRowId)) {
            return new HashMap<>();
        }

        return exhibitionRelationLeaderExtMapper.queryLeadersWithExhibitionRowId(Lists.newArrayList(exhibitionRowId))
                .stream().collect(Collectors.toMap(ExhibitionRelationLeaderDO::getEmployeeNo, i -> i, (u, v) -> u));
    }
}
