package com.zte.mcrm.activity.repository.rep.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivitySummaryApRepository {

    /**
     * 添加关联的AP（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivitySummaryApDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivitySummaryApDO record);

    /**
     * 查询活动关联的所有AP
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivitySummaryApDO> queryAllApForActivity(String activityRowId);

    /**
     * 查询活动关联的AP信息
     * @param activityRowId
     * @return
     */
    Map<String, List<ActivitySummaryApDO>> queryAllByActivityRowId(List<String> activityRowId);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 根据活动Id批次删除
     * @param operator 用户工号
     * @param activityRowIds    活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:47
     */
    int deleteByActivityIds(String operator, List<String> activityRowIds);
}
