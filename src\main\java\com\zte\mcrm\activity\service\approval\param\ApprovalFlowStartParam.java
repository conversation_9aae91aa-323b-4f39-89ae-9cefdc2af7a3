package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.activity.common.enums.activity.ApprovalFlowTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ProcessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * 流程启动参数(后续要求在此基础扩展，目前支持展会报名)
 */
@Getter
@Setter
public class ApprovalFlowStartParam {

    @ApiModelProperty("业务相关主键ID")
    private String bizId;

    /**
     * {@link ApprovalFlowTypeEnum}
     */
    @ApiModelProperty("审批流类型")
    private ApprovalFlowTypeEnum approvalType;

    // 下列属于优先赋值的参数，若param中赋值，则后续启动审批参数优先取值

    @ApiModelProperty("活动标题")
    private String actTitle;

    @ApiModelProperty("活动编号")
    private String actNo;

    @ApiModelProperty("活动链接（拓展活动申请单号）")
    private String actLinkUrl;

    @ApiModelProperty("业务领导审批人列表")
    private List<ApprovedByInfo> approvedByInfoList;

    @ApiModelProperty("提交时间")
    private String submitTime;

}
