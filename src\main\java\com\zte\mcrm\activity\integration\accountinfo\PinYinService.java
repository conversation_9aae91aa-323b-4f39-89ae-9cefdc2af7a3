package com.zte.mcrm.activity.integration.accountinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.ReceptionConstant;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.accountinfo.dto.PinYinDto;
import com.zte.mcrm.common.enums.ServiceAliasEnum;
import com.zte.mcrm.isearch.enums.RequestTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;

/**
 * 拼音服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PinYinService {


    /**
     * 批量拼音生成
     *
     * @param request
     * @return 《name，对象》
     */
    public MsaRpcResponse<Map<String, PinYinDto>> generatePinYinBatch(MsaRpcRequest<Set<String>> request) {
        if (CollectionUtils.isEmpty(request.getBody())) {
            return MsaRpcResponse.successRes(Collections.emptyMap());
        }

        List<Future<PinYinDto>> list = new ArrayList<>(request.getBody().size());
        for (String name : request.getBody()) {
            list.add(ThreadManager.submitToParallel(() -> generatePinYin(MsaRpcRequestUtil.trans(request, e -> name)).getBo()));
        }

        HashMap<String, PinYinDto> data = new HashMap<>();
        for (Future<PinYinDto> f : list) {
            try {
                PinYinDto pinyin = f.get();
                data.put(pinyin.getName(), pinyin);
            } catch (Exception e) {
                log.error("生成拼音时发生异常：", e);
            }
        }

        return MsaRpcResponse.successRes(data);
    }

    /**
     * 生成拼音
     *
     * @param request 如果名称name长度 > 2,则有2个拼音，否则只有1个
     * @return
     */
    public MsaRpcResponse<PinYinDto> generatePinYin(MsaRpcRequest<String> request) {
        PinYinDto pinYin = new PinYinDto();
        if (StringUtils.isBlank(request.getBody())) {
            return MsaRpcResponse.successRes(pinYin);
        }

        String name = request.getBody().trim();
        pinYin.setName(name);
        String pinyinName1 = generatePinYin(request, NumberConstant.ONE);
        if (StringUtils.isBlank(pinyinName1)) {
            // 生成拼音失败。一般来说不会失败
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, ReceptionConstant.GENERATE_PINYIN_FAIL + name);
        }

        pinYin.setPinYin1(pinyinName1);
        if (name.length() > NumberConstant.TWO) {
            pinYin.setPinYin2(generatePinYin(request, NumberConstant.TWO));
        }

        return MsaRpcResponse.successRes(pinYin);
    }

    /**
     * 生成拼音
     *
     * @param request
     * @param order   1或2
     * @return 拼音结果
     */
    String generatePinYin(MsaRpcRequest<String> request, int order) {
        String url = "/contact/generatePinYin";

        Map<String, String> param = new HashMap<>();
        param.put("type", String.valueOf(order));
        param.put("words", request.getBody());

        String pinyin = null;

        try {
            String json = MicroServiceRestUtil.invokeService(ServiceAliasEnum.ACCOUNT.getNormalizedName(), ServiceAliasEnum.ACCOUNT.getNormalizedVersion(),
                    RequestTypeEnum.GET.getValue(), url, JSON.toJSONString(param), request.fetchHeaderMap());
            log.info("生成拼音请求结果：" + json);

            ServiceData<String> res = JSON.parseObject(json, new TypeReference<ServiceData<String>>() {
            });

            if (ServiceDataUtils.isSuccess(res)) {
                pinyin = res.getBo();
            }
        } catch (Exception e) {
            log.error("调用" + ServiceAliasEnum.ACCOUNT.getNormalizedName() + url + "异常", e);
        }

        return pinyin;
    }
}
