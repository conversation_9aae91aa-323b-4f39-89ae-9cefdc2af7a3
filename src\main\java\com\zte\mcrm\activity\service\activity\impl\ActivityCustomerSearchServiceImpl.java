package com.zte.mcrm.activity.service.activity.impl;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.AccountInfoAuthTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.CustomerFrozenFlagEnum;
import com.zte.mcrm.activity.common.enums.activity.CustomerMergeFlagEnum;
import com.zte.mcrm.activity.common.enums.activity.CustomerStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activity.ActivityCustomerSearchService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.vo.Account;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.DictConstant.JOINT_STOCK_COMPANY_ID;

/**
 * 客户活动客户参与人查询
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
@Service
public class ActivityCustomerSearchServiceImpl implements ActivityCustomerSearchService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityCustomerSearchServiceImpl.class);

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private AccountAdapter accountAdapter;
    /**
     * 查询活动创建中用户最近使用的客户
     *
     * @param request
     * @return {@link List < ActivityCustomerInfoVO >}
     * <AUTHOR>
     * @date 2023/5/17 下午2:59
     */
    @Override
    public List<Account> searchRecentlyCustomers(BizRequest<PageQuery<ActivityRecentlySearchParam>> request) {
        PageQuery<ActivityRecentlySearchParam> pageable = request.getParam();
        ActivityRecentlySearchParam param
                = Objects.nonNull(pageable.getParam()) ? pageable.getParam() : new ActivityRecentlySearchParam();
        pageable.setCount(Boolean.FALSE);
        param.setEmpNo(request.getEmpNo());
        if (CollectionUtils.isEmpty(param.getActivityStatusList())) {
            param.setActivityStatusList(ActivityStatusEnum.getEffectiveActivityStatus());
        }
        pageable.setParam(param);
        String orgCode = Optional.ofNullable(param.getOrgCode()).orElse(JOINT_STOCK_COMPANY_ID);
        List<ActivityCustomerInfoDO> customerInfoDOList
                = activityCustomerInfoRepository.selectRecentlyCustomerByUser(pageable);
        if (CollectionUtils.isNotEmpty(customerInfoDOList)) {
            List<String> customerCodes = customerInfoDOList.stream()
                    .map(ActivityCustomerInfoDO::getCustomerCode).distinct().collect(Collectors.toList());
            List<Account> accounts = fetchAccountByTenant(customerCodes, orgCode);
            accounts = accounts.stream().filter(e -> {
                boolean activeFlag = CustomerStatusEnum.EFFECT.isMe(e.getActiveStatusCode());
                boolean mergeFlag = CustomerMergeFlagEnum.in(e.getAcctMergeFlag(), CustomerMergeFlagEnum.MAIN, CustomerMergeFlagEnum.NONE);
                boolean frozenFlag = CustomerFrozenFlagEnum.UNFROZEN.isMe(e.getFrozenFlag());
                return BooleanUtils.and(new Boolean[]{activeFlag , mergeFlag , frozenFlag});
            }).collect(Collectors.toList());
            return accounts;
        }
        return Collections.emptyList();
    }

    /**
     * 获取客户信息
     *
     * @param customerCodes
     * @return {@link List< Account>}
     * <AUTHOR>
     * @date 2023/5/28 下午12:47
     */
    @Override
    public List<Account> fetchAccount(List<String> customerCodes) {
        if (CollectionUtils.isEmpty(customerCodes)) {
            return Collections.emptyList();
        }
        Account account = new Account();
        account.setAccntNumList(customerCodes);
        try {
            ServiceData<List<Account>> serviceData = accountAdapter.authorityAccountWithAccount1(account,
                    AccountInfoAuthTypeEnum.ALL.getCode(), NumberConstant.ONE, customerCodes.size());
            if (CollectionUtils.isNotEmpty(serviceData.getBo())) {
                serviceData.getBo().forEach(e-> e.setBuPath(e.getBuName()));
                return serviceData.getBo();
            }
        } catch (Exception e) {
            logger.error("获取客户信息异常, {}", customerCodes, e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取客户信息
     *
     * @param customerCodes 客户编码
     * @param orgCode       组织编码
     * @return {@link List<Account>}
     * <AUTHOR>
     * @date 2025/3/17
     */
    @Override
    public List<Account> fetchAccountByTenant(List<String> customerCodes, String orgCode) {
        if (CollectionUtils.isEmpty(customerCodes)) {
            return Collections.emptyList();
        }
        Account account = new Account();
        account.setAccntNumList(customerCodes);
        try {
            ServiceData<List<Account>> serviceData = accountAdapter.authorityAccountWithAccount1FetchTenant(account,
                    AccountInfoAuthTypeEnum.ALL.getCode(), NumberConstant.ONE, customerCodes.size(), orgCode);
            if (CollectionUtils.isNotEmpty(serviceData.getBo())) {
                serviceData.getBo().forEach(e-> e.setBuPath(e.getBuName()));
                return serviceData.getBo();
            }
        } catch (Exception e) {
            logger.error("适配租户及法人获取客户信息异常, {}", customerCodes, e);
        }
        return Collections.emptyList();
    }
}
