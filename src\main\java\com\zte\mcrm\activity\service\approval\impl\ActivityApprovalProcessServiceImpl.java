package com.zte.mcrm.activity.service.approval.impl;

import com.zte.mcrm.activity.common.enums.activity.ApproveNodeTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ProcessTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.SamplePointApprovalNodeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessRepository;
import com.zte.mcrm.activity.service.approval.ActivityApprovalProcessService;
import com.zte.mcrm.activity.service.approval.event.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 审批节点 如：合规审批
 * @author: 李龙10317843
 * @create: 2023-05-17 10:46
 * @Version 1.0
 **/
@Service
@Log4j2
public class ActivityApprovalProcessServiceImpl implements ActivityApprovalProcessService {
    @Autowired
    ApprovalLeaderNodeService approvalLeaderNodeService;
    @Autowired
    ApprovalComplianceNodeService approvalComplianceNodeService;
    @Autowired
    ActivityApprovalProcessRepository processRepository;
    @Autowired
    ActivityApprovalProcessNodeRepository processNodeRepository;
    @Autowired
    ComplianceManagerAuditorNodeService complianceManagerAuditorNodeService;
    @Autowired
    BizLeaderAuditorNodeService bizLeaderAuditorNodeService;

    @Override
    public AbstractApprovalNodeService getApprovalNodeServiceByApprovalFlowNo(String approvalFlowNo) {
        AssertUtil.assertNotNull(approvalFlowNo);

        // 获取任务对应的审批节点
        ActivityApprovalProcessDO approvalProcessDO = queryByApprovalFlowNo(approvalFlowNo);
        AssertUtil.assertNotNull(approvalProcessDO);
        return getApprovalNodeServiceByProcessType(approvalProcessDO.getProcessType());
    }

    @Override
    public AbstractApprovalNodeService getApprovalNodeServiceByProcessType(String processType) {
        AssertUtil.assertNotNull(processType);

        if (ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.getCode().equals(processType)) {
            return approvalComplianceNodeService;
        }
        if (ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode().equals(processType)) {
            return approvalLeaderNodeService;
        }
        if (ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.isMe(processType)) {
            return complianceManagerAuditorNodeService;
        }
        if (ApproveNodeTypeEnum.in(processType, ApproveNodeTypeEnum.LEVEL4_LEADER_AUDITOR_NODE_CODE, ApproveNodeTypeEnum.LEVEL3_LEADER_AUDITOR_NODE_CODE, ApproveNodeTypeEnum.LEVEL2_LEADER_AUDITOR_NODE_CODE)) {
            return bizLeaderAuditorNodeService;
        }
        if (SamplePointApprovalNodeEnum.in(processType, SamplePointApprovalNodeEnum.values())) {
            return bizLeaderAuditorNodeService;
        }
        throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "not exists dealNodeService!->" + processType);
    }

    @Override
    public ActivityApprovalProcessDO queryByApprovalFlowNo(String approvalFlowNo) {
        AssertUtil.assertNotNull(approvalFlowNo);

        return processRepository.queryByApprovalFlowNo(approvalFlowNo);
    }
}
