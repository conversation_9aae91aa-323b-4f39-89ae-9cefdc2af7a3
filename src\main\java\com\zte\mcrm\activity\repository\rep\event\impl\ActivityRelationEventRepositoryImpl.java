package com.zte.mcrm.activity.repository.rep.event.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.event.ActivityRelationEventMapper;
import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;
import com.zte.mcrm.activity.repository.rep.event.ActivityRelationEventRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;


/**
 * 客户活动关联事件 服务类 
 * <AUTHOR>
 * @date 2023/06/29
 */
@Service
public class ActivityRelationEventRepositoryImpl implements ActivityRelationEventRepository {
    @Autowired
    private IKeyIdService iKeyIdService;
    	
	@Autowired
	private ActivityRelationEventMapper activityRelationEventMapper;
	
	/**
	 * 根据主键获取实体对象
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2023/06/29
     */
	@Override
	public ActivityRelationEventDO get(String rowId) {
		return activityRelationEventMapper.get(rowId);
	}

	/**
	 * 删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2023/06/29
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int delete(String rowId){
		return activityRelationEventMapper.delete(rowId);
	}

	/**
	 * 新增指定记录
	 * @param entity 实体对象
	 * @return 新增记录个数
	 * <AUTHOR>
     * @date 2023/06/29
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int insert(ActivityRelationEventDO entity){
		String emp = Optional.ofNullable(entity.getCreatedBy()).orElse(HeadersProperties.getXEmpNo());
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdatedBy(emp);
		entity.setCreationDate(now);
		entity.setLastUpdateDate(now);
		entity.setEnabledFlag(BooleanEnum.Y.getCode());
		if (StringUtils.isBlank(entity.getRowId())) {
            entity.setRowId(iKeyIdService.getKeyId());
        }		
		return activityRelationEventMapper.insert(entity);
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改记录个数
	 * <AUTHOR>
     * @date 2023/06/29
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int update(ActivityRelationEventDO entity){
		entity.setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
		entity.setLastUpdateDate(new Date());
		return activityRelationEventMapper.update(entity);
	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param entity 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2023/06/29
     */
	@Override
	public List<ActivityRelationEventDO> getList(ActivityRelationEventDO entity){
		if (entity == null) {
			return Lists.newArrayList();
		}
		return activityRelationEventMapper.getList(entity);
	}

}
