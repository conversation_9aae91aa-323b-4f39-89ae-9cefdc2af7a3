package com.zte.mcrm.activity.common.model;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class PageQuery {
    /**
     * 分页号
     */
    private Integer pageNo;
    /**
     * 分页大小
     */
    private Integer pageSize;
    /**
     * 分页索引 limit index, pageSize
     */
    private Integer index;

    public PageQuery(){
    }

    public PageQuery(int pageNo, int pageSize) {
        setPage(pageNo, pageSize);
    }

    /**
     * 设置分页
     *
     * @param pageNo   分页。从1开始
     * @param pageSize 分页大小
     */
    public PageQuery setPage(int pageNo, int pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;

        index = (pageNo - 1) * pageSize;
        return this;
    }

    public void clearPage() {
        pageNo = null;
        pageSize = null;
        index = null;
    }

    public boolean check() {
        // 要么不分页，要么分页是正确的
        return (pageSize == null || pageSize > 0) && (pageNo == null || pageNo > 0);
    }
}
