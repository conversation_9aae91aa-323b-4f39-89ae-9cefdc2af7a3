package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;

public interface ActivitySummaryRdcMapper {
    /**
     * all field insert
     */
    int insert(ActivitySummaryRdcDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivitySummaryRdcDO record);

    /**
     * query by primary key
     */
    ActivitySummaryRdcDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivitySummaryRdcDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivitySummaryRdcDO record);
}