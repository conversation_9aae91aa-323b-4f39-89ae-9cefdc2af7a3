package com.zte.mcrm.activity.application.cto.convert;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.CtoPlanProductTypeEnum;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.customvisit.util.DateUtils;

import java.util.Date;

/**
 * 转换器类，用于构建和设置CtoPlanOrgFinishDO对象。
 */
public class CtoPlanOrgFinishConvert {

    /**
     * 构建完成计数对象。
     *
     * @param rowId        行ID
     * @param employeeType 员工类型
     * @param productType  产品类型
     * @param finishCount  完成数量
     * @param activityIds  对应拓展活动ID数据
     * @return 构建的CtoPlanOrgFinishDO对象
     */
    public static CtoPlanOrgFinishDO buildFinishCount(String rowId, String employeeType, String productType, Integer finishCount, String activityIds) {
        int count = (finishCount == null) ? 0 : finishCount;

        CtoPlanProductTypeEnum productTypeEnum = CtoPlanProductTypeEnum.getEnumByCode(productType);
        if (productTypeEnum == null) {
            throw new IllegalArgumentException("Cannot find productTypeEnum: " + productType);
        }

        CtoPlanOrgFinishDO obj = initializeCtoPlanOrgFinishDO(rowId);
        productTypeEnum.setFinishCount(obj, employeeType, count, activityIds);

        return obj;
    }

    /**
     * 初始化 CtoPlanOrgFinishDO 对象的通用字段。
     *
     * @param rowId 行ID
     * @return 初始化后的 CtoPlanOrgFinishDO 对象
     */
    private static CtoPlanOrgFinishDO initializeCtoPlanOrgFinishDO(String rowId) {
        CtoPlanOrgFinishDO obj = new CtoPlanOrgFinishDO();
        Date now = new Date();
        obj.setRowId(rowId);
        // 执行时间设置为下一天凌晨1点
        obj.setExeWaitTime(DateUtils.adjustDate(0, 1, 1));
        obj.setExeFinishTime(now);
        obj.setLastUpdateDate(now);
        obj.setExeStatus(BooleanEnum.N.getCode());
        return obj;
    }
}
