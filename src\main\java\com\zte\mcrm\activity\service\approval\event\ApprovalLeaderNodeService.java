package com.zte.mcrm.activity.service.approval.event;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingBizTypeEnum;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityStatusLifecycleRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName ApprovalComplianceNodeService
 * @description: 责任部门领导审批
 * @author: 李龙10317843
 * @create: 2023-05-19 16:36
 * @Version 1.0
 **/
@Service
public class ApprovalLeaderNodeService extends AbstractApprovalNodeService {

    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;


    @Override
    String getCurrentActivityStatus() {
        return ActivityStatusEnum.BUSINESS_APPROVAL.getCode();
    }

    @Override
    String getNodeType() {
        return PendingBizTypeEnum.APPROVAL_LEADER.getType();
    }

    @Override
    String getApprovalCompleteStatus(String approvalResult) {
        return BooleanEnum.Y.getCode().equals(approvalResult) ? ActivityStatusEnum.PROGRESS.getCode()
                : ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS.getCode();
    }


}