package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ExhibitionRelationHotelExtMapper extends ExhibitionRelationHotelMapper {

    /***
     * <p>
     * 根据主键Id列表获取对应记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:08
     * @param rowIds 关联酒店记录主键Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO>
     */
    List<ExhibitionRelationHotelDO> queryAllByHotelRowIds(List<String> rowIds);

    /***
     * <p>
     * 根据展会Id列表获取所有关联酒店记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:09
     * @param exhibitionIds 展会Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO>
     */
    List<ExhibitionRelationHotelDO> queryAllByExhibitionIds(List<String> exhibitionIds);

    /***
     * <p>
     * 批量插入数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联酒店记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationHotelDO> records);

    /***
     * <p>
     * 批量修改数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:10
     * @param records 关联酒店记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationHotelDO> records);

    /***
     * <p>
     * 逻辑删除所有与展会Id关联的酒店记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/17 上午11:11
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id列表
     * @return int
     */
    int softDeleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);

}