package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.common.enums.resource.ReserveScheduleOriTypeEnum;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;

import java.util.List;
import java.util.Map;

/**
 * 申请日程资源对应日程信息
 *
 * <AUTHOR>
 */
public interface ActivityResourceReservationScheduleRepository {

    /**
     * dynamic field insert
     */
    int insertSelective(List<ActivityResourceReservationScheduleDO> recordList);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceReservationScheduleDO record);

    /**
     * 通过活动id 查询对应活动日程信息 （来源是活动的日程）
     *
     * @param activityRowIds
     * @return 《活动ID，活动日程资源新》
     */
    Map<String, ActivityResourceReservationScheduleDO> queryScheduleWithActivityRowIds(List<String> activityRowIds);

    /**
     * 通过活动id 查询所有对应活动日程信息
     *
     * @param activityRowIds
     * @return 《活动ID，活动日程资源新》
     */
    Map<String, List<ActivityResourceReservationScheduleDO>> queryAllScheduleWithActivityRowIds(List<String> activityRowIds);

    /**
     * 通过活动id 判断是否变更过日程
     *
     * @param activityRowId
     * @return 《活动ID，活动日程资源新》
     */
    Boolean checkIfScheduleHasChanged(String activityRowId);

    /**
     * 根据日程来源ID查询日程信息
     *
     * @param oriRowIdList 业务来源rowId
     * @param oriTypeEnum  业务来源类型
     * @return key：来源RowId
     */
    Map<String, ActivityResourceReservationScheduleDO> queryScheduleWithOriRowIds(List<String> oriRowIdList, ReserveScheduleOriTypeEnum oriTypeEnum);

    /**
     * 根据日程ID查询日程信息
     * @param rowIdList
     * @return
     */
    List<ActivityResourceReservationScheduleDO> queryScheduleWithRowIds(List<String> rowIdList);

    /**
     * <p>
     * 逻辑删除申请日程资源记录
     *
     * </p>
     * <AUTHOR>
     * @since 2024/4/28 下午4:08
     * @param operator 操作人
     * @param rowIds 主键id列表
     */
    int softDeleteResourceReservationSchedule(String operator, List<String> rowIds);

}
