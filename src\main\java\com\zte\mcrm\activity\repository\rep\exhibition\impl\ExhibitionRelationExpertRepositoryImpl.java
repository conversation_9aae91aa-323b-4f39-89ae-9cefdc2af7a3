package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationExpertExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationExpertRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ExhibitionRelationExpertRepositoryImpl implements ExhibitionRelationExpertRepository {
    @Resource
    private ExhibitionRelationExpertExtMapper exhibitionRelationExpertExtMapper;

    @Override
    public int insertSelective(ExhibitionRelationExpertDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        return exhibitionRelationExpertExtMapper.insertSelective(record);
    }


    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationExpertDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        return exhibitionRelationExpertExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ExhibitionRelationExpertDO>> queryExpertsWithExhibitionRowId(List<String> exhibitionRowIds) {
        return CollectionUtils.isEmpty(exhibitionRowIds) ? Collections.emptyMap()
                : exhibitionRelationExpertExtMapper.queryExpertsWithExhibitionRowId(exhibitionRowIds)
                .stream().collect(
                        Collectors.groupingBy(ExhibitionRelationExpertDO::getExhibitionRowId));
    }

    @Override
    public Map<String, ExhibitionRelationExpertDO> getExpertNoMapWithExhibitionRowId(String exhibitionRowId) {
        if (StringUtils.isBlank(exhibitionRowId)) {
            return new HashMap<>();
        }

        return exhibitionRelationExpertExtMapper.queryExpertsWithExhibitionRowId(Lists.newArrayList(exhibitionRowId))
                .stream().collect(Collectors.toMap(ExhibitionRelationExpertDO::getEmployeeNo, i -> i, (u, v) -> u));
    }

    @Override
    public List<ExhibitionRelationExpertDO> queryExhibitionRelationExpertList(String exhibitionRowId) {
        return exhibitionRelationExpertExtMapper.queryExpertsWithExhibitionRowId(Lists.newArrayList(exhibitionRowId));
    }

    @Override
    public List<ExhibitionRelationExpertDO> queryCommonExperts(int size) {
        return size <= 0 ? Collections.emptyList()
                :exhibitionRelationExpertExtMapper.queryCommonExperts(size);
    }
}
