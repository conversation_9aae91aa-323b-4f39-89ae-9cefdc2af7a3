package com.zte.mcrm.activity.repository.rep.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationTalkDO;

import java.util.List;

/**
 * ClassName: ActivityRelationTalkRepository
 * Description:
 * date: 2023/5/25 18:52
 *
 * <AUTHOR>
 */
public interface ActivityRelationTalkRepository {

    int deleteByActivityIds(String operator, List<String> activityIds);

    /**
     * 新增一条数据
     *
     * @param record 谈参数据
     * @return 返回结果
     */
    int insertSelective(List<ActivityRelationTalkDO> record);

    /**
     * 新增一条数据
     *
     * @param  activityRowId 活动ID
     * @return 返回结果
     */
    ActivityRelationTalkDO queryByActivityId(String activityRowId);

    /**
     * 查询活动下所有的谈参
     *
     * @param activityRowId 活动Id
     * @return List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/6/8 11:29
     */
    List<ActivityRelationTalkDO> queryAllByActivityRowId(String activityRowId);


    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationTalkDO> queryAllActivityWithNotEnable(String activityRowId);

}
