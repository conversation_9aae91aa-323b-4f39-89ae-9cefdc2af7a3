package com.zte.mcrm.activity.application.export.impl;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.exhibition.ScheduleOrchestrationQueryAppService;
import com.zte.mcrm.activity.application.exhibition.convert.ScheduleOrchestrationQueryConvertor;
import com.zte.mcrm.activity.application.export.ExhibitionExportService;
import com.zte.mcrm.activity.application.export.convert.ExportExhibitionConvertor;
import com.zte.mcrm.activity.application.export.param.ExportExhibitionParam;
import com.zte.mcrm.activity.application.export.vo.*;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.ExcelConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.export.ExcelExportHelper;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.service.exhibition.ExhibitionQueryService;
import com.zte.mcrm.activity.service.exhibition.param.ExhibitionBoardLeaderBO;
import com.zte.mcrm.activity.service.exhibition.param.ExhibitionResourceExportDataSource;
import com.zte.mcrm.activity.web.controller.exhibition.vo.*;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationExportParam;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationVO;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.adapter.vo.ContactVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.customvisit.util.DateUtils;
import com.zte.mcrm.moa.common.MoaConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.SUBTRACT;
import static com.zte.mcrm.activity.common.constant.LookupConstant.EXPORT_CONFIG;
import static com.zte.mcrm.activity.common.constant.LookupConstant.LOOKUP_EXHIBITION_CONFIG;
import static com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum.leaderOrchestrationExportStatus;
import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;
import static com.zte.mcrm.customvisit.util.DateUtils.MMDD;
import static com.zte.mcrm.customvisit.util.DateUtils.YYYY;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午2:06
 **/
@Slf4j
@Service
public class ExhibitionExportServiceImpl implements ExhibitionExportService {

    private final String className = getClass().getName();
    @Autowired
    private ExportExhibitionConvertor exportExhibitionConvertor;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private ExhibitionQueryService exhibitionQueryService;

    @Autowired
    private LocaleMessageSourceBean localeMessage;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;

    @Autowired
    private ScheduleOrchestrationQueryAppService scheduleOrchestrationQueryAppService;

    @Autowired
    private ScheduleOrchestrationQueryConvertor scheduleOrchestrationQueryConvertor;

    @Autowired
    private LookUpExtService lookUpExtService;

    @Autowired
    private AccountAdapter accountAdapter;

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;

    @Override
    public ByteArrayBody exportLeaderView(BizRequest<ExportExhibitionParam> req) {
        ExportExhibitionParam param = req.getParam();
        String exhibitionRowId = param.getExhibitionRowId();
        boolean isZh = LANGUAGE_ZH_CN.equals(req.getLangId());
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(exhibitionRowId);
        if (Objects.isNull(exhibitionInfo)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, localeMessage.getMessage("exhibition.info.does.not.exist"));
        }

        String employeeNo = param.getEmployeeNo();
        String name = localeMessage.getMessage("leader.view");
        String sheetName = CharacterConstant.ALL;
        if (StringUtils.isNotBlank(employeeNo)) {
            EmployeeInfoDTO employeeInfo = userCenterService.getUserInfo(employeeNo);
            if (Objects.isNull(employeeInfo)) {
                throw new BusiException(RetCode.BUSINESSERROR_CODE, localeMessage.getMessage("user.information.does.not.exist"));
            }

            name = isZh ? employeeInfo.getEmpName() : employeeInfo.getEmpNameEN();
            sheetName = name;
        }

        String fileName = getLeaderViewFileName(exhibitionInfo.getStartTime(), exhibitionInfo.getExhibitionName(), name);
        ExhibitionBoardLeaderBO boardLeaderBO = new ExhibitionBoardLeaderBO();
        boardLeaderBO.setExhibitionRowId(exhibitionRowId);
        boardLeaderBO.setEmployeeNo(employeeNo);
        boardLeaderBO.setDealStatus(leaderOrchestrationExportStatus());
        List<ExhibitionDataBoardViewVO> exhibitionDataBoardViewList = exhibitionQueryService.getDataBoardOfLeaderView(boardLeaderBO);
        SimpleExcelExportModel exportModel = exportExhibitionConvertor.toLeaderViewSimpleExcelExportModel(exhibitionDataBoardViewList, sheetName);
        try {
            return ExcelExportHelper.simpleExportExcel(exportModel, fileName);
        } catch (IOException e) {
            log.error("class={}, method={}, 数据看板领导视图导出异常, param={}, 异常信息={}", className,
                    Thread.currentThread().getStackTrace()[1].getMethodName(), param, e.getMessage());
        }
        return null;
    }

    @Override
    public ByteArrayBody exportExpertView(BizRequest<ExportExhibitionParam> req) {
        ExportExhibitionParam param = req.getParam();
        String exhibitionRowId = param.getExhibitionRowId();
        String expertNo = param.getExpertNo();
        String expertDes = employeeAdapter.getEmployeeNameNoByShortNo(expertNo);
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(exhibitionRowId);
        if (Objects.isNull(exhibitionInfo)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, localeMessage.getMessage("exhibition.info.does.not.exist"));
        }

        ExhibitionExpertBoardVO vo = exhibitionQueryService.getExhibitionExpertBoard(exhibitionRowId);
        List<ExhibitionExpertBoardDetailVO> expertBoardDetailVOList = vo.getDetailMap().values().stream().flatMap(Collection::stream)
                .filter(item-> StringUtils.isNotBlank(item.getZteExpertDes())
                        && item.getZteExpertDes().contains(expertNo))
                .sorted(Comparator.comparing(ExhibitionExpertBoardDetailVO::getScheduleDate).thenComparing(ExhibitionExpertBoardDetailVO::getScheduleTime))
                .collect(Collectors.toList());

        SimpleExcelExportModel model = new SimpleExcelExportModel();
        List<ExportExpertViewVO> exportViewList = new ArrayList<>();
        for (int index = 1; index <= expertBoardDetailVOList.size() ; index++) {
            ExportExpertViewVO out = new ExportExpertViewVO();
            BeanUtils.copyProperties(expertBoardDetailVOList.get(index - 1), out);
            out.setIndex(String.valueOf(index));
            out.setMktName(com.zte.mcrm.activity.common.util.StringUtils.getDefValIfBlank(out.getMktName(), CharacterConstant.ZH_NULL));
            out.setMtoName(com.zte.mcrm.activity.common.util.StringUtils.getDefValIfBlank(out.getMtoName(), CharacterConstant.ZH_NULL));
            exportViewList.add(out);
        }
        model.addSheetData(expertDes, exportViewList, ExportExpertViewVO.class);

        try {
            // 【3】生成Excel文件
            String fileName = getRoomViewFileName(exhibitionInfo.getStartTime(),exhibitionInfo.getExhibitionName(),expertDes);
            return ExcelExportHelper.simpleExportExcel(model, fileName);
        } catch (IOException e) {
            log.error("class={}, method={}, exhibitionRowId={}, 异常信息={}", className,
                    Thread.currentThread().getStackTrace()[1].getMethodName(), exhibitionRowId, e.getMessage());
        }
        return null;
    }

    @Override
    public ByteArrayBody exportRoomView(BizRequest<ExportExhibitionParam> req) {
        ExportExhibitionParam param = req.getParam();
        String exhibitionRowId = param.getExhibitionRowId();
        String roomName = param.getRoomName();
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(exhibitionRowId);
        if (Objects.isNull(exhibitionInfo)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, localeMessage.getMessage("exhibition.info.does.not.exist"));
        }

        ExhibitionRoomBoardVO vo = exhibitionQueryService.getExhibitionRoomBoard(exhibitionRowId);

        List<ExhibitionRoomBoardDetailVO> roomBoardDetailVOList = vo.getDetailMap().values().stream().flatMap(Collection::stream)
                .sorted(Comparator.comparing(ExhibitionRoomBoardDetailVO::getScheduleDate).thenComparing(ExhibitionRoomBoardDetailVO::getScheduleTime))
                .collect(Collectors.toList());

        // 【2】转为excel导出模型
        String sheetName = CharacterConstant.ALL;
        SimpleExcelExportModel model = new SimpleExcelExportModel();
        if (StringUtils.isNotBlank(roomName)) {
            roomBoardDetailVOList = roomBoardDetailVOList.stream().filter(e -> StringUtils.equals(roomName, e.getPlaceName())).collect(Collectors.toList());
            sheetName = null!=vo.getRooms()  ? vo.getRooms().get(roomName) : roomName;
        }
        model.addSheetData(sheetName, roomBoardDetailVOList.stream().map(info -> {
            ExportRoomViewVO out = new ExportRoomViewVO();
            BeanUtils.copyProperties(info, out);
            return out;
        }).collect(Collectors.toList()), ExportRoomViewVO.class);

        try {
            // 【3】生成Excel文件
            String fileName = getRoomViewFileName(exhibitionInfo.getStartTime(),exhibitionInfo.getExhibitionName(),roomName);
            return ExcelExportHelper.simpleExportExcel(model, fileName);
        } catch (IOException e) {
            log.error("class={}, method={}, exhibitionRowId={}, 异常信息={}", className,
                    Thread.currentThread().getStackTrace()[1].getMethodName(), exhibitionRowId, e.getMessage());
        }
        return null;
    }

    @Override
    public ByteArrayBody exportRegistrationDetail(BizRequest<ExportExhibitionParam> req) throws IOException {
        ExportExhibitionParam param = req.getParam();
        String exhibitionRowId = param.getExhibitionRowId();
        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(exhibitionRowId);
        if (Objects.isNull(exhibitionInfo)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, localeMessage.getMessage("exhibition.info.does.not.exist"));
        }

        boolean noExportInfo = !param.getNeedExportHotelAndCar() && !param.getNeedExportOrchestration()
                && !param.getNeedExportCusContacts() && !param.getNeedExportFee();
        if (noExportInfo) {
            return null;
        }

        // 资源编排数据导出
        SimpleExcelExportModel excelExportModel = new SimpleExcelExportModel(false);
        if (param.getNeedExportOrchestration()) {
            ScheduleOrchestrationExportParam scheduleOrchestrationExportParam = new ScheduleOrchestrationExportParam();
            scheduleOrchestrationExportParam.setExhibitionRowId(exhibitionRowId);
            scheduleOrchestrationExportParam.setOnlyPassActivity(false);
            BizRequest<ScheduleOrchestrationExportParam> queryReq = BizRequestUtil.createWithCurrentUserSecurity(scheduleOrchestrationExportParam);
            BizResult<ScheduleOrchestrationVO> res = scheduleOrchestrationQueryAppService.scheduleOrchestrationEditList(queryReq);
            ScheduleOrchestrationVO vo = res.getData();
            excelExportModel = scheduleOrchestrationQueryConvertor.exportScheduleOrchestrationData(vo);
        }

        // 酒店车辆数据导出
        if (param.getNeedExportHotelAndCar()) {
            // 加载酒店信息
            List<ExportActivityHotelVO> hotelExportList = Lists.newArrayList();
            ExhibitionResourceExportDataSource exhibitionResourceExportData = exhibitionQueryService.getExhibitionResourceExportData(exhibitionRowId);
            List<ExhibitionHotelDetailVO> hotelData = Optional.ofNullable(exhibitionResourceExportData.getExhibitionHotelDetailList()).orElse(new ArrayList<>());
            for (ExhibitionHotelDetailVO detailVO : hotelData) {
                ExportActivityHotelVO exportActivityHotelVO = new ExportActivityHotelVO();
                BeanUtils.copyProperties(detailVO, exportActivityHotelVO);
                hotelExportList.add(exportActivityHotelVO);
            }
            excelExportModel.addSheetData(ExcelConstant.HOTEL, hotelExportList, ExportActivityHotelVO.class);
            // 加载车辆信息
            List<ExportActivityCarVO> carExportList = Lists.newArrayList();
            List<ExhibitionCarDetailVO> carData = Optional.ofNullable(exhibitionResourceExportData.getExhibitionCarDetailList()).orElse(new ArrayList<>());
            for (ExhibitionCarDetailVO detailVO : carData) {
                ExportActivityCarVO exportActivityCarVO = new ExportActivityCarVO();
                BeanUtils.copyProperties(detailVO, exportActivityCarVO);
                carExportList.add(exportActivityCarVO);
            }
            excelExportModel.addSheetData(ExcelConstant.CAR, carExportList, ExportActivityCarVO.class);
        }

        // 客户参与人导出
        if (param.getNeedExportCusContacts()) {
            List<ExportActivityCustomerPeopleVO> customerExportList = exportContacts(exhibitionRowId);

            excelExportModel.addSheetData(ExcelConstant.CUST_SHEET_NAME, customerExportList, ExportActivityCustomerPeopleVO.class);
        }

        // 费用导出
        if (param.getNeedExportFee()) {
            List<ExportActivityFeeVO> customerExportList = Lists.newArrayList();
            List<ExhibitionFeeDetailsVO> customerData = CollectionExtUtils.getListOrDefaultEmpty(exhibitionQueryService.getExhibitionFeeDetailList(exhibitionRowId));
            for (ExhibitionFeeDetailsVO detailVO : customerData) {
                ExportActivityFeeVO exportActivityFeeVO = new ExportActivityFeeVO();
                BeanUtils.copyProperties(detailVO, exportActivityFeeVO);
                customerExportList.add(exportActivityFeeVO);
            }

            excelExportModel.addSheetData(ExcelConstant.FEE_SHEET_NAME, customerExportList, ExportActivityFeeVO.class);
        }

        return ExcelExportHelper.simpleExportExcel(excelExportModel, ExcelConstant.EXPORT_DATA + DateUtils.convertDateToString(new Date(), MMDD));
    }

    private List<ExportActivityCustomerPeopleVO> exportContacts(String exhibitionRowId) {
        List<ExportActivityCustomerPeopleVO> customerExportList = Lists.newArrayList();
        List<ExhibitionHotelDetailVO> customerData = CollectionExtUtils.getListOrDefaultEmpty(exhibitionQueryService.getExhibitionCustomerPeopleDetailList(exhibitionRowId));
        //获取是否查询手机号快码值
        String exhibition = lookUpExtService.getLookUpMapByType(LOOKUP_EXHIBITION_CONFIG).get(EXPORT_CONFIG);
        Map<String, String> exportPhoneMap = getExportPhoneMap(customerData,exhibition);

        for (ExhibitionHotelDetailVO detailVO : customerData) {
            ExportActivityCustomerPeopleVO exportActivityCustomerPeopleVO = new ExportActivityCustomerPeopleVO();
            BeanUtils.copyProperties(detailVO, exportActivityCustomerPeopleVO);
            String concatNo = Optional.ofNullable(detailVO.getContactNo())
                    .filter(StringUtils::isNotBlank)
                    .orElse(detailVO.getZetPeopleName());
            exportActivityCustomerPeopleVO.setPhoneNumber(exportPhoneMap.get(concatNo));
            customerExportList.add(exportActivityCustomerPeopleVO);
        }
        return customerExportList;
    }

    /**
     * 获取客户参与人、我司参与人手机号码列表
     * @param customerData
     * @return
     */
    protected Map<String, String> getExportPhoneMap(List<ExhibitionHotelDetailVO> customerData,String exportConfig) {
        Map<String, String> exportPhoneMap = new HashMap<>();
        if (MoaConst.CODE_0.equals(exportConfig)) {
            return exportPhoneMap;
        }
        try {
            // 提取所有客户参与人Id
            List<String> contactNos = customerData.stream().map(ExhibitionHotelDetailVO::getContactNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            //客户参与人手机号分批处理
            List<List<String>> batches = Lists.partition(contactNos, NumberConstant.TWO_HUNDRED);
            for (List<String> batch : batches) {
                List<ContactVO> contactVOList = accountAdapter.getContactNums(batch);
                exportPhoneMap.putAll(ListUtils.emptyIfNull(contactVOList).stream()
                        .collect(Collectors.toMap(
                                ContactVO::getConPerNum,
                                ContactVO::fetchMobile
                        )));
            }

            //我司参与人编号
            Set<String> zetPeoples = customerData.stream().map(ExhibitionHotelDetailVO::getZetPeopleName).filter(Objects::nonNull).collect(Collectors.toSet());
            //我司参与人获取手机号
            Map<String, PersonInfoDTO> userInfo = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(zetPeoples)).getBo();
            exportPhoneMap.putAll(userInfo.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().fetchAllModelNum()
                    )));
        } catch (Exception e) {
            log.error("获取客户参与人、我司参与人手机号码列表异常信息:{}", e.getMessage());
        }

        return exportPhoneMap;
    }

    private String getLeaderViewFileName(Date exhibitionStartTime, String exhibitionName, String str) {
        StringBuilder fileName = new StringBuilder();
        fileName.append(DateUtils.convertDateToString(exhibitionStartTime, YYYY));
        fileName.append(exhibitionName);
        fileName.append(localeMessage.getMessage("senior.meeting"));
        fileName.append(DateUtils.convertDateToString(new Date(), MMDD));
        fileName.append(SUBTRACT);
        fileName.append(str);
        return fileName.toString();
    }

    private String getRoomViewFileName(Date exhibitionStartTime, String exhibitionName, String roomName) {
        StringBuilder fileName = new StringBuilder();
        fileName.append(DateUtils.convertDateToString(exhibitionStartTime, YYYY));
        fileName.append(exhibitionName);
        fileName.append(localeMessage.getMessage("senior.meeting"));
        fileName.append(DateUtils.convertDateToString(new Date(), MMDD));
        fileName.append(SUBTRACT);
        if (StringUtils.isNotBlank(roomName)) {
            fileName.append(roomName);
        }else {
            fileName.append(localeMessage.getMessage("room.view"));
        }
        return fileName.toString();
    }
}
