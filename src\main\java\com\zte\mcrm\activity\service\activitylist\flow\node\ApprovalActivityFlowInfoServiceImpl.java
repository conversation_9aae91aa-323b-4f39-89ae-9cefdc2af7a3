package com.zte.mcrm.activity.service.activitylist.flow.node;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityFlowNodeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ApproveResultEnum;
import com.zte.mcrm.activity.common.enums.activity.ProcessTypeEnum;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.service.activitylist.convert.ActivityFlowConvert;
import com.zte.mcrm.activity.service.activitylist.flow.ActivityFlowInfoDataSource;
import com.zte.mcrm.activity.service.activitylist.flow.BaseActivityFlowInfoCreateService;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessNodeQueryVO;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessNodeConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 审批节点
 *
 * <AUTHOR>
 */
@Service
public class ApprovalActivityFlowInfoServiceImpl extends BaseActivityFlowInfoCreateService {

    public ApprovalActivityFlowInfoServiceImpl() {
        super(ActivityFlowNodeEnum.APPROVAL);
    }


    @Override
    public ActivityFlowInfoVO createActivityFlowInfo(ActivityFlowInfoDataSource data) {
        if (ActivityStatusEnum.in(data.getActivityInfo().getActivityStatus(), ActivityStatusEnum.DRAFT)) {
            return null;
        }
        // 活动最新的审批相关的状态（第一次进行审批）
        // 活动最新的审批相关的状态（变更发起的审批）
        ActivityStatusLifecycleDO changeLog = data.fetchLastLifecycle(ActivityStatusEnum.CHANGE);
        /*
          关于审批情况：
          【1】没有发起过变更
             则在根据第一次审批approvalLog来决定如何显示
          【2】有发起过变更
             则根据发起变更changeLog来决定如何显示
          总的来说就是：没有变更就按正常第一次审批展示（有展示，无不展示）；有发起变更，则按变更时审批情况展示（有审批展示，无审批不展示）
         */
        if (changeLog == null) {
            return firstApprovalCase(data);
        } else {
            return changeApprovalCase(data);
        }
    }

    /**
     * 提交活动时第一次发起审批情况
     *
     * @param data
     * @return
     */
    private ActivityFlowInfoVO firstApprovalCase(ActivityFlowInfoDataSource data) {
        return packActivityFlowInfoVO(data, data.fetchFirstApprovalProcess());
    }

    /**
     * 变更活动时第一次发起审批情况
     *
     * @param data
     * @return
     */
    private ActivityFlowInfoVO changeApprovalCase(ActivityFlowInfoDataSource data) {

        return packActivityFlowInfoVO(data, data.fetchChangeApprovalProcess());
    }

    /**
     * 打包审批情况
     *
     * @param data
     * @return
     */
    private ActivityFlowInfoVO packActivityFlowInfoVO(ActivityFlowInfoDataSource data, List<ActivityApprovalProcessDO> approvalProcessList) {
        ActivityStatusLifecycleDO approvalLog = data.fetchLastLifecycle(ActivityStatusEnum.COMPLIANCE_APPROVAL, ActivityStatusEnum.BUSINESS_APPROVAL);
        if (approvalLog == null || CollectionUtils.isEmpty(approvalProcessList)) {
            return null;
        }

        ActivityApprovalProcessDO complianceApproval = approvalProcessList.stream().filter(e -> ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.isMe(e.getProcessType())).findFirst().orElse(null);
        ActivityApprovalProcessDO bizApproval = approvalProcessList.stream().filter(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getProcessType())).findFirst().orElse(null);

        List<ActivityApprovalProcessNodeDO> complianceApprovalNodeList = complianceApproval == null ? Collections.emptyList() : data.fetchApprovalProcessNode(complianceApproval.getRowId());
        List<ActivityApprovalProcessNodeDO> bizApprovalNodeList = bizApproval == null ? Collections.emptyList() : data.fetchApprovalProcessNode(bizApproval.getRowId());

        ActivityFlowInfoVO approvalFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.APPROVAL, BooleanEnum.Y, ActivityFlowNodeEnum.FLOW_DOING);
        // 通过活动状态判断该节点的完成状态
        String stageStatus = ActivityStatusEnum.in(data.getActivityInfo().getActivityStatus(), ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.COMPLIANCE_APPROVAL)
                ? ActivityFlowNodeEnum.FLOW_DOING : ActivityFlowNodeEnum.FLOW_DONE;
        ActivityFlowConvert.fillLightTriggerInfo(approvalFlowInfoVO, null, approvalLog.getLastUpdateDate(), stageStatus, null);

        // 填充审批阶段、审批节点等信息
        Map<String, List<ActivityApprovalProcessNodeQueryVO>> processNodeMap = packApprovalProcess(data, approvalProcessList, complianceApprovalNodeList, bizApprovalNodeList);

        List<ActivityApprovalProcessNodeQueryVO> nodeList = decideApprovalNodeShow(data, processNodeMap);

        approvalFlowInfoVO.setApprovedNum((int) nodeList.stream().filter(e -> ApproveResultEnum.AGREE.isMe(e.getApproveResult())).count());
        approvalFlowInfoVO.setUnApprovedNum((int) nodeList.stream().filter(e -> ApproveResultEnum.PENDING_APPROVAL.isMe(e.getApproveResult())).count());
        approvalFlowInfoVO.setDisAgree((int) nodeList.stream().filter(e -> ApproveResultEnum.DISAGREE.isMe(e.getApproveResult())).count());
        approvalFlowInfoVO.setCanceledNum((int) nodeList.stream().filter(e -> ApproveResultEnum.CANCELED.isMe(e.getApproveResult())).count());

        approvalFlowInfoVO.setProcessMap(processNodeMap);

        // 触发时间取最新节点最后更新时间
        List<ActivityApprovalProcessNodeQueryVO> changedNodeList = nodeList.stream().filter(e -> ApproveResultEnum.in(e.getApproveResult(), ApproveResultEnum.AGREE, ApproveResultEnum.DISAGREE, ApproveResultEnum.CANCELED))
                .sorted(Comparator.comparing(ActivityApprovalProcessNodeQueryVO::getCreationDate))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changedNodeList)) {
            approvalFlowInfoVO.setTriggerTime(changedNodeList.get(changedNodeList.size() - 1).getLastUpdateDate());
        }

        return approvalFlowInfoVO;
    }

    /**
     * 决定节点展示
     *
     * @param data
     * @param processNodeMap
     * @return
     */
    private List<ActivityApprovalProcessNodeQueryVO> decideApprovalNodeShow(ActivityFlowInfoDataSource data, Map<String, List<ActivityApprovalProcessNodeQueryVO>> processNodeMap) {
        List<ActivityApprovalProcessNodeQueryVO> nodeList = new ArrayList<>(processNodeMap.getOrDefault(ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.getCode(), Collections.emptyList()));

        if (!ActivityStatusEnum.COMPLIANCE_APPROVAL.isMe(data.getActivityInfo().getActivityStatus())) {
            List<ActivityApprovalProcessNodeQueryVO> list = processNodeMap.getOrDefault(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode(), Collections.emptyList());
            // 只展示已经审批(同意、转交、拒绝)+待审批的
            List<ActivityApprovalProcessNodeQueryVO> dealedList = list.stream()
                    .filter(e -> ApproveResultEnum.in(e.getApproveResult(), ApproveResultEnum.transfer, ApproveResultEnum.AGREE, ApproveResultEnum.DISAGREE, ApproveResultEnum.CANCELED))
                    .sorted(Comparator.comparing(ActivityApprovalProcessNodeQueryVO::getLevel).reversed().thenComparing(ActivityApprovalProcessNodeQueryVO::getApproveTime))
                    .collect(Collectors.toList());
            // 并行节点会有多个待审批人，因此这里返回列表
            List<ActivityApprovalProcessNodeQueryVO> undealNodeList = list.stream()
                    .filter(e -> ApproveResultEnum.in(e.getApproveResult(), ApproveResultEnum.PENDING_APPROVAL)).collect(Collectors.toList());

            List<ActivityApprovalProcessNodeQueryVO> newList = new ArrayList<>(dealedList);
            if (CollectionUtils.isNotEmpty(undealNodeList)) {
                newList.addAll(undealNodeList);
            }
            nodeList.addAll(newList);
            processNodeMap.put(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode(), newList);
        } else {
            // 如果只是到合规审批，则领导审批信息不展示
            processNodeMap.remove(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode());
        }

        return nodeList;
    }

    /**
     * 打包审批阶段与节点信息
     *
     * @param approvalProcessList
     * @param complianceApprovalNodeList
     * @param bizApprovalNodeList
     * @return
     */
    public Map<String, List<ActivityApprovalProcessNodeQueryVO>> packApprovalProcess(ActivityFlowInfoDataSource data,
                                                                                      List<ActivityApprovalProcessDO> approvalProcessList,
                                                                                      List<ActivityApprovalProcessNodeDO> complianceApprovalNodeList,
                                                                                      List<ActivityApprovalProcessNodeDO> bizApprovalNodeList) {
        Map<String, List<ActivityApprovalProcessNodeQueryVO>> map = new HashMap<>();
        for (ActivityApprovalProcessDO processDO : approvalProcessList) {
            if (ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.isMe(processDO.getProcessType())) {
                map.put(ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.getCode(), ActivityApprovalProcessNodeConverter.buildOfQuery(complianceApprovalNodeList));
            } else {
                // 目前除了业务，就是领导。此外传入的数据只有这两类。这里用else而不是用else if是分支覆盖不了不存在的情况
                map.put(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode(), ActivityApprovalProcessNodeConverter.buildOfQuery(bizApprovalNodeList));
            }
        }

        // 如果是审批不通过，后续的待办都不显示了
        if (ActivityStatusEnum.in(data.getActivityInfo().getActivityStatus(), ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS, ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS)) {
            List<ActivityApprovalProcessNodeQueryVO> complianceList = map.getOrDefault(ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.getCode(), Collections.emptyList());
            List<ActivityApprovalProcessNodeQueryVO> leaderList = map.getOrDefault(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode(), Collections.emptyList());

            map.put(ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.getCode(), complianceList.stream().filter(e -> !ApproveResultEnum.PENDING_APPROVAL.isMe(e.getApproveResult())).collect(Collectors.toList()));
            map.put(ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.getCode(), leaderList.stream().filter(e -> !ApproveResultEnum.PENDING_APPROVAL.isMe(e.getApproveResult())).collect(Collectors.toList()));
        }

        return map;
    }


}
