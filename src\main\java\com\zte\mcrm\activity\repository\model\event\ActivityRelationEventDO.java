package com.zte.mcrm.activity.repository.model.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
* 客户活动关联事件 实体类
* <AUTHOR>
* @date 2023/06/29
*/

@Setter
@Getter
@ToString
@ApiModel(description="客户活动关联事件")
public class ActivityRelationEventDO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String rowId;

    @ApiModelProperty(value = "业务ID")
    private String bizId;

    @ApiModelProperty(value = "业务类型。枚举：ActivityEventBizType")
    private String bizType;

    @ApiModelProperty(value = "操作类型。枚举：ActivityEventOperationTypeEnum")
    private String operationType;

    @ApiModelProperty(value = "事件执行状态。枚举：ActivityEventExcutionStatusEnum")
    private String excutionStatus;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "事件执行时间")
    private Date excuteTime;

    @ApiModelProperty(value = "事件执行失败次数，默认为0")
    private Integer failedTimes;

    @ApiModelProperty(value = "记录创建人")
    private String createdBy;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "记录创建时间")
    private Date creationDate;

    @ApiModelProperty(value = "记录最近修改人")
    private String lastUpdatedBy;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "记录最近修改时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "记录有效标识。Y-有效，N-无效。枚举：BooleanEnum")
    private String enabledFlag;

}