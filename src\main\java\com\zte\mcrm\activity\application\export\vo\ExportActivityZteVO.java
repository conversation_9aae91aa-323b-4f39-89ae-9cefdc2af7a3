package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户活动-我司信息（参与人）
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Getter
@Setter
public class ExportActivityZteVO {
    @Excel(name = "活动编号", orderNum = "1")
    private String activityRequestNo;
    @Excel(name = "议题", orderNum = "2")
    private String activityTitle;
    @Excel(name = "姓名", orderNum = "3")
    private String peopleName;
    @Excel(name = "四层单位编码", orderNum = "4")
    private String peopleDepartmentCode;
    @Excel(name = "四层单位名称", orderNum = "5")
    private String peopleDepartmentName;
    @Excel(name = "职务", orderNum = "6")
    private String positionName;
    @Excel(name = "角色", orderNum = "7")
    private String peopleTypeName;
}
