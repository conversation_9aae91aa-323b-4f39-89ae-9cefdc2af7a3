package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;

public interface ActivityRelationProjectMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationProjectDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationProjectDO record);

    /**
     * query by primary key
     */
    ActivityRelationProjectDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationProjectDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationProjectDO record);
}