package com.zte.mcrm.activity.application.cto.helper;

import com.zte.mcrm.activity.application.model.CtoReportApIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportItemIndicatorVO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * {@code @description CTO报表Excel标签处理辅助类}}
 *
 * <AUTHOR>
 * @date 2024/12/23 下午5:17
 */
@Getter
@Setter
public class CtoPlanSheetDataHelper {
    /* Started by AICoder, pid:db5b6bc5f2a2f0814f14095480925a4063850575 */

    /**
     * 初始化sheet页数据
     *
     * @param ctoPlanOrgFinishDOS     事业部完成数据列表
     * @param ctoPlanProductFinishDOS 产品完成数据列表
     */
    public void initSheetData(List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS, List<CtoPlanProductFinishDO> ctoPlanProductFinishDOS) {
        // 使用forEach方法替代stream().forEach以提高可读性和性能
        ctoPlanOrgFinishDOS.forEach(this::setDefaultsForOrgFinish);

        ctoPlanProductFinishDOS.forEach(this::setDefaultsForProductFinish);
    }

    /**
     * 设置事业部完成数据的默认值
     *
     * @param data 事业部完成数据
     */
    private void setDefaultsForOrgFinish(CtoPlanOrgFinishDO data) {
        data.setRanTarget(Optional.ofNullable(data.getRanTarget()).orElse(0));
        data.setRanFinish(Optional.ofNullable(data.getRanFinish()).orElse(0));

        data.setCcnTarget(Optional.ofNullable(data.getCcnTarget()).orElse(0));
        data.setCcnFinish(Optional.ofNullable(data.getCcnFinish()).orElse(0));

        data.setBnTarget(Optional.ofNullable(data.getBnTarget()).orElse(0));
        data.setBnFinish(Optional.ofNullable(data.getBnFinish()).orElse(0));

        data.setFmTarget(Optional.ofNullable(data.getFmTarget()).orElse(0));
        data.setFmFinish(Optional.ofNullable(data.getFmFinish()).orElse(0));

        data.setSnTarget(Optional.ofNullable(data.getSnTarget()).orElse(0));
        data.setSnFinish(Optional.ofNullable(data.getSnFinish()).orElse(0));

    }

    /**
     * 设置产品完成数据的默认值
     *
     * @param data 产品完成数据
     */
    private void setDefaultsForProductFinish(CtoPlanProductFinishDO data) {
        data.setAccountTarget(Optional.ofNullable(data.getAccountTarget()).orElse(0));
        data.setAccountFinish(Optional.ofNullable(data.getAccountFinish()).orElse(0));
    }

    /* Ended by AICoder, pid:db5b6bc5f2a2f0814f14095480925a4063850575 */

    /**
     * 填充CTO握手工作表数据。
     *
     * @param ctoReportIndicatorVOS 数据列表
     * @param workbook              工作簿对象
     * @param sheetNo               工作表编号
     */
    public void fillSheet(List<CtoReportIndicatorVO> ctoReportIndicatorVOS, Workbook workbook, int sheetNo) {
        Sheet sheet = workbook.getSheetAt(sheetNo);
        int rowIndex = sheet.getRow(1).getRowNum();
        for (CtoReportIndicatorVO reportIndicator : ctoReportIndicatorVOS) {
            Row row = sheet.getRow(++rowIndex);

            // 填充单元格数据
            row.getCell(2).setCellValue(reportIndicator.getHandshakeTarget());
            row.getCell(3).setCellValue(reportIndicator.getHandshakeFinish());
            row.getCell(4).setCellValue(reportIndicator.getHandshakeFinishRate());
            row.getCell(5).setCellValue(reportIndicator.getAccountTarget());
            row.getCell(6).setCellValue(reportIndicator.getAccountFinish());
            row.getCell(7).setCellValue(reportIndicator.getAccountFinishRate());
            row.getCell(8).setCellValue(reportIndicator.getAverageFinishRate());
        }
    }

    /**
     * 填充报告指标数据到工作表中。
     *
     * @param ctoReportApIndicatorVOS 指标数据列表
     * @param workbook                工作簿对象
     */
    public void fillSheet3(List<CtoReportApIndicatorVO> ctoReportApIndicatorVOS, Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(2);
        int rowIndex = sheet.getRow(0).getRowNum();
        for (CtoReportApIndicatorVO reportApIndicator : ctoReportApIndicatorVOS) {
            Row row = sheet.getRow(++rowIndex);
            Cell cell2 = row.getCell(2);
            cell2.setCellValue(reportApIndicator.getYearAPTarget());
            Cell cell3 = row.getCell(3);
            cell3.setCellValue(reportApIndicator.getYearAPFinish());
            Cell cell4 = row.getCell(4);
            cell4.setCellValue(reportApIndicator.getYearAPFinishRate());
        }
    }

    /**
     * 填充报告指标数据到工作表中。
     *
     * @param ctoReportItemIndicatorVOS 指标数据列表
     * @param workbook                  工作簿对象
     */
    public void fillSheet4(List<CtoReportItemIndicatorVO> ctoReportItemIndicatorVOS, Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(3);
        int titleRowIndex = sheet.getRow(0).getRowNum();
        for (CtoReportItemIndicatorVO reportItemIndicatorVO : ctoReportItemIndicatorVOS) {
            int dataRowIndex = ++titleRowIndex;
            Row row = sheet.getRow(dataRowIndex);
            if (row == null) {
                row = sheet.createRow(dataRowIndex);
            }
            setCellValue(reportItemIndicatorVO.getProductName(), row, 0);
            setCellValue(reportItemIndicatorVO.getEmpName(), row, 1);
            setCellValue(reportItemIndicatorVO.getEmpPosition(), row, 2);
            setCellValue(reportItemIndicatorVO.getYearSum(), row, 3);
            setCellValue(reportItemIndicatorVO.getMonthFinish1(), row, 4);
            setCellValue(reportItemIndicatorVO.getMonthFinish2(), row, 5);
            setCellValue(reportItemIndicatorVO.getMonthFinish3(), row, 6);
            setCellValue(reportItemIndicatorVO.getMonthFinish4(), row, 7);
            setCellValue(reportItemIndicatorVO.getMonthFinish5(), row, 8);
            setCellValue(reportItemIndicatorVO.getMonthFinish6(), row, 9);
            setCellValue(reportItemIndicatorVO.getMonthFinish7(), row, 10);
            setCellValue(reportItemIndicatorVO.getMonthFinish8(), row, 11);
            setCellValue(reportItemIndicatorVO.getMonthFinish9(), row, 12);
            setCellValue(reportItemIndicatorVO.getMonthFinish10(), row, 13);
            setCellValue(reportItemIndicatorVO.getMonthFinish11(), row, 14);
            setCellValue(reportItemIndicatorVO.getMonthFinish12(), row, 15);
        }
    }

    /**
     * 设置单元格的值，如果输入值为空或空白，则设置为空字符串。
     *
     * @param cellVal 要设置的值
     * @param row     行对象
     * @param cellNum 单元格编号
     */
    public void setCellValue(Object cellVal, Row row, int cellNum) {
        Cell cell = row.getCell(cellNum);
        if (cell == null) {
            cell = row.createCell(cellNum);
        }
        if (Objects.nonNull(cellVal)) {
            cell.setCellValue(String.valueOf(cellVal));
        }
    }
}
