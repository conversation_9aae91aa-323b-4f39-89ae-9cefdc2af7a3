package com.zte.mcrm.activity.repository.model.exhibition;

import java.util.Date;

/**
 * table:exhibition_info -- 
 */
public class ExhibitionInfoDO {
    /**  */
    private String rowId;

    /** 展会编号 */
    private String exhibitionNo;

    /** 展会名称 */
    private String exhibitionName;

    /** 时区。默认东八区GMT+8 */
    private String timezone;

    /** 展会开始时间 */
    private Date startTime;

    /** 展会截止时间 */
    private Date endTime;

    /** 口号 */
    private String slogan;

    /** 展会级别ExhibitionLevelTypeEnum，公司级展会等 */
    private String exhibitionLevel;

    /** 国家编码 */
    private String countryCode;

    /** 城市编码 */
    private String cityCode;

    /** 展会地点 */
    private String exhibitionPlace;

    /** 官网地址 */
    private String officialWebsite;

    /** 参展证申请网址 */
    private String applyWebsite;

    /** 报名开启状态Y-开启，N-关闭。BooleanEnum */
    private String entryOpenStatus;

    /** 报名开启最新操作时间 */
    private Date entryOpenTime;

    /** 审批层级至2、3、4 */
    private Integer approveLevel;

    /** 原则报名可见Y-可见，N-不可见。BooleanEnum */
    private String principleShow;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后更新人 */
    private String lastUpdatedBy;

    /** 最后更新时间 */
    private Date lastUpdateDate;

    /** 逻辑有效标识，Y-有效，N-无效 */
    private String enabledFlag;

    /** 当前编排类型all-总营编排分营不允许，sub-分页编排总营不应许。OrchestrationControlEnum */
    private String orchestrationControl;

    /** 展会概况 */
    private String exhibitionContent;

    /** 申请原则，最大2000字 */
    private String applicationPrinciple;

    /** 排期原则，最大2000字 */
    private String schedulingPrinciple;

    /** 展会所需资源json[ ],使用枚举RequiredResourceTypeEnum定义 */
    private String requiredResource;

    /**
     * 场所资源类型，区分展会、大会等活动
     */
    private String placeResourceType;

    /**
     * 大会类型
     */
    private String conferenceType;

    /**
     * 展会类型
     */
    private String exhibitionType;

    public String getExhibitionType() {
        return exhibitionType;
    }

    public void setExhibitionType(String exhibitionType) {
        this.exhibitionType = exhibitionType;
    }

    public String getPlaceResourceType() {
        return placeResourceType;
    }

    public void setPlaceResourceType(String placeResourceType) {
        this.placeResourceType = placeResourceType;
    }

    public String getConferenceType() {
        return conferenceType;
    }

    public void setConferenceType(String conferenceType) {
        this.conferenceType = conferenceType;
    }

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo == null ? null : exhibitionNo.trim();
    }

    public String getExhibitionName() {
        return exhibitionName;
    }

    public void setExhibitionName(String exhibitionName) {
        this.exhibitionName = exhibitionName == null ? null : exhibitionName.trim();
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone == null ? null : timezone.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan == null ? null : slogan.trim();
    }

    public String getExhibitionLevel() {
        return exhibitionLevel;
    }

    public void setExhibitionLevel(String exhibitionLevel) {
        this.exhibitionLevel = exhibitionLevel == null ? null : exhibitionLevel.trim();
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode == null ? null : countryCode.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getExhibitionPlace() {
        return exhibitionPlace;
    }

    public void setExhibitionPlace(String exhibitionPlace) {
        this.exhibitionPlace = exhibitionPlace == null ? null : exhibitionPlace.trim();
    }

    public String getOfficialWebsite() {
        return officialWebsite;
    }

    public void setOfficialWebsite(String officialWebsite) {
        this.officialWebsite = officialWebsite == null ? null : officialWebsite.trim();
    }

    public String getApplyWebsite() {
        return applyWebsite;
    }

    public void setApplyWebsite(String applyWebsite) {
        this.applyWebsite = applyWebsite == null ? null : applyWebsite.trim();
    }

    public String getEntryOpenStatus() {
        return entryOpenStatus;
    }

    public void setEntryOpenStatus(String entryOpenStatus) {
        this.entryOpenStatus = entryOpenStatus == null ? null : entryOpenStatus.trim();
    }

    public Date getEntryOpenTime() {
        return entryOpenTime;
    }

    public void setEntryOpenTime(Date entryOpenTime) {
        this.entryOpenTime = entryOpenTime;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getPrincipleShow() {
        return principleShow;
    }

    public void setPrincipleShow(String principleShow) {
        this.principleShow = principleShow == null ? null : principleShow.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getOrchestrationControl() {
        return orchestrationControl;
    }

    public void setOrchestrationControl(String orchestrationControl) {
        this.orchestrationControl = orchestrationControl == null ? null : orchestrationControl.trim();
    }

    public String getExhibitionContent() {
        return exhibitionContent;
    }

    public void setExhibitionContent(String exhibitionContent) {
        this.exhibitionContent = exhibitionContent == null ? null : exhibitionContent.trim();
    }

    public String getApplicationPrinciple() {
        return applicationPrinciple;
    }

    public void setApplicationPrinciple(String applicationPrinciple) {
        this.applicationPrinciple = applicationPrinciple == null ? null : applicationPrinciple.trim();
    }

    public String getSchedulingPrinciple() {
        return schedulingPrinciple;
    }

    public void setSchedulingPrinciple(String schedulingPrinciple) {
        this.schedulingPrinciple = schedulingPrinciple == null ? null : schedulingPrinciple.trim();
    }

    public String getRequiredResource() {
        return requiredResource;
    }

    public void setRequiredResource(String requiredResource) {
        this.requiredResource = requiredResource == null ? null : requiredResource.trim();
    }
}