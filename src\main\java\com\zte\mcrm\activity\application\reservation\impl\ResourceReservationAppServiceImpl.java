package com.zte.mcrm.activity.application.reservation.impl;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.application.reservation.ResourceReservationAppService;
import com.zte.mcrm.activity.application.reservation.convert.ResourceReservationParamConvert;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceReservationRepository;
import com.zte.mcrm.activity.service.reservation.ResourceReservationService;
import com.zte.mcrm.activity.service.reservation.param.ReserveResourceParam;
import com.zte.mcrm.activity.web.controller.reservation.param.ReplyReservationParam;
import com.zte.mcrm.activity.web.controller.reservation.param.ReserveScheduleResourceParam;
import com.zte.mcrm.activity.web.controller.reservation.vo.ReserveDealCommonVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 活动资源预约应用管理服务
 *
 * <AUTHOR>
 */
@Service
public class ResourceReservationAppServiceImpl implements ResourceReservationAppService {
    @Autowired
    private ResourceReservationService resourceReservationService;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityResourceReservationRepository activityResourceReservationRepository;

    @Override
    public ServiceData<ReserveDealCommonVO> reserveScheduleResource(ReserveScheduleResourceParam param) {
        ActivityInfoDO activityInfo = activityInfoRepository.selectByPrimaryKey(param.getActivityRowId());
        if (activityInfo == null) {
            return ServiceDataUtils.businessError(null);
        }

        if (CollectionUtils.isNotEmpty(param.getResourceList())) {
            ReserveResourceParam rp = ResourceReservationParamConvert.toScheduleResourceParam(param, activityInfo);
            return resourceReservationService.reserveSeparateResource(BizRequestUtil.createWithCurrentUser(rp));

        }

        return ServiceDataUtils.success(null);
    }

    @Override
    public ServiceData<ReserveDealCommonVO> replyReservation(ReplyReservationParam param) {
        // 暂时不做
        return null;
    }

}
