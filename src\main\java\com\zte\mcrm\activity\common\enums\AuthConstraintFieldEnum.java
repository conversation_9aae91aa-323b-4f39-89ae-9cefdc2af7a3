package com.zte.mcrm.activity.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 约束类型枚举，只是一个约束id到code的一个转换关系，实际约束存在两个问题：
 * 1. 约束查询接口只返回了约束id，但是id在各个环境不一致，需要一个一致性转换
 * 2. 约束可能会存在变化的可能，通过一致性转换，哪个约束变化，只要最终效果是正确的，哪怕id与code不一致也不存在问题
 *
 * <AUTHOR>
 * @date 2023/8/14 上午9:22
 */
@Getter
public enum AuthConstraintFieldEnum {

    /**
     * 申请人部门
     */
    APPLY_DEPARTMENT("APPLY_DEPARTMENT"),

    /**
     * 主客户单位 group 和 account
     */
    CUSTOMER_GROUP_ACCOUNT("CUSTOMER_GROUP_ACCOUNT"),

    /**
     * 交流方向
     */
    COMMUNICATION_DIRECTION("COMMUNICATION_DIRECTION"),

    /**
     * 活动类型
     */
    ACTIVITY_TYPE("ACTIVITY_TYPE"),

    /**
     * 客户主管部门
     */
    CUSTOMER_BELONG("CUSTOMER_BELONG"),
    ;

    private final String code;

    AuthConstraintFieldEnum(String code) {
        this.code = code;
    }

    /**
     * 是否当前枚举
     *
     * @param code
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/11 下午4:51       
     */
    public boolean isMe(String code) {
        return this.code.equalsIgnoreCase(code);
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return {@link AuthConstraintFieldEnum}
     * <AUTHOR>
     * @date 2023/8/14 上午11:23
     */
    public static AuthConstraintFieldEnum getEnum(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AuthConstraintFieldEnum e : AuthConstraintFieldEnum.values()) {
            if (e.isMe(code)) {
                return e;
            }
        }
        return null;
    }
}
