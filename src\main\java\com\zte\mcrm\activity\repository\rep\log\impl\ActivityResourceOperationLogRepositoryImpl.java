package com.zte.mcrm.activity.repository.rep.log.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.log.ActivityResourceOperationLogExtMapper;
import com.zte.mcrm.activity.repository.model.authority.ActivityResourceOperationAuthDO;
import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;
import com.zte.mcrm.activity.repository.rep.log.ActivityResourceOperationLogRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户活动资源操作日志DAO
 *
 * <AUTHOR>
 * @date 2024/11/13 上午10:36
 */
@Repository
public class ActivityResourceOperationLogRepositoryImpl implements ActivityResourceOperationLogRepository {

    @Autowired
    private ActivityResourceOperationLogExtMapper mapper;

    @Autowired
    private IKeyIdService keyIdService;

    /**
     * 根据业务id批量查询操作日志
     *
     * @param bizRelatedIdList 业务id
     * @param bizType          业务类型
     * @return {@link Map<String, List<ActivityResourceOperationLogDO>>}
     * <AUTHOR>
     * @date 2024/11/13 下午5:20
     */
    @Override
    public Map<String, List<ActivityResourceOperationLogDO>> selectByBizRelatedIdList(List<String> bizRelatedIdList, String bizType) {
        if (CollectionUtils.isEmpty(bizRelatedIdList)) {
            return Collections.emptyMap();
        }
        return mapper.selectByBizRelatedIdList(bizRelatedIdList, bizType).stream()
                .collect(Collectors.groupingBy(ActivityResourceOperationLogDO::getBizRelatedId));
    }

    /**
     * 批量新增操作日志
     *
     * @param list 操作日志
     * @return {@link int}
     */
    @Override
    public int batchInsert(List<ActivityResourceOperationLogDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        Date now = new Date();
        String empNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (ActivityResourceOperationLogDO operationLogDO : list) {
            if (StringUtils.isBlank(operationLogDO.getRowId())) {
                operationLogDO.setRowId(keyIdService.getKeyId());
            }
            operationLogDO.setEnabledFlag(BooleanEnum.Y.getCode());
            operationLogDO.setCreationDate(now);
            operationLogDO.setLastUpdateDate(now);
            operationLogDO.setCreatedBy(empNo);
            operationLogDO.setLastUpdatedBy(empNo);
        }
        return mapper.batchInsert(list);
    }
}
