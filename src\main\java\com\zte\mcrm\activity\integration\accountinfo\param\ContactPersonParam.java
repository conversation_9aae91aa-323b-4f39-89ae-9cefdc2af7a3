package com.zte.mcrm.activity.integration.accountinfo.param;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ContactPersonParam
 * @projectName zte-crm-account-info
 * @description: TODO
 * @date 2024/1/16 9:37
 */
@Getter
@Setter
public class ContactPersonParam {
    /**
     * 客户编码（与联系人编码不能同时为空，同时为空接口会返回空集合）
     */
    private List<String> customerCodes;
    /**
     * 只查询生效状态，false-查所有状态：草稿、评审中、生效、失效、禁用
     */
    private boolean onlyStatusEffect = true;
    /**
     * 联系人编码（与客户编码不能同时为空，同时为空接口会返回空集合）
     */
    private List<String> contactNoList;

    /**
     * 联系人名称（模糊匹配）
     */
    private List<String> nameList;
    /**
     * 联系人名称拼音（模糊匹配）
     */
    private List<String> namePinyinList;
}
