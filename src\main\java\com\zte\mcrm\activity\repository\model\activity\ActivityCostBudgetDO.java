package com.zte.mcrm.activity.repository.model.activity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * table:activity_cost_budget -- 
 */
public class ActivityCostBudgetDO {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 费用 */
    private BigDecimal feeAmount;

    /** 预算所属部门 */
    private String feeBelongOrg;

    /** 币种 */
    private String currency;

    /** 预算支出类型 */
    private String feeType;

    /** 预算支出描述 */
    private String feeDesc;

    /** 申请方（需求提出方）费用规定。快码类型：SAMPLE_VISIT_FEE_REQUIRE */
    private String applyFeeRequire;

    /** 接收方（样板点维护方）费用规定。快码类型：SAMPLE_VISIT_FEE_REQUIRE */
    private String receiveFeeRequire;

    /** 版本 */
    private Integer version;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getFeeBelongOrg() {
        return feeBelongOrg;
    }

    public void setFeeBelongOrg(String feeBelongOrg) {
        this.feeBelongOrg = feeBelongOrg == null ? null : feeBelongOrg.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType == null ? null : feeType.trim();
    }

    public String getFeeDesc() {
        return feeDesc;
    }

    public void setFeeDesc(String feeDesc) {
        this.feeDesc = feeDesc == null ? null : feeDesc.trim();
    }

    public String getApplyFeeRequire() {
        return applyFeeRequire;
    }

    public void setApplyFeeRequire(String applyFeeRequire) {
        this.applyFeeRequire = applyFeeRequire == null ? null : applyFeeRequire.trim();
    }

    public String getReceiveFeeRequire() {
        return receiveFeeRequire;
    }

    public void setReceiveFeeRequire(String receiveFeeRequire) {
        this.receiveFeeRequire = receiveFeeRequire == null ? null : receiveFeeRequire.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}