package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleItemPeopleExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/17 17:44
 */
@Component
public class ActivityRelationSchedulePeopleInfoRepositoryImpl implements ActivityScheduleItemPeopleRepository {
    @Autowired
    private ActivityScheduleItemPeopleExtMapper peopleExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    public int insertSelective(ActivityScheduleItemPeopleDO record) {
        setDefaultValue(record);
        return peopleExtMapper.insertSelective(record);
    }

    /**
     * 设置默认值
     *
     * @param schedulePeopleItemDO
     * <AUTHOR>
     */
    private void setDefaultValue(ActivityScheduleItemPeopleDO schedulePeopleItemDO) {
        schedulePeopleItemDO.setRowId(Optional.ofNullable(schedulePeopleItemDO.getRowId())
                .orElse(keyIdService.getKeyId()));
        schedulePeopleItemDO.setCreatedBy(Optional.ofNullable(schedulePeopleItemDO.getCreatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        schedulePeopleItemDO.setLastUpdatedBy(Optional.ofNullable(schedulePeopleItemDO.getLastUpdatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        schedulePeopleItemDO.setCreationDate(new Date());
        schedulePeopleItemDO.setLastUpdateDate(new Date());
        schedulePeopleItemDO.setPhoneNum(Optional.ofNullable(schedulePeopleItemDO.getPhoneNum())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setPeopleNo(Optional.ofNullable(schedulePeopleItemDO.getPeopleNo())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setPeopleLabel(Optional.ofNullable(schedulePeopleItemDO.getPeopleLabel())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setPeopleName(Optional.ofNullable(schedulePeopleItemDO.getPeopleName())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setPeopleType(Optional.ofNullable(schedulePeopleItemDO.getPeopleType())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setPosition(Optional.ofNullable(schedulePeopleItemDO.getPosition())
                .orElse(Strings.EMPTY));
        schedulePeopleItemDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int batchInsert(List<ActivityScheduleItemPeopleDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return NumberConstant.ZERO;
        }
        records.forEach(this::setDefaultValue);
        peopleExtMapper.batchInsert(records);
        return records.size();
    }

    @Override
    public Map<String, List<ActivityScheduleItemPeopleDO>> getRelationSchedulePeopleInfoIds(List<String> activityScheduleItemRowIds) {
        if (CollectionUtils.isEmpty(activityScheduleItemRowIds)) {
            return Collections.emptyMap();
        }
        return peopleExtMapper.queryAllSchedulePeopleByScheduleItemRowIds(activityScheduleItemRowIds)
                .stream().collect(Collectors.groupingBy(ActivityScheduleItemPeopleDO::getActivityScheduleItemRowId));
    }

    @Override
    public List<ActivityScheduleItemPeopleDO> getRelationSchedulePeopleInfoList(List<String> activityScheduleItemRowIds) {
        if (CollectionUtils.isEmpty(activityScheduleItemRowIds)) {
            return Collections.emptyList();
        }
        return peopleExtMapper.queryAllSchedulePeopleByScheduleItemRowIds(activityScheduleItemRowIds);
    }

    @Override
    public int batchUpdate(List<ActivityScheduleItemPeopleDO> records) {
        if(CollectionUtils.isEmpty(records)){
            return NumberConstant.ZERO;
        }
        String empNo = HeadersProperties.getXEmpNo();
        Date updatedDate = new Date();
        List<ActivityScheduleItemPeopleDO> updateRecords =records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getRowId()))
                .map(record ->{
                    record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy())
                            .orElse(empNo));
                    record.setLastUpdateDate(updatedDate);
                    return record;
                }).collect(Collectors.toList());
        return  CollectionUtils.isEmpty(updateRecords)? NumberConstant.ZERO: peopleExtMapper.batchUpdate(updateRecords);
    }

    @Override
    public List<ActivityScheduleItemPeopleDO> queryAllByScheduleItemRowId(String activityScheduleItemRowId) {
        return StringUtils.isBlank(activityScheduleItemRowId) ? Collections.emptyList()
                : peopleExtMapper.queryAllByScheduleItemRowId(activityScheduleItemRowId);
    }

    @Override
    public int deleteByScheduleItemRowIds(String operator, List<String> scheduleItemRowIds) {
        if (CollectionUtils.isEmpty(scheduleItemRowIds)) {
            return NumberConstant.ZERO;
        }
        return peopleExtMapper.deleteByScheduleItemRowIds(operator, scheduleItemRowIds);
    }

    @Override
    public int deleteByScheduleItemRowIdsWithoutPeopleTypes(String operator, List<String> scheduleItemRowIds, List<String> peopleTypes) {
        if (CollectionUtils.isEmpty(scheduleItemRowIds)) {
            return NumberConstant.ZERO;
        }
        return peopleExtMapper.deleteByScheduleItemRowIdsWithoutPeopleTypes(operator, scheduleItemRowIds, peopleTypes);
    }

    @Override
    public Map<String, List<ActivityScheduleItemPeopleDO>> queryAllSchedulePeopleByActivityRowIds(List<String> activityRowIds) {
        if (CollectionUtils.isEmpty(activityRowIds)) {
            return Collections.emptyMap();
        }
        return peopleExtMapper.queryAllSchedulePeopleByActivityRowIds(activityRowIds)
                .stream().collect(Collectors.groupingBy(ActivityScheduleItemPeopleDO::getActivityRowId));
    }
}
