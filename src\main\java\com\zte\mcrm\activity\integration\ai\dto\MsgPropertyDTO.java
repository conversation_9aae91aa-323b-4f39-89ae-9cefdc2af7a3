package com.zte.mcrm.activity.integration.ai.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/5 13:58
 */
@Setter
@Getter
@ToString
public class MsgPropertyDTO implements Serializable {
    private static final long serialVersionUID = 5766982820952593086L;

    /**
     * 业务号的中英文名称，选填，base64编码，解码后如下：zh=工作通知|en=Work Notice。
     * 当作为独立业务号会话时必填，发工作通知时必填。
     */
    private String sname;
    /**
     * 发送者（Sender-Name）图像url地址，选填，当需要在业务号会话中的区分发送者头像时必填。
     */
    private String logo;
    /**
     * 业务标识，当时iCenter的业务时不填。第三方业务系统时必填。
     */
    private String sysCode;
    /**
     * 出现并且等于1，自动消息置顶
     * 如果需要关联更新已有的消息置顶消息，在请求行中的msgid填写原来消息的msgid。
     */
    private String msgTop;
    /**
     * 填写消息发送者（机器人业务号id）， 必填。
     */
    private String serviceid;
    /**
     * 文本消息：不填。
     * 消息卡片：指的是串联一系列相关联卡片（msgid）的id号， 由调用方生成， 最大长度30位，格式SD+yyyyMMddHHmmssSSS+3位随机数+每个调用方系统自定义的英文缩写。
     * 选填。当需要关联的消息卡片时，必填。
     */
    private String seriesID;


}
