package com.zte.mcrm.activity.application.exhibition.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.exhibition.ExhibitionCreateAppService;
import com.zte.mcrm.activity.application.exhibition.business.ExhibitionBusiness;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.ExhibitionConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.RoleConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.event.TaskEventOperationTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.authorityclient.VisitUppService;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.repository.model.exhibition.*;
import com.zte.mcrm.activity.repository.model.exhibition.param.ExhibitionSubmitDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.event.CommonTaskEventRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.*;
import com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery;
import com.zte.mcrm.activity.service.event.param.CommonTaskEventQueryParam;
import com.zte.mcrm.activity.web.controller.exhibition.param.*;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.MdmAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.constant.AreaConstant;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.HrConditionVO;
import com.zte.mcrm.adapter.vo.MdmAreaVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.cust.constants.Constants;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.mcrm.activity.common.constant.ExhibitionConstant.PERCENTAGE_SYMBOL_TWO;
import static com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum.EXHIBITION;
import static com.zte.mcrm.activity.common.enums.event.TaskEventExecutionStatusEnum.WAIT;
import static com.zte.mcrm.activity.common.enums.event.TaskEventOperationTypeEnum.*;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.ENABLED_FLAG_Y;

/**
 * <AUTHOR>
 * @title: ExhibitionCreateAppServiceImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会创建接口
 * @date 2023/9/619:21
 */
@Service
public class ExhibitionCreateAppServiceImpl implements ExhibitionCreateAppService {
    private static final Logger logger = LoggerFactory.getLogger(ExhibitionCreateAppServiceImpl.class);
    @Autowired
    private IKeyIdService keyIdService;
    @Autowired
    private ExhibitionInfoRepository infoRepository;
    @Autowired
    private ExhibitionRelationAttachmentRepository attachmentRepository;
    @Autowired
    private ExhibitionRelationRoomRepository roomRepository;
    @Autowired
    private ExhibitionDirectorRepository directorRepository;
    @Autowired
    private ExhibitionRelationLeaderRepository leaderRepository;
    @Autowired
    private ExhibitionRelationExpertRepository expertRepository;
    @Autowired
    private ExhibitionRelationCarRepository carRepository;
    @Autowired
    private ExhibitionRelationHotelRepository hotelRepository;
    @Autowired
    private CommonTaskEventRepository commonTaskEventRepository;
    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;
    @Autowired
    private MdmAdapter mdmAdapter;
    @Autowired
    private LocaleMessageSourceBean locale;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private VisitUppService visitUppService;

    @Autowired
    private LookUpExtService lookUpExtService;


    @Override
    public String exhibitionSubmit(BizRequest<ExhibitionSubmitParam> request) {

        List<String> roleList = visitUppService.queryUserRoleAuthority(MsaRpcRequestUtil.createWithCurrentUser());
        if (!roleList.contains(RoleConstant.AUTHORITY_ROLE_EXHIBITION_ADMIN)){
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, locale.getMessage("you.are.not.the.exhibition.administrator.and.are.not.allowed.to.insert"));
        }
        ExhibitionBusiness.validatorExhibitionSubmitParam(request);

        ExhibitionSubmitDO exhibitionSubmitDO = prefectExhibitionSubmitDO(request);

        insertExhibition(exhibitionSubmitDO);

        return exhibitionSubmitDO.getInfoDO().getExhibitionNo();
    }

    @Override
    public List<OrgInfoVO> getDirectorAuth(HrConditionVO hrConditionVO) {
        PageRows<OrgInfoVO> orgInfoVOPageRows;
        try {
            orgInfoVOPageRows = hrmUsercenterAdapter.queryOrgInfoPageRows(hrConditionVO);
        } catch (Exception e) {
            logger.error("调用HR服务获取组织架构信息异常message={}",e.getMessage());
            throw new BusiException(RetCode.BUSINESSERROR_CODE, locale.getMessage("exhibition.get.org.info.exception"));
        }
        return orgInfoVOPageRows.getRows();
    }

    @Override
    public List<MdmAreaDTO> queryCountryAndCity(BizRequest<MdmAreaVO> request) {
        List<MdmAreaDTO> mdmAreaTemps = mdmAdapter.queryAreaInfo(request.getParam());
        logger.info("MDM接口返回mdmAreaTemps={}",JSON.toJSONString(mdmAreaTemps));
        List<MdmAreaDTO> mdmAreaDTOList = null;
        if (CollectionUtils.isNotEmpty(mdmAreaTemps)){
            List<MdmAreaDTO> areaDTOList = mdmAreaTemps.stream().filter(x -> StringUtils.isNotBlank(x.getDesc50())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(areaDTOList)){
                mdmAreaDTOList = new ArrayList<>(areaDTOList.size());
                for (MdmAreaDTO mdm : areaDTOList){
                    //中国-湖南省-长沙市 转化为 中国-长沙市
                    String desc50 = mdm.getDesc50();
                    String startStr = desc50.substring(NumberConstant.ZERO, desc50.indexOf(AreaConstant.SEPARATION_CHARACTER));
                    String endStr = desc50.substring(desc50.lastIndexOf(AreaConstant.SEPARATION_CHARACTER));
                    mdm.setDesc50(startStr + endStr);
                    mdmAreaDTOList.add(mdm);
                }
            }
        }
        return getCountryMdmArea(request, mdmAreaDTOList);
    }

    /**
     * 获取国家MDM数据
     * @param request
     * @param mdmAreaDTOList
     * @return
     * <AUTHOR>
     * @date 2023/11/4
     */
    public List<MdmAreaDTO> getCountryMdmArea(BizRequest<MdmAreaVO> request, List<MdmAreaDTO> mdmAreaDTOList) {
        if (CollectionUtils.isEmpty(mdmAreaDTOList)){
            request.getParam().setSynCode(AreaConstant.QUERY_SERACH_R_GJDQ_QUERY);
            request.getParam().setAreaDefaultUrl(AreaConstant.QUERY_SERACH_GJDQ);
            request.getParam().setDesc2(AreaConstant.AREA_TYPE_COUNTRY);
            List<MdmAreaDTO> mdmAreas = mdmAdapter.queryAreaInfo(request.getParam());
            if (CollectionUtils.isNotEmpty(mdmAreas)){
                mdmAreaDTOList = new ArrayList<>(mdmAreas.size());
                for (MdmAreaDTO mdm : mdmAreas){
                    //国家不需要这个字段，兼容前端
                    mdm.setDesc70(null);
                    mdm.setDesc3(mdm.getDesc50());
                    mdmAreaDTOList.add(mdm);
                }
            }

        }
        return mdmAreaDTOList;
    }

    @Override
    public String exhibitionUpdate(BizRequest<ExhibitionSubmitParam> request) {
        logger.info("展会修改参数request={}", JSON.toJSONString(request));
        ExhibitionBusiness.validatorExhibitionSubmitParam(request);

        ExhibitionSubmitDO exhibitionSubmitDO = prefectExhibitionSubmitDO(request);

        insertExhibition(exhibitionSubmitDO);

        return exhibitionSubmitDO.getInfoDO().getExhibitionNo();
    }


    /**
     * 完善展会提交对象
     * @param request 入参业务对象
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    private ExhibitionSubmitDO prefectExhibitionSubmitDO(BizRequest<ExhibitionSubmitParam> request) {
        ExhibitionSubmitDO exhibitionSubmitDO = new ExhibitionSubmitDO();
        exhibitionSubmitDO.setOptionEmpNo(request.getEmpNo());
        String exhibitionRowId = request.getParam().getInfoParam().getRowId();
        if (StringUtils.isBlank(exhibitionRowId)){
            exhibitionRowId = keyIdService.getKeyId();
            exhibitionSubmitDO.setInfoOptionType(ExhibitionConstant.EXHIBITION_SUBMIT_INSERT);
        } else {
            exhibitionSubmitDO.setInfoOptionType(ExhibitionConstant.EXHIBITION_SUBMIT_UPDATE);
        }
        exhibitionSubmitDO.setInfoDO(initExhibitionInfoDO(request, exhibitionRowId));
        exhibitionSubmitDO.setAttachmentDOList(initExhibitionRelationAttachmentDO(request.getParam().getAttachmentParams(),exhibitionRowId));
        exhibitionSubmitDO.setDirectorDOList(initExhibitionDirectorDO(request.getParam().getDirectorParams(), exhibitionRowId));
        exhibitionSubmitDO.setLeaderDOList(initExhibitionRelationLeaderDO(request.getParam().getLeaderParams(), exhibitionRowId));
        exhibitionSubmitDO.setExpertDOList(initExhibitionRelationExpertDO(request.getParam().getExpertParams(), exhibitionRowId));
        exhibitionSubmitDO.setRoomDOList(initExhibitionRoomDO(request.getParam().getRoomParams(),exhibitionRowId));
        exhibitionSubmitDO.setHotelDOList(initExhibitionHotelDO(request.getParam().getHotelParams(),exhibitionRowId));
        exhibitionSubmitDO.setCarDOList(initExhibitionCarDO(request.getParam().getCarParams(),exhibitionRowId));
        return exhibitionSubmitDO;
    }

    /**
     * 初始化新增展会车辆信息
     * @param carParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/10/16
     */
    private List<ExhibitionRelationCarDO> initExhibitionCarDO(List<ExhibitionRelationCarParam> carParams,String exhibitionRowId){
        List<ExhibitionRelationCarDO> carDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(carParams)){
            carParams.forEach(car -> {
                ExhibitionRelationCarDO carDO = new ExhibitionRelationCarDO();
                BeanUtils.copyProperties(car,carDO);
                carDO.setExhibitionRowId(exhibitionRowId);
                carDOList.add(carDO);
            });
        }
        replenishCar(carDOList,exhibitionRowId);
        return carDOList;
    }

    /**
     * 初始化新增展会酒店信息
     * @param hotelParamsMap 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/10/16
     */
    private List<ExhibitionRelationHotelDO> initExhibitionHotelDO(Map<String, List<ExhibitionRelationHotelParam>> hotelParamsMap,String exhibitionRowId){
        List<ExhibitionRelationHotelDO> relationHotelDOList = new ArrayList<>();
        if (null != hotelParamsMap && NumberConstant.ZERO != hotelParamsMap.size()){
            for(String key : hotelParamsMap.keySet()){
                List<ExhibitionRelationHotelParam> hotelParams = hotelParamsMap.get(key);
                List<ExhibitionRelationHotelDO> hotelDOList = new ArrayList<>();
                hotelParams.forEach(hotelParam ->{
                    ExhibitionRelationHotelDO hotelDO = new ExhibitionRelationHotelDO();
                    BeanUtils.copyProperties(hotelParam,hotelDO);
                    hotelDO.setExhibitionRowId(exhibitionRowId);
                    hotelDOList.add(hotelDO);
                });
                replenishHotel(hotelDOList,exhibitionRowId);
                relationHotelDOList.addAll(hotelDOList);
            }
        } else {
            List<ExhibitionRelationHotelDO> hotelDOList = new ArrayList<>();
            replenishHotel(hotelDOList,exhibitionRowId);
            relationHotelDOList.addAll(hotelDOList);
        }
        return relationHotelDOList;
    }

    /**
     * 初始化新增展会会议室信息
     * @param roomParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/10/16
     */
    private List<ExhibitionRelationRoomDO> initExhibitionRoomDO(List<ExhibitionRelationRoomParam> roomParams,String exhibitionRowId){
        List<ExhibitionRelationRoomDO> roomDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roomParams)){
            for (ExhibitionRelationRoomParam roomParam : roomParams){
                ExhibitionRelationRoomDO exhibitionRelationRoomDO = new ExhibitionRelationRoomDO();
                BeanUtils.copyProperties(roomParam,exhibitionRelationRoomDO);
                exhibitionRelationRoomDO.setExhibitionRowId(exhibitionRowId);
                roomDOList.add(exhibitionRelationRoomDO);
            }
        }
        replenishRoom(roomDOList,exhibitionRowId);
        return roomDOList;
    }


    /**
     * 保存展会新增信息:
     * 因为更新的时候会用到
     * @param exhibitionSubmitDO 展会新增数据库存储对象
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertExhibition(ExhibitionSubmitDO exhibitionSubmitDO) {
        ExhibitionInfoDO infoDO = exhibitionSubmitDO.getInfoDO();
        insertOrUpdateInfo(exhibitionSubmitDO, infoDO);
        insertOrUpdateDirect(exhibitionSubmitDO);
        insertOrUpdateLeader(exhibitionSubmitDO);
        insertOrUpdateExpert(exhibitionSubmitDO);
        insertOrUpdateRoom(exhibitionSubmitDO);
        insertOrUpdateHotel(exhibitionSubmitDO);
        insertOrUpdateCar(exhibitionSubmitDO);
        insertOrUpdateAttachment(exhibitionSubmitDO);
        return exhibitionSubmitDO.getInfoDO().getRowId();
    }

    /**
     * 新增或修改附件信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateAttachment(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationAttachmentDO> attachmentDOList = exhibitionSubmitDO.getAttachmentDOList();
        if (CollectionUtils.isNotEmpty(attachmentDOList)){
            attachmentDOList.forEach(att ->{
                if (StringUtils.isBlank(att.getRowId())){
                    att.setRowId(keyIdService.getKeyId());
                    att.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    att.setLastUpdateDate(new Date());
                    att.setEnabledFlag(ENABLED_FLAG_Y);
                    att.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    att.setCreationDate(new Date());
                    attachmentRepository.insertSelective(att);
                } else {
                    att.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    att.setCreationDate(new Date());
                    attachmentRepository.updateByPrimaryKeySelective(att);
                }
            });
        }
    }

    /**
     * 新增或修改车辆信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateCar(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationCarDO> carDOList = exhibitionSubmitDO.getCarDOList();
        if (CollectionUtils.isNotEmpty(carDOList)){
            carDOList.forEach(car ->{
                if (StringUtils.isBlank(car.getRowId())){
                    car.setRowId(keyIdService.getKeyId());
                    car.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    car.setLastUpdateDate(new Date());
                    car.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    car.setCreationDate(new Date());
                    car.setEnabledFlag(ENABLED_FLAG_Y);
                    carRepository.insertSelective(car);
                } else {
                    car.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    car.setCreationDate(new Date());
                    carRepository.updateByPrimaryKeySelective(car);
                }
            });
        }
    }

    /**
     * 新增或修改酒店信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateHotel(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationHotelDO> hotelDOList = exhibitionSubmitDO.getHotelDOList();
        if (CollectionUtils.isNotEmpty(hotelDOList)){
            hotelDOList.forEach(hotel ->{
                if (StringUtils.isBlank(hotel.getRowId())){
                    hotel.setRowId(keyIdService.getKeyId());
                    hotel.setLastUpdateDate(new Date());
                    hotel.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    hotel.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    hotel.setCreationDate(new Date());
                    hotel.setEnabledFlag(ENABLED_FLAG_Y);
                    hotelRepository.insertSelective(hotel);
                } else {
                    hotel.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    hotel.setCreationDate(new Date());
                    hotelRepository.updateByPrimaryKeySelective(hotel);
                }
            });
        }
    }

    /**
     * 新增或修改会议室信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateRoom(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationRoomDO> roomDOList = exhibitionSubmitDO.getRoomDOList();
        if (CollectionUtils.isNotEmpty(roomDOList)){
            roomDOList.forEach(room ->{
                if (StringUtils.isBlank(room.getRowId())){
                    room.setRowId(keyIdService.getKeyId());
                    room.setLastUpdateDate(new Date());
                    room.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    room.setCreationDate(new Date());
                    room.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    room.setEnabledFlag(ENABLED_FLAG_Y);
                    roomRepository.insertSelective(room);
                } else {
                    room.setLastUpdateDate(new Date());
                    room.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    roomRepository.updateByPrimaryKeySelective(room);
                }
            });
        }
    }

    /**
     * 新增或修改专家信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateExpert(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationExpertDO> expertDOList = exhibitionSubmitDO.getExpertDOList();
        if (CollectionUtils.isNotEmpty(expertDOList)){
            expertDOList.forEach(expert ->{
                if (StringUtils.isBlank(expert.getRowId())){
                    expert.setRowId(keyIdService.getKeyId());
                    expert.setCreationDate(new Date());
                    expert.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    expert.setLastUpdateDate(new Date());
                    expert.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    expert.setEnabledFlag(ENABLED_FLAG_Y);
                    expertRepository.insertSelective(expert);
                } else {
                    expert.setLastUpdateDate(new Date());
                    expert.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    expertRepository.updateByPrimaryKeySelective(expert);
                }
            });
        }
    }

    /**
     * 新增或修改领导信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateLeader(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionRelationLeaderDO> leaderDOList = exhibitionSubmitDO.getLeaderDOList();
        if (CollectionUtils.isNotEmpty(leaderDOList)){
            leaderDOList.forEach(leader ->{
                if (StringUtils.isBlank(leader.getRowId())){
                    leader.setRowId(keyIdService.getKeyId());
                    leader.setCreationDate(new Date());
                    leader.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    leader.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    leader.setLastUpdateDate(new Date());
                    leader.setEnabledFlag(ENABLED_FLAG_Y);
                    leaderRepository.insertSelective(leader);
                } else {
                    leader.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    leader.setLastUpdateDate(new Date());
                    leaderRepository.updateByPrimaryKeySelective(leader);
                }
            });
        }
    }

    /**
     * 新增或修改负责人信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateDirect(ExhibitionSubmitDO exhibitionSubmitDO) {
        List<ExhibitionDirectorDO> directorDOList = exhibitionSubmitDO.getDirectorDOList();
        if (CollectionUtils.isNotEmpty(directorDOList)){
            directorDOList.forEach(director ->{
                if (StringUtils.isBlank(director.getRowId())){
                    director.setRowId(keyIdService.getKeyId());
                    director.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    director.setCreationDate(new Date());
                    director.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    director.setLastUpdateDate(new Date());
                    director.setEnabledFlag(ENABLED_FLAG_Y);
                    directorRepository.insertSelective(director);
                } else {
                    director.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                    director.setLastUpdateDate(new Date());
                    directorRepository.updateByPrimaryKeySelective(director);
                }
            });
        }
    }

    /**
     * 新增或修改展现基本信息
     * @param exhibitionSubmitDO
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void insertOrUpdateInfo(ExhibitionSubmitDO exhibitionSubmitDO, ExhibitionInfoDO infoDO) {
        if (Objects.nonNull(infoDO)){
            if (ExhibitionConstant.EXHIBITION_SUBMIT_INSERT.equals(exhibitionSubmitDO.getInfoOptionType())){
                infoDO.setCreatedBy(exhibitionSubmitDO.getOptionEmpNo());
                infoDO.setCreationDate(new Date());
                infoDO.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                infoDO.setLastUpdateDate(new Date());
                infoDO.setEnabledFlag(ENABLED_FLAG_Y);
                infoRepository.insertSelective(Lists.newArrayList(infoDO));
            } else {
                infoDO.setLastUpdatedBy(exhibitionSubmitDO.getOptionEmpNo());
                infoDO.setLastUpdateDate(new Date());
                infoRepository.updateByPrimaryKeySelective(infoDO);
            }
        }
    }

    /**
     * 补充车辆信息对象
     * @param carTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishCar(List<ExhibitionRelationCarDO> carTargetList,String rowId) {
        List<ExhibitionRelationCarDO> carDOList = carRepository.getRelationCarListByExhibitionRowIds(Sets.newHashSet(rowId)).get(rowId);
        if (CollectionUtils.isNotEmpty(carDOList)){
            Map<String, ExhibitionRelationCarDO> hotelDOMap = carDOList.stream().collect(Collectors.toMap(ExhibitionRelationCarDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> targetRowIdList = carTargetList.stream().filter(leader -> StringUtils.isNotBlank(leader.getRowId())).map(ExhibitionRelationCarDO::getRowId).collect(Collectors.toList());
            List<String> catRowIdList = carDOList.stream().map(ExhibitionRelationCarDO::getRowId).collect(Collectors.toList());
            catRowIdList.removeAll(targetRowIdList);
            if (CollectionUtils.isNotEmpty(catRowIdList)){
                catRowIdList.forEach(car ->{
                    ExhibitionRelationCarDO carDO = hotelDOMap.get(car);
                    if (Objects.nonNull(carDO)){
                        carDO.setEnabledFlag(Constants.FLAG_NO);
                        carTargetList.add(carDO);
                    }
                });
            }
        }
    }

    /**
     * 补充酒店信息对象
     * @param hotelTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishHotel(List<ExhibitionRelationHotelDO> hotelTargetList,String rowId) {
        List<ExhibitionRelationHotelDO> hotelDOList = hotelRepository.getRelationHotelListByExhibitionRowIds(Sets.newHashSet(rowId)).get(rowId);
        if (CollectionUtils.isNotEmpty(hotelDOList)){
            Map<String, ExhibitionRelationHotelDO> hotelDOMap = hotelDOList.stream().collect(Collectors.toMap(ExhibitionRelationHotelDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> targetRowIdList = hotelTargetList.stream().filter(leader -> StringUtils.isNotBlank(leader.getRowId())).map(ExhibitionRelationHotelDO::getRowId).collect(Collectors.toList());
            List<String> hotelRowIdList = hotelDOList.stream().map(ExhibitionRelationHotelDO::getRowId).collect(Collectors.toList());
            hotelRowIdList.removeAll(targetRowIdList);
            if (CollectionUtils.isNotEmpty(hotelRowIdList)){
                hotelRowIdList.forEach(hotel ->{
                    ExhibitionRelationHotelDO hotelDO = hotelDOMap.get(hotel);
                    if (Objects.nonNull(hotelDO)){
                        hotelDO.setEnabledFlag(Constants.FLAG_NO);
                        hotelTargetList.add(hotelDO);
                    }
                });
            }
        }
    }

    /**
     * 补充会议室信息对象
     * @param roomTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishRoom(List<ExhibitionRelationRoomDO> roomTargetList,String rowId) {
        List<ExhibitionRelationRoomDO> roomDOList = roomRepository.getRelationRoomListByExhibitionRowIds(Sets.newHashSet(rowId)).get(rowId);
        if (CollectionUtils.isNotEmpty(roomDOList)){
            Map<String, ExhibitionRelationRoomDO> roomDOMap = roomDOList.stream().collect(Collectors.toMap(ExhibitionRelationRoomDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> targetRowIdList = roomTargetList.stream().filter(leader -> StringUtils.isNotBlank(leader.getRowId())).map(ExhibitionRelationRoomDO::getRowId).collect(Collectors.toList());
            List<String> roomRowIdList = roomDOList.stream().map(ExhibitionRelationRoomDO::getRowId).collect(Collectors.toList());
            roomRowIdList.removeAll(targetRowIdList);
            if (CollectionUtils.isNotEmpty(roomRowIdList)){
                roomRowIdList.forEach(room ->{
                    ExhibitionRelationRoomDO roomDO = roomDOMap.get(room);
                    if (Objects.nonNull(roomDO)){
                        roomDO.setEnabledFlag(Constants.FLAG_NO);
                        roomTargetList.add(roomDO);
                    }
                });
            }
        }
    }

    /**
     * 补充专家信息对象
     * @param expertTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishExpert(List<ExhibitionRelationExpertDO> expertTargetList, String rowId) {
        List<ExhibitionRelationExpertDO> expertDOList = expertRepository.queryExpertsWithExhibitionRowId(Lists.newArrayList(rowId)).get(rowId);
        if (CollectionUtils.isNotEmpty(expertDOList)){
            Map<String, ExhibitionRelationExpertDO> expertDOMap = expertDOList.stream().collect(Collectors.toMap(ExhibitionRelationExpertDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> targetRowIdList = expertTargetList.stream().filter(leader -> StringUtils.isNotBlank(leader.getRowId())).map(ExhibitionRelationExpertDO::getRowId).collect(Collectors.toList());
            List<String> expertRowIdList = expertDOList.stream().map(ExhibitionRelationExpertDO::getRowId).collect(Collectors.toList());
            expertRowIdList.removeAll(targetRowIdList);
            if (CollectionUtils.isNotEmpty(expertRowIdList)){
                expertRowIdList.forEach(expert ->{
                    Optional.ofNullable(expertDOMap.get(expert))
                            .ifPresent(expertDO -> {
                                expertDO.setEnabledFlag(Constants.FLAG_NO);
                                expertTargetList.add(expertDO);
                            });
                });
            }
        }
    }

    /**
     * 补充领导信息对象
     * @param leaderTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishLeader(List<ExhibitionRelationLeaderDO> leaderTargetList, String rowId) {
        List<ExhibitionRelationLeaderDO> leaderDOList = leaderRepository.queryLeadersWithExhibitionRowId(Lists.newArrayList(rowId)).get(rowId);
        if (CollectionUtils.isNotEmpty(leaderDOList)){
            Map<String, ExhibitionRelationLeaderDO> leaderDOMap = leaderDOList.stream().collect(Collectors.toMap(ExhibitionRelationLeaderDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> targetRowIdList = leaderTargetList.stream().filter(leader -> StringUtils.isNotBlank(leader.getRowId())).map(ExhibitionRelationLeaderDO::getRowId).collect(Collectors.toList());
            List<String> leaderRowIdList = leaderDOList.stream().map(ExhibitionRelationLeaderDO::getRowId).collect(Collectors.toList());
            leaderRowIdList.removeAll(targetRowIdList);
            if (CollectionUtils.isNotEmpty(leaderRowIdList)){
                leaderRowIdList.forEach(leader ->{
                    ExhibitionRelationLeaderDO leaderDO = leaderDOMap.get(leader);
                    if (Objects.nonNull(leaderDO)){
                        leaderDO.setEnabledFlag(Constants.FLAG_NO);
                        leaderTargetList.add(leaderDO);
                    }
                });
            }
        }
    }

    /**
     * 补充负责人信息对象
     * @param directorTargetList
     * @param exhibitionId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishDirect(List<ExhibitionDirectorDO> directorTargetList, String exhibitionId) {
        List<ExhibitionDirectorDO> directorDOList = directorRepository.queryDirectorByExhibitionRowId(Lists.newArrayList(exhibitionId)).get(exhibitionId);
        if (CollectionUtils.isNotEmpty(directorDOList)){
            Map<String, ExhibitionDirectorDO> directorDOMap = directorDOList.stream().collect(Collectors.toMap(ExhibitionDirectorDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> tempRowIdList = directorTargetList.stream().filter(dir -> StringUtils.isNotBlank(dir.getRowId())).map(ExhibitionDirectorDO::getRowId).collect(Collectors.toList());
            List<String> directorRowIdList = directorDOList.stream().map(ExhibitionDirectorDO::getRowId).collect(Collectors.toList());
            directorRowIdList.removeAll(tempRowIdList);
            if (CollectionUtils.isNotEmpty(directorRowIdList)){
                directorRowIdList.forEach(dir ->{
                    ExhibitionDirectorDO directorDO = directorDOMap.get(dir);
                    if (Objects.nonNull(directorDO)){
                        directorDO.setEnabledFlag(Constants.FLAG_NO);
                        directorTargetList.add(directorDO);
                    }
                });
            }
        }
    }

    /**
     * 补充附件信息对象
     * @param attachmentTargetList
     * @param rowId
     * @return
     * <AUTHOR>
     * @date 2023/10/20
     */
    private void replenishAttachment(List<ExhibitionRelationAttachmentDO> attachmentTargetList, String rowId) {
        List<ExhibitionRelationAttachmentDO> attachmentDOList = attachmentRepository.queryAttachmentByExhibitionRowId(Lists.newArrayList(rowId)).get(rowId);
        //求差集
        if (CollectionUtils.isNotEmpty(attachmentDOList)){
            Map<String, ExhibitionRelationAttachmentDO> attachmentMap = attachmentDOList.stream().collect(Collectors.toMap(ExhibitionRelationAttachmentDO::getRowId, v -> v, (k1, k2) -> k1));
            List<String> tempRowIdList = attachmentTargetList.stream().filter(att -> StringUtils.isNotBlank(att.getRowId())).map(ExhibitionRelationAttachmentDO::getRowId).collect(Collectors.toList());
            List<String> attachmentRowIdList = attachmentDOList.stream().map(ExhibitionRelationAttachmentDO::getRowId).collect(Collectors.toList());
            attachmentRowIdList.removeAll(tempRowIdList);
            if (CollectionUtils.isNotEmpty(attachmentRowIdList)){
                attachmentRowIdList.forEach(att ->{
                    ExhibitionRelationAttachmentDO attachmentDO = attachmentMap.get(att);
                    if (Objects.nonNull(attachmentDO)){
                        attachmentDO.setEnabledFlag(Constants.FLAG_NO);
                        attachmentTargetList.add(attachmentDO);
                    }
                });
            }
        }
    }

    /**
     * 初始化新增展会基本信息
     * @param request 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    public ExhibitionInfoDO initExhibitionInfoDO(BizRequest<ExhibitionSubmitParam> request, String exhibitionRowId) {
        ExhibitionInfoParam infoParam = request.getParam().getInfoParam();
        ExhibitionInfoDO infoDO = new ExhibitionInfoDO();
        BeanUtils.copyProperties(infoParam,infoDO);
        if (StringUtils.isBlank(infoDO.getExhibitionNo())){
            infoDO.setExhibitionNo(generatedExhibitionNo());
        }
        if (StringUtils.isNotBlank(infoDO.getCountryCode()) && StringUtils.isBlank(infoDO.getCityCode())){
            infoDO.setCityCode(CharacterConstant.EMPTY_STR);
        }
        infoDO.setRequiredResource(JSON.toJSONString(infoParam.getResourcesList()));
        infoDO.setRowId(exhibitionRowId);
        return infoDO;
    }

    /**
     * 初始化新增提交日期基本信息
     * @param attachmentParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    private List<ExhibitionRelationAttachmentDO> initExhibitionRelationAttachmentDO(List<ExhibitionScheduleAttachmentParam> attachmentParams, String exhibitionRowId) {
        List<ExhibitionRelationAttachmentDO> attachmentDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachmentParams)){
            for (ExhibitionScheduleAttachmentParam param : attachmentParams){
                ExhibitionRelationAttachmentDO expertDO = new ExhibitionRelationAttachmentDO();
                BeanUtils.copyProperties(param,expertDO);
                expertDO.setExhibitionRowId(exhibitionRowId);
                attachmentDOList.add(expertDO);
            }
        }
        replenishAttachment(attachmentDOList,exhibitionRowId);
        return attachmentDOList;
    }

    /**
     * 初始化新增专家信息
     * @param expertParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    private List<ExhibitionRelationExpertDO> initExhibitionRelationExpertDO(List<ExhibitionRelationExpertParam> expertParams, String exhibitionRowId) {
        List<ExhibitionRelationExpertDO> experts = new ArrayList<>();
        Set<String> employeeNos = new HashSet<>();
        if (CollectionUtils.isNotEmpty(expertParams)) {
            for (ExhibitionRelationExpertParam param : expertParams) {
                employeeNos.add(param.getEmployeeNo());
            }
            MsaRpcResponse<Map<String, PersonInfoDTO>> mapMsaRpcResponse = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(employeeNos));
            for (ExhibitionRelationExpertParam param : expertParams){
                ExhibitionRelationExpertDO expertDO = new ExhibitionRelationExpertDO();
                BeanUtils.copyProperties(param,expertDO);
                expertDO.setExhibitionRowId(exhibitionRowId);
                PersonInfoDTO personInfoDTO = mapMsaRpcResponse.getBo().get(param.getEmployeeNo());
                if (null != personInfoDTO) {
                    expertDO.setPositionName(personInfoDTO.getPostName());
                    expertDO.setEmployeeName(personInfoDTO.getEmpName());
                }
                experts.add(expertDO);
            }
        }
        replenishExpert(experts,exhibitionRowId);
        return experts;
    }


    /**
     * 初始化新增展会领导信息
     * @param leaderParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    private List<ExhibitionRelationLeaderDO> initExhibitionRelationLeaderDO(List<ExhibitionRelationLeaderParam> leaderParams, String exhibitionRowId) {
        List<ExhibitionRelationLeaderDO> leaders = new ArrayList<>();
        Set<String> employeeNos = new HashSet<>();
        if (CollectionUtils.isNotEmpty(leaderParams)) {
            for (ExhibitionRelationLeaderParam param : leaderParams) {
                employeeNos.add(param.getEmployeeNo());
            }
        }
        MsaRpcResponse<Map<String, PersonInfoDTO>> mapMsaRpcResponse = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(employeeNos));
        if (CollectionUtils.isNotEmpty(leaderParams)){
            for (ExhibitionRelationLeaderParam param : leaderParams){
                ExhibitionRelationLeaderDO leaderDO = new ExhibitionRelationLeaderDO();
                BeanUtils.copyProperties(param,leaderDO);
                leaderDO.setExhibitionRowId(exhibitionRowId);
                PersonInfoDTO personInfoDTO = mapMsaRpcResponse.getBo().get(param.getEmployeeNo());
                if (null != personInfoDTO) {
                    leaderDO.setPositionName(personInfoDTO.getPostName());
                    leaderDO.setEmployeeName(personInfoDTO.getEmpName());
                }
                leaders.add(leaderDO);
            }
        }
        replenishLeader(leaders,exhibitionRowId);
        return leaders;
    }

    /**
     * 初始化新增展会负责人信息
     * @param directorParams 前端请求对象
     * @param exhibitionRowId 展会主键Id
     * @return
     * <AUTHOR>
     * @date 2023/9/7
     */
    private List<ExhibitionDirectorDO> initExhibitionDirectorDO(List<ExhibitionDirectorParam> directorParams, String exhibitionRowId) {
        List<ExhibitionDirectorDO> directors = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(directorParams)){
            for (ExhibitionDirectorParam param : directorParams){
                ExhibitionDirectorDO directorDO = new ExhibitionDirectorDO();
                BeanUtils.copyProperties(param,directorDO);
                String employeeNoStr = param.getEmployeeNoList().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(CharacterConstant.COMMA));
                directorDO.setEmployeeNo(employeeNoStr);
                String orgAuth = param.getOrgAuthList().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(CharacterConstant.COMMA));
                directorDO.setOrgAuth(orgAuth);
                directorDO.setExhibitionRowId(exhibitionRowId);
                directors.add(directorDO);
            }
        }
        replenishDirect(directors,exhibitionRowId);
        return directors;
    }

    void initSchedulePushNoticeTaskEvent(String exhibitionRowId, ExhibitionSubmitDO exhibitionSubmitDO) {
        CommonTaskEventQueryParam queryParam = new CommonTaskEventQueryParam();
        queryParam.setBizId(exhibitionRowId);
        queryParam.setBizType(EXHIBITION.getCode());
        List<CommonTaskEventDO> commonTaskEventList = commonTaskEventRepository.listCommonTaskEventByCondition(queryParam).stream().filter(item ->
                TaskEventOperationTypeEnum.in(item.getOperationType(), EXHIBITION_WORK_NOTICE, EXHIBITION_DAILY_PUSH_WORK_NOTICE, EXHIBITION_ADVANCE_PUSH_WORK_NOTICE))
                .sorted(Comparator.comparing(CommonTaskEventDO::getExecuteTime)).collect(Collectors.toList());
        // 如果之前该展会下没有任何工作通知相关的定时任务 则新增
        if (CollectionUtils.isEmpty(commonTaskEventList)) {
            exhibitionSubmitDO.setNeedAddExhibitionTaskEvent(generateExhibitionWorkNoticeTaskEvent(exhibitionRowId));
            return;
        }

        // 如果之前该展会下存在工作通知相关的定时任务
        List<CommonTaskEventDO> needModifyExhibitionTaskEventList = new ArrayList<>();
        Map<String, List<CommonTaskEventDO>> exhibitionTaskEventMaps = commonTaskEventList.stream().collect(Collectors.groupingBy(CommonTaskEventDO::getOperationType));
        needModifyExhibitionTaskEventList.addAll(Optional.ofNullable(exhibitionTaskEventMaps.get(EXHIBITION_DAILY_PUSH_WORK_NOTICE.getCode())).orElse(new ArrayList<>())
                .stream().map(item -> {
                    CommonTaskEventDO commonTaskEventDO = new CommonTaskEventDO();
                    commonTaskEventDO.setRowId(item.getRowId());
                    commonTaskEventDO.setEnabledFlag(BooleanEnum.N.getCode());
                    return commonTaskEventDO;
                }).collect(Collectors.toList()));
        needModifyExhibitionTaskEventList.addAll(Optional.ofNullable(exhibitionTaskEventMaps.get(EXHIBITION_ADVANCE_PUSH_WORK_NOTICE.getCode())).orElse(new ArrayList<>())
                .stream().map(item -> {
                    CommonTaskEventDO commonTaskEventDO = new CommonTaskEventDO();
                    commonTaskEventDO.setRowId(item.getRowId());
                    commonTaskEventDO.setEnabledFlag(BooleanEnum.N.getCode());
                    return commonTaskEventDO;
                }).collect(Collectors.toList()));
        List<CommonTaskEventDO> workNoticeTaskEventList = exhibitionTaskEventMaps.get(EXHIBITION_WORK_NOTICE.getCode());
        if (CollectionUtils.isEmpty(workNoticeTaskEventList)) {
            exhibitionSubmitDO.setNeedAddExhibitionTaskEvent(generateExhibitionWorkNoticeTaskEvent(exhibitionRowId));
        } else {
            CommonTaskEventDO alreadyExhibitionWorkNotice = new CommonTaskEventDO();
            BeanUtils.copyProperties(workNoticeTaskEventList.get(0), alreadyExhibitionWorkNotice);
            alreadyExhibitionWorkNotice.setExecuteTime(new Date());
            alreadyExhibitionWorkNotice.setExecutionStatus(WAIT.getCode());
            alreadyExhibitionWorkNotice.setHandledTimes(NumberConstant.ZERO);
        }
        exhibitionSubmitDO.setNeedModifyExhibitionTaskEvent(needModifyExhibitionTaskEventList);
    }

    private CommonTaskEventDO generateExhibitionWorkNoticeTaskEvent(String exhibitionRowId) {
        CommonTaskEventDO schedulePushNoticeTaskEvent = new CommonTaskEventDO();
        Date currentDate = new Date();
        String operator = HeadersProperties.getXEmpNo();
        schedulePushNoticeTaskEvent.setBizId(exhibitionRowId);
        schedulePushNoticeTaskEvent.setBizType(EXHIBITION.getCode());
        schedulePushNoticeTaskEvent.setOperationType(EXHIBITION_WORK_NOTICE.getCode());
        schedulePushNoticeTaskEvent.setExecutionStatus(WAIT.getCode());
        schedulePushNoticeTaskEvent.setHandledTimes(NumberConstant.ZERO);
        schedulePushNoticeTaskEvent.setExecuteTime(currentDate);
        schedulePushNoticeTaskEvent.setCreatedBy(operator);
        schedulePushNoticeTaskEvent.setCreationDate(currentDate);
        schedulePushNoticeTaskEvent.setLastUpdatedBy(operator);
        schedulePushNoticeTaskEvent.setLastUpdateDate(currentDate);
        return schedulePushNoticeTaskEvent;
    }

    /**
     * 生成展会编号-生成规则：年+月+三位序号
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    private String generatedExhibitionNo(){
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        int monthTemp = currentDate.getMonthValue();
        String month = ExhibitionBusiness.getMonth(monthTemp);
        ExhibitionInfoQuery query = new ExhibitionInfoQuery();
        query.initCreateExhibitDateRange();
        int exhibitionCount= infoRepository.getCountExhibitionInfo(query);
        String sequenceNumber = String.format(ExhibitionConstant.SEQUENCE_NUMBER_FORMAT, exhibitionCount);
        StringBuilder stringBuilder = new StringBuilder();
        return stringBuilder.append(year).append(month).append(sequenceNumber).toString();
    }
    @Override
    public List<MdmAreaDTO> queryCountryOrCity(BizRequest<MdmAreaVO> request) {
        /* Started by AICoder, pid:4c8907ef4a1c497fb14b1a8886ce9504 */
        // 获取城市数据
        List<MdmAreaDTO> mdmCityTemps = mdmAdapter.queryAreaInfo(request.getParam());
        logger.info("MDM接口返回mdmAreaTemps={}", JSON.toJSONString(mdmCityTemps));
        List<MdmAreaDTO> mdmAreaDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mdmCityTemps)) {
            // 过滤并转换为 List，如果需要的话
            mdmAreaDTOList = mdmCityTemps.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getDesc50()))
                    .peek(mdm -> {
                        String desc50 = mdm.getDesc50();
                        String startStr = desc50.substring(0, desc50.indexOf(AreaConstant.SEPARATION_CHARACTER));
                        String endStr = desc50.substring(desc50.lastIndexOf(AreaConstant.SEPARATION_CHARACTER));
                        mdm.setDesc50(startStr + endStr);
                    })
                    .collect(Collectors.toList());
        }
        // 获取国家数据
        List<MdmAreaDTO> mdmCountryTemps = getCountryMdmArea(request, new ArrayList<>());
        // 合并去重
        Collection<MdmAreaDTO> mergedList = Stream.concat(mdmAreaDTOList.stream(), mdmCountryTemps.stream())
                .collect(Collectors.toMap(
                        MdmAreaDTO::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                )).values();
        // 排序
        List<MdmAreaDTO> result = new ArrayList<>(mergedList);
        String searchContent = Optional.ofNullable(request.getParam().getDesc3())
                .orElse(StringUtils.EMPTY)
                .replaceAll(PERCENTAGE_SYMBOL_TWO,StringUtils.EMPTY);
        /* Started by AICoder, pid:0b08e0bb781742cd89fa106086e7cab9 */
        // 增加按照匹配度排序 全匹配排前面
        result.sort(Comparator.comparing(MdmAreaDTO::getDesc50, (a1, a2) -> {
            if (Objects.equals(a1,searchContent)){
                return -1;
            }
            if (Objects.equals(a2,searchContent)){
                return 1;
            }
            return a1.compareTo(a2);
        }).thenComparing(MdmAreaDTO::getDesc60, Comparator.nullsLast(String::compareTo))
          .thenComparing(MdmAreaDTO::getCode, Comparator.nullsLast(String::compareTo)));
        return result;
        /* Ended by AICoder, pid:4c8907ef4a1c497fb14b1a8886ce9504 */
    }
}
