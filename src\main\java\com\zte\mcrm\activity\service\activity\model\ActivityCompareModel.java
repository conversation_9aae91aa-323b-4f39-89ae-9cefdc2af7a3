package com.zte.mcrm.activity.service.activity.model;

import lombok.Data;

/**
 * <AUTHOR> 10333830
 * @date 2023-08-31 10:21
 */
@Data
public class ActivityCompareModel<T> {
    /**
     *  对比对象类型
     */
    private Class<T> clazz;
    /**
     *  新对象
     */
    private T newBean;
    /**
     *  旧对象
     */
    private T oldBean;

    public ActivityCompareModel (T newBean, T oldBean, Class<T> clazz) {
        this.newBean = newBean;
        this.oldBean = oldBean;
        this.clazz = clazz;
    }
}
