package com.zte.mcrm.activity.repository.mapper.care;

import com.zte.mcrm.activity.repository.model.care.MyCarePeopleDO;
import com.zte.mcrm.activity.web.controller.resource.param.MyCarePeopleParam;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MyCarePeopleExtMapper extends MyCarePeopleMapper {

    /**
     * 插叙我关注的所有人
     * @param param
     * @return
     */
    List<MyCarePeopleDO> queryAllMyCarePeople(MyCarePeopleParam param);

    List<MyCarePeopleDO> queryEmpNoTransList(DataTransParam searchParam);
}