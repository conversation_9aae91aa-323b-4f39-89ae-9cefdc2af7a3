package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityRelationSolutionExtMapper extends ActivityRelationSolutionMapper {

    /**
     * 查询活动相关方案
     *
     * @param activityRowId
     * @return
     */
    List<ActivityRelationSolutionDO> queryAllSolutionForActivity(@Param("activityRowId") String activityRowId);

    /**
     * 批量插入
     *
     * @param list 项目列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list")List<ActivityRelationSolutionDO> list);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 查询所有-包含无效数据
     * 增加 enabled_flag = 'Y'，推送ES不需要无效方案
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationSolutionDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationSolutionDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    /**
     * 批量更新
     * @param list  列表
     * @return int
     * <AUTHOR>
     * date: 2024/1/19 14:20
     */
    int batchUpdateByPrimaryKeySelective(@Param("list") List<ActivityRelationSolutionDO> list);

}