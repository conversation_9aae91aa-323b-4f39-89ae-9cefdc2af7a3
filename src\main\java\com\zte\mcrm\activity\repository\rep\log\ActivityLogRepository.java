package com.zte.mcrm.activity.repository.rep.log;

import com.zte.mcrm.activity.repository.model.log.ActivityLogDO;

import java.util.List;

/**
 * 活动日志
 *
 * @author: 汤踊10285568
 * @date: 2023/9/2 21:35
 */
public interface ActivityLogRepository {
    /**
     * 根据ID查询
     *
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2023/09/02
     */
    ActivityLogDO get(String rowId);

    /**
     * 查询列表
     *
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2023/09/02
     */
    List<ActivityLogDO> getList(ActivityLogDO entity);

    /**
     * 删除
     *
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2023/06/29
     */
    int delete(String rowId);

    /**
     * 新增
     *
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int insert(ActivityLogDO entity);

    /**
     * 更新
     *
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2023/09/02
     */
    int update(ActivityLogDO entity);
}
