package com.zte.mcrm.activity.common.model;

import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务处理结果（业务层，对于接口层勿用）
 * <pre>
 *     只有业务明确处理是失败的，才是失败；
 *     对于正常处理结果都是成功的（如：PO创建成功、接收请求成功然后异步处理）
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BizResult<T> implements Serializable {

    private static final long serialVersionUID = -5321515536743848208L;

    /** 请求结果 */
    private boolean flag;
    /** 业务处理code。参考ServiceDataUtils中定义的code */
    private String code;
    /** 请求结果信息 */
    private String msg;
    /** 扩展信息 */
    private Map<String, Object> extMsg = new HashMap<>();

    /** 请求结果返回数据 */
    private T data;

    private BizResult(boolean flag, String msg, T data) {
        this.flag = flag;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 添加扩展信息
     * @param key 扩展信息key
     * @param msg 扩展信息
     */
    public void addExtMsg(String key, Object msg) {
        extMsg.put(key, msg);
    }

    /**
     * 构建成功结果
     * @param data 成功结果响应
     * @param <T>
     * @return 构建成功结果
     */
    public static <T> BizResult<T> buildSuccessRes(T data) {
        BizResult<T> res = new BizResult<T>(true, "success", data);
        res.code = ServiceDataUtils.SUCCESS_CODE;
        return res;
    }

    /**
     * 构建失败结果
     * @param msg 失败描述
     * @param data 失败结果响应
     * @param <T>
     * @return 构建失败结果
     */
    public static <T> BizResult<T> buildFailRes(String msg, T data) {
        BizResult<T> res = new BizResult<T>(false, msg, data);
        res.code = ServiceDataUtils.BUSINESSERROR_CODE;
        return res;
    }

    /**G
     * 通过code码构建结果
     * @param code
     * @param msg
     * @param data
     * @param <T>
     * @return
     */
    public static <T> BizResult<T> buildBizResult(String code, String msg, T data) {
        boolean flag = ServiceDataUtils.SUCCESS_CODE.equals(code) || ServiceDataUtils.SUCCESS_TERMINATION.equals(code);
        BizResult<T> res = new BizResult<T>(flag, msg, data);
        res.code = code;
        return res;
    }

    /**
     * 请求结果是否成功
     * @param res 请求结果
     * @return true-成功，false-失败
     */
    public static boolean success(BizResult<?> res) {

        return res != null && (res.isFlag() || ServiceDataUtils.SUCCESS_CODE.equals(res.code));
    }

}
