package com.zte.mcrm.activity.service.approval.impl;

import com.zte.iss.approval.sdk.bean.OpinionDTO;
import com.zte.iss.approval.sdk.bean.ReassignDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingNoticeStatusEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.common.util.BeanToMapUtil;
import com.zte.mcrm.activity.common.util.CopyUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.approval.ApprovalBaseService;
import com.zte.mcrm.activity.integration.upp.UppAuthorityService;
import com.zte.mcrm.activity.integration.upp.param.UppAuthQueryParam;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.ZteHrmHolEmployeeRepository;
import com.zte.mcrm.activity.integration.usercenter.dto.EmpHolManagerDTO;
import com.zte.mcrm.activity.integration.usercenter.dto.EmpManagerDTO;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalInfoRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.service.approval.ActivityApprovalInfoService;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalFlowStartParam;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalTaskReassignParam;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalTaskSubmitParam;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalInfoQueryVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalInfoReturnVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessNodeQueryVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessQueryVO;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalInfoConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessNodeConverter;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.service.model.activity.ActivityApprovalInfo;
import com.zte.mcrm.adapter.UserCenterPgAdapter;
import com.zte.mcrm.authority.bo.AuthorityUserBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.FLOW_CODE_ACTIVITY_MERGE_COMPLIANCE;
import static com.zte.mcrm.activity.common.constant.I18Constants.NOTICE_APPROVAL_RESUBMIT;
import static com.zte.mcrm.activity.common.constant.I18Constants.NOTICE_APPROVAL_TRANSFER_SELF;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.integration.usercenter.ZteHrmHolEmployeeRepository.MANAGER_LEVEL_FOUR;
import static com.zte.mcrm.activity.integration.usercenter.ZteHrmHolEmployeeRepository.MANAGER_LEVEL_THREE;
import static com.zte.mcrm.expansion.common.constant.CustExpansionConstant.CUST_COMPLIANCE_DIRECTOR;

/**
 * <AUTHOR> 10317843
 * @ClassName ActivityApprovalInfoServiceImpl
 * @description: 审批对外服务类
 * @author: 李龙10317843
 * @create: 2023-05-17 10:48
 * @Version 1.0
 * @date 2023/05/22
 */
@Service
@Slf4j
public class ActivityApprovalInfoServiceImpl implements ActivityApprovalInfoService {
    @Autowired
    private ActivityApprovalInfoRepository approvalInfoRepository;
    @Autowired
    private ActivityApprovalProcessRepository approvalProcessRepository;
    @Autowired
    private ActivityApprovalProcessNodeRepository approvalProcessNodeRepository;
    @Autowired
    private ApprovalBaseService approvalBaseService;
    @Autowired
    private UppAuthorityService uppAuthorityService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ZteHrmHolEmployeeRepository holEmployeeRepository;
    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;
    @Autowired
    private ActivityISearchService iSearchService;
    @Autowired
    private UserCenterPgAdapter userCenterPgAdapter;
    @Value("${approval.default.person}")
    private String approvalDefaultPerson;

    @Override
    public void startApproval(BizRequest<ActivityApprovalFlowStartParam> bizRequest) {
        try {
            ActivityApprovalFlowStartParam param = bizRequest.getParam();
            // 校验入参
            ValidationUtils.validateObject(param);
            // 防止重复提交，单个活动只能启动一个审批流
            List<ActivityApprovalInfoDO> approvalInfoDOList = approvalInfoRepository.queryAllByActivityRowId(param.getActRowId());
            approvalInfoDOList = approvalInfoDOList.stream().filter(e -> BooleanEnum.N.isMe(e.getInstanceStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(approvalInfoDOList)) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_RESUBMIT);
            }
            // 填充参数
            fillApprovalPerson(param);
            // 将参数转换模板变量
            Map<String, Object> paramsMap = BeanToMapUtil.beanToMap(param);
            // 启动审批流
            String flowInstanceId = approvalBaseService.approvalFlowStartByRid(FLOW_CODE_ACTIVITY_MERGE_COMPLIANCE, param.getActRowId(), paramsMap);
            // 业务数据处理
            ActivityApprovalInfo activityApprovalInfoAdd = ActivityApprovalInfoConverter.buildAdd(param.getActRowId(), flowInstanceId, bizRequest.getEmpNo());
            approvalInfoRepository.insertSelective(ActivityApprovalInfoConverter.INSTANCE.toEntity(activityApprovalInfoAdd));
        } catch (BizRuntimeException e) {
            log.error("call startApproval error!", e);
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "tips.approval.center.callback.error");
        }
    }

    /**
     * 填充审核人
     *
     * @param param
     * @throws Exception
     */
    private void fillApprovalPerson(ActivityApprovalFlowStartParam param) {
        // 合规经理
        EmployeeInfoDTO employeeComplianceManager = queryComplianceManager(param.getApplyDepartmentNo());
        if (employeeComplianceManager!=null){
            param.setComplianceAuditor(employeeComplianceManager.getEmpUIID());
        }
        if (StringUtils.isNotEmpty(param.getLeaderAuditor())){
            param.setLeaderAuditor(param.getLeaderAuditor());
           return;
        }
        // 责任部门领导
        EmployeeInfoDTO employeeLeader = queryLeader(param);
        if (employeeLeader!=null){
            param.setLeaderAuditor(employeeLeader.getEmpUIID());
        }
    }

    /**
     * 获取合规经理
     *
     * @param applyDepartmentNo
     * @return
     * @throws Exception
     */
    public EmployeeInfoDTO queryComplianceManager(String applyDepartmentNo) {
        UppAuthQueryParam uppAuthQueryParam = new UppAuthQueryParam(CUST_COMPLIANCE_DIRECTOR, applyDepartmentNo);
        AuthorityUserBO complianceUser = uppAuthorityService.getRoleUserListOfFirst(uppAuthQueryParam);
        return complianceUser == null ? userCenterService.getUserInfo(approvalDefaultPerson)
                : userCenterService.getUserInfo(complianceUser.getUserId());
    }

    /**
     * 获取部门领导
     *
     * @param param
     * @return
     * @throws Exception
     */
    private EmployeeInfoDTO queryLeader(ActivityApprovalFlowStartParam param) {
        List<EmpManagerDTO> level4MangerList = userCenterPgAdapter.findByOrgId(MANAGER_LEVEL_FOUR, param.getApplyDepartmentNo());
        if (CollectionUtils.isNotEmpty(level4MangerList)) {
            return userCenterService.getUserInfo(level4MangerList.iterator().next().getManagerID());
        }
        List<EmpManagerDTO> level3MangerList =userCenterPgAdapter
                .findByOrgId(MANAGER_LEVEL_THREE, param.getApplyDepartmentNo());
        if (CollectionUtils.isNotEmpty(level3MangerList)) {
            return userCenterService.getUserInfo(level3MangerList.iterator().next().getManagerID());
        }
        return userCenterService.getUserInfo(approvalDefaultPerson);
    }

    @Override
    public ActivityApprovalInfoQueryVO getApprovalProcessByActivityRowId(String activityRowId) {
        // 获取审批主表数据
        ActivityApprovalInfoDO activityApprovalInfoDO = approvalInfoRepository.queryByActivityRowId(activityRowId);
        if (activityApprovalInfoDO == null) {
            return null;
        }
        ActivityApprovalInfoQueryVO activityApprovalInfoQueryVO =
                ActivityApprovalInfoConverter.buildOfQuery(activityApprovalInfoDO);
        // 获取审批节点数据
        List<ActivityApprovalProcessDO> approvalProcessList =
                approvalProcessRepository.queryByActivityRowId(activityRowId);
        AssertUtil.assertNotEmpty(approvalProcessList);
        List<ActivityApprovalProcessQueryVO> approvalProcessQueryVOList =
                ActivityApprovalProcessConverter.buildOfQuery(approvalProcessList);

        if (approvalProcessQueryVOList != null) {
            for (ActivityApprovalProcessQueryVO processQueryVO : approvalProcessQueryVOList) {
                // 获取审批节点详情数据
                List<ActivityApprovalProcessNodeDO> processNodeDOS =
                        approvalProcessNodeRepository.queryByProcessRowId(processQueryVO.getRowId());
                if (CollectionUtils.isNotEmpty(processNodeDOS)) {
                    List<ActivityApprovalProcessNodeQueryVO> processNodeQueryVOLists =
                            ActivityApprovalProcessNodeConverter.buildOfQuery(processNodeDOS);
                    processQueryVO.setProcessNodeQueryVOList(processNodeQueryVOLists);
                }
            }
        }
        activityApprovalInfoQueryVO.setProcessQueryVOList(approvalProcessQueryVOList);

        return activityApprovalInfoQueryVO;
    }

    @Override
    public ActivityApprovalInfoReturnVO taskOpinionSubmit(BizRequest<ActivityApprovalTaskSubmitParam> bizRequest) {
        try {
            // 1. 提交到审批中心
            ActivityApprovalTaskSubmitParam param = bizRequest.getParam();
            ValidationUtils.validateObject(param);
            //  防止重复提交
            List<ActivityPendingNoticeDO> unFinishPendingNoticeList = activityPendingNoticeRepository
                    .queryByBusinessIdAndStatus(param.getTaskId(), PendingNoticeStatusEnum.WAIT_DEAL.getStatus());
            if (CollectionUtils.isEmpty(unFinishPendingNoticeList)) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_RESUBMIT);
            }
            // 2. 保存本地数据(通过回调持久化,这里不做持久化是因为移动端可以审批，程序端监控不到，所以统一在回调处做)
            String taskId = approvalBaseService.taskOpinionSubmit(CopyUtil.copy(param, OpinionDTO.class));
            // 3. 更新待办状态为完成中
            activityPendingNoticeRepository.updateStatusByBusinessId(param.getTaskId(), PendingNoticeStatusEnum.FINISH.getStatus());

            ActivityApprovalInfoReturnVO res = new ActivityApprovalInfoReturnVO();
            res.setTaskId(taskId);
            res.setActivityRowId(unFinishPendingNoticeList.get(ZERO).getActivityRowId());
            iSearchService.asyncSendActivityData2ISearch(res.getActivityRowId());
            return res;
        } catch (BizRuntimeException e) {
            log.error("call taskOpinionSubmit error!", e);
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "tips.approval.center.callback.error");
        }
    }

    @Override
    public ActivityApprovalInfoReturnVO taskReassign(BizRequest<ActivityApprovalTaskReassignParam> bizRequest) {
        try {
            ActivityApprovalTaskReassignParam param = bizRequest.getParam();
            ValidationUtils.validateObject(param);
            //  防止重复提交
            List<ActivityPendingNoticeDO> unFinishPendingNoticeList = activityPendingNoticeRepository
                    .queryByBusinessIdAndStatus(param.getApprovalFlowNo(), PendingNoticeStatusEnum.WAIT_DEAL.getStatus());
            if (CollectionUtils.isEmpty(unFinishPendingNoticeList)) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_RESUBMIT);
            }
            // 不能转交给自己
            if (param.getApproveBy().equals(bizRequest.getEmpNo())) {
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_TRANSFER_SELF);
            }
            // 执行转交
            ReassignDTO reassignDTO = new ReassignDTO();
            reassignDTO.setTaskId(param.getApprovalFlowNo());
            reassignDTO.setTaskReceiver(param.getApproveBy());
            reassignDTO.setOpinion(param.getRemark());
            String taskId = approvalBaseService.taskReassign(reassignDTO);
            // 更新待办状态为完成中
            activityPendingNoticeRepository.updateStatusByBusinessId(param.getApprovalFlowNo(), PendingNoticeStatusEnum.FINISH.getStatus());

            ActivityApprovalInfoReturnVO res = new ActivityApprovalInfoReturnVO();
            res.setTaskId(taskId);
            res.setActivityRowId(unFinishPendingNoticeList.get(ZERO).getActivityRowId());
            iSearchService.asyncSendActivityData2ISearch(res.getActivityRowId());
            return res;
        } catch (BizRuntimeException e) {
            log.error("call taskReassign error!", e);
            throw new BizRuntimeException(BizRuntimeException.BIZ_ERROR_CODE, "tips.approval.center.callback.error");
        }
    }

}