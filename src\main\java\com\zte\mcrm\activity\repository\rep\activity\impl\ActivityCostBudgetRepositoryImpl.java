package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityCostBudgetExtMapper;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityCostBudgetMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityCostBudgetDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCostBudgetRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: <EMAIL>
 * @create: 2024-02-21 13:47
 * @Version 1.0
 **/
@Repository
public class ActivityCostBudgetRepositoryImpl implements ActivityCostBudgetRepository {

    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private ActivityCostBudgetExtMapper activityCostBudgetExtMapper;

    @Override
    public int insertSelective(List<ActivityCostBudgetDO> activityCostBudgetList) {
        if (CollectionUtils.isEmpty(activityCostBudgetList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityCostBudgetDO activityCostBudgetDO : activityCostBudgetList) {
            activityCostBudgetDO.setRowId(keyIdService.getKeyId());
            activityCostBudgetDO.setCreatedBy(HeadersProperties.getXEmpNo());
            activityCostBudgetDO.setCreationDate(new Date());
            activityCostBudgetDO.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            activityCostBudgetDO.setLastUpdateDate(new Date());
            activityCostBudgetDO.setEnabledFlag(BooleanEnum.Y.getCode());
            activityCostBudgetExtMapper.insertSelective(activityCostBudgetDO);
        }
        return activityCostBudgetList.size();
    }

    @Override
    public List<ActivityCostBudgetDO> queryCostBudgetByActivityRowIds(String activityRowId) {
        return StringUtils.isEmpty(activityRowId) ? Collections.emptyList() :
                activityCostBudgetExtMapper.queryCostBudgetByActivityRowIds(activityRowId);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if(CollectionUtils.isEmpty(rowIds)){
            return NumberConstant.ZERO;
        }

        return activityCostBudgetExtMapper.deleteByRowIds(operator,rowIds);
    }

    @Override
    public Map<String, List<ActivityCostBudgetDO>> queryActivityCostBudgetByActivityRowIds(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityCostBudgetExtMapper.getActivityCostBudgetByActivityRowIds(activityRowIds).stream().collect(Collectors.groupingBy(ActivityCostBudgetDO::getActivityRowId));
    }
}
