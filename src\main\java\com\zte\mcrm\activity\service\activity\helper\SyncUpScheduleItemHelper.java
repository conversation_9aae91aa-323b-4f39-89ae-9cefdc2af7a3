package com.zte.mcrm.activity.service.activity.helper;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ScheduleItemPeopleTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonPageDTO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.service.activity.convert.ScheduleItemModelConvert;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemModel;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemPeopleModel;
import com.zte.mcrm.activity.service.activity.model.ScheduleItemModel;
import com.zte.mcrm.activity.service.person.PersonSearchService;
import com.zte.mcrm.activity.service.person.convert.ZtePeopleLabelConvert;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.param.PersonPageParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityZtePeopleVO;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.PersonPageVO;
import com.zte.mcrm.customermgr.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@code @description 同步日程辅助类}
 *
 * <AUTHOR>
 * @date 2025/1/14 下午7:08
 */
@Slf4j
@Component
public class SyncUpScheduleItemHelper {
    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;

    @Autowired
    private ActivityScheduleItemPeopleRepository scheduleItemPeopleRepository;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private PersonSearchService personSearchService;
    @Autowired
    private ZtePeopleLabelConvert ztePeopleLabelConvert;

    /* Started by AICoder, pid:4a6a7d3283e2ae114336091350550739ab9929a4 */

    /**
     * 同步一线活动
     *
     * @param scheduleRequest 日程信息入参
     */
    public void syncUpActivity(BizRequest<ScheduleItemModel> scheduleRequest) {
        ScheduleItemModel scheduleItemModel = scheduleRequest.getParam();
        List<ActivityScheduleItemModel> activityScheduleItemModels = scheduleItemModel.getActivityScheduleItemModels();

        if (CollectionUtils.isEmpty(activityScheduleItemModels)) {
            log.info("[同步一线活动] 没有可同步的活动");
            return;
        }

        // 一次查询所有活动的人员，获取Map集合，k -> 活动ID，v -> 该活动下的所有人员
        Set<String> activityRowIds = activityScheduleItemModels.stream()
                .map(ActivityScheduleItemModel::getActivityRowId)
                .collect(Collectors.toSet());

        // 查询数据库中的我司参与人
        Map<String, List<ActivityRelationZtePeopleDO>> rowIdZtePeopleMapDb = activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(activityRowIds);
        //活动ID->人员编码Set的容器，用于判断是否重复新增我司参与人
        Map<String, Set<String>> activityPeopleNoMap = Maps.newHashMap();
        for (ActivityScheduleItemModel activityScheduleItemModel : activityScheduleItemModels) {
            List<ActivityScheduleItemPeopleModel> scheduleItemPeopleModels = activityScheduleItemModel.getScheduleItemPeopleModels();

            // 获取当前活动的我司参与人和客户参与人
            String activityRowId = activityScheduleItemModel.getActivityRowId();
            List<ActivityRelationZtePeopleDO> activityZtePeopleDb = rowIdZtePeopleMapDb.getOrDefault(activityRowId, Collections.emptyList());

            //新增我司参与人
            addZtePeople(scheduleItemPeopleModels, activityZtePeopleDb, activityRowId, activityPeopleNoMap);
        }
    }

    /* Ended by AICoder, pid:4a6a7d3283e2ae114336091350550739ab9929a4 */

    /* Started by AICoder, pid:f3cf6f32bf840b614ffa08ab502548466fb41a1b */

    /**
     * 新增我司参与人
     *
     * @param scheduleItemPeopleModels 入参日程人员
     * @param activityZtePeopleDb      数据库活动中的我司参与人
     * @param activityRowId            活动ID
     * @param activityPeopleNoMap      活动ID->判断是否重复添加人员编码的的容器
     */
    void addZtePeople(List<ActivityScheduleItemPeopleModel> scheduleItemPeopleModels, List<ActivityRelationZtePeopleDO> activityZtePeopleDb,
                      String activityRowId, Map<String, Set<String>> activityPeopleNoMap) {
        Set<String> dbZtePeopleCodes = activityZtePeopleDb.stream()
                .filter(t -> ActivityPeopleTypeEnum.in(t.getPeopleType(), ActivityPeopleTypeEnum.allParticipantsType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode)
                .collect(Collectors.toSet());
        //判断是否重复添加人员编码：获取活动中已添加的我司参与人
        Set<String> addedPeopleContain = activityPeopleNoMap.getOrDefault(activityRowId, Sets.newHashSet());

        // 新增：遍历传入的日程人员，如果没有新增过且数据库的人员编号匹配不到，则对应传入的人员为待新增的人员
        List<ActivityScheduleItemPeopleModel> addedPeoples = scheduleItemPeopleModels.stream()
                .filter(t -> ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(t.getPeopleType()))
                .filter(t -> !dbZtePeopleCodes.contains(t.getPeopleNo()))
                .filter(t -> !addedPeopleContain.contains(t.getPeopleNo()))
                .collect(Collectors.toList());

        if (!addedPeoples.isEmpty()) {
            List<ActivityRelationZtePeopleDO> addPeoples = ScheduleItemModelConvert.INSTANCE.scheduleItemPeopleModelToZtePeopleDOList(addedPeoples);
            //这里需要补充人员的peopleLabel，peopleName，positionName，deptAttribute，deptFullName
            this.fillPeopleInfo(addPeoples);
            Set<String> preAddPeople = addPeoples.stream().map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toSet());
            //将本次新添加的人员放入已添加人员编码的的容器中，用于后续判断是否重复添加人员
            addedPeopleContain.addAll(preAddPeople);
            activityPeopleNoMap.put(activityRowId, addedPeopleContain);
            log.info("[同步一线活动]本次待新增的我司参与人：{}", JacksonUtil.toJson(addPeoples));
            log.info("[同步一线活动]活动ID：{}，已新增的我司参与人：{}", activityRowId, JacksonUtil.toJson(addedPeopleContain));
            activityRelationZtePeopleRepository.batchInsert(addPeoples);
        }
    }

    /* Ended by AICoder, pid:f3cf6f32bf840b614ffa08ab502548466fb41a1b */

    /* Started by AICoder, pid:z6909880c9e75ed14a8a09a5c080748ea181f1ca */

    /**
     * 同步一线日程
     *
     * @param scheduleRequest 日程信息入参
     */
    public void syncUpActivityScheduleItem(BizRequest<ScheduleItemModel> scheduleRequest) {
        ScheduleItemModel scheduleItemModel = scheduleRequest.getParam();

        // 获取活动日程列表
        List<ActivityScheduleItemModel> activityScheduleItemModels = scheduleItemModel.getActivityScheduleItemModels();

        // 如果没有可同步的日程，记录日志并返回
        if (CollectionUtils.isEmpty(activityScheduleItemModels)) {
            log.info("[同步一线日程] 没有可同步的日程");
            return;
        }

        // 将模型转换为数据对象，并记录待更新的日程信息
        List<ActivityScheduleItemDO> activityScheduleItemDOS = ScheduleItemModelConvert.INSTANCE.scheduleItemModelToDO(activityScheduleItemModels);
        log.info("[同步一线日程] 待更新的日程：{}", JacksonUtil.toJson(activityScheduleItemDOS));

        // 批量更新日程
        scheduleItemRepository.batchUpdate(activityScheduleItemDOS);

        // 一次查询所有日程的人员，获取Map集合，key -> 日程ID，value -> 该日程下的所有人员
        List<String> scheduleItemRowIds = activityScheduleItemModels.stream()
                .map(ActivityScheduleItemModel::getRowId)
                .collect(Collectors.toList());

        Map<String, List<ActivityScheduleItemPeopleDO>> activitySchedulePeopleDbMap = scheduleItemPeopleRepository.getRelationSchedulePeopleInfoIds(scheduleItemRowIds);

        for (ActivityScheduleItemModel activityScheduleItemModel : activityScheduleItemModels) {
            // 删除人员
            removeScheduleItemPeople(activitySchedulePeopleDbMap, activityScheduleItemModel);

            // 新增人员
            addScheduleItemPeople(activitySchedulePeopleDbMap, activityScheduleItemModel);
        }
    }

    /* Ended by AICoder, pid:z6909880c9e75ed14a8a09a5c080748ea181f1ca */

    /**
     * 删除日程人员
     *
     * @param activitySchedulePeopleDbMap 所有日程的人员Map集合，key -> 日程ID，value -> 该日程下的所有人员
     * @param activityScheduleItemModel   日程入参
     */
    private void removeScheduleItemPeople(Map<String, List<ActivityScheduleItemPeopleDO>> activitySchedulePeopleDbMap, ActivityScheduleItemModel activityScheduleItemModel) {
        //删除：人员类型属于：我司参与人、客户参与人
        List<ActivityScheduleItemPeopleModel> scheduleItemPeopleModels = activityScheduleItemModel.getScheduleItemPeopleModels();

        //查询数据库已存在的人员
        List<ActivityScheduleItemPeopleDO> activityScheduleItemPeopleDb = activitySchedulePeopleDbMap.getOrDefault(activityScheduleItemModel.getRowId(), Collections.emptyList());

        //遍历数据库的人员列表(只过滤出我司参与人、客户参与人)，如果传入的人员编号列表匹配不到，则对应数据库人员为待删除的人员
        List<ActivityScheduleItemPeopleDO> removedPeoples = activityScheduleItemPeopleDb.stream()
                .filter(t -> ScheduleItemPeopleTypeEnum.in(t.getPeopleType(),
                        ScheduleItemPeopleTypeEnum.ZTE_PEOPLE,
                        ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT))
                .filter(t -> scheduleItemPeopleModels.stream()
                        .noneMatch(t1 -> StringUtils.equals(t1.getPeopleType(), t.getPeopleType())
                                && StringUtils.equals(t1.getPeopleNo(), t.getPeopleNo())))
                .collect(Collectors.toList());

        //待删除的日程人员
        if (!removedPeoples.isEmpty()) {
            List<ActivityScheduleItemPeopleDO> removePeoples = removedPeoples.stream().map(t -> {
                ActivityScheduleItemPeopleDO removePeople = new ActivityScheduleItemPeopleDO();
                removePeople.setRowId(t.getRowId());
                removePeople.setEnabledFlag(BooleanEnum.N.getCode());
                return removePeople;
            }).collect(Collectors.toList());
            log.info("[同步一线日程]待删除的日程人员：{}", JacksonUtil.toJson(removePeoples));
            scheduleItemPeopleRepository.batchUpdate(removePeoples);
        }
    }

    /**
     * 新增日程人员
     *
     * @param activitySchedulePeopleDbMap 所有日程的人员Map集合，key -> 日程ID，value -> 该日程下的所有人员
     * @param activityScheduleItemModel   日程入参
     */
    private void addScheduleItemPeople(Map<String, List<ActivityScheduleItemPeopleDO>> activitySchedulePeopleDbMap, ActivityScheduleItemModel activityScheduleItemModel) {
        //新增：人员类型属于：我司参与人、客户参与人
        List<ActivityScheduleItemPeopleModel> scheduleItemPeopleModels = activityScheduleItemModel.getScheduleItemPeopleModels();

        //查询数据库已存在的人员
        List<ActivityScheduleItemPeopleDO> activityScheduleItemPeopleDb = activitySchedulePeopleDbMap.getOrDefault(activityScheduleItemModel.getRowId(), Collections.emptyList());

        //遍历传入的人员列表(只过滤出我司参与人、客户参与人)，如果数据库的人员编号匹配不到，则对应传入的人员为待新增的人员
        List<ActivityScheduleItemPeopleModel> addedPeoples = scheduleItemPeopleModels.stream()
                .filter(t -> StringUtils.isNotBlank(t.getPeopleNo()))
                .filter(t -> ScheduleItemPeopleTypeEnum.in(t.getPeopleType(),
                        ScheduleItemPeopleTypeEnum.ZTE_PEOPLE,
                        ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT))
                .filter(t -> activityScheduleItemPeopleDb.stream()
                        .noneMatch(t1 -> StringUtils.equals(t1.getPeopleType(), t.getPeopleType())
                                && StringUtils.equals(t1.getPeopleNo(), t.getPeopleNo())))
                .collect(Collectors.toList());

        //待新增的日程人员
        if (!addedPeoples.isEmpty()) {
            List<ActivityScheduleItemPeopleDO> addPeoples = ScheduleItemModelConvert.INSTANCE.scheduleItemPeopleModelToDO(addedPeoples);
            //因待新增的人员可能之前已经删除过，为防止人员rowId主键重复导致冲突，这里需要将待新增的人员rowId置空
            addPeoples.forEach(t -> t.setRowId(null));
            log.info("[同步一线日程]待新增的日程人员：{}", JacksonUtil.toJson(addPeoples));
            scheduleItemPeopleRepository.batchInsert(addPeoples);
        }
    }

    /**
     * 补充人员其他信息：如标签、岗位等
     *
     * @param addPeoples 待新增的人员
     */
    void fillPeopleInfo(List<ActivityRelationZtePeopleDO> addPeoples) {
        ActivityRecentlySearchParam param = new ActivityRecentlySearchParam();
        List<String> peopleCodes = addPeoples.stream().map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toList());

        Map<String, ActivityZtePeopleVO> ztePeopleMap = ztePeopleLabelConvert.toZtePeopleVO(param, peopleCodes).stream()
                .collect(Collectors.toMap(ActivityZtePeopleVO::getPeopleCode, Function.identity(), (v1, v2) -> v1));

        PageQuery<PersonPageParam> pageQuery = new PageQuery<>();
        PersonPageParam personPageParam = new PersonPageParam();
        pageQuery.setPageNo(1);
        pageQuery.setPageSize(1);
        pageQuery.setParam(personPageParam);
        addPeoples.forEach(t -> {
            ActivityZtePeopleVO people = ztePeopleMap.get(t.getPeopleCode());
            if (people != null) {
                t.setPeopleLabel(people.getPeopleLabel());
                t.setPeopleName(people.getPeopleName());
                t.setPositionName(people.getPositionName());
                t.setDeptFullName(people.getDeptFullName());
            }
            t.setRowId(null);
            t.setPeopleType(ActivityPeopleTypeEnum.PARTICIPANTS.getCode());
        });
    }
}
