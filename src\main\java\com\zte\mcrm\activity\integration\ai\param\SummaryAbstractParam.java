package com.zte.mcrm.activity.integration.ai.param;

import com.zte.mcrm.activity.integration.ai.dto.PeopleInfoDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 会议纪要摘要AI生成接口参数
 *
 * <AUTHOR>
 * @date 2025/3/5 下午7:41
 */
@Getter
@Setter
@ToString
public class SummaryAbstractParam {

    /**
     * key为客户编码，value为客户联系人信息。岗位 + 空格 + 姓名
     */
    private Map<String, List<PeopleInfoDTO>> peopleInfoMap;

    /**
     * 遗留问题
     */
    private List<String> question;

    /**
     * 会议纪要内容
     */
    private String summaryContent;

    /**
     * 附件下载链接
     */
    private String fileUrl;

    /**
     * 附件类型
     */
    private String fileType;

    /**
     * 语言，目前需要支持中英
     */
    private List<String> languages;

}
