package com.zte.mcrm.activity.repository.model.comment;

import java.util.Date;

/**
 * table:activity_relation_comment -- 
 */
public class ActivityRelationCommentDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String activityRowId;

    private String commentEmpNo;

    /** ICenter沟通交流评论的消息id。 */
    private String icenterCommentId;
    private String icenterParentCommentId;

    private String parentCommentEmpNo;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getIcenterCommentId() {
        return icenterCommentId;
    }

    public void setIcenterCommentId(String icenterCommentId) {
        this.icenterCommentId = icenterCommentId == null ? null : icenterCommentId.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    /** 评论关联的父级员工编号（比如：回复A信息，那么这里就是A信息的评论员工编号） */
    public String getParentCommentEmpNo() {
        return parentCommentEmpNo;
    }

    public void setParentCommentEmpNo(String parentCommentEmpNo) {
        this.parentCommentEmpNo = parentCommentEmpNo;
    }

    /** 评论关联的父级评审ID（比如：回复A信息，那么这里就是A信息的评论ID） */
    public String getIcenterParentCommentId() {
        return icenterParentCommentId;
    }

    public void setIcenterParentCommentId(String icenterParentCommentId) {
        this.icenterParentCommentId = icenterParentCommentId;
    }

    /** 当前评论人员工编号 */
    public String getCommentEmpNo() {
        return commentEmpNo;
    }

    public void setCommentEmpNo(String commentEmpNo) {
        this.commentEmpNo = commentEmpNo;
    }
}