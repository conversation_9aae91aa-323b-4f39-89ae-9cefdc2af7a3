package com.zte.mcrm.activity.common.config;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.util.StringUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * LCM授权配置（<a href="https://i.zte.com.cn/#/shared/c2c230795c78479fbd0b08794441aa5d/wiki/page/3d2447d108da408e951937f536207bd6/view">...</a>）
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@ConfigurationProperties(
        prefix = "lcm.auth.config",
        ignoreUnknownFields = true
)
public class LcmAuthConfig {
    /**
     * 授权token
     */
    private String feederSystemToken;

    /**
     * 俄罗斯，乌克兰0182,0219
     */
    private String limitCountry;
    /**
     * 对于国家地区是xxx时，对应联系人需要传入拼音。中国大陆 0001
     */
    private String pinyinLocal;

    /**
     * 获取LCM受限制国家
     * @return
     */
    public String[] fetchLimitCountry() {
        if (StringUtils.isBlank(limitCountry)) {
            return new String[]{};
        }

        return limitCountry.split(CharacterConstant.COMMA);
    }

    /**
     * 获取LCM受限制国家
     * @return
     */
    public String[] fetchPinyinLocal() {
        if (StringUtils.isBlank(pinyinLocal)) {
            return new String[]{};
        }

        return pinyinLocal.split(CharacterConstant.COMMA);
    }
}
