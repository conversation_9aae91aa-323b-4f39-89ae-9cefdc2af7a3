package com.zte.mcrm.activity.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 日期格式化工具
 *
 * <AUTHOR>
 * @date 2023/8/31 下午3:17
 */
public class DateFormatUtil {

    private DateFormatUtil() {
    }

    /**
     * 日期格式-点分隔，e.g. 2021.07.29
     */
    public static final String DATE_POINT_PATTERN = "yyyy.MM.dd";
    /**
     * 年
     */
    public static final String DATE_YYYY = "yyyy";
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String DATE_YYYY_MM_DD_HH_MM_SS_PATTERN = "yyyy-MM-dd HH:mm:ss";
    /**
     * yyyy-MM-dd HH:mm
     */
    public static final String DATE_YYYY_MM_DD_HH_MM_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String DATE_YYYY_MM_DD_PATTERN = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_SLASH = "yyyy/MM/dd";

    /**
     * 日期格式化
     *
     * @param date      时间
     * @param pattern   格式，不能为空
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/8/31 下午3:13
     */
    public static String format(final Date date, final String pattern) {
        if (Objects.isNull(date)) {
            return StringUtils.EMPTY;
        }
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 日期范围格式化
     *
     * @param start     开始时间
     * @param end       结束时间
     * @param pattern   格式，不能为空
     * @param split     日期范围分隔符，不能为空
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/8/31 下午3:16
     */
    public static String format(final Date start, final Date end, final String pattern, final String split) {
        String startDate = format(start, pattern);
        String endDate = format(end, pattern);
        return String.join(split, startDate, endDate);
    }
}
