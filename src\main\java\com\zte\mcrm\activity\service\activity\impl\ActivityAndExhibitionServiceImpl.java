package com.zte.mcrm.activity.service.activity.impl;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.service.activity.ActivityAndExhibitionService;
import com.zte.mcrm.activity.service.summary.ActivitySummaryPushDataService;
import com.zte.mcrm.activity.service.summary.dto.SummaryPushContentDTO;
import com.zte.mcrm.activity.service.summary.param.ActivitySummaryPushDataParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/6 15:13
 */
@Service
public class ActivityAndExhibitionServiceImpl implements ActivityAndExhibitionService {
    @Autowired
    private ActivitySummaryPushDataService activitySummaryPushDataService;

    @Override
    public SummaryPushContentDTO getSummaryData(String exhibitionId, String empNo) {
        ActivitySummaryPushDataParam param = new ActivitySummaryPushDataParam();
        param.setExhibitionId(exhibitionId);
        param.setTargetNo(empNo);
        BizRequest<ActivitySummaryPushDataParam> pushDataParamBizRequest = BizRequestUtil.createWithCurrentUserSecurity(param);
        BizResult<SummaryPushContentDTO> bizResult = activitySummaryPushDataService.getSummaryPushData(pushDataParamBizRequest);
        SummaryPushContentDTO obj = bizResult.getData();
        if (obj != null&& obj.getSummaryDateMap()!=null&& !obj.getSummaryDateMap().isEmpty()) {
            // 使用 TreeMap 来排序 map 的 key
            obj.setSummaryDateMap(new TreeMap<>(obj.getSummaryDateMap()));
            return obj;
        }
        return null;
    }
}
