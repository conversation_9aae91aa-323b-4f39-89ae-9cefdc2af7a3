package com.zte.mcrm.activity.repository.rep.authority.param;

import com.zte.mcrm.activity.common.model.PageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/12/27 16:22
 */
@Getter
@Setter
@ToString
public class TalkAuthorityQuery extends PageQuery {

    /**
     * 被授权人
     */
    private List<String> empNoList;

    /**
     * 营销组织
     */
    private List<String> orgIdList;

    /**
     * 交流方向
     */
    private List<String> directionCommunicationList;

    /**
     * 营销组织 + 交流方向
     */
    private List<String> combineList;

    /**
     * 是否需要交流方向与营销组织共同过滤
     */
    private boolean needBothAuthFilter = false;
}
