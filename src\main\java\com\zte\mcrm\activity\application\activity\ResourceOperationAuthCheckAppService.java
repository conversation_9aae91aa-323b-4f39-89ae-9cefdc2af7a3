package com.zte.mcrm.activity.application.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.service.attachment.impl.ActivityAttachmentServiceImpl;
import com.zte.mcrm.activity.service.file.impl.DownloadServiceImpl;
import com.zte.mcrm.activity.web.controller.authority.params.OperationAuthCheckParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceAddParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceDeleteParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceDownloadParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourcePreviewParam;
import com.zte.mcrm.common.cloududm.service.FileInfoServiceImpl;

import javax.servlet.http.HttpServletResponse;

/**
 * 资源操作权限校验
 *
 * <AUTHOR>
 * @date 2024/11/20 下午3:04
 */
public interface ResourceOperationAuthCheckAppService {

    /**
     * 操作权限校验
     *
     * @param request
     * @return {@link BizResult < Boolean>}
     * <AUTHOR>
     * @date 2024/11/19 下午3:54
     */
    BizResult<Boolean> checkAuth(BizRequest<OperationAuthCheckParam> request);

    /**
     * 附件新增
     * {@link ActivityAttachmentServiceImpl#insertSelective(BizRequest)}
     *
     * @param request
     * @return {@link BizResult<String>}
     * <AUTHOR>
     * @date 2024/11/21 下午8:55
     */
    BizResult<String> addAttachment(BizRequest<AttachmentResourceAddParam> request);

    /**
     * 附件删除
     * {@link ActivityAttachmentServiceImpl#deleteByRowId(String)}
     *
     * @param request
     * @return {@link BizResult< Integer>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:31
     */
    BizResult<Integer> deleteAttachment(BizRequest<AttachmentResourceDeleteParam> request);

    /**
     * 附件预览
     * {@link FileInfoServiceImpl#getPreviewUrl(String, String, String)}
     *
     * @param request
     * @return {@link BizResult< String>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:38
     */
    BizResult<String> previewAttachment(BizRequest<AttachmentResourcePreviewParam> request);

    /**
     * 附件下载
     * {@link DownloadServiceImpl#download(BizRequest, HttpServletResponse)}
     *
     * @param request
     * @return {@link BizResult<Void>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:39
     */
    BizResult<Void> downloadAttachment(BizRequest<AttachmentResourceDownloadParam> request, HttpServletResponse response);




}
