package com.zte.mcrm.activity.repository.rep.people.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.people.ActivityRelationZtePeopleExtMapper;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityRelationZtePeopleQuery;
import com.zte.mcrm.activity.service.resource.vo.ActivityTimesCountVO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.ENABLED_FLAG_Y;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 活动我司人员
 *
 * <AUTHOR>
 */
@Component
public class ActivityRelationZtePeopleRepositoryImpl implements ActivityRelationZtePeopleRepository {
    @Resource
    private ActivityRelationZtePeopleExtMapper activityRelationZtePeopleExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivityRelationZtePeopleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return ZERO;
        }

        String empNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (ActivityRelationZtePeopleDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                empNo = StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : empNo;
                record.setRowId(keyIdService.getKeyId());
                record.setCreatedBy(empNo);
                record.setLastUpdatedBy(empNo);
                record.setCreationDate(new Date());
                record.setLastUpdateDate(new Date());
                record.setEnabledFlag(ENABLED_FLAG_Y);
            }
            activityRelationZtePeopleExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationZtePeopleDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityRelationZtePeopleExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationZtePeopleDO> queryAllZtePeopleForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityRelationZtePeopleExtMapper.queryAllZtePeopleForActivity(activityRowId);
    }

    @Override
    public List<ActivityRelationZtePeopleDO> queryInfoList(ActivityRelationZtePeopleQuery activityRelationZtePeopleQuery) {
        return null == activityRelationZtePeopleQuery ? Collections.emptyList() : activityRelationZtePeopleExtMapper.queryInfoList(activityRelationZtePeopleQuery);
    }

    /**
     * 查找用户最近创建的活动中使用的中兴参与人
     *
     * @param pageQuery
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:57
     */
    @Override
    public List<ActivityRelationZtePeopleDO> selectRecentlyZtePeopleByUser(PageQuery<ActivityRecentlySearchParam> pageQuery) {
        if (!pageQuery.validatePage() || StringUtils.isBlank(pageQuery.getParam().getEmpNo())) {
            return Collections.emptyList();
        }
        PageInfo<ActivityRelationZtePeopleDO> pageInfo
                = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> activityRelationZtePeopleExtMapper.selectRecentlyZtePeopleByUser(pageQuery.getParam()));
        return pageInfo.getList();
    }

    /**
     * 根据工号统计活动参与数量
     *
     * @param codeList           工号
     * @param activityStatusList 活动状态
     * @return {@link List<  ActivityTimesCountVO  >}
     * <AUTHOR>
     * @date 2023/5/22 下午2:29
     */
    @Override
    public List<ActivityTimesCountVO> selectCountByPeopleCode(List<String> codeList, List<String> activityStatusList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        return activityRelationZtePeopleExtMapper.selectCountByPeopleCode(codeList, activityStatusList);
    }

    @Override
    public List<ActivityRelationZtePeopleDO> queryByActivityRowIdAndPeopleType(String activityRowId, List<String> codes) {
        return activityRelationZtePeopleExtMapper.queryByActivityRowIdAndPeopleType(activityRowId, codes);
    }

    /**
     * 批量获取讲师
     * @param activityRowIds
     * @param codes
     * @return
     */
    @Override
    public Map<String, List<ActivityRelationZtePeopleDO>> queryByActivityRowIdsAndPeopleType(List<String> activityRowIds, List<String> codes) {
        List<ActivityRelationZtePeopleDO> relationZtePeopleDOList = activityRelationZtePeopleExtMapper.queryByActivityRowIdsAndPeopleType(activityRowIds, codes);
        return CollectionUtils.isEmpty(relationZtePeopleDOList) ? Collections.emptyMap(): relationZtePeopleDOList.stream()
                .collect(Collectors.groupingBy(ActivityRelationZtePeopleDO::getActivityRowId));

    }

    /**
     * 批量更新
     *
     * @param updateList
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    @Override
    public int batchUpdateByPrimaryKey(List<ActivityRelationZtePeopleDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        String updateBy = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (ActivityRelationZtePeopleDO entity : updateList) {
            entity.setLastUpdatedBy(updateBy);
            entity.setLastUpdateDate(new Date());
        }
        return activityRelationZtePeopleExtMapper.batchUpdateByPrimaryKey(updateList);
    }

    @Override
    public int batchUpdateByPrimaryKeyWithoutUpdateInfo(List<ActivityRelationZtePeopleDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        return activityRelationZtePeopleExtMapper.batchUpdateByPrimaryKey(updateList);
    }

    /**
     * 查询指定的参与人，具备权限开关，默认只查具备权限的
     *
     * @param query 查询实体
     * @return List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/5/23 10:35
     */
    @Override
    public List<ActivityRelationZtePeopleDO> queryZtePeople(ActivityRelationZtePeopleQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        return activityRelationZtePeopleExtMapper.queryInfoList(query);
    }

    /**
     * 根据活动主键查询参与人-仅查询有权限的人的信息
     *
     * @param activityRowId 活动主键Id
     * @param minAuthCount  权限值
     * @return ActivityRelationZtePeopleDO
     * <AUTHOR>
     * date: 2023/5/21 14:37
     */
    @Override
    public List<ActivityRelationZtePeopleDO> queryZtePeopleByActivityRowId(String activityRowId, Integer minAuthCount) {
        if (StringUtils.isBlank(activityRowId)) {
            return Lists.newArrayList();
        }
        ActivityRelationZtePeopleQuery query = new ActivityRelationZtePeopleQuery();
        query.setActivityRowId(activityRowId);
        query.setMinAuthCount(minAuthCount);
        return this.queryZtePeople(query);
    }

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchInsert(List<ActivityRelationZtePeopleDO> list) {
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return ZERO;
        }
        list.forEach(this::setDefaultValue);
        activityRelationZtePeopleExtMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 批量插入不赋予默认值
     *
     * @param list 列表
     * @return
     * <AUTHOR>
     */
    @Override
    public int batchInsertWithoutDefault(List<ActivityRelationZtePeopleDO> list) {
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return ZERO;
        }
        for (ActivityRelationZtePeopleDO ztePeopleDO : list) {
            ztePeopleDO.setRowId(keyIdService.getKeyId());
        }
        activityRelationZtePeopleExtMapper.batchInsert(list);
        return list.size();
    }

    /**
     * 设置默认值
     *
     * @param ztePeopleDO 实体类
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    private void setDefaultValue(ActivityRelationZtePeopleDO ztePeopleDO) {
        ztePeopleDO.setRowId(Optional.ofNullable(ztePeopleDO.getRowId()).orElse(keyIdService.getKeyId()));
        ztePeopleDO.setCreatedBy(Optional.ofNullable(ztePeopleDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        ztePeopleDO.setLastUpdatedBy(Optional.ofNullable(ztePeopleDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        ztePeopleDO.setAuthCount(Optional.ofNullable(ztePeopleDO.getAuthCount()).orElse(NumberConstant.ONE));
        ztePeopleDO.setCreationDate(new Date());
        ztePeopleDO.setLastUpdateDate(new Date());
        ztePeopleDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }


    @Override
    public Map<String, List<ActivityRelationZtePeopleDO>> getZtePeopleListByActivityRowIds(Set<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityRelationZtePeopleExtMapper.getZtePeopleListByActivityRowIds(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityRelationZtePeopleDO::getActivityRowId));
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return ZERO;
        }
        return activityRelationZtePeopleExtMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return ZERO;
        }
        return activityRelationZtePeopleExtMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public int deleteBatch(List<String> rowIdList) {
        return CollectionUtils.isEmpty(rowIdList) ? 0 : activityRelationZtePeopleExtMapper.deleteBatch(rowIdList);
    }

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效联系人
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityRelationZtePeopleDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityRelationZtePeopleExtMapper.queryAllActivityWithNotEnable(activityRowId);
    }

    /**
     * 查询没有名字的我司联系人
     *
     * @param activityRowId
     * @param limit
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2024/1/3 下午11:45
     */
    @Override
    public List<ActivityRelationZtePeopleDO> selectNoNameList(String activityRowId, String rowId, int limit) {
        return activityRelationZtePeopleExtMapper.selectNoNameList(activityRowId, rowId, limit);
    }

    @Override
    public List<String> getUpdateSaventLabelRowIds(List<String> activityRowIds, List<String> expertCodes) {
        return activityRelationZtePeopleExtMapper.getUpdateSaventLabelRowIds(activityRowIds, expertCodes);
    }

    @Override
    public int updateSaventLabel(List<String> rowIds) {
        return activityRelationZtePeopleExtMapper.updateSaventLabel(rowIds);
    }
}
