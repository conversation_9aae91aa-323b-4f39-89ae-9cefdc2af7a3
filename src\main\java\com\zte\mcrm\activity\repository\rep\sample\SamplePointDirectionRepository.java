package com.zte.mcrm.activity.repository.rep.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointDirectionDO;

import java.util.List;
import java.util.Map;

/**
 * 样板点展示方向
 *
 * <AUTHOR>
 */
public interface SamplePointDirectionRepository {
    /**
     * 新增样板点展示方向
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<SamplePointDirectionDO> recordList);

    /**
     * 更新样板点展示方向
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SamplePointDirectionDO record);

    /**
     * 删除样板点展示方向
     *
     * @param samplePointRowId
     * @return
     */
    int deleteBySamplePointRowId(String samplePointRowId);

    /**
     * description 根据样板点id列表查询关联展示方向
     *
     * @param samplePointRowIdList 样板点id列表
     * @return 展示方向
     * <AUTHOR>
     * @createDate 2024/2/7 下午4:16
     */
    Map<String, List<SamplePointDirectionDO>> queryDirectionBySamplePointRowId(List<String> samplePointRowIdList);
}
