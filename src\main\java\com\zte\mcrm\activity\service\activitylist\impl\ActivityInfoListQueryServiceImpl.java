package com.zte.mcrm.activity.service.activitylist.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.auth.ActivityRoleConstraintModel;
import com.zte.mcrm.activity.common.auth.CustomerIntegrationAuthModel;
import com.zte.mcrm.activity.common.config.CustomerIntegrationUppConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.LookupConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupDto;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtServiceImpl;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryApRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRdcRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRepository;
import com.zte.mcrm.activity.service.activity.ActivityOpportunityService;
import com.zte.mcrm.activity.service.activitylist.ActivityInfoListQueryService;
import com.zte.mcrm.activity.service.activitylist.ActivityNodeService;
import com.zte.mcrm.activity.service.activitylist.convert.ActivityFlowConvert;
import com.zte.mcrm.activity.service.activitylist.param.*;
import com.zte.mcrm.activity.service.activitylist.vo.*;
import com.zte.mcrm.activity.service.approval.ActivityApprovalInfoService;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.service.people.ActivityZtePeopleService;
import com.zte.mcrm.activity.service.reservation.ActivityResourceReservationService;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityOpportunityRecordVO;
import com.zte.mcrm.activity.web.controller.activitylist.convert.ActivityInfoListConvert;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.adapter.bo.EmployeeBO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.common.util.DateUtil;
import com.zte.mcrm.common.util.ObjectUtils;
import com.zte.mcrm.custcomm.common.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.LookupConstant.PARENT_LOOKUP_TYPE_EXTENSION_TYPE;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ONE;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.in;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 14:33
 */
@Service
public class ActivityInfoListQueryServiceImpl implements ActivityInfoListQueryService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityInfoListQueryServiceImpl.class);

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;
    @Autowired
    private ActivitySummaryRepository activitySummaryRepository;
    @Autowired
    private ActivitySummaryApRepository activitySummaryApRepository;
    @Autowired
    private ActivitySummaryRdcRepository activitySummaryRdcRepository;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    @Autowired
    private ActivityResourceReservationService activityResourceReservationService;

    @Autowired
    private ActivityApprovalProcessRepository activityApprovalProcessRepository;

    @Autowired
    private ActivityApprovalInfoService activityApprovalInfoService;

    @Autowired
    private ActivityStatusLifecycleRepository statusLifecycleRepository;
    @Autowired
    private static LocaleMessageSourceBean lmsb;

    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;

    @Autowired
    private LookUpExtServiceImpl lookUpExtService;

    @Autowired
    private ActivityCommunicationDirectionRepository activityCommunicationDirectionRepository;

    @Autowired
    private ActivityNodeService nodeService;

    @Autowired
    private ActivityZtePeopleService ztePeopleService;
    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;

    @Autowired
    private ActivityEsSearchRepository activityEsSearchRepository;

    @Autowired
    private ActivityISearchService iSearchService;
    @Autowired
    private CustomerIntegrationUppConfig uppConfig;
    @Autowired
    private ActivityScheduleItemPeopleRepository  scheduleItemPeopleRepository;
    @Autowired
    private ExhibitionDirectorRepository exhibitionDirectorRepository;
    @Autowired
    private SamplePointInfoRepository samplePointInfoRepository;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private ActivityOpportunityService activityOpportunityService;

    @Override
    public PageRows<ActivityInfoVO> getActivityInfoList(BizRequest<ActivityInfoSearchParamVO> bizRequest, Integer pageNo, Integer pageSize){
        // 【1】查询准备各个相关数据
        ActivityInfoQuery activityInfoQuery = ActivityInfoListConvert.toActivityInfoQuery(bizRequest, pageNo, pageSize);
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(HeadersProperties.getXEmpNo());
        activityInfoQuery.setAuthModel(authModel);
        StandardActivityDetailDataSource detailDataSource = getStandardActivityDetailDataSource(activityInfoQuery);
        PageRows<ActivityInfoDO> activityInfoDoPageRows = detailDataSource.getActivityInfoDoPageRows();
        List<ActivityInfoDO> activityInfoDOList = detailDataSource.getActivityInfoList();
        if (CollectionUtils.isEmpty(activityInfoDOList)) {
            return PageRowsUtil.buildEmptyPage(activityInfoQuery);
        }
        // 【2】数据打包转换
        return PageRowsUtil.buildPageRow((int) activityInfoDoPageRows.getCurrent(), (int)activityInfoDoPageRows.getPageSize()
                , (int) activityInfoDoPageRows.getTotal(), toActivityInfoVoList(detailDataSource, authModel));
    }

    @Override
    public List<ActivityConvergenceInfoVO> queryActivityConvergenceInfo(BizRequest<ActivityInfoSearchParamVO> req) {
        ActivityInfoQuery activityInfoQuery = ActivityInfoListConvert.toActivityInfoQuery(req, 1, 0);
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(req.getEmpNo());
        activityInfoQuery.setAuthModel(authModel);
        Map<String, String> validTypeMap = lookUpExtService.listLookupByParentType(PARENT_LOOKUP_TYPE_EXTENSION_TYPE)
                .stream().collect(Collectors.toMap(FastLookupDto::getLookupCode, FastLookupDto::getMeaning));
        return activityEsSearchRepository.searchConvergeInfo(activityInfoQuery).stream()
                .filter(item -> {
                    item.setActivityTypeName(validTypeMap.get(item.getActivityType()));
                    return validTypeMap.containsKey(item.getActivityType());
                }).sorted(new Comparator<ActivityConvergenceInfoVO>() {
                    @Override
                    public int compare(ActivityConvergenceInfoVO o1, ActivityConvergenceInfoVO o2) {
                        return ActivityTypeEnum.compare(o1.getActivityType(), o2.getActivityType());
                    }
                }).collect(Collectors.toList());
    }

    /**
     * 获取活动权限信息
     * @param param 查询参数
     * @return java.util.List<ActivityInfoVO>
     * <AUTHOR>
     * date: 2023/8/29 10:26
     */
    @Override
    public List<ActivityInfoVO> getActivityAuthInfo(BizRequest<List<String>> param){
        if (param == null) {
            return Lists.newArrayList();
        }
        List<String> activityRowIdList = param.getParam();
        if (CollectionUtils.isEmpty(activityRowIdList)) {
            return Lists.newArrayList();
        }
        // 【1】查询准备各个相关数据
        ActivityInfoQuery activityInfoQuery = new ActivityInfoQuery();
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(HeadersProperties.getXEmpNo());
        activityInfoQuery.setActivityRowIdList(activityRowIdList);
        activityInfoQuery.setPage(ONE, activityRowIdList.size());
        activityInfoQuery.setAuthModel(authModel);
        StandardActivityDetailDataSource detailDataSource = this.getStandardActivityDetailDataSource(activityInfoQuery);

        // 【2】数据打包转换
        return CollectionUtils.isEmpty(detailDataSource.getActivityInfoList()) ?
                Lists.newArrayList() : fillAuthInfo(detailDataSource, authModel);
    }

    /**
     * 填充权限信息
     * @param detailDataSource  活动数据源
     * @param authModel 权限模型
     * @return java.util.List<com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO>
     * <AUTHOR>
     * date: 2023/8/29 10:19
     */
    private List<ActivityInfoVO> fillAuthInfo(StandardActivityDetailDataSource detailDataSource, CustomerIntegrationAuthModel authModel) {
        // 打包基本信息
        List<ActivityInfoVO> activityInfoVoList = packActivityInfo(detailDataSource);
        // 操作权限
        fillOperationAuth(activityInfoVoList, detailDataSource, authModel);
        return activityInfoVoList;
    }

    /**
     * 获取数据源
     * @param activityInfoQuery 活动信息查询条件
     * @return com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource
     * <AUTHOR>
     * date: 2023/8/29 10:24
     */
    @Override
    public StandardActivityDetailDataSource getStandardActivityDetailDataSource(ActivityInfoQuery activityInfoQuery) {
        StandardActivityDetailDataSource detailDataSource = new StandardActivityDetailDataSource();
        // 这个方法权限判断也做了复用，做下兼容处理，如果参数有传id走sql查询，不会影响查询效率，同时防止因为ES延迟或丢失导致权限判断异常
        PageRows<ActivityInfoDO> activityInfoDoPageRows;
        if (CollectionUtils.isEmpty(activityInfoQuery.getActivityRowIdList())) {
            activityInfoDoPageRows = activityEsSearchRepository.searchPage(activityInfoQuery);
        } else {
            activityInfoDoPageRows = activityInfoRepository.searchByPage(activityInfoQuery);
        }
        List<ActivityInfoDO> activityInfoDOList = activityInfoDoPageRows.getRows();
        if (CollectionUtils.isEmpty(activityInfoDOList)) {
            detailDataSource.setActivityInfoList(Lists.newArrayList());
            return detailDataSource;
        }
        detailDataSource.setActivityInfoList(activityInfoDOList);
        detailDataSource.setActivityInfoDoPageRows(activityInfoDoPageRows);
        getDetailDataSource(detailDataSource);
        return detailDataSource;
    }

    @Override
    public StandardActivityDetailDataSource getStandardActivityDetailDataSource(ActivityDataSourceQuery query) {
        StandardActivityDetailDataSource detailDataSource = new StandardActivityDetailDataSource();
        List<String> activityRowIds = query.fetchActivityRowIdList();

        if (query.isNeedActivity()) {
            if (CollectionUtils.isEmpty(query.getActivityInfoList())) {
                detailDataSource.setActivityInfoList(activityInfoRepository.selectByIds(activityRowIds));
            } else {
                detailDataSource.setActivityInfoList(query.getActivityInfoList());
            }
        }

        if (query.isNeedActivityDirection()) {
            detailDataSource.setDirectionMap(activityCommunicationDirectionRepository.queryAllByActivityRowId(activityRowIds));
        }

        if (query.isNeedPending()) {
            detailDataSource.setPendingNoticeMap(activityPendingNoticeRepository.queryAllPendingByActivityRowId(new HashSet<>(activityRowIds)));
        }

        // 填充客户、客户参与人、我司参与人
        fillActivityParticipants(query, detailDataSource);
        // 填充活动关联的原始信息（展会/大会、样板点等）
        fillActivityOrigin(query, detailDataSource);
        // 填充会议纪要相关的内容（会议纪要，AP，RDC，遗留问题等）
        fillActivitySummery(query, detailDataSource);

        return detailDataSource;
    }

    /**
     * 填充会议纪要相关的内容（会议纪要，AP，RDC，遗留问题等）
     *
     * @param query
     * @param detailDataSource
     */
    void fillActivitySummery(ActivityDataSourceQuery query, StandardActivityDetailDataSource detailDataSource) {
        List<String> activityRowIds = query.fetchActivityRowIdList();
        if (query.isNeedAp()) {
            detailDataSource.setSummaryApMap(activitySummaryApRepository.queryAllByActivityRowId(activityRowIds));
        }

        if (query.isNeedRdc()) {
            detailDataSource.setSummaryRdcMap(activitySummaryRdcRepository.queryAllRdcForActivity(activityRowIds));
        }
    }

    /**
     * 填充参与人等（客户、客户参与人、我司参与人）
     * @param query
     * @param detailDataSource
     */
    void fillActivityParticipants(ActivityDataSourceQuery query, StandardActivityDetailDataSource detailDataSource) {
        List<String> activityRowIds = query.fetchActivityRowIdList();
        if (query.isNeedActivityCustomerInfo()) {
            detailDataSource.setCustomerInfoMap(activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(new HashSet<>(activityRowIds)));
        }

        if (query.isNeedActivityCustomerPeople()) {
            detailDataSource.setCustPeopleMap(activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(new HashSet<>(activityRowIds)));
        }

        if (query.isNeedActivityZtePeople()) {
            detailDataSource.setZtePeopleMap(activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(new HashSet<>(activityRowIds)));
        }
    }

    /**
     * 填充活动相关的来源信息（展会/大会、样板点）
     * @param query
     * @param detailDataSource
     */
    void fillActivityOrigin(ActivityDataSourceQuery query, StandardActivityDetailDataSource detailDataSource) {
        // 处理圈复杂度
        List<ActivityInfoDO> acList = detailDataSource.getActivityInfoList();
        if (CollectionUtils.isEmpty(acList)) {
            return;
        }

        // 需要展会信息
        if (query.isNeedExhibition()) {
            detailDataSource.setActivityRowId2ExhibitionMap(getActivityIdAndExhibitionInfoDOMap(acList));
        }

        if (query.isNeedSample()) {
            detailDataSource.setActivityRowId2SamplePointMap(getActivityIdAndSamplePointInfoDOMap(acList));
        }
    }

    /**
     * 活动详情获取权限数据
     * @param activityRowId 活动ID
     * @return
     */
    @Override
    public List<ActivityInfoVO> getActivityDetailDataSource(String activityRowId) {
        List<ActivityInfoVO> list = Lists.newArrayList();
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(activityRowId);
        if(Objects.isNull(activityInfoDO)){
            return list;
        }
        StandardActivityDetailDataSource detailDataSource = new StandardActivityDetailDataSource();
        //获取权限校验数据
        Map<String, List<ActivityScheduleItemPeopleDO>> schedulePeopleMap =  scheduleItemPeopleRepository.queryAllSchedulePeopleByActivityRowIds(Lists.newArrayList(activityRowId));
        Map<String,List<ExhibitionDirectorDO>> exhibitionDirectionMap = exhibitionDirectorRepository.queryDirectorByExhibitionRowId(Lists.newArrayList(activityInfoDO.getOriginRowId()));
        detailDataSource.setActivityInfoList(Lists.newArrayList(activityInfoDO));
        detailDataSource.setScheduleItem2People(schedulePeopleMap);
        detailDataSource.setExhibitionDirectionMap(exhibitionDirectionMap);
        getDetailDataSource(detailDataSource);
        //获取权限模型
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(HeadersProperties.getXEmpNo());
        // 【2】数据打包转换
        return fillAuthInfo(detailDataSource, authModel);
    }

    /**
     * 获取所需数据
     * @param detailDataSource
     * @return
     */
    private StandardActivityDetailDataSource getDetailDataSource(StandardActivityDetailDataSource detailDataSource){
        List<ActivityInfoDO> activityInfoDOList = detailDataSource.getActivityInfoList();
        if (CollectionUtils.isEmpty(activityInfoDOList)) {
            detailDataSource.setActivityInfoList(Lists.newArrayList());
            return detailDataSource;
        }
        Set<String> activityRowIds = activityInfoDOList.stream().map(item -> item.getRowId()).collect(Collectors.toSet());

        // 查询客户信息
        Map<String, List<ActivityCustomerInfoDO>> customerMap = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(activityRowIds);
        // 查询客户联系人
        Map<String, List<ActivityRelationCustPeopleDO>> custPeopleMap = activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(activityRowIds);
        // 查询中兴联系人
        Map<String, List<ActivityRelationZtePeopleDO>> ztePeopleMap= activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(activityRowIds);
        // 交流方向
        Map<String, List<ActivityCommunicationDirectionDO>> directionMap = activityCommunicationDirectionRepository.queryAllByActivityRowId(Lists.newArrayList(activityRowIds));
        // 待办信息
        Map<String, List<ActivityPendingNoticeDO>> pendingNoticeMap = activityPendingNoticeRepository.queryAllPendingByActivityRowId(activityRowIds);
        detailDataSource.setActivityRowId2ExhibitionMap(getActivityIdAndExhibitionInfoDOMap(activityInfoDOList));
        detailDataSource.setActivityRowId2SamplePointMap(getActivityIdAndSamplePointInfoDOMap(activityInfoDOList));
        detailDataSource.setActivityInfoList(activityInfoDOList);
        detailDataSource.setCustomerInfoMap(customerMap);
        detailDataSource.setCustPeopleMap(custPeopleMap);
        detailDataSource.setZtePeopleMap(ztePeopleMap);
        detailDataSource.setDirectionMap(directionMap);
        detailDataSource.setPendingNoticeMap(pendingNoticeMap);
        return detailDataSource;
    }

    private Map<String, ExhibitionInfoDO> getActivityIdAndExhibitionInfoDOMap(List<ActivityInfoDO> activityInfoDOList) {
        Map<String, ExhibitionInfoDO> result = Maps.newHashMap();
        List<String> exhibitionIds = activityInfoDOList.stream()
                .filter(e -> in(e.getOriginType(), EXHIBITION, CONFERENCE) && StringUtils.isNotBlank(e.getOriginRowId()))
                .map(e -> e.getOriginRowId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(exhibitionIds)) {
            return result;
        }
        Map<String, ExhibitionInfoDO> exhibitionInfoMap = exhibitionInfoRepository.queryExhibitionInfoByRowId(exhibitionIds);
        for (ActivityInfoDO activityInfoDO : activityInfoDOList) {
            if (!ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), EXHIBITION, CONFERENCE)) {
                continue;
            }
            result.put(activityInfoDO.getRowId(), exhibitionInfoMap.get(activityInfoDO.getOriginRowId()));
        }
        return result;
    }

    private Map<String, SamplePointInfoDO> getActivityIdAndSamplePointInfoDOMap(List<ActivityInfoDO> activityInfoDOList) {
        Map<String, SamplePointInfoDO> result = Maps.newHashMap();
        List<String> samplePointIds = activityInfoDOList.stream()
                .filter(e -> in(e.getOriginType(), SAMPLE_POINT) && StringUtils.isNotBlank(e.getOriginRowId()))
                .map(e -> e.getOriginRowId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samplePointIds)) {
            return result;
        }
        Map<String, SamplePointInfoDO> samplePointInfoMap = samplePointInfoRepository.querySamplePointInfoByRowIds(samplePointIds);
        for (ActivityInfoDO activityInfoDO : activityInfoDOList) {
            if (!ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), SAMPLE_POINT)) {
                continue;
            }
            result.put(activityInfoDO.getRowId(), samplePointInfoMap.get(activityInfoDO.getOriginRowId()));
        }
        return result;
    }



    /**
     * 打包数据
     * @param detailDataSource
     * @return
     */
    private List<ActivityInfoVO> toActivityInfoVoList(StandardActivityDetailDataSource detailDataSource, CustomerIntegrationAuthModel authModel){
        // 打包基本信息
        List<ActivityInfoVO> activityInfoVoList = packActivityInfo(detailDataSource);
        // 填充我司参与人
        fillZtePeopleInfo(activityInfoVoList, detailDataSource);
        // 填充客户信息
        fillCustomerInfo(activityInfoVoList, detailDataSource);
        // 填充客户联系人
        fillCustomerPeopleInfo(activityInfoVoList, detailDataSource);

        // 操作权限
        fillOperationAuth(activityInfoVoList, detailDataSource, authModel);

        return activityInfoVoList;
    }

    /**
     * 判断用户是否具有操作权限-以及当前活动是否可以执行相关操作
     *
     * @param activityInfoVoList    活动行数据
     * @param detailDataSource      活动原始数据
     * @param authModel             权限模型
     * <AUTHOR>
     * @date 2023/8/18 下午5:26
     */
    private void fillOperationAuth(List<ActivityInfoVO> activityInfoVoList,
                                   StandardActivityDetailDataSource detailDataSource,
                                   CustomerIntegrationAuthModel authModel) {
        // 导出按钮是否展示，增加拓展活动查询员
        boolean finalExportButton = isExportButtonShow(authModel);
        activityInfoVoList.forEach(item -> {
            ActivityOperatorFlagVO operatorFlagVO = new ActivityOperatorFlagVO();
            String rowId = item.getRowId();
            String activityStatus = item.getActivityStatus();
            List<ActivityPendingNoticeDO> pendingNoticeDOList = detailDataSource.getPendingNoticeMap().get(rowId);
            Set<String> dealWithType = CollectionUtils.isEmpty(pendingNoticeDOList) ? Sets.newHashSet()
                    : pendingNoticeDOList.stream()
                    .filter(e -> PendingNoticeStatusEnum.WAIT_DEAL.isMe(e.getPendingStatus()))
                    .filter(e -> StringUtils.equals(e.getNoticerNo(), BizRequestUtil.createWithCurrentUser().getEmpNo()))
                    .map(ActivityPendingNoticeDO::getPendingBizType).collect(Collectors.toSet());
            boolean isDraft = ActivityStatusEnum.DRAFT.isMe(activityStatus);
            boolean isProgress = ActivityStatusEnum.PROGRESS.isMe(activityStatus);
            boolean isApprove = ActivityStatusEnum.in(activityStatus, ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.COMPLIANCE_APPROVAL);
            boolean isFinish = ActivityStatusEnum.in(activityStatus, ActivityStatusEnum.FINISH, ActivityStatusEnum.EVALUATED);
            boolean receptionFlag = ActivityTypeEnum.CUSTOMER_VISIT_ACTIVITY.isMe(item.getActivityType())
                    && !ActivityStatusEnum.in(activityStatus, ActivityStatusEnum.DRAFT,
                    ActivityStatusEnum.COMPLIANCE_APPROVAL_NOT_PASS, ActivityStatusEnum.BUSINESS_APPROVAL_NOT_PASS);
            boolean isComplianceApproval = dealWithType.contains(PendingBizTypeEnum.APPROVAL_COMPLIANCE.getType());
            boolean isBizApproval = dealWithType.contains(PendingBizTypeEnum.APPROVAL_LEADER.getType());
            // 草稿可删除和编辑
            operatorFlagVO.setEditable(isDraft && authModel.editable(detailDataSource, rowId));
            operatorFlagVO.setDeletable(isDraft && authModel.deletable(detailDataSource, rowId));
            // 草稿不可转发
            operatorFlagVO.setForwardable(!isDraft && authModel.viewable(detailDataSource, rowId));
            // 活动审批中/进行中可撤销
            operatorFlagVO.setCancelable((isProgress || isApprove) && authModel.cancelable(detailDataSource, rowId));
            // 活动进行中可变更
            operatorFlagVO.setChangeable(isProgress && authModel.changeable(detailDataSource, rowId));
            // 活动执行完成可作废
            operatorFlagVO.setVoidable(isFinish && authModel.voidable(detailDataSource, rowId));
            // 用户所有有权限的活动都可复制
            operatorFlagVO.setCopyable(true);
            operatorFlagVO.setReceptionFlag(receptionFlag);
            // 是否审批
            operatorFlagVO.setIsApproval(isApprove && (isBizApproval || isComplianceApproval));
            TalkOperatorVO talkOperatorVO = authModel.talkOperateable(detailDataSource, item);
            operatorFlagVO.setTalkOperate(talkOperatorVO.getTalkOperate());
            operatorFlagVO.setTalkManager(talkOperatorVO.getTalkManager());
            item.setOperatorFlagInfo(operatorFlagVO);
            item.setExportButton(finalExportButton);
        });

    }

    /**
     * 拓展活动列表导出按钮是否展示
     *
     * @param authModel
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/8/13 下午2:48
     */
    /* Started by AICoder, pid:fd148nc523g0b521428a0926e033cc1408200ca6 */
    private boolean isExportButtonShow(CustomerIntegrationAuthModel authModel) {
        Set<String> roleCode = authModel.getConstraintModelList().stream()
                .map(ActivityRoleConstraintModel::getRoleCode)
                .collect(Collectors.toSet());

        return roleCode.contains(uppConfig.getCustomerIntegrationActivityAdminRoleCode())
                || roleCode.contains(uppConfig.getCustomerIntegrationCrmManagerRoleCode())
                || roleCode.contains(uppConfig.getCustomerIntegrationCrmManagerNoMktRoleCode())
                || roleCode.contains(uppConfig.getCustomerIntegrationActivityInquirerRoleCode());
    }

    /* Ended by AICoder, pid:fd148nc523g0b521428a0926e033cc1408200ca6 */

    /**
     * 我司参与人信息
     * @param list
     * @param detailDataSource
     */
    private void fillZtePeopleInfo(List<ActivityInfoVO> list, StandardActivityDetailDataSource detailDataSource){
        // 获取当前登录人信息
        String xEmpNo = HeadersProperties.getXEmpNo();
        list.forEach( activityInfoVO -> {
            List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList = new ArrayList<>(10);
            activityRelationZtePeopleDOList.addAll(detailDataSource.fetchActivityZtePeople(activityInfoVO.getRowId(), ActivityPeopleTypeEnum.allParticipantsType()));
            Set<String> ztePeople = Sets.newHashSet();

            // 我司参与人 姓名+工号
            activityRelationZtePeopleDOList.forEach(vo -> {
                String peopleCode = vo.getPeopleCode();
                String peopleName = vo.getPeopleName();
                ztePeople.add(peopleName + peopleCode);
            });

            // 我司参与人 工号
            List<String> peopleCodeList = activityRelationZtePeopleDOList.stream().map(ActivityRelationZtePeopleDO::getPeopleCode)
                    .distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            String peopleCodeStr = StringUtils.join(peopleCodeList, CharacterConstant.SEMICOLON_EN);
            String ztePeopleName = StringUtils.join(ztePeople, CharacterConstant.PAUSE_MARK);
            activityInfoVO.setZtePeopleId(peopleCodeStr);
            activityInfoVO.setZtePeopleName(ztePeopleName);
            activityInfoVO.setZteContactNum(activityRelationZtePeopleDOList.size());

            // 活动是否被关注
            List<ActivityRelationZtePeopleDO> activityFollowPeopleList = detailDataSource.fetchActivityZtePeople(activityInfoVO.getRowId()
                    , ActivityPeopleTypeEnum.FOLLOW.getCode());
            activityFollowPeopleList.forEach( activityRelationZtePeopleDO -> {
                if (StringUtils.equals(xEmpNo, activityRelationZtePeopleDO.getPeopleCode())) {
                    activityInfoVO.setActivityNotice(true);
                }
            });

        });
    }

    /**
     * 填充客户联系人信息
     * @param list
     * @param detailDataSource
     */
    private void fillCustomerPeopleInfo(List<ActivityInfoVO> list, StandardActivityDetailDataSource detailDataSource) {
        list.forEach(vo -> {
            List<ActivityRelationCustPeopleDO> custPeopleDOList = detailDataSource.fetchActivityMainCustPeople(vo.getRowId(), vo.getCustomerCode());
            if(CollectionUtils.isNotEmpty(custPeopleDOList)){
                List<String> contactNoList = custPeopleDOList.stream().map(ActivityRelationCustPeopleDO::getContactNo)
                        .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                String contactStr = StringUtils.join(contactNoList, CharacterConstant.SEMICOLON_EN);
                vo.setContactNoList(contactNoList);
                vo.setContactNo(contactStr);

                List<String> contactNameList = custPeopleDOList.stream().map(ActivityRelationCustPeopleDO::getContactName)
                        .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                String contactNameStr = StringUtils.join(contactNameList, CharacterConstant.PAUSE_MARK);
                vo.setContactNameList(contactNameList);
                vo.setContactName(contactNameStr);
                vo.setContactNum(contactNameList.size());
            }
        });
    }

    /**
     * 填充客户信息
     * @param list
     * @param detailDataSource
     */
    private void fillCustomerInfo(List<ActivityInfoVO> list, StandardActivityDetailDataSource detailDataSource){
        for (ActivityInfoVO activityInfoVo : list) {
            ActivityCustomerInfoDO activityCustomerInfoDO = detailDataSource.fetchActivityMainCustomerInfo(activityInfoVo.getRowId());
            if(null != activityCustomerInfoDO){
                activityInfoVo.setCustomerCode(activityCustomerInfoDO.getCustomerCode());
                activityInfoVo.setCustomerName(activityCustomerInfoDO.getCustomerName());
            }
        }
    }

    /**
     * 打包基础信息
     * @param detailDataSource
     * @return
     */
    List<ActivityInfoVO> packActivityInfo(StandardActivityDetailDataSource detailDataSource){
        List<ActivityInfoVO> activityInfoVoList = Lists.newArrayList();
        List<ActivityInfoDO> activityInfoList = detailDataSource.getActivityInfoList();
        if(CollectionUtils.isEmpty(activityInfoList)){
            return activityInfoVoList;
        }

        // 获取当前登录人信息
        String xEmpNo = HeadersProperties.getXEmpNo();
        List<String> applyPeopleNoList = activityInfoList.stream().map(ActivityInfoDO::getApplyPeopleNo).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> createByList = activityInfoList.stream().map(ActivityInfoDO::getCreatedBy).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        applyPeopleNoList.addAll(createByList);
        List<String> allPeopleList = applyPeopleNoList.stream().distinct().collect(Collectors.toList());
        Map<String, EmployeeBO> allPeopleNoMap = employeeAdapter.getEmpInfosByShortNo(allPeopleList);
        Map<String, String> extensionTypeMap = lookUpExtService.getLookUpMapByParentType(PARENT_LOOKUP_TYPE_EXTENSION_TYPE);
        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);

        Map<String, String> exhibitionInfoDOMap = getActivityNameMap(activityInfoList);

        for (ActivityInfoDO activityInfoDO : activityInfoList) {
            ActivityInfoVO activityInfoVo = new ActivityInfoVO();
            BeanUtils.copyProperties(activityInfoDO, activityInfoVo);

            // 设置展会名称
            setExhibitionName(exhibitionInfoDOMap, activityInfoDO, activityInfoVo);

            // 活动状态
            activityInfoVo.setActivityStatusName(statusMap.get(activityInfoDO.getActivityStatus()));
            // 申请人
            if(null != allPeopleNoMap){
                EmployeeBO employeeBO = allPeopleNoMap.get(activityInfoDO.getApplyPeopleNo());
                if(!Objects.isNull(employeeBO)){
                    activityInfoVo.setApplyPeopleName(employeeBO.getName() + activityInfoDO.getApplyPeopleNo());
                }
                employeeBO = allPeopleNoMap.get(activityInfoDO.getCreatedBy());
                if(!Objects.isNull(employeeBO)){
                    activityInfoVo.setCreatedByName(employeeBO.getName() + activityInfoDO.getCreatedBy());
                }
            }
            if(null != extensionTypeMap){
                activityInfoVo.setActivityTypeName(extensionTypeMap.get(activityInfoDO.getActivityType()));
            }
            activityInfoVoList.add(activityInfoVo);
        }
        return activityInfoVoList;
    }

    /**
     * 返回活动对应拓展类型的名称
     * @param activityInfoList
     * @return
     */
    private Map<String, String> getActivityNameMap(List<ActivityInfoDO> activityInfoList) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return result;
        }
        Map<String, ExhibitionInfoDO> exhibitionInfoDOMap = getExhibitionInfoDOMap(activityInfoList);
        Map<String, SamplePointInfoDO> samplePointInfoDOMap = getSamplePointInfoDOMap(activityInfoList);
        for (ActivityInfoDO activityInfoDO : activityInfoList) {
            if (ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), EXHIBITION, CONFERENCE)) {
                ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoDOMap.get(activityInfoDO.getOriginRowId());
                result.put(activityInfoDO.getRowId(),  exhibitionInfoDO == null ? null : exhibitionInfoDO.getExhibitionName());
            } else if (ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), SAMPLE_POINT)) {
                SamplePointInfoDO samplePointInfoDO = samplePointInfoDOMap.get(activityInfoDO.getOriginRowId());
                result.put(activityInfoDO.getRowId(), samplePointInfoDO == null ? null : samplePointInfoDO.getSamplePointName());
            }
        }
        return result;
    }

    private Map<String, ExhibitionInfoDO> getExhibitionInfoDOMap(List<ActivityInfoDO> activityInfoList) {
        List<String> exhibitionRowIds = getOriginRowIds(activityInfoList, EXHIBITION, CONFERENCE);
        Map<String, ExhibitionInfoDO> exhibitionInfoDOMap = exhibitionInfoRepository.queryExhibitionInfoByRowId(exhibitionRowIds);
        return exhibitionInfoDOMap;
    }

    private Map<String, SamplePointInfoDO> getSamplePointInfoDOMap(List<ActivityInfoDO> activityInfoList) {
        List<String> sampleRowIds = getOriginRowIds(activityInfoList, SAMPLE_POINT);
        Map<String, SamplePointInfoDO> samplePointInfoDOMap = samplePointInfoRepository.querySamplePointInfoByRowIds(sampleRowIds);
        return samplePointInfoDOMap;
    }



    private List<String> getOriginRowIds(List<ActivityInfoDO> activityInfoList, ActivityOriginTypeEnum... originTypeEnums) {
        List<String> originRowIds = new ArrayList<>();
        for (ActivityInfoDO activityInfoDO : activityInfoList) {
            if(ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), originTypeEnums)){
                originRowIds.add(activityInfoDO.getOriginRowId());
            }
        }
        return originRowIds;
    }

    private void setExhibitionName(Map<String, String> activityNameMap, ActivityInfoDO activityInfoDO, ActivityInfoVO activityInfoVo) {
        String activityName = activityNameMap.get(activityInfoDO.getRowId());
        if (null != activityName) {
            activityInfoVo.setExhibitionName(activityName);
        }
    }

    /**
     * 关注活动
     * @param activityRowId 活动id
     * @return
     */
    @Override
    public int followActivityTask(String activityRowId) {
        // 获取当前登录人信息
        String xEmpNo = HeadersProperties.getXEmpNo();
        if(StringUtils.isBlank(activityRowId) || StringUtils.isBlank(xEmpNo)){
            logger.info("活动ID为空或登录人工号为空");
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "activity.login.is.null.activity.follow.or.cancel.fail");
        }

        ActivityRelationZtePeopleQuery query = new ActivityRelationZtePeopleQuery();
        query.setActivityRowId(activityRowId);
        query.setPeopleCode(xEmpNo);
        query.setPeopleType(ActivityPeopleTypeEnum.FOLLOW.getCode());
        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList = activityRelationZtePeopleRepository.queryInfoList(query);
        int res;
        if(CollectionUtils.isNotEmpty(activityRelationZtePeopleDOList)){
            // 取消关注
            ActivityRelationZtePeopleDO activityRelationZtePeopleDO = activityRelationZtePeopleDOList.get(0);
            ActivityRelationZtePeopleDO cancelRelationDo = new ActivityRelationZtePeopleDO();
            cancelRelationDo.setRowId(activityRelationZtePeopleDO.getRowId());
            cancelRelationDo.setEnabledFlag(BooleanEnum.N.getCode());
            res = activityRelationZtePeopleRepository.updateByPrimaryKeySelective(cancelRelationDo);
        } else {
            // 查询关注人的信息--新增关注
            EmployeeBO employeeByShortNo = employeeAdapter.getEmployeeByShortNo(xEmpNo);
            ActivityRelationZtePeopleDO activityRelationZtePeopleDO = ActivityInfoListConvert.toActivityRelationZtePeopleDO(employeeByShortNo);
            activityRelationZtePeopleDO.setActivityRowId(activityRowId);
            res = activityRelationZtePeopleRepository.insertSelective(Lists.newArrayList(activityRelationZtePeopleDO));
        }
        iSearchService.asyncSendActivityData2ISearch(activityRowId);
        return res;
    }


    /**
     * 活动流程图
     * @param acRowId
     * @return
     */
    @Override
    public List<ActivityFlowInfoVO> activityFlow(String acRowId) {
        List<ActivityFlowInfoVO> flowInfoVOS = Lists.newArrayList();
        //活动信息
        ActivityInfoDO infoDO = activityInfoRepository.selectByPrimaryKey(acRowId);
        //活动关联人员
        Map<String, String> peopleMap = ztePeopleService.queryZtePeopleInfoByActivityRowId(acRowId);
        //查询活动状态变更记录
        List<ActivityStatusLifecycleDO> activityStatusLifecycleDOList = statusLifecycleRepository.queryStatusForActivity(acRowId);
        if (ObjectUtils.isEmpty(infoDO) || MapUtils.isEmpty(peopleMap) || CollectionUtils.isEmpty(activityStatusLifecycleDOList)) {
            return flowInfoVOS;
        }
        String activityStatus = infoDO.getActivityStatus();
        Map<String, List<ActivityStatusLifecycleDO>> statusToMap = activityStatusLifecycleDOList.stream().collect(Collectors.groupingBy(ActivityStatusLifecycleDO::getStatusTo));
        // 排序生命周期
        sortLifecycle(statusToMap);
        //添加活动启动节点
        flowInfoVOS.add(nodeService.getLaunchNode(statusToMap, infoDO, peopleMap));
        //资源申请节点
        nodeService.getRequestNode(flowInfoVOS, statusToMap, peopleMap, infoDO);
        //活动审批节点：活动状态大于等于19（合规审批中）并且 需要审批
        nodeService.getApprovalNode(flowInfoVOS, statusToMap, infoDO);
        //活动执行
        nodeService.getExecuteNode(flowInfoVOS, activityStatus, statusToMap, peopleMap);
        //活动撤销
        nodeService.getCancelNode(flowInfoVOS, statusToMap, peopleMap);
        //活动完成
        nodeService.getCompleteNode(flowInfoVOS, statusToMap, acRowId);
        //活动作废
        nodeService.getInvalidateNode(flowInfoVOS, statusToMap, peopleMap);
        // 将活动与商机绑定解绑记录插入到对应节点中
        this.insertAcOptLogToFlowList(acRowId, flowInfoVOS);
        return flowInfoVOS;
    }

    void insertAcOptLogToFlowList(String activityRowId, List<ActivityFlowInfoVO> flowInfoList) {
        Map<String, List<ActivityOpportunityRecordVO>> activityStatusRecordMap = activityOpportunityService.getActivityStatusRecordMap(activityRowId);
        if (MapUtils.isEmpty(activityStatusRecordMap)) {
            return;
        }

        Map<String, ActivityFlowInfoVO> activityFlowNodeMap =
                flowInfoList.stream().collect(Collectors.toMap(ActivityFlowInfoVO::getNodeCode, i -> i, (u, v) -> u));
        activityStatusRecordMap.forEach((flowNode, records) -> {
            records = records.stream().sorted(Comparator.comparing(ActivityOpportunityRecordVO::getOperationTime).reversed()).collect(Collectors.toList());
            if (activityFlowNodeMap.containsKey(flowNode)) {
                ActivityFlowInfoVO activityFlowInfoVO = activityFlowNodeMap.get(flowNode);
                activityFlowInfoVO.setActivityBindOpportunityRecords(records);
                return;
            }

            ActivityFlowNodeEnum flowNodeEnum = ActivityFlowNodeEnum.getByCode(flowNode);
            if (Objects.isNull(flowNodeEnum)) {
                return;
            }
            ActivityFlowInfoVO flowInfoVO = ActivityFlowConvert.enumToVo(flowNodeEnum, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
            flowInfoVO.setActivityBindOpportunityRecords(records);
            flowInfoList.add(flowInfoVO);
        });
    }

    /**
     * 排序各生命周期
     * @param statusToMap   状态信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/5 15:29
     */
    private void sortLifecycle(Map<String, List<ActivityStatusLifecycleDO>> statusToMap) {
        for (String key : statusToMap.keySet()) {
            List<ActivityStatusLifecycleDO> list = statusToMap.get(key);
            list.sort(Comparator.comparing(ActivityStatusLifecycleDO::getCreationDate).reversed());
            statusToMap.put(key, list);
        }
    }

    @Override
    public PageRows<ActivityInfoFolVO> queryPage4Fol(ActivityInfoPageQuery queryParam) {
        logger.info("queryList4Fol param:{}", JSON.toJSONString(queryParam));
        queryParam.setStartDate(DateUtil.startOfStartDay(queryParam.getStartDate()));
        queryParam.setEndDate(DateUtil.endOfEndDay(queryParam.getEndDate()));

        PageRows<ActivityInfoFolVO> pageRows = activityInfoRepository.pageQuery4Fol(queryParam.getPageNo(), queryParam.getPageSize(), queryParam);
        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);
        List<ActivityInfoFolVO> activityInfoFolList = pageRows.getRows().stream().peek(item ->
                item.setActivityStatusName(statusMap.get(item.getActivityStatus()))).collect(Collectors.toList());

        return PageRowsUtil.buildPageRow(pageRows.getCurrent(), pageRows.getPageSize(), pageRows.getTotal(), activityInfoFolList);
    }

    @Override
    public List<ActivityInfoFolVO> queryList4Fol(ActivityInfoListQuery queryParam) {
        logger.info("queryList4Fol param:{}", JSON.toJSONString(queryParam));
        if (CollectionUtils.isEmpty(queryParam.getActivityRequestNoList())) {
            return new ArrayList<>();
        }
        // 1. 查询指定活动编号列表的已完成、已评价的活动列表
        List<ActivityInfoFolVO> activityInfoFolList = activityInfoRepository.queryList4Fol(queryParam);
        if (CollectionUtils.isEmpty(activityInfoFolList)) {
            return new ArrayList<>();
        }
        Set<String> activityRowIdSet = new HashSet<>();
        List<ActivityInfoFolVO> filterFolList = activityInfoFolList.stream().peek(item -> activityRowIdSet.add(item.getRowId())).collect(Collectors.toList());
        Map<String, List<ActivityRelationZtePeopleDO>> activityRowIdsMap =
                activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(activityRowIdSet);
        //  2. 当operater为申请人 或 活动参与人时 hit字段视为命中
        for (ActivityInfoFolVO activityInfoFolVO : filterFolList) {
            List<ActivityRelationZtePeopleDO> ztePeoples = Optional.ofNullable(activityRowIdsMap.get(activityInfoFolVO.getRowId()))
                .orElse(new ArrayList<>()).stream().filter(item -> {
                    if (!StringUtils.equals(item.getPeopleCode(), queryParam.getOperater())) {
                        return false;
                    }
                    // 当operater为申请人 或 活动参与人时 视为命中
                    return ActivityPeopleTypeEnum.in(item.getPeopleType(), APPLICANT, LECTURER, PARTICIPANTS, ORGANIZER, SITE_PEOPLE);
                }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ztePeoples)) {
                continue;
            }
            activityInfoFolVO.setHit((byte) ONE);
        }
        return filterFolList;
    }

}
