package com.zte.mcrm.activity.common.config;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 文档云配置
 *
 * <AUTHOR>
 * @date 2023/5/26 上午9:13
 */
@Component
@Getter
public class CloudDiskConfig {

    /**
     * 原始服务名称
     */
    @Value("${cloudDiskSDK.xOriginServiceName:}")
    private String xOriginServiceName;

    /**
     * AccessKey
     */
    @Value("${cloudDiskSDK.xOrgId:}")
    private String xOrgId;

    /**
     * SecKey
     */
    @Value("${cloudDiskSDK.xSecretKey:}")
    private String xSecretKey;

    /**
     * url
     */
    @Value("${cloudDiskSDK.host:}")
    private String host;

    /**
     * 超时时间,240000
     */
    @Value("${cloudDiskSDK.socketTimeOut:240000}")
    private Integer socketTimeOut;

    /**
     * 静态资源uri
     */
    @Value("${cloud.disk.static.download.uri:/static/objects/download/}")
    private String cloudDiskStaticDownloadUri;

    /**
     * 获取对应地址
     * @param cloudKeyToken
     * @return
     */
    public String fetchUrl(String cloudKeyToken) {
        return this.host + this.cloudDiskStaticDownloadUri + this.xOriginServiceName + CharacterConstant.SLASH + cloudKeyToken + CharacterConstant.TYPE_VIEW;
    }
}
