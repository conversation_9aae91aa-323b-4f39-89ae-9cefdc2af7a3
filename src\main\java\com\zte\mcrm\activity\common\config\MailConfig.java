package com.zte.mcrm.activity.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
public class MailConfig {

    /**
     * AI生成日程谈参摘要失败发送邮件接收人
     */
    @Value("${mail.config.talk.abstract.failed.mail.to:}")
    private String talkAbstractFailedMailTo;

}
