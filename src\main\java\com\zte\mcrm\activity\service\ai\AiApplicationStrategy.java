package com.zte.mcrm.activity.service.ai;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationVO;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/22 17:29
 */
public interface AiApplicationStrategy {
    boolean support(String businessType);

    String processBusiness(AiApplicationVO req);
}
