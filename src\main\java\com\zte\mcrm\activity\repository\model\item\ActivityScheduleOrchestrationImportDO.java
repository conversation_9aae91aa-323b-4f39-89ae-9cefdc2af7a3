package com.zte.mcrm.activity.repository.model.item;

import java.util.Date;

/**
 * table:activity_schedule_orchestration_import -- 
 */
public class ActivityScheduleOrchestrationImportDO {
    /** 记录主键 */
    private String rowId;

    /** 展会/大会ID -- Exhibition_info#row_id */
    private String exhibitionRowId;

    /** 文件名称 */
    private String uploadFileName;

    /** 上传文件对应云文档token */
    private String uploadFileToken;

    /** 解析失败对应错误信息描述对应云文档token */
    private String failFileToken;

    /** 资源编排导入状态。见枚举ScheduleOrchestrationImportStatusEnum */
    private String scheduleOrchestrationImportStatus;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最后修改人 */
    private String lastUpdatedBy;

    /** 记录最后修改时间 */
    private Date lastUpdateDate;

    /** 行有效标识。见枚举BooleanEnum。Y-有效，N无效 */
    private String enabledFlag;

    /** 备注 */
    private String remark;

    /** 文件类型 */
    private String fileType;

    /** 是否同步日程见BooleanEnum。Y-同步，N-不同步 */
    private String synSchedule;

    /** 是否发送通知BooleanEnum。Y-发，N-不发 */
    private String synNotice;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getExhibitionRowId() {
        return exhibitionRowId;
    }

    public void setExhibitionRowId(String exhibitionRowId) {
        this.exhibitionRowId = exhibitionRowId == null ? null : exhibitionRowId.trim();
    }

    public String getUploadFileName() {
        return uploadFileName;
    }

    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName == null ? null : uploadFileName.trim();
    }

    public String getUploadFileToken() {
        return uploadFileToken;
    }

    public void setUploadFileToken(String uploadFileToken) {
        this.uploadFileToken = uploadFileToken == null ? null : uploadFileToken.trim();
    }

    public String getFailFileToken() {
        return failFileToken;
    }

    public void setFailFileToken(String failFileToken) {
        this.failFileToken = failFileToken == null ? null : failFileToken.trim();
    }

    public String getScheduleOrchestrationImportStatus() {
        return scheduleOrchestrationImportStatus;
    }

    public void setScheduleOrchestrationImportStatus(String scheduleOrchestrationImportStatus) {
        this.scheduleOrchestrationImportStatus = scheduleOrchestrationImportStatus == null ? null : scheduleOrchestrationImportStatus.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType == null ? null : fileType.trim();
    }

    public String getSynSchedule() {
        return synSchedule;
    }

    public void setSynSchedule(String synSchedule) {
        this.synSchedule = synSchedule == null ? null : synSchedule.trim();
    }

    public String getSynNotice() {
        return synNotice;
    }

    public void setSynNotice(String synNotice) {
        this.synNotice = synNotice == null ? null : synNotice.trim();
    }
}