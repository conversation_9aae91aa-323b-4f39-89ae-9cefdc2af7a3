package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.expansion.access.vo.ApprovalNodeCreatedInfoVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * table:activity_approval_process_node --
 */
@Getter
@Setter
@ToString
public class ActivityApprovalProcessNodeAddParam {

    /**
     * 拓展活动id
     */
    @NotNull
    private String activityRowId;


    /**
     * 审批单号-对应审批中心 taskId
     */
    @NotNull
    private String approvalFlowNo;

    /**
     * 审批人
     */
    @NotNull
    private String approveBy;


    /**
     * 如果是转交过来的，则该有值，转交来源id（本表的row_id）
     */
    private String transFrom;

    /**
     * 审批意见备注
     */
    private String remark;

    /** 记录创建人 */
    @NotNull
    private String createdBy;

    /** 记录最近修改人 */
    @NotNull
    private String lastUpdatedBy;


    public void buildOfAdd(ApprovalNodeCreatedInfoVO param) {
        this.activityRowId = param.getBusinessId();
        // taskId
        this.approvalFlowNo = param.getApproverMap().values().iterator().next();
        this.approveBy = param.getApproverMap().keySet().iterator().next();
        this.createdBy = approveBy;
        this.lastUpdatedBy = approveBy;
    }


}