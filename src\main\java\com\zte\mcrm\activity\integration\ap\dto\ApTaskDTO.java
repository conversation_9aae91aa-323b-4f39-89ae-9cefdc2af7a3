package com.zte.mcrm.activity.integration.ap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/19 15:31
 */
@Setter
@Getter
@ToString
public class ApTaskDTO {

    /**查询到的AP总条数， 可能大于data_list的条数*/
    private Integer total;

    /**查询到的AP， 每次返回的有条数限制*/
    @JSONField(name = "data_list")
    private List<ApTaskVO> dataList;
}
