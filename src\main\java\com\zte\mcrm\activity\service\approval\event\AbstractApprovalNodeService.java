package com.zte.mcrm.activity.service.approval.event;

import com.google.common.collect.Sets;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingNoticeStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ProcessStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.AssertUtil;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.infocenter.InfoCenterService;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.service.approval.event.helper.ApprovalHelper;
import com.zte.mcrm.activity.service.approval.param.*;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityApprovalProcessNodeConverter;
import com.zte.mcrm.activity.service.converter.activity.ActivityStatusLifecycleConverter;
import com.zte.mcrm.activity.service.converter.notice.ActivityPendingNoticeConverter;
import com.zte.mcrm.activity.service.converter.people.ActivityRelationZtePeopleConverter;
import com.zte.mcrm.activity.service.event.ActivityEventService;
import com.zte.mcrm.activity.service.event.convert.ActivityEventConvert;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.service.model.activity.ActivityBO;
import com.zte.mcrm.activity.service.model.people.ActivityRelationZtePeople;
import com.zte.mcrm.activity.service.notice.ActivityPendingNoticeService;
import com.zte.mcrm.activity.service.notice.param.ActivityPendingNoticeBO;
import com.zte.mcrm.activity.service.summary.ActivitySummaryService;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.service.converter.ActivityInfoConverter.buildApprovalCompletePendNotice;

/**
 * @ClassName AbstractApprovalNodeService
 * @description: 审批流基础处理方法
 * @author: 李龙10317843
 * @create: 2023-05-19 16:34
 * @Version 1.0
 **/

@Log4j2
@Component
public abstract class AbstractApprovalNodeService {
    @Autowired
    protected ActivityApprovalInfoRepository approvalInfoRepository;
    @Autowired
    protected ActivityApprovalProcessRepository approvalProcessRepository;
    @Autowired
    protected ActivityApprovalProcessNodeRepository approvalProcessNodeRepository;
    @Autowired
    protected ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ActivityRelationZtePeopleRepository relationZtePeopleRepository;
    @Autowired
    private ActivityPendingNoticeRepository pendingNoticeRepository;
    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityPendingNoticeService noticeService;
    @Autowired
    private ActivityEventService eventService;
    @Autowired
    private ActivitySummaryService summaryService;
    @Autowired
    private InfoCenterService infoCenterService;
    @Autowired
    private ActivityUrlConfig activityUrlConfig;
    @Autowired
    private ActivityISearchService iSearchService;

    /**
     * 获取本节点活动状态
     *
     * @return
     */
    abstract String getCurrentActivityStatus();

    /**
     * 获取审批节点类型
     *
     * @return {@link String}
     */
    abstract String getNodeType();

    /**
     * 获取审批完成状态
     *
     * @param approvalResult
     * @return
     */
    abstract String getApprovalCompleteStatus(String approvalResult);


    /**
     * 节点创建-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void nodeCreateOfKafka(BizRequest<ActivityApprovalProcessAddParam> bizRequest) {
        ApprovalHelper approvalHelper = new ApprovalHelper();
        ActivityApprovalProcessAddParam processAddParam = bizRequest.getParam();
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(processAddParam.getActivityRowId());
        ValidationUtils.validateObject(processAddParam);
        ActivityApprovalProcessNodeAddParam processNodeAddParam = processAddParam.getProcessNodeAddParam();
        ValidationUtils.validateObject(processNodeAddParam);
        if (activityInfoDO == null || !ActivityStatusEnum.in(activityInfoDO.getActivityStatus(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL, ActivityStatusEnum.BUSINESS_APPROVAL)) {
            log.error("活动不存在，或活动状态不在审批中，不可创建审批节点，活动信息为：{}", activityInfoDO);
            return;
        }
        EmployeeInfoDTO approve = userCenterService.getUserInfo(processNodeAddParam.getApproveBy());
        AssertUtil.assertNotNull(approve);
        approvalHelper.setApprove(approve);
        // 添加节点数据
        List<ActivityApprovalProcessDO> approvalProcessDOList = approvalProcessRepository
                .queryByActivityRowIdAndProcessType(processAddParam.getActivityRowId(), processAddParam.getProcessType());
        approvalProcessDOList = approvalProcessDOList.stream().filter(e -> ProcessStatusEnum.ACTIVE.isMe(e.getProcessStatus()))
                .collect(Collectors.toList());
        ActivityApprovalProcessDO approvalProcessDO = CollectionUtils.isEmpty(approvalProcessDOList) ?
                ActivityApprovalProcessConverter.buildOfAdd(processAddParam) : approvalProcessDOList.get(ZERO);
        // 添加节点详情
        ActivityApprovalProcessNodeDO approvalProcessNodeDO = ActivityApprovalProcessNodeConverter.buildOfAdd(approvalProcessDO,
                processNodeAddParam, approve);
        // 更新主表状态数据
        ActivityInfoDO activityInfoUpdate = ActivityInfoConverter.buildOfUpdateStatus(approvalProcessNodeDO.getActivityRowId(),
                getCurrentActivityStatus(), approvalProcessNodeDO.getApproveBy());
        // 插入生命周期
        ActivityStatusLifecycleDO activityStatusLifecycleDO
                = ActivityStatusLifecycleConverter.buildOfAdd(activityInfoDO, getCurrentActivityStatus(),
                approvalProcessNodeDO.getApproveBy());
        approvalHelper.setActivityInfo(activityInfoDO);
        approvalHelper.setApprovalProcessAdd(approvalProcessDO);
        approvalHelper.setApprovalProcessNodeAdd(approvalProcessNodeDO);
        activityInfoRepository.updateByPrimaryKeySelective(activityInfoUpdate);
        approvalProcessRepository.insertSelective(approvalProcessDO);
        approvalProcessNodeRepository.insertSelective(approvalProcessNodeDO);
        lifecycleRepository.insertSelective(Collections.singletonList(activityStatusLifecycleDO));
        //新增待办
        addPendingNotice(approvalHelper);

        this.sync2Es(activityInfoDO.getRowId());
    }


    /**
     * 审批节点完成-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void nodeCompleteOfKafka(BizRequest<ActivityApprovalProcessNodeCompleteParam> bizRequest) {
        // 校验数据
        ActivityApprovalProcessNodeCompleteParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(param.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        // 获取节点详情数据
        ActivityApprovalProcessNodeDO processNodeDO = approvalProcessNodeRepository
                .queryByActIdAndApprovalFlowNo(param.getActivityRowId(), param.getApprovalFlowNo());
        AssertUtil.assertNotNull(processNodeDO);
        // 获取审批节点数据
        ActivityApprovalProcessDO processDO = approvalProcessRepository.selectByPrimaryKey(processNodeDO.getApprovalProcessRowId());
        AssertUtil.assertNotNull(processDO);
        // 更新节点详情数据
        EmployeeInfoDTO approve = userCenterService.getUserInfo(param.getApproveBy());
        AssertUtil.assertNotNull(approve);
        ActivityApprovalProcessNodeDO processNodeUpdate = ActivityApprovalProcessNodeConverter.buildOfComplete(processDO, param);
        processNodeUpdate.setRowId(processNodeDO.getRowId());
        processNodeUpdate.setApproverName(approve.getEmpName());
        // 更新节点状态
        ActivityApprovalProcessDO approvalProcessUpdate = ActivityApprovalProcessConverter
                .buildOfComplete(processDO.getRowId(), processNodeUpdate.getLastUpdatedBy());
        // 更新主表状态数据
        String newStatus = getApprovalCompleteStatus(param.getApproveResult());
        // 填充活动状态
        ActivityInfoDO activityInfoUpdate = ActivityInfoConverter.buildOfUpdateStatus(processDO.getActivityRowId(),
                newStatus, processNodeUpdate.getLastUpdatedBy());

        approvalProcessRepository.updateByPrimaryKeySelective(approvalProcessUpdate);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(processNodeUpdate);
        activityInfoRepository.updateByPrimaryKeySelective(activityInfoUpdate);
        pendingNoticeRepository.updateByActivityRowIdAndBizType(processDO.getActivityRowId(),
                getNodeType(), PendingNoticeStatusEnum.FINISH.getStatus());

        this.sync2Es(activityInfoDO.getRowId());

    }

    /**
     * 审批节点任务转交-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskReassignOfKafka(BizRequest<ActivityApprovalTaskReassignParam> bizRequest) throws Exception {
        ApprovalHelper approvalHelper = new ApprovalHelper();
        // 校验数据
        ActivityApprovalTaskReassignParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);
        // 获取审批人数据
        EmployeeInfoDTO approve = userCenterService.getUserInfo(param.getApproveBy());
        // 获取上一节点数据
        ActivityApprovalProcessNodeDO lastNode = approvalProcessNodeRepository.queryByActIdAndApprovalFlowNo(param.getActivityRowId(), param.getApprovalFlowNo());
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(lastNode.getActivityRowId());
        ActivityApprovalProcessDO approvalProcessDO = approvalProcessRepository.selectByPrimaryKey(lastNode.getApprovalProcessRowId());
        // 转交新增节点详情数据
        ActivityApprovalProcessNodeDO approvalProcessNodeDO = ActivityApprovalProcessNodeConverter.buildAddOfReassign(param, lastNode, approve);
        ActivityApprovalProcessNodeDO lastNodeUpdate = ActivityApprovalProcessNodeConverter.buildUpdateOfReassign(param, lastNode);
        // 查询审批人信息
        approvalHelper.setApprove(approve);
        approvalHelper.setActivityInfo(activityInfoDO);
        approvalHelper.setApprovalProcessAdd(approvalProcessDO);
        approvalHelper.setApprovalProcessNodeAdd(approvalProcessNodeDO);
        approvalProcessNodeRepository.updateByPrimaryKeySelective(lastNodeUpdate);
        pendingNoticeRepository.updateByActivityRowIdAndBizType(lastNode.getActivityRowId(), getNodeType(), PendingNoticeStatusEnum.FINISH.getStatus());
        approvalProcessNodeRepository.insertSelective(approvalProcessNodeDO);
        addPendingNotice(approvalHelper);
    }

    /**
     * 新增待办信息
     *
     * @param approvalHelper
     */
    protected void addPendingNotice(ApprovalHelper approvalHelper) {
        // 新增待办
        ActivityPendingNoticeDO activityPendingNoticeAdd = ActivityPendingNoticeConverter.buildOfAddApproval(this.getNodeType(), approvalHelper);
        // 新增参与人
        ActivityRelationZtePeople ztePeople = ActivityRelationZtePeopleConverter.buildOfAdd(approvalHelper);
        pendingNoticeRepository.insertSelective(Collections.singletonList(activityPendingNoticeAdd));
        activityRelationZtePeopleRepository.insertSelective(Collections.singletonList(ActivityRelationZtePeopleConverter.INSTANCE.toEntity(ztePeople)));
    }

    /**
     * 审批全流程完成-kafka回调
     *
     * @param bizRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeOfKafka(BizRequest<ActivityApprovalProcessCompleteParam> bizRequest) {
        ActivityApprovalProcessCompleteParam param = bizRequest.getParam();
        ValidationUtils.validateObject(param);

        // 获取活动主表信息
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(param.getActivityRowId());
        AssertUtil.assertNotNull(activityInfoDO);
        // 更新主流程状态
        List<ActivityApprovalInfoDO> approvalInfoDOList =
                approvalInfoRepository.queryAllByActivityRowId(param.getActivityRowId());
        for (ActivityApprovalInfoDO approvalInfoDO : approvalInfoDOList) {
            ActivityApprovalInfoDO approvalInfoUpdate = new ActivityApprovalInfoDO();
            approvalInfoUpdate.setRowId(approvalInfoDO.getRowId());
            approvalInfoUpdate.setInstanceStatus(BooleanEnum.Y.getCode());
            approvalInfoRepository.updateByPrimaryKeySelective(approvalInfoUpdate);
        }
        // 填充活动生命周期
        String newStatus = getApprovalCompleteStatus(param.getApproveResult());
        ActivityStatusLifecycleDO activityStatusLifecycleAdd = ActivityStatusLifecycleConverter.buildOfAdd(activityInfoDO,
                newStatus, param.getApproveBy());
        lifecycleRepository.insertSelective(Collections.singletonList(activityStatusLifecycleAdd));
        if (BooleanEnum.N.getCode().equals(param.getApproveResult())) {
            this.sync2Es(activityInfoDO.getRowId());
            // 如果审批不通过，不发送反馈纪要 待办和邮件
            return;
        }
        try {
			// 生成活动待反馈待办并发送邮件
			ActivityBO activityBO = new ActivityBO();
			List<ActivityRelationZtePeopleDO> listZtePeopleInfo = activityRelationZtePeopleRepository
			        .queryByActivityRowIdAndPeopleType(activityInfoDO.getRowId(), ActivityPeopleTypeEnum.getPendingNoticePerson(activityInfoDO.getActivityType()));
			AssertUtil.assertNotEmpty(listZtePeopleInfo);
			activityBO.setActivityInfoDO(activityInfoDO);
			activityBO.setListZtePeopleInfo(listZtePeopleInfo);
			Set<String> setStr = Sets.newHashSet();
			setStr.add(activityInfoDO.getCreatedBy());
			setStr.add(activityInfoDO.getApplyPeopleNo());
			List<EmployeeInfoDTO> submitUser = userCenterService.getUserInfoList(new ArrayList<>(setStr));
			BizRequest<ActivityPendingNoticeBO> pendingBizRequest = buildApprovalCompletePendNotice(activityBO, submitUser);
			noticeService.insertPendingNoticeAndCreateEvent(pendingBizRequest);
		} catch (Exception e) {
			log.error("发送待办失败 activityRowId："+param.getActivityRowId(),e);
		}

        // 创建事件
        createEvent(activityInfoDO);
        this.sync2Es(activityInfoDO.getRowId());
    }

    /**
     * 创建事件
     *
     * @param activityInfoDO    活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/6/29 14:49
     */
    private void createEvent(ActivityInfoDO activityInfoDO) {
        // 创建 关闭会议纪要事件
        createCloseSummaryEvent(activityInfoDO);
    }

    /**
     * 创建 关闭会议纪要事件
     *
     * @param activityInfoDO    活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/6/29 15:45
     */
    private void createCloseSummaryEvent(ActivityInfoDO activityInfoDO) {
        // 如果活动已结束，则事件执行时间以审批通过时间进行计算，否则以活动结束时间进行计算
        Date endTime = activityInfoDO.getEndTime().before(new Date()) ? new Date() : activityInfoDO.getEndTime();
        ActivityRelationEventVO eventVO = ActivityEventConvert.buildEventVoByActivityDo(activityInfoDO, endTime, StringUtils.EMPTY);
        BizRequest<ActivityRelationEventVO> bizRequest = BizRequestUtil.createWithCurrentUser(eventVO);
        summaryService.createCloseSummaryEvent(bizRequest);
    }

    /**
     * 事务提交后同步ES
     *
     * @param activityId
     * <AUTHOR>
     * @date 2024/10/28 下午3:31
     */
    protected void sync2Es(String activityId) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    async2Es(activityId);
                }
            });
        } else {
            async2Es(activityId);
        }
    }

    /**
     * 异步同步ES
     *
     * @param activityId
     * <AUTHOR>
     * @date 2024/10/28 下午3:39
     */
    private void async2Es(String activityId) {
        ThreadManager.submitToAsync(() -> iSearchService.asyncSendActivityData2ISearch(activityId));
    }

}
