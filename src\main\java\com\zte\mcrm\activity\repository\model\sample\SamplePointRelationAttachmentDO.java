package com.zte.mcrm.activity.repository.model.sample;

import java.util.Date;

/**
 * table:sample_point_relation_attachment -- 
 */
public class SamplePointRelationAttachmentDO {
    /** 主键 */
    private String rowId;

    /** 样板点ID */
    private String samplePointRowId;

    /** 附件所属场景。SamplePointAttachmentSceneTypeEnum */
    private String samplePointAttachmentSceneType;

    /** 云文档这是对应key */
    private String fileToken;

    /** 文件名 */
    private String fileName;

    /** 附件大小byte */
    private Long fileSize;

    /** 备注 */
    private String remark;

    /** 显示顺序 */
    private Integer showOrder;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举见BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getSamplePointRowId() {
        return samplePointRowId;
    }

    public void setSamplePointRowId(String samplePointRowId) {
        this.samplePointRowId = samplePointRowId == null ? null : samplePointRowId.trim();
    }

    public String getSamplePointAttachmentSceneType() {
        return samplePointAttachmentSceneType;
    }

    public void setSamplePointAttachmentSceneType(String samplePointAttachmentSceneType) {
        this.samplePointAttachmentSceneType = samplePointAttachmentSceneType == null ? null : samplePointAttachmentSceneType.trim();
    }

    public String getFileToken() {
        return fileToken;
    }

    public void setFileToken(String fileToken) {
        this.fileToken = fileToken == null ? null : fileToken.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}