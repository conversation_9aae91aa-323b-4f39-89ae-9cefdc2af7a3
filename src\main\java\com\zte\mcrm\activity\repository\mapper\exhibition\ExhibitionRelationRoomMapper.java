package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationRoomDO;

public interface ExhibitionRelationRoomMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationRoomDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationRoomDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationRoomDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationRoomDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationRoomDO record);
}