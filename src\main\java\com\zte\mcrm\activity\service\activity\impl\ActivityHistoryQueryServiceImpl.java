package com.zte.mcrm.activity.service.activity.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.common.enums.item.ZtePeopleOrderEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupDto;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupSearchDTO;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationProjectRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationSolutionRepository;
import com.zte.mcrm.activity.service.activity.ActivityHistoryQueryService;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.web.controller.activity.param.CustomerLatestActivityParam;
import com.zte.mcrm.activity.web.controller.activity.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.SEMICOLON_CN;
import static com.zte.mcrm.activity.common.constant.LookupConstant.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.PARTICIPANTS;
import static com.zte.mcrm.activity.service.converter.ActivityInfoConverter.assembleZtePeopleInfo;

/**
 * 历史活动信息查询服务
 *
 * <AUTHOR>
 */
@Service
public class ActivityHistoryQueryServiceImpl implements ActivityHistoryQueryService {

    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;
    @Autowired
    private ActivityCommunicationDirectionRepository communicationDirectionRepository;
    @Autowired
    private ActivityRelationProjectRepository projectRepository;
    @Autowired
    private ActivityRelationZtePeopleRepository ztePeopleRepository;
    @Autowired
    private ActivityRelationSolutionRepository solutionRepository;
    @Autowired
    private ActivityRelationAttachmentRepository relationAttachmentRepository;
    @Autowired
    private LookUpExtService lookUpExtService;

    @Override
    public BizResult<ActivityInfoVO> custLatestActivity(BizRequest<CustomerLatestActivityParam> request) {
        CustomerLatestActivityParam param = request.getParam();
        ActivityInfoQuery query = new ActivityInfoQuery();
        query.setApplyPeopleNo(request.getEmpNo());
        query.setActivityTypeList(param.getActivityTypeList());
        query.setCustomerCodeList(param.getCustomerCodeList());
        // 查询活动基本信息
        ActivityInfoDO info = activityInfoRepository.getLatestActivityInfo(query);
        ActivityInfoVO vo = null;
        // 活动信息补充
        if (info != null) {
            vo = new ActivityInfoVO();
            BeanUtils.copyProperties(info, vo);
            // 获取主要客户信息
            this.getCustPeopleList(vo);
            // 获取交流方向信息和关联活动项目信息
            this.getBaseInfo(vo);
            // 获取我司参与人、讲师、方案信息
            this.getActivityZtePeopleListInfo(vo);
        }
        return BizResult.buildSuccessRes(vo);
    }

    public void getCustPeopleList(ActivityInfoVO activityInfo){
        List<ActivityCustomerInfoDO> custList = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIds(Collections.singleton(activityInfo.getRowId())).get(activityInfo.getRowId());
        // 从业务数据来说，肯定都是不为空的。防止CCA
        ActivityCustomerInfoDO mainCust = CollectionUtils.isEmpty(custList) ? new ActivityCustomerInfoDO()
                : custList.stream().filter(e -> StringUtils.equals(activityInfo.getCustomerCode(), e.getCustomerCode())).findFirst().orElse(new ActivityCustomerInfoDO());
        List<ActivityRelationCustPeopleDO> custPeopleList = activityRelationCustPeopleRepository.getCustPeopleListByActivityRowIds(Collections.singleton(activityInfo.getRowId())).get(activityInfo.getRowId());
        if (CollectionUtils.isNotEmpty(custPeopleList)) {
            activityInfo.setCustPeopleList(custPeopleList.stream().filter(p -> StringUtils.equals(p.getCustomerCode(), activityInfo.getCustomerCode())).map(p -> {
                CustPeopleInfoVO custPeople = new CustPeopleInfoVO();

                custPeople.setMainCust(p.getMainCust());
                custPeople.setCustomerName(mainCust.getCustomerName());
                custPeople.setCustomerCode(p.getCustomerCode());
                custPeople.setContactNo(p.getContactNo());
                custPeople.setContactName(p.getContactName());
                custPeople.setPositionName(p.getPositionName());
                custPeople.setContactLevel(p.getContactLevel());

                return custPeople;
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 获取交流方向信息和关联活动项目信息
     * @param activityInfo
     */
    public void getBaseInfo(ActivityInfoVO activityInfo) {
        List<ActivityCommunicationDirectionDO> listCommunication = communicationDirectionRepository.queryAllByActivityRowId(activityInfo.getRowId());
        List<ActivityRelationProjectDO> listProject = projectRepository.queryAllProjectForActivity(activityInfo.getRowId());
        //组合活动基本信息
        this.assemDirectionInfo(listCommunication, activityInfo);
        this.assemProjectInfo(listProject, activityInfo);
        // 获取活动交流方向的数据
        this.getHisCommunicationResult(activityInfo);
    }

    public void getHisCommunicationResult(ActivityInfoVO activityInfo) {
        List<FastLookupDto> listParentFastlookp = this.listCommunicationByParentType();
        Map<String, List<FastLookupDto>> mapByParentType = listParentFastlookp.stream().collect(Collectors.groupingBy(FastLookupDto::getParentLookupType));
        List<FastLookupDto> listFastlookupChild = MapUtils.isEmpty(mapByParentType) ? Lists.newArrayList()
                : mapByParentType.get(PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR);
        List<HistoryCommunicateDirectionVO> historyCommunicateDirectionVOS = activityInfo.getListHistoryCommunicationDirection();
        if (CollectionUtils.isEmpty(historyCommunicateDirectionVOS)) {
            return;
        }
        this.buildHisCommunicationResult(historyCommunicateDirectionVOS, listFastlookupChild);
    }

    public static void buildHisCommunicationResult(List<HistoryCommunicateDirectionVO> communicateDirectionVOList, List<FastLookupDto> listFastLookupChild){
        Map<String, FastLookupDto> childMap = listFastLookupChild.stream().collect(Collectors.toMap(FastLookupDto::getLookupCode, Function.identity(), (o1, o2) -> o1));
        for (HistoryCommunicateDirectionVO directionVO : communicateDirectionVOList) {
            FastLookupDto child = childMap.get(directionVO.getCommunicationDirection());
            if (Objects.nonNull(child)) {
                directionVO.setCommunicationDirectionName(child.getMeaning());
            }
        }
    }

    public List<FastLookupDto> listCommunicationByParentType() {
        FastLookupSearchDTO searchDTOByParentType = new FastLookupSearchDTO();
        List<String> listParentLookupType = Arrays.asList(COMMUNICATION_DIRECTOR_LEVEL1, PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR, PARENT_LOOKUP_TYPE_EXTENSION_TYPE);
        searchDTOByParentType.setListParentLookupType(listParentLookupType);
        return lookUpExtService.batchSearch(searchDTOByParentType);
    }

    /**
     * 补充交流方向信息
     * @param listCommunication 交流方向
     * @param activityInfo 活动基本信息
     */
    public static void assemDirectionInfo(List<ActivityCommunicationDirectionDO> listCommunication, ActivityInfoVO activityInfo) {
        if (CollectionUtils.isEmpty(listCommunication)) {
            activityInfo.setListHistoryCommunicationDirection(Lists.newArrayList());
            return;
        }
        List<HistoryCommunicateDirectionVO> list = Lists.newArrayList();
        for (ActivityCommunicationDirectionDO communicationDirectionDO : listCommunication) {
            HistoryCommunicateDirectionVO directionVO = new HistoryCommunicateDirectionVO();
            BeanUtils.copyProperties(communicationDirectionDO, directionVO);
            list.add(directionVO);
        }
        activityInfo.setListHistoryCommunicationDirection(list);
    }

    /**
     * 补充活动项目信息
     * @param listProject  项目信息
     * @param activityInfo 活动信息
     */
    public static void assemProjectInfo(List<ActivityRelationProjectDO> listProject, ActivityInfoVO activityInfo) {
        if (CollectionUtils.isEmpty(listProject)) {
            return;
        }
        ActivityRelationProjectDO relationProject = listProject.get(0);
        activityInfo.setProjectCode(relationProject.getProjectCode());
        activityInfo.setProjectName(relationProject.getProjectName());
    }

    /**
     * 补充我司员工信息与讲师以及方案信息
     * @param activityInfo 活动信息
     */
    public void getActivityZtePeopleListInfo(ActivityInfoVO activityInfo) {
        List<ActivityRelationZtePeopleDO> listZtePeople = ztePeopleRepository.queryAllZtePeopleForActivity(activityInfo.getRowId());
        List<ActivityRelationSolutionDO> listSolution = solutionRepository.queryAllSolutionForActivity(activityInfo.getRowId());
        List<HistoryZtePeopleListVO> listHistoryZtePeople = assembleZtePeopleInfo(listZtePeople, listSolution);
        activityInfo.setListHistoryZtePeopleListVO(listHistoryZtePeople);
    }

    public static List<HistoryZtePeopleListVO> assembleZtePeopleInfo(List<ActivityRelationZtePeopleDO> listZtePeople,
                                                          List<ActivityRelationSolutionDO> listSolution) {
        if (CollectionUtils.isEmpty(listZtePeople)) {
            return Lists.newArrayList();
        }
        List<HistoryZtePeopleListVO> listZtePeopleInfo = Lists.newArrayList();
        List<ActivityRelationZtePeopleDO> listParticipants = listZtePeople.stream()
                .filter(item -> ActivityPeopleTypeEnum.isActivityParticipants(item.getPeopleType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listParticipants)) {
            return listZtePeopleInfo;
        }
        List<ActivityRelationZtePeopleDO> participants = getActivityDistPeopleType(listParticipants);
        Map<String, ActivityRelationSolutionDO> solutionMap = listSolution.stream()
                .collect(Collectors.toMap(ActivityRelationSolutionDO::getRelationPeopleRowId,
                        Function.identity(), (v1, v2) -> v1));
        boolean isNoSolution = MapUtils.isEmpty(solutionMap);
        // 补充方案信息
        Map<String, Set<String>> activityParticipantMaps = new HashMap<>();
        participants.forEach(e -> {
            String peopleType = e.getPeopleType();
            String peopleCode = e.getPeopleCode();
            Set<String> participantSet = Optional.ofNullable(activityParticipantMaps.get(peopleType)).orElse(new HashSet<>());
            // 按人员类型去重
            if (participantSet.contains(peopleCode)) {
                return;
            }
            participantSet.add(peopleCode);
            activityParticipantMaps.put(peopleType, participantSet);

            HistoryZtePeopleListVO historyZtePeopleListVO = new HistoryZtePeopleListVO();
            BeanUtils.copyProperties(e, historyZtePeopleListVO);
            historyZtePeopleListVO.setPeopleTypeName(ActivityPeopleTypeEnum.getDescByCode(e.getPeopleType()));

            if (!isNoSolution) {
                ActivityRelationSolutionDO solutionDO = Optional.ofNullable(solutionMap.get(e.getRowId()))
                        .orElse(new ActivityRelationSolutionDO());
                historyZtePeopleListVO.setSolutionRowId(solutionDO.getRowId());
                historyZtePeopleListVO.setSolutionNameCn(solutionDO.getSolutionNameCn());
                historyZtePeopleListVO.setSolutionNameEn(solutionDO.getSolutionNameEn());
                historyZtePeopleListVO.setSolutionUrl(solutionDO.getSolutionUrl());
                historyZtePeopleListVO.setPlanId(solutionDO.getPlanId());
            }
            listZtePeopleInfo.add(historyZtePeopleListVO);
        });
        // 返回的我司参与人要按照职位进行排序
        return listZtePeopleInfo;
    }

    public static List<ActivityRelationZtePeopleDO> getActivityDistPeopleType(List<ActivityRelationZtePeopleDO> listParticipants) {
        List<ActivityRelationZtePeopleDO> participants = new ArrayList<>(listParticipants.size());
        Map<String, List<ActivityRelationZtePeopleDO>> codeGroupMap = listParticipants.stream().collect(Collectors.groupingBy(ActivityRelationZtePeopleDO::getPeopleCode));
        codeGroupMap.forEach((k,v) ->{
            if (v.size() > NumberConstant.ONE){
                participants.addAll(v.stream().filter(x -> !PARTICIPANTS.isMe(x.getPeopleType())).collect(Collectors.toList()));
            } else {
                participants.addAll(v);
            }
        });
        return participants;
    }
}
