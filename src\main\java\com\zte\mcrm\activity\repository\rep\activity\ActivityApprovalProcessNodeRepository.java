package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 活动审批信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityApprovalProcessNodeRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityApprovalProcessNodeDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityApprovalProcessNodeDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityApprovalProcessNodeDO> queryAllByActivityRowId(String activityRowId);

    /**
     * 根据活动id批量获取当前数据集合
     *
     * @param activityRowIds
     * @return
     */
    Map<String, List<ActivityApprovalProcessNodeDO>> queryAllByActivityRowIds(List<String> activityRowIds);

    /**
     * 根据审批节点id获取当前数据集合
     *
     * @param approvalProcessRowId
     * @return
     */
    List<ActivityApprovalProcessNodeDO> queryByProcessRowId(String approvalProcessRowId);

    /**
     * 根据活动id和审批流节点id获取数据
     *
     * @param activityRowId
     * @param approvalFlowNo
     * @return
     */
    ActivityApprovalProcessNodeDO queryByActIdAndApprovalFlowNo(String activityRowId, String approvalFlowNo);

    /**
     * 根据活动id和节点类型
     *
     * @param activityRowId
     * @param nodeType
     * @return
     */
    List<ActivityApprovalProcessNodeDO> queryByActIdAndNodeType(String activityRowId, String nodeType);

    /**
     * 根据主键列表删除信息
     *
     * @param rowIds
     * @return
     */
    int deleteByRowIds(List<String> rowIds);


}
