package com.zte.mcrm.activity.repository.mapper.magazine;

import com.zte.mcrm.activity.repository.model.magazine.MagazineSubscriptionDO;

/**
 * 杂志订阅基础Mapper
 * 包含标准CRUD操作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MagazineSubscriptionMapper {
    
    /**
     * 根据主键查询
     * 
     * @param rowId 主键ID
     * @return 杂志订阅对象
     */
    MagazineSubscriptionDO selectByPrimaryKey(String rowId);
    
    /**
     * 动态字段插入
     * 
     * @param record 杂志订阅对象
     * @return 影响行数
     */
    int insertSelective(MagazineSubscriptionDO record);
    
    /**
     * 根据主键动态更新
     * 
     * @param record 杂志订阅对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(MagazineSubscriptionDO record);
} 