package com.zte.mcrm.activity.application.cto.impl;

import com.zte.mcrm.activity.application.cto.CtoPlanExportAndEmailReportProcessService;
import com.zte.mcrm.activity.application.cto.CtoPlanReportService;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanApiHelperVO;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanExeReportRepository;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * {@code @description 服务实现类，用于处理CTO计划生成及发送报表}
 *
 * <AUTHOR>
 * @date 2024/12/23 上午9:47
 */
@Slf4j
@Service
public class CtoPlanExportAndEmailReportProcessServiceImpl implements CtoPlanExportAndEmailReportProcessService {
    @Value("${zte.crm.api.empNo:10318011}")
    public String crmEmpNo;

    @Autowired
    private CtoPlanExeReportRepository exeReportRepository;
    @Autowired
    private CtoPlanReportService ctoPlanReportService;
    @Autowired
    private CtoPlanApiHelperVO ctoPlanApiHelperVO;

    @Override
    public void processDataByPlanId(String ctoPlanInfoId) {
        CtoPlanExportParam ctoPlanExportParam = new CtoPlanExportParam();
        ctoPlanExportParam.setPlanId(ctoPlanInfoId);
        ctoPlanExportParam.setPlanName(ctoPlanApiHelperVO.getPlanName(ctoPlanInfoId));
        BizRequest<CtoPlanExportParam> bizReq = BizRequestUtil.createWithCurrentUser(ctoPlanExportParam);
        if (StringUtils.isBlank(bizReq.getEmpNo())) {
            bizReq.setEmpNo(crmEmpNo);
        }
        ctoPlanReportService.generateAndSendEmailCTOReport(bizReq);
    }
}
