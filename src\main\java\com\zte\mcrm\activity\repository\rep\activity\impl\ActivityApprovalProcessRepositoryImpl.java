package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityApprovalProcessExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/11 13:55
 */
@Service
public class ActivityApprovalProcessRepositoryImpl implements ActivityApprovalProcessRepository {

    @Autowired
    private ActivityApprovalProcessExtMapper activityApprovalProcessExtMapper;

    @Autowired
    private IKeyIdService iKeyIdService;

    @Override
    public int insert(ActivityApprovalProcessDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(iKeyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return activityApprovalProcessExtMapper.insert(record);
    }

    @Override
    public int insertSelective(ActivityApprovalProcessDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(iKeyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return activityApprovalProcessExtMapper.insertSelective(record);
    }

    @Override
    public ActivityApprovalProcessDO selectByPrimaryKey(String rowId) {
        return activityApprovalProcessExtMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public int updateByPrimaryKey(ActivityApprovalProcessDO record) {
        record.setLastUpdateDate(new Date());
        return activityApprovalProcessExtMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityApprovalProcessDO record) {
        record.setLastUpdateDate(new Date());
        return activityApprovalProcessExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityApprovalProcessDO> getApprovalProcessListByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId)
                ? Lists.newArrayList() : activityApprovalProcessExtMapper.getApprovalProcessListByActivityRowId(activityRowId);
    }

    @Override
    public int insertByBatch(List<ActivityApprovalProcessDO> activityApprovalProcessDOList) {
        activityApprovalProcessDOList.forEach(e -> {
            e.setCreationDate(new Date());
            e.setLastUpdateDate(new Date());
            e.setEnabledFlag(BooleanEnum.Y.getCode());
            e.setRowId(iKeyIdService.getKeyId());
        });

        return activityApprovalProcessExtMapper.insertByBatch(activityApprovalProcessDOList);
    }

    @Override
    public int deleteByRowId(String approveRowId) {
        ActivityApprovalProcessDO activityApprovalProcessDO = new ActivityApprovalProcessDO();
        activityApprovalProcessDO.setEnabledFlag(BooleanEnum.N.getCode());
        activityApprovalProcessDO.setLastUpdateDate(new Date());
        activityApprovalProcessDO.setRowId(approveRowId);
        return activityApprovalProcessExtMapper.updateByPrimaryKeySelective(activityApprovalProcessDO);
    }

    @Override
    public List<ActivityApprovalProcessDO> queryByActivityRowIdAndProcessType(String activityRowId, String processType) {
        return activityApprovalProcessExtMapper.queryByActivityRowIdAndProcessType(activityRowId, processType);
    }

    @Override
    public ActivityApprovalProcessDO queryByApprovalFlowNo(String approvalFlowNo) {
        return activityApprovalProcessExtMapper.queryByApprovalFlowNo(approvalFlowNo);
    }

    @Override
    public List<ActivityApprovalProcessDO> queryByActivityRowId(String activityRowId) {
        return activityApprovalProcessExtMapper.queryByActivityRowId(activityRowId);
    }

    /**
     * 通过活动Id更新
     * @param record 更新记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/4 13:53
     */
    @Override
    public int updateByActivityRowIdSelective(ActivityApprovalProcessDO record) {
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
        return activityApprovalProcessExtMapper.updateByActivityRowIdSelective(record);
    }

    /**
     * 通过主键ID列表删除节点
     * @param rowIds
     * @return
     */
    @Override
    public int deleteByRowIds(List<String> rowIds) {
        return activityApprovalProcessExtMapper.deleteByRowIds(rowIds);
    }
}
