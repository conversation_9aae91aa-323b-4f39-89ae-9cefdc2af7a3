package com.zte.mcrm.activity.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> ********
 * @date 2023-11-15 10:11
 **/
@Configuration
public class InOneConfig {
    @Value("${inone.switch:false}")
    private boolean inOneSwitch;

    @Value("${lcm.inone.switch:true}")
    private boolean lcmInOneSwitch;

    @Value("${inone.host:https://icosg.test.zte.com.cn}")
    private String inOneHost;

    @Value("${inone.caller.app.code: }")
    private String callerAppCode;

    @Value("${inone.account.info.path:/ZXISS_SS300/ACCOUNT_INFO/zte-crm-account-info}")
    private String inOneAccountInfoServiceName;

    @Value("${inone.eva.reception.path:/ZXICCP_iSales300/ISALES_CRM}")
    private String inOneEvaReceptionServiceName;

    @Value("${inone.misccomponents.path:/zte-iss-gcsc-misccomponents/test}")
    private String inOneMiscComponentsTestServiceName;

    @Value("${inone.eva.mid.path:/ZXISS_SS300/EVA_MID}")
    private String inOneEvaMidServiceName;


    public String getInOneMiscComponentsTestServiceName(){
        return inOneMiscComponentsTestServiceName;
    }

    public String getInOneEvaReceptionServiceName(){
        return inOneEvaReceptionServiceName;
    }

    public String getInOneEvaMidServiceName(){
        return inOneEvaMidServiceName;
    }

    public String getInOneAccountInfoServiceName(){
        return inOneAccountInfoServiceName;
    }

    public boolean isInOneSwitch() {
        return inOneSwitch;
    }

    public boolean isLcmInOneSwitch() {
        return lcmInOneSwitch;
    }

    public String getInOneHost() {
        return inOneHost;
    }

    public String getCallerAppCode() {
        return callerAppCode;
    }

}
