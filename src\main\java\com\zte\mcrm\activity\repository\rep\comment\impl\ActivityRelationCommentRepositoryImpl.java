package com.zte.mcrm.activity.repository.rep.comment.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.comment.ActivityRelationCommentExtMapper;
import com.zte.mcrm.activity.repository.model.comment.ActivityRelationCommentDO;
import com.zte.mcrm.activity.repository.rep.comment.ActivityRelationCommentRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.custcomm.common.constant.CustCommConstants.ENABLED_FLAG_N;

@Service
public class ActivityRelationCommentRepositoryImpl implements ActivityRelationCommentRepository {
    @Autowired
    private ActivityRelationCommentExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityRelationCommentDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationCommentDO record) {
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationCommentDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public int insertSelective(List<ActivityRelationCommentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return ZERO;
        }
        for (ActivityRelationCommentDO record : recordList) {
            this.insertSelective(record);
        }

        return recordList.size();
    }

    /**
     * 根据评论Id查询
     *
     * @param commentId
     * @return: ActivityRelationCommentDO
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 14:42
     */
    @Override
    public ActivityRelationCommentDO selectByPrimaryKey(String commentId) {
        if (StringUtils.isBlank(commentId)) {
            return null;
        }
        return extMapper.selectByPrimaryKey(commentId);
    }

    /**
     * 根据主键逻辑删除
     *
     * @param rowId
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 14:54
     */
    @Override
    public int deleteByPrimaryKey(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return ZERO;
        }
        ActivityRelationCommentDO record = new ActivityRelationCommentDO();
        record.setRowId(rowId);
        record.setEnabledFlag(ENABLED_FLAG_N);
        return this.updateByPrimaryKeySelective(record);
    }

}