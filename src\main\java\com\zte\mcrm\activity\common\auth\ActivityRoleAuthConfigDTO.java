package com.zte.mcrm.activity.common.auth;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 客户融合活动角色权限配置
 *
 * <AUTHOR>
 * @date 2023/8/18 下午5:33
 */
@Getter
@Setter
@ToString
public class ActivityRoleAuthConfigDTO {

    /**
     * 角色编号
     */
    private String roleCode;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 是否可删除
     */
    private Boolean deletable;

    /**
     * 是否可作废
     */
    private Boolean voidable;

    /**
     * 是否可撤回
     */
    private Boolean cancelable;

    /**
     * 是否可变更
     */
    private Boolean changeable;

    /**
     * 约束映射，id映射编号
     */
    private Map<String, List<String>> constraintMapping;

}
