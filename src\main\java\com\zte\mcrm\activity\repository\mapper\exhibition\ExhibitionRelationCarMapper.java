package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO;

public interface ExhibitionRelationCarMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationCarDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationCarDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationCarDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationCarDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationCarDO record);
}