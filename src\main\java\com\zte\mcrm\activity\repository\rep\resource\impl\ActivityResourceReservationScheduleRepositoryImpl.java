package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.resource.ReserveScheduleOriTypeEnum;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityResourceReservationScheduleExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceReservationScheduleRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ActivityResourceReservationScheduleRepositoryImpl implements ActivityResourceReservationScheduleRepository {
    @Resource
    private ActivityResourceReservationScheduleExtMapper activityResourceReservationScheduleExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<ActivityResourceReservationScheduleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        Date now = new Date();
        for (ActivityResourceReservationScheduleDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            record.setCreationDate(now);
            record.setLastUpdateDate(now);
            activityResourceReservationScheduleExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityResourceReservationScheduleDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityResourceReservationScheduleExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, ActivityResourceReservationScheduleDO> queryScheduleWithActivityRowIds(List<String> activityRowIds) {

        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap()
                : activityResourceReservationScheduleExtMapper.queryScheduleWithActivityRowIds(activityRowIds)
                .stream().filter(e -> ReserveScheduleOriTypeEnum.ACTIVITY.isMe(e.getReserveOriType())).collect(
                        Collectors.toMap(ActivityResourceReservationScheduleDO::getActivityRowId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, List<ActivityResourceReservationScheduleDO>> queryAllScheduleWithActivityRowIds(List<String> activityRowIds) {

        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap()
                : activityResourceReservationScheduleExtMapper.queryScheduleWithActivityRowIds(activityRowIds).stream()
                .collect(Collectors.groupingBy(ActivityResourceReservationScheduleDO::getActivityRowId));
    }

    @Override
    public Boolean checkIfScheduleHasChanged(String activityRowId) {
        if (StringUtils.isEmpty(activityRowId)) {
            return Boolean.FALSE;
        }
        return CollectionUtils.isNotEmpty(activityResourceReservationScheduleExtMapper.queryExpiredSchedules(activityRowId));
    }

    @Override
    public Map<String, ActivityResourceReservationScheduleDO> queryScheduleWithOriRowIds(List<String> oriRowIdList, ReserveScheduleOriTypeEnum oriTypeEnum) {
        return CollectionUtils.isEmpty(oriRowIdList) ? Collections.emptyMap()
                : activityResourceReservationScheduleExtMapper.queryScheduleWithOriRowIds(oriRowIdList, oriTypeEnum == null ? null : oriTypeEnum.getType())
                .stream().collect(
                        Collectors.toMap(ActivityResourceReservationScheduleDO::getOriRowId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public List<ActivityResourceReservationScheduleDO> queryScheduleWithRowIds(List<String> rowIdList) {
        return CollectionUtils.isEmpty(rowIdList) ? Collections.emptyList()
                : activityResourceReservationScheduleExtMapper.queryScheduleWithRowIds(rowIdList);
    }

    @Override
    public int softDeleteResourceReservationSchedule(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return 0;
        }

        operator = StringUtils.isBlank(operator) ? HeadersProperties.getXEmpNo() : operator;
        return activityResourceReservationScheduleExtMapper.softDeleteByRowIds(operator, rowIds);
    }

}
