package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivityRelationProjectExtMapper extends ActivityRelationProjectMapper {

    /**
     * 查询活动相关项目
     *
     * @param activityRowId
     * @return
     */
    @Deprecated
    List<ActivityRelationProjectDO> queryAllProjectForActivity(@Param("activityRowId") String activityRowId);

    /**
     * 查询活动相关项目
     * @param activityIds
     * @return
     */
    List<ActivityRelationProjectDO> queryAllByActivityRowId(@Param("activityIds") List<String> activityIds);

    /**
     * 批量插入
     *
     * @param list 项目列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list")List<ActivityRelationProjectDO> list);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);


    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    List<ActivityRelationProjectDO> queryAllProject();

    int batchUpdateByPrimaryKey(List<ActivityRelationProjectDO> updateList);
}