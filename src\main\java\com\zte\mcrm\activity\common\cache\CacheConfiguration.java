package com.zte.mcrm.activity.common.cache;

import com.zte.mcrm.activity.common.cache.cache.CacheConfig;
import com.zte.mcrm.activity.common.cache.client.AreaDataCacheClient;
import com.zte.mcrm.activity.common.cache.client.HrOrgDataCacheClient;
import com.zte.mcrm.activity.common.cache.loader.AreaDataLoader;
import com.zte.mcrm.activity.common.cache.loader.HrOrgDataLoader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置注入
 * <pre>
 * 相关设计见：https://i.zte.com.cn/#/space/51c1f5f86d7e48cdaa8a7cc507dac49c/wiki/page/c0f79e1eb1394085874610dcee8782cd/view
 *     备注：
 *     > 此外在现有代码直接调用接口本身就会产生大量的临时变量，大字符串，其使用内存更频繁更多，gc会更多
 *     > 使用适当缓存其实是有利于系统稳定性
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-09-22
 */
@Configuration
public class CacheConfiguration {
    private static final int SIZE = 1024;
    private static final int TIME = 12;

    /**
     * 国家/地区、省、城市等区域信息缓存客户端
     *
     * @param dictCacheDataLoader
     * @return
     */
    @Bean
    public AreaDataCacheClient areaDataCacheClient(AreaDataLoader dictCacheDataLoader) {
        return new AreaDataCacheClient(dictCacheDataLoader, buildCommonConfig());
    }

    /**
     * hr组织数据缓存客户端
     *
     * @param hrOrgDataLoader
     * @return
     */
    @Bean
    public HrOrgDataCacheClient hrOrgDataCacheClient(HrOrgDataLoader hrOrgDataLoader) {
        return new HrOrgDataCacheClient(hrOrgDataLoader, buildCommonConfig());
    }


    /**
     * 公共配置
     *
     * @return
     */
    public static CacheConfig buildCommonConfig() {
        CacheConfig config = new CacheConfig();
        config.setExpireTimeUnit(TimeUnit.HOURS);
        config.setExpireTime(TIME);
        // 目前数据参数不超过一千
        config.setMaxSize(SIZE);

        return config;
    }
}
