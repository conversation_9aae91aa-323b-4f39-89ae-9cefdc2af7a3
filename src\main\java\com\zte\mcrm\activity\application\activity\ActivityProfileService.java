package com.zte.mcrm.activity.application.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.web.controller.activity.vo.ProfileActivityInfoDataVO;
import com.zte.mcrm.dataservice.dto.ProfileActivityApDTO;
import com.zte.mcrm.dataservice.model.ActivityApDataQuery;
import com.zte.mcrm.dataservice.model.ActivityDataQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ActivityProfileService
 * @projectName zte-crm-custinfo-custvisit
 * @description: 客户画像接口
 * @date 2024/6/18 14:12
 */
public interface ActivityProfileService {

    /**
     * 客户画像-活动列表
     * @param bizRequest
     * @return pageRow
     * <AUTHOR>
     * @date 2024/6/18
     */
    PageRows<ProfileActivityInfoDataVO> queryActivityDate(BizRequest<ActivityDataQuery> bizRequest) throws Exception;

    /**
     * 客户画像-活动AP列表
     * @param bizRequest
     * @return pageRow
     * <AUTHOR>
     * @date 2024/6/18
     */
    List<ProfileActivityApDTO> queryActivityApDate(BizRequest<ActivityApDataQuery> bizRequest) throws Exception;


}
