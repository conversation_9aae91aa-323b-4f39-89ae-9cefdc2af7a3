package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanOrgParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanProductParam;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月20日9:29
 */
@Getter
@Setter
public class CtoPlanPublishDTO implements Serializable {

    /**
     * CTO拓展计划ID
     */
    private String ctoPlanInfoId;

    /**
     * CTO周期开始时间: 2024-01-01 00:00:00
     */
    private Date scopeStart;

    /**
     * CTO周期结束时间: 2024-08-01 23:59:59
     */
    private Date scopeEnd;

    private String empNo;

    /**
     * 报表推送接收人
     */
    private List<String> publishReceiver;

    /**
     * 事业部数据
     */
    private List<CtoPlanOrgFinishDO> orgFinishDOList;

    /**
     * 产品数据
     */
    private List<CtoPlanProductFinishDO> productFinishDOList;

    /**
     * 执行报表数据
     */
    private List<CtoPlanExeReportDO> exeReportDOList;

    /**
     * 待执行时间,不包含执行报表的待执行时间
     */
    private Date exeWaitTime;

}
