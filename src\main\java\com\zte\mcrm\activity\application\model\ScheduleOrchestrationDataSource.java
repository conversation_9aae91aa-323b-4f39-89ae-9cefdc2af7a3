package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ScheduleOrchestrationRoleEnum;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.service.schedule.impl.support.ScheduleOrchestrationConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class ScheduleOrchestrationDataSource extends StandardActivityDetailDataSource {
    /**
     * true-表示编排模式（编辑），false-仅查看最新
     */
    private boolean editModel;
    /**
     * 是否需要编排过滤数据。true-需要过滤（即只有日程汇总含会议室 或 领导资源的才展示出来），false-不过滤，所有数据都展示
     */
    private boolean needOrchestrationFiler;
    /**
     * 对应展会
     */
    private ExhibitionInfoDO exhibitionInfo;
    /**
     * 展会对应资源编排的版本列表
     */
    private List<ActivityScheduleOrchestrationVersionDO> scheduleOrchestrationVersionList;
    /**
     * 展会对应资源编排的版本对应的编排明细《编排版本RowId，编排明细列表》
     */
    private Map<String, List<ActivityScheduleOrchestrationDetailDO>> scheduleOrchestrationVersionDetailMap;

    /**
     * 展会中关联维护人员
     */
    private List<ExhibitionDirectorDO> directorList;

    /**
     * 即是分营又是总营
     */
    private String roleTypeAll;

    /**
     * 获取某人的资源编排版本信息
     *
     * @param empNo 员工编号
     * @return 资源编排版本信息
     */
    public ActivityScheduleOrchestrationVersionDO fetchScheduleOrchestrationVersion(String empNo) {
        return CollectionUtils.isEmpty(scheduleOrchestrationVersionList) ? null
                : scheduleOrchestrationVersionList.stream().filter(e -> StringUtils.equals(empNo, e.getCreatedBy())).findFirst().orElse(null);
    }

    /**
     * 获取最新的【已发布】的版本
     *
     * @return 资源编排版本信息
     */
    public ActivityScheduleOrchestrationVersionDO fetchLastPublishScheduleOrchestrationVersion() {
        return CollectionUtils.isEmpty(scheduleOrchestrationVersionList) ? null : scheduleOrchestrationVersionList.stream()
                .filter(e -> ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH.equalsIgnoreCase(e.getVersionStatus()))
                .max(Comparator.comparing(ActivityScheduleOrchestrationVersionDO::getPublishTime))
                .orElse(null);
    }

    /**
     * 从资源编排版本中获取对应日程详情
     *
     * @param versionRowId 版本ID
     * @return
     */
    public List<ActivityScheduleOrchestrationDetailDO> fetchScheduleItemDetailFromVersion(String versionRowId) {
        List<ActivityScheduleOrchestrationDetailDO> list = null;
        if (scheduleOrchestrationVersionDetailMap != null) {
            list = scheduleOrchestrationVersionDetailMap.get(versionRowId);
        }

        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 从资源编排版本中获取对应日程
     *
     * @param versionRowId      版本ID
     * @param scheduleItemRowId 日程ID
     * @return
     */
    public ActivityScheduleOrchestrationDetailDO fetchScheduleItemFromVersion(String versionRowId, String scheduleItemRowId) {

        return fetchScheduleItemDetailFromVersion(versionRowId).stream()
                .filter(e -> StringUtils.equals(scheduleItemRowId, e.getScheduleItemRowId())).findFirst().orElse(null);
    }

    /**
     * 判断某人编排角色类型
     *
     * @param empNo 员工编号
     * @return Y-总营，N-分营,null-无该角色
     */
    public String fetchScheduleOrchestrationType(String empNo) {
        ExhibitionDirectorDO adminDirector = filterDirector(directorList, ExhibitionDirectorRoleTypeEnum.RESOURCE_ADMIN, empNo);
        if (adminDirector != null) {
            return BooleanEnum.Y.getCode();
        }
        ExhibitionDirectorDO subDirector = filterDirector(directorList, ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN, empNo);
        if (subDirector != null) {
            return BooleanEnum.N.getCode();
        }

        return null;
    }

    /**
     * 判断编排人的角色类型
     * @param empNo
     * @return
     */
    public ScheduleOrchestrationRoleEnum fetchScheduleOrchestrationRole(String empNo) {
        ExhibitionDirectorDO adminDirector = filterDirector(directorList, ExhibitionDirectorRoleTypeEnum.RESOURCE_ADMIN, empNo);
        if (Objects.nonNull(adminDirector)) {
            return ScheduleOrchestrationRoleEnum.ALL_ADMIN;
        }
        ExhibitionDirectorDO subDirector = filterDirector(directorList, ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN, empNo);
        if (Objects.nonNull(subDirector)) {
            return ScheduleOrchestrationRoleEnum.SUB_ADMIN;
        }
        return ScheduleOrchestrationRoleEnum.PERSONAL;
    }

    /**
     * 获取部门权限
     *
     * @param empNo
     * @return 返回null-表示是拥有【所有权限】，其他情况表示权限数据（如果返回的权限数据是空集合表示无权限）
     */
    public Set<String> fetchScheduleOrchestrationOrgAuth(String empNo) {
        if (BooleanEnum.Y.isMe(fetchScheduleOrchestrationType(empNo))) {
            return null;
        }

        // 将分营的所有部门权限数据汇总打平（每个维护人员中的权限是多个以,拼接的）
        return getOrgAuth(empNo);
    }

    public Set<String> getOrgAuth(String empNo) {
        return CollectionUtils.isEmpty(directorList) ? Collections.emptySet()
                : directorList.stream().filter(e ->
                        ExhibitionDirectorRoleTypeEnum.RESOURCE_POINTS_ADMIN.isMe(e.getRoleType())
                                && StringUtils.isNotBlank(e.getEmployeeNo())
                                && e.getEmployeeNo().contains(empNo))
                .map(ExhibitionDirectorDO::getOrgAuth)
                .filter(StringUtils::isNotBlank)
                .map(e -> e.split(CharacterConstant.COMMA))
                .flatMap(Arrays::stream)
                .collect(Collectors.toSet());
    }

    /**
     * 获取一个这样的橘色
     *
     * @param directorList
     * @param roleTypeEnum
     * @param empNo
     * @return
     */
    public ExhibitionDirectorDO filterDirector(List<ExhibitionDirectorDO> directorList, ExhibitionDirectorRoleTypeEnum roleTypeEnum, String empNo) {
        if (CollectionUtils.isEmpty(directorList)) {
            return null;
        }

        return directorList.stream()
                .filter(e -> roleTypeEnum.isMe(e.getRoleType()) && StringUtils.isNotBlank(e.getEmployeeNo()) && e.getEmployeeNo().contains(empNo))
                .findFirst()
                .orElse(null);
    }
}
