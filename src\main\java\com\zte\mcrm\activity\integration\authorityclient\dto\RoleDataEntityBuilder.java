package com.zte.mcrm.activity.integration.authorityclient.dto;

import com.google.common.collect.Lists;
import com.zte.itp.authorityclient.entity.input.DataVo;
import com.zte.itp.authorityclient.entity.input.RoleDataEntity;
import com.zte.itp.authorityclient.entity.input.RoleDataVo;
import com.zte.mcrm.activity.common.enums.upp.UppConstraintIncludeEnum;
import com.zte.mcrm.activity.common.enums.upp.UppConstraintTypeEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * UPP 角色约束查询用户参数构造器
 *
 * <AUTHOR>
 * @date 2023/5/23 下午7:11
 */
public class RoleDataEntityBuilder {

    /**
     * 角色编号
     */
    private String roleCode;

    /**
     * 约束
     */
    private List<DataVo> datas;

    public static RoleDataEntityBuilder newBuilder() {
        return new RoleDataEntityBuilder();
    }

    /**
     * 初始化，必须
     *
     * @param roleCode
     * @return {@link RoleDataEntityBuilder}
     * @throws
     * <AUTHOR>
     * @date 2023/5/23 下午6:52
     */
    public RoleDataEntityBuilder init(String roleCode) {
        this.roleCode = roleCode;
        this.datas = Lists.newArrayList();
        return this;
    }

    /**
     * 构建参数
     *
     * @return {@link RoleDataEntity}
     * <AUTHOR>
     * @date 2023/5/23 下午7:01
     */
    public RoleDataEntity build() {
        RoleDataVo roleDataVo = new RoleDataVo();
        roleDataVo.setRoleCode(this.roleCode);
        roleDataVo.setDatas(this.datas);
        RoleDataEntity entity = new RoleDataEntity();
        entity.setRoleDataVoList(Lists.newArrayList(roleDataVo));
        return entity;
    }

    /**
     * 合并多个条件
     *
     * @param entities
     * @return {@link RoleDataEntity}
     * <AUTHOR>
     * @date 2023/6/5 下午7:55
     */
    public static RoleDataEntity mergeBatch(List<RoleDataEntity> entities) {
        List<RoleDataVo> roleDataVoList = Lists.newArrayList();
        for (RoleDataEntity entity : entities) {
            if (CollectionUtils.isNotEmpty(entity.getRoleDataVoList())) {
                roleDataVoList.addAll(entity.getRoleDataVoList());
            }
        }
        RoleDataEntity batch = new RoleDataEntity();
        batch.setRoleDataVoList(roleDataVoList);
        return batch;
    }

    /**
     * 内部约束，默认向上搜索（仅树状约束支持）
     *
     * @param constraintId      约束ID
     * @param constraintValue   约束值
     * @return {@link RoleDataEntityBuilder}
     * <AUTHOR>
     * @date 2023/5/23 下午7:06
     */
    public RoleDataEntityBuilder addOrgConstraint(String constraintId, String constraintValue) {
        DataVo dataVo = new DataVo();
        dataVo.setIsInclude(UppConstraintIncludeEnum.UPWARD_RECURSION.getCode());
        dataVo.setType(UppConstraintTypeEnum.ORG.getCode());
        dataVo.setConstraintId(constraintId);
        dataVo.setData(constraintValue);
        this.datas.add(dataVo);
        return this;
    }

    /**
     * 外部约束，默认向上搜索（仅树状约束支持）
     *
     * @param constraintId      约束ID
     * @param constraintValue   约束值
     * @return {@link RoleDataEntityBuilder}
     * <AUTHOR>
     * @date 2023/5/23 下午7:06
     */
    public RoleDataEntityBuilder addOuterConstraint(String constraintId, String constraintValue) {
        DataVo dataVo = new DataVo();
        dataVo.setIsInclude(UppConstraintIncludeEnum.UPWARD_RECURSION.getCode());
        dataVo.setType(UppConstraintTypeEnum.OUTER.getCode());
        dataVo.setConstraintId(constraintId);
        dataVo.setData(constraintValue);
        this.datas.add(dataVo);
        return this;
    }
}
