package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/***
 * 并行任务执行器
 * <pre>
 *     主要用于同步访问接口加快响应，比如：一些列表查询的步骤很多，可以将不相干的事分离并行执行
 * </pre>
 *
 * <AUTHOR>
 */
public class ParallelTaskExecutor extends BaseTaskExecutor {
    private static final int FIX = 16;

    public ParallelTaskExecutor() {
        // 时长对于他无意义
        super("Parallel", new ThreadPoolExecutor.CallerRunsPolicy());
        initParam(FIX, FIX, 10, new LinkedBlockingQueue<>(FIX));
    }
}
