package com.zte.mcrm.activity.repository.model.relation;

import java.util.Date;

/**
 * table:activity_relation_talk -- 
 */
public class ActivityRelationTalkDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 空间id */
    private String spaceId;

    /** 谈参编码(content_id) */
    private String talkCode;

    /** 谈参名称 */
    private String talkName;

    /** 类型 */
    private String type;

    /** 连接 */
    private String talkLink;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(String spaceId) {
        this.spaceId = spaceId == null ? null : spaceId.trim();
    }

    public String getTalkCode() {
        return talkCode;
    }

    public void setTalkCode(String talkCode) {
        this.talkCode = talkCode == null ? null : talkCode.trim();
    }

    public String getTalkName() {
        return talkName;
    }

    public void setTalkName(String talkName) {
        this.talkName = talkName == null ? null : talkName.trim();
    }

    public String getTalkLink() {
        return talkLink;
    }

    public void setTalkLink(String talkLink) {
        this.talkLink = talkLink == null ? null : talkLink.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}