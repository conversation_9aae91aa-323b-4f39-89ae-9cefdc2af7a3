package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityResourceReservationMapper {
    /**
     * all field insert
     */
    int insert(ActivityResourceReservationDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityResourceReservationDO record);

    /**
     * query by primary key
     */
    ActivityResourceReservationDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityResourceReservationDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityResourceReservationDO record);

    /**
     * 新工号切换
     */
    List<ActivityResourceReservationDO> queryEmpNoTransList(DataTransParam searchParam);
}