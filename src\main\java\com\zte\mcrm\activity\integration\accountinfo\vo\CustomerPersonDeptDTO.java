package com.zte.mcrm.activity.integration.accountinfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 联系人所在客户部门信息VO
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Getter
@Setter
public class CustomerPersonDeptDTO {
    @ApiModelProperty("主键")
    private String rowId;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("更新人")
    private String updatedBy;

    @ApiModelProperty("最后更新时间")
    private String updatedTime;

    @ApiModelProperty("部门名称")
    private String name;

    @ApiModelProperty("部门分类")
    private String category;

    @ApiModelProperty("所属部门")
    private String parentId;

    @ApiModelProperty("所属客户")
    private String accountId;

    @ApiModelProperty("部门职责")
    private String duty;

    @ApiModelProperty("邮编")
    private String postCode;

    @ApiModelProperty("部门通信地址")
    private String address;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("电子邮件")
    private String email;
}
