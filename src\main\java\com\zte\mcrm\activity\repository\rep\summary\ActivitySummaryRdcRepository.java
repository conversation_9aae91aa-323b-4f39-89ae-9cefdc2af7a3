package com.zte.mcrm.activity.repository.rep.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-12-05 15:12
 **/
public interface ActivitySummaryRdcRepository {
    /**
     * 添加关联的RDC（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivitySummaryRdcDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivitySummaryRdcDO record);

    /**
     * 查询活动关联的所有RDC
     *
     * @param activityRowIds 活动RowIds
     * @return
     */
    Map<String, List<ActivitySummaryRdcDO>> queryAllRdcForActivity(List<String> activityRowIds);

}
