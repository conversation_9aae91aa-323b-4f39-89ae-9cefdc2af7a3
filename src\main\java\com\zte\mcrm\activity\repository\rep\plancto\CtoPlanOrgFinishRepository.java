package com.zte.mcrm.activity.repository.rep.plancto;

import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import com.zte.mcrm.activity.web.PageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CTO握手事业部维度达成
 *
 * <AUTHOR>
 */
public interface CtoPlanOrgFinishRepository {
    /**
     * 插入数据
     *
     * @param list
     */
    int batchInsert(@Param("list") List<CtoPlanOrgFinishDO> list);

    /**
     * 动态更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CtoPlanOrgFinishDO record);

    /**
     * 批量更新
     * @param list
     * @return
     */
    int batchUpdate(@Param("list") List<CtoPlanOrgFinishDO> list);

    /**
     * 分页查询事业部维度数据
     * @param pageQuery
     * @return
     */
    PageRows<CtoPlanOrgFinishDO> pageOrgFinish(PageQuery<String> pageQuery);

    /**
     * 根据计划ID查询事业部数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanOrgFinishDO> listOrgFinishByPlanInfoId(String planInfoId);
    /**
     * 获取所有待处理的营销部分数据
     *
     */
    List<String> listByUndoProcess();
    /**
     * 根据计划id获取列表数据
     *
     * @param planId
     * @return
     */
    List<CtoPlanOrgFinishDO> selectByPlanId(String planId);

    /**
     * 计算达成场次
     *
     * @param countParam
     * @return
     */
    Integer countFinishByAccountAndParticipant(@Param("param") SelectCtoActivityParamDTO countParam );

    /**
     * 计算AP(营销维度)
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoReportApIndicatorDO> listOrgApIndicatorByPlanInfoId(String ctoPlanInfoId);

    /**
     * 计算AP(产品维度)
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoReportApIndicatorDO> listProductApIndicatorByPlanInfoId(String ctoPlanInfoId);

    /**
     * 查询事业部维度详情
     * @param rowId
     * @return
     */
    CtoPlanDetailDTO queryOrgDetail(String rowId);
}
