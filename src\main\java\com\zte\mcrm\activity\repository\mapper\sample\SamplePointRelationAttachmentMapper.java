package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointRelationAttachmentDO;

public interface SamplePointRelationAttachmentMapper {
    /**
     * all field insert
     */
    int insert(SamplePointRelationAttachmentDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(SamplePointRelationAttachmentDO record);

    /**
     * query by primary key
     */
    SamplePointRelationAttachmentDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(SamplePointRelationAttachmentDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(SamplePointRelationAttachmentDO record);
}