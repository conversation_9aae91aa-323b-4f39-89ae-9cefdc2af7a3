package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ResourceCtoPersonDO;

import java.util.List;


/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-11
 */
public interface ResourceCtoPersonMapper {
    /* Started by AICoder, pid:y3f5av77daif96f142330a19103ca90eb095c12e */
    int batchInsert(List<ResourceCtoPersonDO> list);
    int insertSelective(ResourceCtoPersonDO record);
    int batchUpdate(List<ResourceCtoPersonDO> list);
    int updateByPrimaryKeySelective(ResourceCtoPersonDO record);
    List<ResourceCtoPersonDO> selectByPrimaries(List<String> primaries);
    /* Ended by AICoder, pid:y3f5av77daif96f142330a19103ca90eb095c12e */
}
