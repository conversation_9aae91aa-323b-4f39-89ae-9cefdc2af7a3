package com.zte.mcrm.activity.repository.model.activity;

import java.util.Date;

/**
 * table:activity_opportunity_info -- 
 */
public class ActivityOpportunityInfoDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 商机编号 */
    private String opportunityId;

    /** 绑定标识（1绑定，2解绑） */
    private Integer bindFlag;

    /** 绑定人 */
    private String bindBy;

    /** 绑定时间 */
    private Date bindDate;

    /** 活动绑定时状态。枚举：ActivityStatusEnum */
    private String activityBindStatus;

    /** 解绑人 */
    private String unbindBy;

    /** 解绑时间 */
    private Date unbindDate;

    /** 活动解绑时状态。枚举：ActivityStatusEnum */
    private String activityUnbindStatus;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getOpportunityId() {
        return opportunityId;
    }

    public void setOpportunityId(String opportunityId) {
        this.opportunityId = opportunityId == null ? null : opportunityId.trim();
    }

    public Integer getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(Integer bindFlag) {
        this.bindFlag = bindFlag;
    }

    public String getBindBy() {
        return bindBy;
    }

    public void setBindBy(String bindBy) {
        this.bindBy = bindBy == null ? null : bindBy.trim();
    }

    public Date getBindDate() {
        return bindDate;
    }

    public void setBindDate(Date bindDate) {
        this.bindDate = bindDate;
    }

    public String getActivityBindStatus() {
        return activityBindStatus;
    }

    public void setActivityBindStatus(String activityBindStatus) {
        this.activityBindStatus = activityBindStatus == null ? null : activityBindStatus.trim();
    }

    public String getUnbindBy() {
        return unbindBy;
    }

    public void setUnbindBy(String unbindBy) {
        this.unbindBy = unbindBy == null ? null : unbindBy.trim();
    }

    public Date getUnbindDate() {
        return unbindDate;
    }

    public void setUnbindDate(Date unbindDate) {
        this.unbindDate = unbindDate;
    }

    public String getActivityUnbindStatus() {
        return activityUnbindStatus;
    }

    public void setActivityUnbindStatus(String activityUnbindStatus) {
        this.activityUnbindStatus = activityUnbindStatus == null ? null : activityUnbindStatus.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}