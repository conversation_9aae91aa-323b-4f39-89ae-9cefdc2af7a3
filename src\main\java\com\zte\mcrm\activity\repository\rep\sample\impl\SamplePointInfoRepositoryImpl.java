package com.zte.mcrm.activity.repository.rep.sample.impl;

import com.google.common.collect.Maps;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.sample.SamplePointInfoExtMapper;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.repository.rep.sample.param.SamplePointInfoQuery;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 样板点信息
 *
 * <AUTHOR>
 */
@Component
public class SamplePointInfoRepositoryImpl implements SamplePointInfoRepository {
    @Resource
    private SamplePointInfoExtMapper samplePointInfoExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<SamplePointInfoDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (SamplePointInfoDO infoDO : recordList) {
            if (StringUtils.isBlank(infoDO.getRowId())) {
                infoDO.setRowId(keyIdService.getKeyId());
            }
            samplePointInfoExtMapper.insertSelective(infoDO);
        }
        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(SamplePointInfoDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return samplePointInfoExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int getMonthCount(SamplePointInfoQuery queryParams) {
        return samplePointInfoExtMapper.getMonthCount(queryParams);
    }

    @Override
    public PageRows<SamplePointInfoDO> querySamplePointInfoList(SamplePointInfoQuery query) {
        if(!query.check()){
            return PageRowsUtil.buildEmptyPage(query);
        }

        List<SamplePointInfoDO> samplePointInfoDOList = samplePointInfoExtMapper.getList(query);

        int total = samplePointInfoExtMapper.countList(query);
        return PageRowsUtil.buildPageRow(query.getPageNo().longValue(), query.getPageSize().longValue(), total, samplePointInfoDOList);
    }

    @Override
    public List<SamplePointInfoDO> queryEffectiveSamplePointInfoList() {
        return samplePointInfoExtMapper.queryEffectiveSamplePointList();
    }

    @Override
    public SamplePointInfoDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : samplePointInfoExtMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public Map<String, SamplePointInfoDO> querySamplePointInfoByRowIds(List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return Maps.newHashMap();
        }
        List<SamplePointInfoDO> samplePointInfoDOS = samplePointInfoExtMapper.querySamplePointInfoByIds(rowIds);
        return CollectionUtils.isEmpty(samplePointInfoDOS) ? Maps.newHashMap() : samplePointInfoDOS.stream()
                .collect(Collectors.toMap(SamplePointInfoDO::getRowId, e -> e, (k1, k2) -> k1));
    }
}
