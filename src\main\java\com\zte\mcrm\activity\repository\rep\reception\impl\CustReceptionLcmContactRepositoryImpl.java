package com.zte.mcrm.activity.repository.rep.reception.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.reception.CustReceptionLcmContactExtMapper;
import com.zte.mcrm.activity.repository.model.reception.CustReceptionLcmContactDO;
import com.zte.mcrm.activity.repository.rep.reception.CustReceptionLcmContactRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * 客户接待 - 客户联系人LCM扫描信息
 *
 * <AUTHOR>
 */
@Component
public class CustReceptionLcmContactRepositoryImpl implements CustReceptionLcmContactRepository {
        @Autowired
        private IKeyIdService iKeyIdService;

        @Autowired
        private CustReceptionLcmContactExtMapper extMapper;

        @Override
        public List<CustReceptionLcmContactDO> getByHeaderId(String headerId) {
                return StringUtils.isBlank(headerId) ? Collections.emptyList() : extMapper.getByHeaderIds(Collections.singletonList(headerId.trim()));
        }

        /**
         * 通过头Id删除
         * @return int
         * <AUTHOR>
         * date: 2023/12/20 16:01
         */
        @Override
        public int deleteByHeaderId(String headerId) {
                if(StringUtils.isBlank(headerId)) {
                        return ZERO;
                }
                CustReceptionLcmContactDO contactDO = new CustReceptionLcmContactDO();
                contactDO.setHeaderId(headerId);
                contactDO.setEnabledFlag(BooleanEnum.N.getCode());
                contactDO.setLastUpdateDate(new Date());
                contactDO.setLastUpdatedBy(HeadersProperties.getXEmpNo());
                return extMapper.updateByHeaderIdSelective(contactDO);
        }

        /**
         * 批量插入客户LCM扫描信息
         * @param list  列表
         * @return int
         * <AUTHOR>
         * date: 2023/12/20 15:38
         */
        @Override
        public int batchInsert(List<CustReceptionLcmContactDO> list) {
                if (CollectionUtils.isEmpty(list)) {
                        return ZERO;
                }
                list.forEach(this::setDefaultValue);
                return extMapper.batchInsert(list);
        }


        /**
         * 设置默认值
         * @param lcmContactDO 实例
         * @return void
         * <AUTHOR>
         * date: 2023/12/20 16:03
         */
        private void setDefaultValue(CustReceptionLcmContactDO lcmContactDO) {
                lcmContactDO.setRowId(Optional.ofNullable(lcmContactDO.getRowId()).orElse(iKeyIdService.getKeyId()));
                lcmContactDO.setCreatedBy(Optional.ofNullable(lcmContactDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
                lcmContactDO.setLastUpdatedBy(Optional.ofNullable(lcmContactDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
                lcmContactDO.setCreationDate(new Date());
                lcmContactDO.setLastUpdateDate(new Date());
                lcmContactDO.setEnabledFlag(BooleanEnum.Y.getCode());
        }
}
