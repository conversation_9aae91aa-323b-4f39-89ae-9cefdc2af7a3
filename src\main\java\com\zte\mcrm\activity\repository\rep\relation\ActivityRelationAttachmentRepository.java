package com.zte.mcrm.activity.repository.rep.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动关联的附件信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityRelationAttachmentRepository {


    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityRelationAttachmentDO record);


    /**
     * 插入数据
     * @param recordList
     * @return
     */
    int insertSelectiveList(List<ActivityRelationAttachmentDO> recordList);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityRelationAttachmentDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityRelationAttachmentDO> queryAllByActivityRowId(String activityRowId);


    /**
     * 根据活动id列表获取当前数据集合
     *
     * @param activityRowIds
     * @return
     */
    List<ActivityRelationAttachmentDO> queryAttachmentByActivityRowIds(List<String> activityRowIds);

    /**
     * 根据sceneOriginRowId列表获取附件列表
     *
     * @param sceneOriginRowIds
     * @return
     */
    List<ActivityRelationAttachmentDO> queryActivityAttachmentList(List<String> sceneOriginRowIds);

    /**
     * 根据sceneOriginRowId获取当前数据（日程安排查单条）（该方法后续不要使用）
     * @param sceneOriginRowId
     * @return
     */
    ActivityRelationAttachmentDO queryAllBySceneOriginRowId(String sceneOriginRowId);

    /**
     * 查询活动和会议关联的附件
     * @param  activityRowId
     * @return
     */
    List<ActivityRelationAttachmentDO> queryByActivityRowIdAndSummaryId(String activityRowId, String activitySummaryRowId);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    int deleteByActivityIds(String operator, List<String> activityIds);


    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量插入数据
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityRelationAttachmentDO> recordList);
    /**
     * 批量修改数据
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchUpdate(List<ActivityRelationAttachmentDO> recordList);

    /**
     * 批量修改数据(不更新最后更新信息)
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     */
    int batchUpdateWithoutLastUpdate(List<ActivityRelationAttachmentDO> recordList);

    /**
     * 根据sceneOriginRowIds批量软删除
     * @param operator
     * @param sceneOriginRowIds
     * @return
     */
    int deleteBySceneOriginRowIds(String operator, List<String> sceneOriginRowIds);

    List<ActivityRelationAttachmentDO> queryErrorType(List<String> errorTypeList);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationAttachmentDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationAttachmentDO> queryAllActivityWithNotEnable(String activityRowId);

    /**
     * 按照主键和类型更新
     * @param attachmentDO  附件
     * @return int
     * <AUTHOR>
     * date: 2024/1/18 20:12
     */
    int updateByPrimaryKeySelectiveAndType(ActivityRelationAttachmentDO attachmentDO);

    /**
     * 根据条件查询附件信息
     *
     * @param param 附件信息
     * @return 附件信息
     */
    ActivityRelationAttachmentDO queryByCondition(ActivityRelationAttachmentDO param);

    /**
     * 根据业务id和附件类型查询单个附件
     *
     * @param attachmentSceneType
     * @param sceneOriginRowId
     * @return {@link ActivityRelationAttachmentDO}
     * <AUTHOR>
     * @date 2025/3/2 下午12:34
     */
    ActivityRelationAttachmentDO queryAttachmentBySceneOriginRowIdAndType(String attachmentSceneType, String sceneOriginRowId);
}
