package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationCarExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationCarRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午3:17
 **/
@Repository
public class ExhibitionRelationCarRepositoryImpl implements ExhibitionRelationCarRepository {

    @Autowired
    private ExhibitionRelationCarExtMapper carExtMapper;

    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ExhibitionRelationCarDO record) {
        setDefaultValue(record);
        return carExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationCarDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }

        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return carExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ExhibitionRelationCarDO> queryAllByCarRowIds(List<String> carRowIds) {
        return CollectionUtils.isEmpty(carRowIds) ? Collections.emptyList()
                : carExtMapper.queryAllByCarRowIds(carRowIds);
    }

    @Override
    public Map<String, List<ExhibitionRelationCarDO>> getRelationCarListByExhibitionRowIds(Set<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return Collections.emptyMap();
        }

        return carExtMapper.queryAllByExhibitionIds(Lists.newArrayList(exhibitionRowIds))
                .stream().collect(Collectors.groupingBy(ExhibitionRelationCarDO::getExhibitionRowId));
    }

    @Override
    public int batchInsert(List<ExhibitionRelationCarDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        return carExtMapper.batchInsert(records.stream().map(record -> {
            this.setDefaultValue(record);
            return record;
        }).collect(Collectors.toList()));
    }

    @Override
    public int batchUpdate(List<ExhibitionRelationCarDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        String empNo = HeadersProperties.getXEmpNo();
        Date updatedDate = new Date();
        List<ExhibitionRelationCarDO> needUpdateRecords = records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getRowId()))
                .map(record -> {
                    record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(empNo));
                    record.setLastUpdateDate(updatedDate);
                    return record;
                }).collect(Collectors.toList());

        return CollectionUtils.isEmpty(needUpdateRecords) ? ZERO : carExtMapper.batchUpdate(needUpdateRecords);
    }

    @Override
    public int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return ZERO;
        }

        return carExtMapper.softDeleteByExhibitionRowIds(operator, exhibitionRowIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return ZERO;
        }

        List<ExhibitionRelationCarDO> needDeletedRecords = rowIds.stream().map(rowId -> {
            ExhibitionRelationCarDO record = new ExhibitionRelationCarDO();
            record.setRowId(rowId);
            record.setLastUpdatedBy(operator);
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.N.getCode());
            return record;
        }).collect(Collectors.toList());
        return carExtMapper.batchUpdate(needDeletedRecords);
    }

    private void setDefaultValue(ExhibitionRelationCarDO exhibitionRelationCarDO) {
        exhibitionRelationCarDO.setRowId(Optional.ofNullable(exhibitionRelationCarDO.getRowId()).orElse(keyIdService.getKeyId()));
        exhibitionRelationCarDO.setCreatedBy(Optional.ofNullable(exhibitionRelationCarDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationCarDO.setLastUpdatedBy(Optional.ofNullable(exhibitionRelationCarDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationCarDO.setCreationDate(new Date());
        exhibitionRelationCarDO.setLastUpdateDate(new Date());
        exhibitionRelationCarDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }
}
