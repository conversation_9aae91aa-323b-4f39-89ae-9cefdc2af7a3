package com.zte.mcrm.activity.service.activity;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.service.activity.param.ActivityCancelParam;

/**
 * 活动取消
 *
 * <AUTHOR>
 * @date 2023/9/1 下午1:43
 */
public interface ActivityCancelService {

    /**
     * 活动取消
     *
     * @param request
     * @return {@link ServiceData< Void>}
     * <AUTHOR>
     * @date 2023/9/1 下午1:44
     */
    ServiceData<Void> cancel(BizRequest<ActivityCancelParam> request);

    /**
     * 撤销活动关联审批
     * @param activityId    活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/9/3 23:26
     */
    void revokeApproval(String activityId);
}
