package com.zte.mcrm.activity.application.cto.impl;

import com.zte.mcrm.activity.application.cto.CtoPlanReportApIndicatorService;
import com.zte.mcrm.activity.application.model.CtoReportApIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportIndicatorVO;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.enums.CtoPlanProductTypeEnum;
import com.zte.mcrm.activity.common.enums.SaleDivisionEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanOrgFinishRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 全年AP数据统计
 * @author: 罗振6005002932
 * @Date: 2024-12-21
 */
@Service
public class CtoPlanReportApIndicatorServiceImpl implements CtoPlanReportApIndicatorService {

    @Autowired
    private CtoPlanProductFinishRepository productFinishRepository;
    @Autowired
    private CtoPlanOrgFinishRepository orgFinishRepository;

    @Override
    public List<CtoReportApIndicatorVO> ctoPlanApIndicatorSheetData(String ctoPlanInfoId) {
        // 获取数据
        List<CtoReportApIndicatorDO> ctoReportApIndicatorDOS = this.fetchApIndicatorList(ctoPlanInfoId);
        // 遍历产品完成列表，处理每一个产品完成数据并生成报表
        return ctoReportApIndicatorDOS.stream()
                .map(apIndicator -> processApIndicator(apIndicator))
                .collect(Collectors.toList());
    }

    /**
     * 查询AP完成数据
     * @param ctoPlanInfoId
     * @return
     */
    private List<CtoReportApIndicatorDO> fetchApIndicatorList(String ctoPlanInfoId) {
        // 直接使用一个列表来存储最终结果
        List<CtoReportApIndicatorDO> apLists = new ArrayList<>();
        // 分别获取组织和产品的指标列表并添加到最终列表中
        List<CtoReportApIndicatorDO> orgApLists = this.buildOrgApList(orgFinishRepository.listOrgApIndicatorByPlanInfoId(ctoPlanInfoId));
        List<CtoReportApIndicatorDO> productApList = this.buildProductApList(orgFinishRepository.listProductApIndicatorByPlanInfoId(ctoPlanInfoId));
        apLists.addAll(orgApLists);
        apLists.addAll(productApList);
        return apLists;
    }

    /**
     * 构建组织AP完成数
     * @param orgApLists
     * @return
     */
    protected List<CtoReportApIndicatorDO> buildOrgApList(List<CtoReportApIndicatorDO> orgApLists) {
        // orgApLists根据字段assessment过滤，去除为空的数据，然后在根据这个字段转为MAP<STRING ,CtoReportApIndicatorDO>
        Map<String, CtoReportApIndicatorDO> orgApMap = orgApLists.stream().filter(Objects::nonNull)
                .filter(orgApList -> StringUtils.isNotBlank(orgApList.getAssessmentUnit()))
                .collect(Collectors.toMap(CtoReportApIndicatorDO::getAssessmentUnit, Function.identity()));

        List<CtoReportApIndicatorDO> apLists = new ArrayList<>();
        // 统计维度：事业部，握手、国家/客户指标
        for (SaleDivisionEnum division : SaleDivisionEnum.values()) {
            CtoReportApIndicatorDO newModel = new CtoReportApIndicatorDO();
            if(orgApMap.get(division.getCode()) == null){
                newModel.setAssessmentUnit(division.getCode());
                newModel.setYearAPTarget(BigDecimal.ZERO);
                newModel.setYearAPFinish(BigDecimal.ZERO);
            }else {
                newModel = orgApMap.get(division.getCode());;
            }
            apLists.add(newModel);
        }
        return apLists;
    }

    /**
     * 构建产品AP完成数
     * @param productApLists
     * @return
     */
    protected List<CtoReportApIndicatorDO> buildProductApList(List<CtoReportApIndicatorDO> productApLists) {
        // orgApLists根据字段assessment过滤，去除为空的数据，然后在根据这个字段转为MAP<STRING ,CtoReportApIndicatorDO>
        Map<String, CtoReportApIndicatorDO> orgApMap = productApLists.stream().filter(Objects::nonNull)
                .filter(productApList -> StringUtils.isNotBlank(productApList.getAssessmentUnit()))
                .collect(Collectors.toMap(CtoReportApIndicatorDO::getAssessmentUnit, Function.identity()));

        List<CtoReportApIndicatorDO> apLists = new ArrayList<>();
        // 统计维度：事业部，握手、国家/客户指标
        for (CtoPlanProductTypeEnum division : CtoPlanProductTypeEnum.values()) {
            CtoReportApIndicatorDO newModel = new CtoReportApIndicatorDO();
            if(orgApMap.get(division.getCode()) == null){
                newModel.setAssessmentUnit(division.getCode());
                newModel.setYearAPTarget(BigDecimal.ZERO);
                newModel.setYearAPFinish(BigDecimal.ZERO);
            }else {
                newModel = orgApMap.get(division.getCode());;
            }
            apLists.add(newModel);
        }
        return apLists;
    }


    /* Started by AICoder, pid:qc0b02244cr093d14e040839f0fbfe2f3ef0d321 */
    /**
     * 构建sheet页数据
     * @param apIndicator
     * @return
     */
    protected CtoReportApIndicatorVO processApIndicator(CtoReportApIndicatorDO apIndicator) {
        CtoReportApIndicatorVO vo = new CtoReportApIndicatorVO();

        // 使用Optional获取值，如果为空则使用BigDecimal.ZERO
        BigDecimal yearAPTarget = Optional.ofNullable(apIndicator.getYearAPTarget()).orElse(BigDecimal.ZERO);
        BigDecimal yearAPFinish = Optional.ofNullable(apIndicator.getYearAPFinish()).orElse(BigDecimal.ZERO);

        // 计算完成率，避免除以零的情况
        String yearAPFinishRate = !yearAPTarget.equals(BigDecimal.ZERO)
                ? yearAPFinish.multiply(BigDecimal.valueOf(100)).divide(yearAPTarget, 1, RoundingMode.HALF_UP) +
                CharacterConstant.PERCENT : "0%";

        // 设置目标、完成值和完成率
        vo.setYearAPTarget(yearAPTarget.toPlainString());
        vo.setYearAPFinish(yearAPFinish.toPlainString());
        vo.setYearAPFinishRate(yearAPFinishRate);

        return vo;
    }
    /* Ended by AICoder, pid:qc0b02244cr093d14e040839f0fbfe2f3ef0d321 */
}
