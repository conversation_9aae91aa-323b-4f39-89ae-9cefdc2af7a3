package com.zte.mcrm.activity.common.util;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件下载工具类
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
public class DownFileUtil {


    /**
     * 将文件写入到响应流中（备注：通过字节数组的，一般使用于较小文件，建议10M以下；超过的建议读文件、写入响应流）
     *
     * @param byteArrayBody 文件信息
     * @param response      响应流
     * @throws IOException
     */
    public static void downFile2Response(ByteArrayBody byteArrayBody, HttpServletResponse response) throws IOException {
        if (null == byteArrayBody) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_DOWNLOAD_ERROR);
        }

        String fileName = byteArrayBody.getFilename();
        fileName = StringUtils.isBlank(fileName) ? "file" : fileName;
        // 自定义头 解决下载文件名乱码问题
        String encodedFileName = UrlEncoderUtils.hasUrlEncoded(fileName) ? fileName : URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        // 设置请求头 - 前端允许获取的请求头
        response.addHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition,Download-Filename");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + encodedFileName);
        response.addHeader("Download-Filename", encodedFileName);

        byteArrayBody.writeTo(response.getOutputStream());
    }

}
