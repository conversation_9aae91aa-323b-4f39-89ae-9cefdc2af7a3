package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryFeedbackDO;

public interface ActivitySummaryFeedbackMapper {
    /**
     * all field insert
     */
    int insert(ActivitySummaryFeedbackDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivitySummaryFeedbackDO record);

    /**
     * query by primary key
     */
    ActivitySummaryFeedbackDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivitySummaryFeedbackDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivitySummaryFeedbackDO record);
}