package com.zte.mcrm.activity.application.exhibition.convert;

import com.zte.mcrm.activity.application.model.StandardExhibitionDataSource;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.ExhibitionConstant;
import com.zte.mcrm.activity.common.enums.ConferenceTypeEnum;
import com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ConferenceLevelTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionLevelTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionRoomTypeEnum;
import com.zte.mcrm.activity.common.enums.resource.RequiredResourceTypeEnum;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.*;
import com.zte.mcrm.activity.integration.lookupapi.LookUpService;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.mdm.LocalAreaQueryService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.exhibition.*;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDO;
import com.zte.mcrm.activity.repository.rep.resource.ResourceBizExpertRepository;
import com.zte.mcrm.activity.service.common.lookup.CommunicationDirectorComponent;
import com.zte.mcrm.activity.service.common.lookup.model.CommunicationDirectorTwoLevelModel;
import com.zte.mcrm.activity.service.exhibition.convert.ExhibitionRelationResourceConvert;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityZtePeopleVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.*;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.COMMA;
import static com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum.EXHIBITION;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.CONFERENCE_SAVANT;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.EXHIBITION_SAVANT;

/**
 * <AUTHOR>
 */
@Component
public class ExhibitionQuerySupportConvertor {

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private LocalAreaQueryService localAreaQueryService;
    @Autowired
    private ResourceBizExpertRepository resourceBizExpertRepository;
    @Autowired
    private ExhibitionRelationResourceConvert relationResourceConvert;
    @Autowired
    private CommunicationDirectorComponent communicationDirectorComponent;
    @Autowired
    private LookUpExtService lookUpExtService;
    @Autowired
    private LookUpService lookUpService;

    /**
     * 可报名展会数据转换
     *
     * @param data
     * @return
     */
    public PageRows<SelectExhibitionVO> convert2SelectExhibitionVO(StandardExhibitionDataSource data) {
        List<ExhibitionInfoDO> list = data.getExhibitionPage().getRows();

        Map<String, PersonInfoDTO> empMap = new HashMap<>();
        Map<String, MdmAreaDTO> areaMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            empMap.putAll(fetchEmpInfo(list.stream().map(e -> fetchEmpNoFromExhibition(e.getRowId(), data)).flatMap(Collection::stream).collect(Collectors.toSet())));
            Set<String> countryCodes = list.stream().map(ExhibitionInfoDO::getCountryCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Set<String> cityCodes = list.stream().map(ExhibitionInfoDO::getCityCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            areaMap.putAll(localAreaQueryService.queryCountryLocal(MsaRpcRequestUtil.createWithCurrentUser(countryCodes)).getBo());
            areaMap.putAll(localAreaQueryService.queryCity(MsaRpcRequestUtil.createWithCurrentUser(cityCodes)).getBo());
        }

        CommunicationDirectorTwoLevelModel directorTwoLevelModel = communicationDirectorComponent.fetchCommunicationDirectorTwoLevelModel();

        PageRows<SelectExhibitionVO> res = PageRowsUtil.singleBuildPageRow(data.getExhibitionPage(), exhibitionInfo ->
                toSelectExhibitionVO(data, exhibitionInfo, empMap, areaMap, directorTwoLevelModel));

        fillApproveLevelDesc(res.getRows());
        return res;
    }

    /**
     * 转为SelectExhibitionVO
     *
     * @param data
     * @param exhibitionInfo
     * @return
     */
    public SelectExhibitionVO toSelectExhibitionVO(StandardExhibitionDataSource data, ExhibitionInfoDO exhibitionInfo) {
        Map<String, PersonInfoDTO> empMap = fetchEmpInfoFromExhibition(exhibitionInfo.getRowId(), data);

        Map<String, MdmAreaDTO> areaMap = new HashMap<>();
        if (StringUtils.isNotBlank(exhibitionInfo.getCountryCode())) {
            areaMap.putAll(localAreaQueryService.queryCountryLocal(MsaRpcRequestUtil.createWithCurrentUser(Collections.singleton(exhibitionInfo.getCountryCode()))).getBo());
        }

        if (StringUtils.isNotBlank(exhibitionInfo.getCityCode())) {
            areaMap.putAll(localAreaQueryService.queryCity(MsaRpcRequestUtil.createWithCurrentUser(Collections.singleton(exhibitionInfo.getCityCode()))).getBo());
        }

        CommunicationDirectorTwoLevelModel directorTwoLevelModel = communicationDirectorComponent.fetchCommunicationDirectorTwoLevelModel();

        SelectExhibitionVO vo = toSelectExhibitionVO(data, exhibitionInfo, empMap, areaMap, directorTwoLevelModel);

        fillApproveLevelDesc(Collections.singletonList(vo));
        return vo;
    }

    /**
     * 转为SelectExhibitionVO
     *
     * @param data
     * @param exhibitionInfo
     * @param model 专家专业方向模型
     * @return
     */
    private SelectExhibitionVO toSelectExhibitionVO(StandardExhibitionDataSource data, ExhibitionInfoDO exhibitionInfo,
                                                    Map<String, PersonInfoDTO> empMap, Map<String, MdmAreaDTO> areaMap,
                                                    CommunicationDirectorTwoLevelModel model) {
        SelectExhibitionVO vo = new SelectExhibitionVO();
        BeanUtils.copyProperties(exhibitionInfo, vo);

        vo.setTimeZone(exhibitionInfo.getTimezone());
        vo.setStartTime(DateFormatUtil.format(exhibitionInfo.getStartTime(), DateFormatUtil.YYYY_MM_DD_SLASH));
        vo.setEndTime(DateFormatUtil.format(exhibitionInfo.getEndTime(), DateFormatUtil.YYYY_MM_DD_SLASH));
        vo.setExhibitionDays(DateComputerUtil.diffDays(exhibitionInfo.getStartTime(), exhibitionInfo.getEndTime()));
        vo.setResourceList(RequiredResourceTypeEnum.parseRequiredResource(exhibitionInfo.getRequiredResource()));

        List<ExhibitionRelationExpertDO> expertList = data.fetchExhibitionExpert(vo.getRowId());
        List<ExhibitionRelationLeaderDO> leaderList = data.fetchExhibitionLeader(vo.getRowId());
        List<ExhibitionRelationAttachmentDO> attachmentList = data.fetchAttachment(vo.getRowId());

        vo.setExpertList(toExhibitionRelationExpertVO(expertList, empMap, model));
        vo.setLeaderList(toExhibitionRelationLeaderVO(leaderList, empMap));
        vo.setCarList(relationResourceConvert.carDosToVos(data.fetchCar(vo.getRowId())));
        vo.setHotelList(relationResourceConvert.hotelDosToVos(data.fetchHotel(vo.getRowId())));
        vo.setRoomList(toExhibitionRelationRoomVO(data.fetchRoom(vo.getRowId()), empMap));

        if(CollectionUtils.isNotEmpty(vo.getRoomList())){
            vo.getRoomList().stream().forEach(elem ->{
                elem.setRoomTypeName(ExhibitionRoomTypeEnum.getDescByType(elem.getRoomType()));
            });
        }

        if (CollectionUtils.isNotEmpty(attachmentList)) {
            vo.setAttachmentList(attachmentList.stream().map(e -> {
                ExhibitionRelationAttachmentVO attachmentVO = new ExhibitionRelationAttachmentVO();
                BeanUtils.copyProperties(e, attachmentVO);
                return attachmentVO;
            }).collect(Collectors.toList()));
        }

        MdmAreaDTO country = areaMap.get(exhibitionInfo.getCountryCode());
        MdmAreaDTO city = areaMap.get(exhibitionInfo.getCityCode());
        String langId = BizRequestUtil.createWithCurrentUser().getLangId();
        if (country != null) {
            vo.setCountryCodeName(country.fetchName(langId));
        }
        if (city != null) {
            vo.setCityCodeName(city.fetchName(langId));
        }

        return vo;
    }


    /**
     * 数据转换
     *
     * @return
     */
    public JoinExhibitionSelPeopleVO fillJoinExhibitionSelPeopleVO(JoinExhibitionSelPeopleVO vo, StandardExhibitionDataSource data) {
        ExhibitionInfoDO exhibitionInfoDO = data.fetchExhibition(vo.getExhibitionRowId());
        List<ExhibitionRelationExpertDO> expertList = data.fetchExhibitionExpert(vo.getExhibitionRowId());
        List<ExhibitionRelationLeaderDO> leaderList = data.fetchExhibitionLeader(vo.getExhibitionRowId());
        Set<String> bizExperts = resourceBizExpertRepository.selectByEmployeeNos(expertList.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toList()))
                .stream().map(ResourceBizExpertDO::getEmployeeNo).collect(Collectors.toSet());

        Map<String, PersonInfoDTO> empMap = fetchEmpInfoFromExhibition(vo.getExhibitionRowId(), data);

        Map<String, PersonInfoDTO> expertMap = getPersonInfoMap(expertList.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toList()));
        Map<String, PersonInfoDTO> leaderMap = getPersonInfoMap(leaderList.stream().map(ExhibitionRelationLeaderDO::getEmployeeNo).collect(Collectors.toList()));

        CommunicationDirectorTwoLevelModel model = communicationDirectorComponent.fetchCommunicationDirectorTwoLevelModel();
        String expertPeopleLabelName = getExpertPeopleLabelName(Optional.ofNullable(exhibitionInfoDO).orElse(new ExhibitionInfoDO()).getPlaceResourceType());
        vo.setExpertList(expertList.stream().map(e -> toExpertPeopleVO(e, expertMap, model, empMap, bizExperts))
                .peek(expertPeopleVO -> expertPeopleVO.appendPeopleLabelName(expertPeopleLabelName, CharacterConstant.PAUSE_MARK))
                .collect(Collectors.toList()));
        vo.setLeaderList(leaderList.stream().map(e -> toLeaderPeopleVO(e, leaderMap, empMap)).collect(Collectors.toList()));

        return vo;
    }

    public String getExpertPeopleLabelName(String placeResourceType) {
        PlaceResourceTypeEnum placeResourceTypeEnum = PlaceResourceTypeEnum.getEnumByCode(placeResourceType);
        if (Objects.isNull(placeResourceTypeEnum)) {
            return CharacterConstant.EMPTY_STR;
        }

        PeopleRoleLabelEnum peopleRoleLabelEnum = EXHIBITION.isMe(placeResourceTypeEnum.getCode()) ? EXHIBITION_SAVANT : CONFERENCE_SAVANT;
        return peopleRoleLabelEnum.getLabelByLangId(BizRequestUtil.createWithCurrentUser().getLangId());
    }

    /**
     * 转为领导人员信息
     * @param leader
     * @param leaderMap
     * @param empMap
     * @return
     */
    ActivityZtePeopleVO toLeaderPeopleVO(ExhibitionRelationLeaderDO leader, Map<String, PersonInfoDTO> leaderMap, Map<String, PersonInfoDTO> empMap) {
        ActivityZtePeopleVO peopleVO = new ActivityZtePeopleVO();
        peopleVO.setPeopleName(leader.getEmployeeName());
        peopleVO.setPeopleCode(leader.getEmployeeNo());
        // 岗位名称、全称，实际前端未用到
        peopleVO.setPositionName(leader.getPositionName());
        peopleVO.setPostName(leader.getPositionName());
        peopleVO.setPeopleLabel(PeopleRoleLabelEnum.LEADER.getCode());
        peopleVO.setPeopleLabelName(PeopleRoleLabelEnum.LEADER.getLabelByLangId(BizRequestUtil.createWithCurrentUser().getLangId()));
        // 已经在领导的tab页，按新需求不重复展示标签
        peopleVO.setPeopleLabelNameShow(null);
        // 联系电话
        peopleVO.setTelephoneNo(getTelephoneNo(leader.getEmployeeNo(), leaderMap));

        fillActivityZtePeopleVO(peopleVO, empMap.get(leader.getEmployeeNo()));

        return peopleVO;
    }

    /**
     * 转为专家人员信息
     * @param expertDO
     * @param expertMap
     * @param model
     * @param empMap
     * @return
     */
    ActivityZtePeopleVO toExpertPeopleVO(ExhibitionRelationExpertDO expertDO, Map<String, PersonInfoDTO> expertMap, CommunicationDirectorTwoLevelModel model,
                                         Map<String, PersonInfoDTO> empMap, Set<String> bizExperts) {
        ActivityZtePeopleVO peopleVO = new ActivityZtePeopleVO();
        // 展会专家栏
        peopleVO.setPeopleLabel(PeopleRoleLabelEnum.SAVANT.getCode());
        // 如果是业务线专家，拼接
        if (bizExperts.contains(expertDO.getEmployeeNo())) {
            String labelName = PeopleRoleLabelEnum.SAVANT.getLabelByLangId(BizRequestUtil.createWithCurrentUser().getLangId());
            peopleVO.appendPeopleLabelName(labelName, CharacterConstant.PAUSE_MARK);
            // 专家tab只展示产品线业务专家
            peopleVO.setPeopleLabelNameShow(labelName);
        }

        peopleVO.setPeopleCode(expertDO.getEmployeeNo());
        peopleVO.setPeopleName(expertDO.getEmployeeName());
        // 岗位名称、全称，实际前端未用到
        peopleVO.setPositionName(expertDO.getPositionName());
        peopleVO.setPostName(expertDO.getPositionName());
        // 联系电话
        peopleVO.setTelephoneNo(getTelephoneNo(expertDO.getEmployeeNo(), expertMap));

        String communicateDirection = expertDO.getCommunicateDirection();
        if(StringUtils.isNotBlank(communicateDirection)) {
            String[] arr = communicateDirection.split(COMMA);
            peopleVO.setCommunicateDirectionDesc(model.fetchMeaningWithSplit(Arrays.stream(arr).collect(Collectors.toSet()),COMMA));
        }

        peopleVO.setExpertise(expertDO.getExpertise());

        fillActivityZtePeopleVO(peopleVO, empMap.get(expertDO.getEmployeeNo()));
        return peopleVO;
    }

    /**
     * 填充审批层级描述
     * @param list
     */
    void fillApproveLevelDesc(List<SelectExhibitionVO> list) {
        // toSelectExhibitionVO参数不够用了
        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, String> meaningMap = lookUpService.queryMeaningMapByLookupType(MsaRpcRequestUtil.createWithCurrentUser(ExhibitionConstant.EXHIBITION_APPROVE_LEVEL)).getBo();
        Map<String, String> exhibitionResourceTypeMap
                = lookUpService.queryMeaningMapByLookupType(MsaRpcRequestUtil.createWithCurrentUser(ExhibitionConstant.EXHIBITION_RESOURCE_TYPE)).getBo();
        Map<String, String> exhibitionTypeMap
                = lookUpService.queryMeaningMapByLookupType(MsaRpcRequestUtil.createWithCurrentUser(ExhibitionConstant.EXHIBITION_TYPE)).getBo();
        for (SelectExhibitionVO vo : list) {
            vo.setConferenceTypeName(ConferenceTypeEnum.getDescByType(vo.getConferenceType()));
            vo.setExhibitionLevelName(getCurrentLevelName(vo));
            vo.setApproveLevelDesc(meaningMap.get(String.valueOf(vo.getApproveLevel())));
            vo.setExhibitionTypeName(exhibitionTypeMap.get(vo.getExhibitionType()+""));
            Optional.ofNullable(vo.getHotelList()).ifPresent(hotelList -> {
                hotelList.forEach(item -> {
                    item.setResourceTypeName(exhibitionResourceTypeMap.get(item.getResourceType()));
                });
            });
            Optional.ofNullable(vo.getCarList()).ifPresent(carList -> {
                carList.forEach(item -> {
                    item.setResourceTypeName(exhibitionResourceTypeMap.get(item.getResourceType()));
                });
            });
        }
    }

    public String getCurrentLevelName(SelectExhibitionVO  vo){
        if(PlaceResourceTypeEnum.CONFERENCE.isMe(vo.getPlaceResourceType())){
            return ConferenceLevelTypeEnum.getDescByType(vo.getExhibitionLevel());
        }
        return ExhibitionLevelTypeEnum.getDescByType(vo.getExhibitionLevel());
    }


    /**
     * description 获取联系电话
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/18 下午3:08
     */
    String getTelephoneNo(String employNo, Map<String, PersonInfoDTO> personInfoMap) {
        PersonInfoDTO personInfoDTO = personInfoMap.get(employNo);
        if (null != personInfoDTO && CollectionUtils.isNotEmpty(personInfoDTO.getMobile())
                && null != personInfoDTO.getMobile().get(0)) {
            return personInfoDTO.getMobile().get(0).getNumber();
        }
        return Strings.EMPTY;
    }

    /**
     * description 查询员工的人事信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/18 下午3:09
     */
    Map<String, PersonInfoDTO> getPersonInfoMap(List<String> employNos) {
        if (CollectionUtils.isEmpty(employNos)) {
            return Collections.emptyMap();
        }
        Set<String> employNoSet = new HashSet<>();
        for (String employNo : employNos) {
            employNoSet.add(employNo);
        }
        MsaRpcResponse<Map<String, PersonInfoDTO>> mapMsaRpcResponseLeader = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(employNoSet));
        return mapMsaRpcResponseLeader.getBo();
    }

    /**
     * 转为专家VO
     *
     * @param expertList 专家列表
     * @param empMap     员工map
     * @return 专家vo
     */
    private List<ExhibitionRelationExpertVO> toExhibitionRelationExpertVO(List<ExhibitionRelationExpertDO> expertList, Map<String, PersonInfoDTO> empMap,
                                                                          CommunicationDirectorTwoLevelModel model) {
        if (CollectionUtils.isNotEmpty(expertList)) {
            return expertList.stream().map(e -> {
                ExhibitionRelationExpertVO expertVO = new ExhibitionRelationExpertVO();
                BeanUtils.copyProperties(e, expertVO);

                if (StringUtils.isNotBlank(e.getCommunicateDirection())) {
                    expertVO.setCommunicateDirectionDesc(
                            model.fetchMeaningWithSplit(Arrays.stream(e.getCommunicateDirection().split(CharacterConstant.COMMA)).collect(Collectors.toSet()),
                                    CharacterConstant.COMMA)
                    );
                }

                PersonInfoDTO empInfo = empMap.get(e.getEmployeeNo());
                if (empInfo != null) {
                    expertVO.setEmployeeName(empInfo.getEmpName());
                    expertVO.setEmployeeDesc(expertVO.getEmployeeName() + expertVO.getEmployeeNo());
                } else {
                    expertVO.setEmployeeName(e.getEmployeeNo());
                    expertVO.setEmployeeDesc(e.getEmployeeNo());
                }

                return expertVO;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 转为领导VO
     *
     * @param leaderList 领导信息
     * @param empMap     员工map
     * @return 领导vo
     */
    private List<ExhibitionRelationLeaderVO> toExhibitionRelationLeaderVO(List<ExhibitionRelationLeaderDO> leaderList, Map<String, PersonInfoDTO> empMap) {
        if (CollectionUtils.isNotEmpty(leaderList)) {
            return leaderList.stream().map(e -> {
                ExhibitionRelationLeaderVO leaderVO = new ExhibitionRelationLeaderVO();
                BeanUtils.copyProperties(e, leaderVO);

                PersonInfoDTO empInfo = empMap.get(e.getEmployeeNo());
                if (empInfo != null) {
                    leaderVO.setEmployeeName(empInfo.getEmpName());
                    leaderVO.setEmployeeDesc(leaderVO.getEmployeeName() + leaderVO.getEmployeeNo());
                } else {
                    leaderVO.setEmployeeName(e.getEmployeeNo());
                    leaderVO.setEmployeeDesc(e.getEmployeeNo());
                }

                return leaderVO;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * @param roomList
     * @param empMap
     * @return
     */
    List<ExhibitionRelationRoomVO> toExhibitionRelationRoomVO(List<ExhibitionRelationRoomDO> roomList, Map<String, PersonInfoDTO> empMap) {
        if (CollectionUtils.isEmpty(roomList)) {
            return Collections.emptyList();
        }

        List<ExhibitionRelationRoomVO> resList = relationResourceConvert.roomDosToVos(roomList);

        Map<String, Set<String>> room2Leader = roomList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getMainLeader()))
                .collect(Collectors.toMap(
                        ExhibitionRelationRoomDO::getRowId, room -> Arrays.stream(room.getMainLeader().split(COMMA)).collect(Collectors.toSet())));

        for (ExhibitionRelationRoomVO vo : resList) {
            Set<String> leaders = room2Leader.get(vo.getRowId());
            if (CollectionUtils.isNotEmpty(leaders)) {
                vo.setMainLeaders(leaders.stream().map(empNo -> {
                    PersonInfoDTO info = empMap.get(empNo);

                    ExhibitionEmployeeVO empVo = new ExhibitionEmployeeVO();
                    empVo.setEmployeeNo(empNo);
                    empVo.setEmployeeName(CharacterConstant.EMPTY_STR);

                    if (info != null) {
                        empVo.setEmployeeName(info.getEmpName());
                        empVo.setEmployeeNameEn(info.getEmpNameEN());
                        empVo.setPositionName(info.getPostName());
                    }
                    empVo.setEmployeeNoAndName(empVo.getEmployeeName() + empVo.getEmployeeNo());
                    return empVo;
                }).collect(Collectors.toList()));
            }
        }

        return resList;
    }

    /**
     * 填充人员信息
     *
     * @param peopleVO   人员VO
     * @param personInfo HR人员信息
     */
    private void fillActivityZtePeopleVO(ActivityZtePeopleVO peopleVO, PersonInfoDTO personInfo) {
        // 如果可以查到信息，以最新的为准
        if (personInfo != null) {
            peopleVO.setPeopleName(personInfo.getEmpName());
            peopleVO.setPeopleNameEn(personInfo.getEmpNameEN());
            peopleVO.setPositionName(personInfo.getPostName());
            peopleVO.setPostName(personInfo.getPostName());
            peopleVO.setPostFullName(personInfo.getPostFullName());
            peopleVO.setPhotoUrl(personInfo.getPhotoUrl());
            peopleVO.setDeptFullName(personInfo.getOrgFullName());
            peopleVO.setDeptAttribute(personInfo.getOrgName());
        }

    }

    /**
     * 从会议室资源中活动所有人员工编号
     *
     * @param exhibitionRowId
     * @param data
     * @return
     */
    private Set<String> fetchEmpNoFromRoomMainLeader(String exhibitionRowId, StandardExhibitionDataSource data) {

        List<ExhibitionRelationRoomDO> roomList = data.fetchRoom(exhibitionRowId);

        return roomList.stream().map(ExhibitionRelationRoomDO::getMainLeader).filter(StringUtils::isNotBlank)
                .map(e -> Arrays.stream(e.split(COMMA)).collect(Collectors.toList())).
                flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    /**
     * 从某个还在那会信息中获取所有相关人员员工编号
     *
     * @param exhibitionRowId 展会
     * @param data            数据源
     * @return
     */
    private Set<String> fetchEmpNoFromExhibition(String exhibitionRowId, StandardExhibitionDataSource data) {
        List<ExhibitionRelationExpertDO> expertList = data.fetchExhibitionExpert(exhibitionRowId);
        List<ExhibitionRelationLeaderDO> leaderList = data.fetchExhibitionLeader(exhibitionRowId);

        Set<String> empNoSet = new HashSet<>(fetchEmpNoFromRoomMainLeader(exhibitionRowId, data));
        if (CollectionUtils.isNotEmpty(expertList)) {
            empNoSet.addAll(expertList.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(leaderList)) {
            empNoSet.addAll(leaderList.stream().map(ExhibitionRelationLeaderDO::getEmployeeNo).collect(Collectors.toList()));
        }
        return empNoSet;
    }

    /**
     * 获取某展会中关联员工信息
     *
     * @param exhibitionRowId
     * @param data
     * @return
     */
    private Map<String, PersonInfoDTO> fetchEmpInfoFromExhibition(String exhibitionRowId, StandardExhibitionDataSource data) {
        return fetchEmpInfo(fetchEmpNoFromExhibition(exhibitionRowId, data));
    }


    /**
     * 获取
     *
     * @param empNoSet
     * @return
     */
    private Map<String, PersonInfoDTO> fetchEmpInfo(Set<String> empNoSet) {
        MsaRpcResponse<Map<String, PersonInfoDTO>> res = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(empNoSet));
        return res.getBo();
    }
}
