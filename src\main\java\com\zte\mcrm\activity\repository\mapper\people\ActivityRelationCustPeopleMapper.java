package com.zte.mcrm.activity.repository.mapper.people;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityRelationCustPeopleMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationCustPeopleDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationCustPeopleDO record);

    /**
     * query by primary key
     */
    ActivityRelationCustPeopleDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationCustPeopleDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationCustPeopleDO record);

    /**
     * 新工号切换
     */
    List<ActivityRelationCustPeopleDO> queryEmpNoTransList(DataTransParam searchParam);
}