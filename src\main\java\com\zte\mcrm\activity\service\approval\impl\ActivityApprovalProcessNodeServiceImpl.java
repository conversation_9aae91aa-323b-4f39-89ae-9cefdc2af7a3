package com.zte.mcrm.activity.service.approval.impl;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.activity.service.approval.ActivityApprovalProcessNodeService;
import com.zte.mcrm.activity.service.approval.param.ActivityApprovalProcessNodeAddParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 审批节点-子节点 如：合规审批-第一审批人
 * @author: 李龙10317843
 * @create: 2023-05-17 10:46
 * @Version 1.0
 **/
@Service
public class ActivityApprovalProcessNodeServiceImpl implements ActivityApprovalProcessNodeService {
    @Autowired
    private ActivityApprovalProcessNodeRepository activityApprovalProcessNodeRepository;

    @Override
    public void save(BizRequest<ActivityApprovalProcessNodeAddParam> bizRequest) throws Exception {


    }
}