package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.web.controller.activity.vo.CustUnitInfoVO;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityCustomerInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户活动客户信息转换
 *
 * <AUTHOR>
 * @date 2023/5/17 下午5:03
 */
public class ActivityCustomerInfoConvert {

    private ActivityCustomerInfoConvert() {
    }

    /**
     * 转换成VO
     *
     * @param customerInfoDOList
     * @return {@link List< ActivityCustomerInfoVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午5:05
     */
    public static List<ActivityCustomerInfoVO> convert2VO(List<ActivityCustomerInfoDO> customerInfoDOList) {
        if (CollectionUtils.isEmpty(customerInfoDOList)) {
            return Collections.emptyList();
        }
        return customerInfoDOList.stream().map(item -> {
            ActivityCustomerInfoVO vo = new ActivityCustomerInfoVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(ActivityCustomerInfoVO::getCustomerCode))), ArrayList::new));
    }

    /**
     * 转换成InfoVO
     *
     * @param customerInfoDOList    客户信息DO
     * @return {@link List< CustUnitInfoVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午5:05
     */
    public static List<CustUnitInfoVO> convert2InfoVO(List<ActivityCustomerInfoDO> customerInfoDOList) {
        if (CollectionUtils.isEmpty(customerInfoDOList)) {
            return Collections.emptyList();
        }
        return customerInfoDOList.stream().map(item -> {
            CustUnitInfoVO vo = new CustUnitInfoVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(CustUnitInfoVO::getCustomerCode))), ArrayList::new));
    }
}
