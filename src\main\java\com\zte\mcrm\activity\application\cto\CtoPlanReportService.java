package com.zte.mcrm.activity.application.cto;

import com.zte.mcrm.activity.application.model.CtoReportIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportItemIndicatorVO;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;

import java.util.Date;
import java.util.List;

public interface CtoPlanReportService {
    /**
     * 生成名单盘活
     *
     * @param ctoPlanInfoId
     * @param analysisDate
     * @return
     */
    List<CtoReportItemIndicatorVO> generateActivationReport(String ctoPlanInfoId, Date analysisDate);

    /**
     * 活动数据-规划详表覆盖报表sheet数据、核心领导覆盖报表sheet数据
     *
     * @param planId   计划ID
     * @param isLeader 是否领导
     * @return
     */
    List<CtoReportIndicatorVO> generatePlanDetailReport(String planId, boolean isLeader);

    /**
     * 将CTO报表发送邮件给接收人
     *
     * @param req 入参
     */
    void generateAndSendEmailCTOReport(BizRequest<CtoPlanExportParam> req);
}
