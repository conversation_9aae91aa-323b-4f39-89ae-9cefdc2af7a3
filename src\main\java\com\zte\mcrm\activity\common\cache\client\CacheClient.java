package com.zte.mcrm.activity.common.cache.client;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * 缓存使用客户端
 * <pre>
 *   关于fetchAndTrans方法的使用
 *   1、其各个实现类的扩展方法本质上就是对fetchAndTrans的扩张
 *   2、可以不扩展fetchAndTrans
 *   3、在各实现类中，1和2的方式都有，为了展示写法罢了
 *   4、在客户端如果有不满足的方法，可以调用fetchAndTrans方法，传入对应转换函数即可
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-09-20
 */
public interface CacheClient<V> {

    /**
     * 获取缓存数据
     *
     * @param key key
     * @return 缓存数据
     */
    V fetchCache(String key);

    /**
     * 批量获取缓存数据
     *
     * @param keys key
     * @return 缓存数据
     */
    Map<String, V> fetchAllCache(Set<String> keys);

    /**
     * 主动添加缓存
     *
     * @param key key
     * @param val value
     */
    void addCache(String key, V val);

    /**
     * 删除缓存
     *
     * @param key 缓存key
     */
    void remove(String key);

    /**
     * 删除所有缓存
     */
    boolean removeAll();

    /**
     * 不走缓存
     * @param key
     * @return
     */
    default V fetchCacheWithoutCache(String key) {
        // 将缓存删除，重新加载
        remove(key);
        return fetchCache(key);
    }

    /**
     * 获取缓存数据并返回转换后的数据
     * @param key key
     * @param trans 转换规则
     * @param <T> 转换后的数据类型
     * @return 转换后的数据
     */
    default <T> T fetchAndTrans(String key, Function<V, T> trans){
        V val = fetchCache(key);

        return val == null ? null : trans.apply(val);
    }
}
