package com.zte.mcrm.activity.repository.rep.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryFeedbackDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-12-08 14:14
 **/
public interface ActivitySummaryFeedbackRepository {
    /**
     * 添加关联的兴管家反馈（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivitySummaryFeedbackDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivitySummaryFeedbackDO record);

    /**
     * 查询活动关联的所有兴管家反馈
     *
     * @param activityRowIds 活动RowIds
     * @return
     */
    Map<String, List<ActivitySummaryFeedbackDO>> queryAllFeedbackForActivity(List<String> activityRowIds);

}
