package com.zte.mcrm.activity.application.icrm.convert;

import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.auth.CustomerIntegrationAuthModel;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.LookupConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.DateComputerUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.ap.ApSystemService;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtServiceImpl;
import com.zte.mcrm.activity.integration.rdc.RdcService;
import com.zte.mcrm.activity.integration.rdc.dto.RdcItemDto;
import com.zte.mcrm.activity.integration.rdc.dto.RdcPeopleDesDto;
import com.zte.mcrm.activity.integration.rdc.dto.SystemStateDto;
import com.zte.mcrm.activity.integration.rdc.dto.WantBelongProductCategoryDto;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;
import com.zte.mcrm.activity.web.controller.icrm.param.CustomerRelationParam;
import com.zte.mcrm.activity.web.controller.icrm.vo.CustomerRelationActivityApVO;
import com.zte.mcrm.activity.web.controller.icrm.vo.CustomerRelationActivityRdcVO;
import com.zte.mcrm.activity.web.controller.icrm.vo.CustomerRelationActivityVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.PAUSE_MARK;

/**
 * 客户关联活动相关信息转换
 *
 * <AUTHOR>
 */
@Component
public class CustomerRelationActivityConvert {
    @Autowired
    private RdcService rdcService;
    @Autowired
    private ApSystemService apSystemService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private LookUpExtServiceImpl lookUpExtService;
    @Autowired
    private ActivityUrlConfig activityUrlConfig;
    @Autowired
    private HrmOrgInfoSearchService hrmOrgInfoSearchService;

    /**
     * @return
     */
    public List<CustomerRelationActivityVO> toCustomerRelationActivityVO(BizRequest<CustomerRelationParam> request, StandardActivityDetailDataSource ds) {
        List<ActivityInfoDO> activityList = ds.getActivityInfoList();
        if (CollectionUtils.isEmpty(activityList)) {
            return Collections.emptyList();
        }
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(request.getEmpNo());
        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);
        Map<String, String> extensionTypeMap = lookUpExtService.getLookUpMapByParentType(LookupConstant.PARENT_LOOKUP_TYPE_EXTENSION_TYPE);
        Map<String, OrgInfoVO> orgMap = hrmOrgInfoSearchService.getOrgInfoByOrgIds(activityList.stream()
                .map(ActivityInfoDO::getApplyDepartmentNo).filter(StringUtils::isNotBlank).collect(Collectors.toList()));

        return activityList.stream().map(ac -> {
            CustomerRelationActivityVO vo = new CustomerRelationActivityVO();
            vo.setDetailUrl(activityUrlConfig.fetchDetailUrl(ac.getRowId(), ac.getActivityType(), "first"));
            vo.setActivityAuth(BooleanEnum.getCode(authModel.viewable(ds, ac.getRowId())));
            vo.setActivityRowId(ac.getRowId());
            vo.setActivityTitle(ac.getActivityTitle());
            vo.setEndTime(ac.getEndTime());
            vo.setStartTime(ac.getStartTime());
            vo.setActivityRequestNo(ac.getActivityRequestNo());
            vo.setActivityStatus(ac.getActivityStatus());
            vo.setActivityStatusName(statusMap.get(ac.getActivityStatus()));
            vo.setActivityType(ac.getActivityType());
            vo.setActivityTypeName(extensionTypeMap.get(ac.getActivityType()));

            fillCustomerInfo(ds, ac, vo);
            fillZteInfo(ds, ac, vo);
            fillApplyInfo(ds, ac, vo, orgMap);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 填充我司相关信息
     *
     * @param ds
     * @param ac
     * @param vo
     */
    void fillZteInfo(StandardActivityDetailDataSource ds, ActivityInfoDO ac, CustomerRelationActivityVO vo) {
        List<ActivityRelationZtePeopleDO> zteList = ds.fetchActivityZtePeople(ac.getRowId(), ActivityPeopleTypeEnum.allParticipantsType());
        zteList = CollectionExtUtils.getListOrDefaultEmpty(zteList);
        List<String> zteNoList = new ArrayList<>(zteList.size());
        List<String> zteNameList = new ArrayList<>(zteList.size());
        List<String> zteDescList = new ArrayList<>(zteList.size());
        for (ActivityRelationZtePeopleDO zte : zteList) {
            zteNoList.add(zte.getPeopleCode());
            zteNameList.add(zte.getPeopleName());
            zteDescList.add(zte.getPeopleName() + zte.getPeopleCode());
        }
        vo.setZteEmpNoList(zteNoList);
        vo.setZteEmpNameList(zteNameList);
        vo.setZteEmpDesc(String.join(CharacterConstant.COMMA, zteDescList));
    }

    /**
     * 填充申请相关信息
     *
     * @param ds
     * @param ac
     * @param vo
     */
    void fillApplyInfo(StandardActivityDetailDataSource ds, ActivityInfoDO ac, CustomerRelationActivityVO vo, Map<String, OrgInfoVO> orgMap) {
        // 在活动关联人员中肯定只有一条信息。如果找不到则新建活动那有问题
        List<ActivityRelationZtePeopleDO> applyList = ds.fetchActivityZtePeople(ac.getRowId(), ActivityPeopleTypeEnum.APPLICANT)
                .stream().filter(e -> StringUtils.equals(ac.getApplyPeopleNo(), e.getPeopleCode())).collect(Collectors.toList());
        if (!applyList.isEmpty()) {
            vo.setApplyEmpName(applyList.get(0).getPeopleName());
        }
        vo.setApplyEmpNo(ac.getApplyPeopleNo());
        vo.setApplyEmpDesc(vo.getApplyEmpName() + vo.getApplyEmpNo());
        vo.setApplyOrgNo(ac.getApplyDepartmentNo());
        vo.setApplyOrgName(ac.getApplyDepartmentNo());
        OrgInfoVO org = orgMap.get(ac.getApplyDepartmentNo());
        if (org != null) {
            vo.setApplyOrgName(org.getHrOrgName());
            vo.setApplyOrgFullName(org.getHrOrgNamePath());
        }
    }

    /**
     * 填充客户相关信息
     *
     * @param ds
     * @param ac
     * @param vo
     */
    void fillCustomerInfo(StandardActivityDetailDataSource ds, ActivityInfoDO ac, CustomerRelationActivityVO vo) {
        ActivityCustomerInfoDO mainCust = ds.fetchActivityMainCustomerInfo(ac.getRowId());
        if (mainCust != null) {
            vo.setMainCustomerCode(mainCust.getCustomerCode());
            vo.setMainCustomerName(mainCust.getCustomerName());
        }

        List<ActivityRelationCustPeopleDO> mainContactList = ds.fetchActivityMainCustPeople(ac.getRowId());
        List<String> mainContactNoList = new ArrayList<>(mainContactList.size());
        List<String> mainContactNameList = new ArrayList<>(mainContactList.size());
        for (ActivityRelationCustPeopleDO contact : mainContactList) {
            mainContactNoList.add(contact.getContactNo());
            mainContactNameList.add(contact.getContactName());
        }
        vo.setMainContactNoList(mainContactNoList);
        vo.setMainContactNameList(mainContactNameList);
        vo.setMainContactDesc(String.join(CharacterConstant.COMMA, mainContactNameList));
    }

    /**
     * @param request
     * @param ds
     * @return
     */
    public List<CustomerRelationActivityApVO> toCustomerRelationActivityApVO(BizRequest<CustomerRelationParam> request, StandardActivityDetailDataSource ds) {
        List<ActivityInfoDO> activityList = ds.getActivityInfoList();
        if (CollectionUtils.isEmpty(activityList)) {
            return Collections.emptyList();
        }

        Map<String, List<ApTaskVO>> activityApMap = fetchApTaskInfo(request, ds, activityList).stream().collect(Collectors.groupingBy(ApTaskVO::getEventId));
        if (activityApMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<CustomerRelationActivityApVO> apVoList = new ArrayList<>(activityApMap.size() * NumberConstant.TWO);
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(request.getEmpNo());

        for (ActivityInfoDO ac : activityList) {
            List<ApTaskVO> apList = activityApMap.get(ac.getRowId());
            if (CollectionUtils.isNotEmpty(apList)) {
                for (ApTaskVO ap : apList) {
                    apVoList.add(packCustomerRelationActivityApVO(ds, ac, ap, authModel));
                }
            }
        }

        // AP截止时间剩余天数升序
        return apVoList.stream().sorted(Comparator.comparingInt(CustomerRelationActivityApVO::getLeaveDay)).collect(Collectors.toList());
    }

    /**
     * 打包AP信息
     *
     * @param ds
     * @param ac
     * @param ap
     * @param authModel
     * @return
     */
    CustomerRelationActivityApVO packCustomerRelationActivityApVO(StandardActivityDetailDataSource ds, ActivityInfoDO ac, ApTaskVO ap, CustomerIntegrationAuthModel authModel) {
        CustomerRelationActivityApVO vo = new CustomerRelationActivityApVO();
        vo.setActivityAuth(BooleanEnum.getCode(authModel.viewable(ds, ac.getRowId())));
        vo.setDetailUrl(activityUrlConfig.fetchDetailUrl(ac.getRowId(), ac.getActivityType(), "first"));
        vo.setActivityRowId(ac.getRowId());
        vo.setActivityTitle(ac.getActivityTitle());
        vo.setActivityRequestNo(ac.getActivityRequestNo());

        vo.setApLightStatus(String.valueOf(ap.getLightStatus()));
        vo.setApLightStatusName(String.valueOf(ap.getLightStatus()));
        vo.setApNo(ap.getApNo());
        vo.setApName(ap.getApName());
        vo.setApStatus(String.valueOf(ap.getApStatus()));
        vo.setApStatusName(String.valueOf(ap.getApStatus()));
        vo.setApResponsible(ap.getResponsiblePersionNo());
        vo.setApResponsibleName(ap.getResponsiblePersionZh());
        vo.setApResponsibleDesc(vo.getApResponsibleName() + vo.getApResponsible());
        // 编码被注释了
        vo.setApResponsibleOrg(ap.getResponsibleUnitZh());
        vo.setApResponsibleOrgName(ap.getResponsibleUnitZh());
        vo.setApResponsibleOrgFullName(ap.getResponsibleUnitZh());

        vo.setApStartTime(ap.getBeginTime());
        vo.setApEndTime(ap.getEndTime());
        vo.setLeaveDay(DateComputerUtil.leaveDay(ap.getEndTime()));

        return vo;
    }

    /**
     * 获取AP任务信息
     *
     * @param request
     * @param ds
     * @param activityList
     * @return
     */
    List<ApTaskVO> fetchApTaskInfo(BizRequest<CustomerRelationParam> request, StandardActivityDetailDataSource ds, List<ActivityInfoDO> activityList) {
        Set<String> eventIds = new HashSet<>();
        for (ActivityInfoDO ac : activityList) {
            List<ActivitySummaryApDO> apList = ds.fetchSummaryAp(ac.getRowId());
            if (CollectionUtils.isNotEmpty(apList)) {
                eventIds.addAll(apList.stream().map(ActivitySummaryApDO::getEventId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
        }

        return apSystemService.fetchApTaskInfo(MsaRpcRequestUtil.createWithBizReq(request, e -> eventIds));
    }

    /**
     * 数据转换
     *
     * @param request
     * @param ds
     * @return
     */
    public List<CustomerRelationActivityRdcVO> toCustomerRelationActivityRdcVO(BizRequest<CustomerRelationParam> request, StandardActivityDetailDataSource ds) {
        Map<String, List<ActivitySummaryRdcDO>> rdcMap = ds.getSummaryRdcMap();
        if (MapUtils.isEmpty(rdcMap)) {
            return Collections.emptyList();
        }

        Set<String> rdcIds = rdcMap.values().stream().flatMap(Collection::stream).map(ActivitySummaryRdcDO::getRdcId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (rdcIds.isEmpty()) {
            return Collections.emptyList();
        }

        return queryRdcInfo(BizRequestUtil.copyRequest(request, e -> (rdcIds)));
    }

    /**
     * 查询rdc信息
     *
     * @param request rdcId列表
     * @return
     */
    public List<CustomerRelationActivityRdcVO> queryRdcInfo(BizRequest<Set<String>> request) {
        MsaRpcRequest<Set<String>> rdcRequest = MsaRpcRequestUtil.createWithBizReq(request);
        MsaRpcResponse<List<RdcItemDto>> rdcRes = rdcService.queryRdcRr(rdcRequest);
        List<RdcItemDto> bo = rdcRes.getBo();
        if (CollectionUtils.isEmpty(bo)) {
            return Collections.emptyList();
        }

        return bo.stream().map(this::toCustomerRelationActivityRdcVO).collect(Collectors.toList());
    }

    /**
     * @param info
     * @return
     */
    CustomerRelationActivityRdcVO toCustomerRelationActivityRdcVO(RdcItemDto info) {
        CustomerRelationActivityRdcVO vo = new CustomerRelationActivityRdcVO();

        SystemStateDto systemStateDto = info.getSystemState();
        if (null != systemStateDto) {
            vo.setStatus(systemStateDto.getName());
            vo.setStatusZh(systemStateDto.getNameZh());
            vo.setStatusEn(systemStateDto.getNameEn());
        }

        vo.setRdcId(info.getId());
        vo.setTitle(info.getSystemTitle());
        vo.setSubmitTime(info.getSubmitTime());
        vo.setExpectFinishTime(info.getExpectedFinishDate());
        vo.setLeaveDay(DateComputerUtil.leaveDay(info.getExpectedFinishDate()));

        WantBelongProductCategoryDto productCategoryDto = info.getWantBelongProductCategory();
        if (null != productCategoryDto) {
            vo.setProductLine(productCategoryDto.getName());
        }

        RdcPeopleDesDto createdByDes = info.getSystemCreatedBy();
        if (null != createdByDes) {
            vo.setCreatedByDes(createdByDes.getNameDisplayLongZh());
            vo.setCreatedBy(createdByDes.getUserId());
            vo.setCreatedName(createdByDes.getName());
        }

        List<RdcPeopleDesDto> rdcPeopleDesList = info.getSystemAssignedTo();
        if (CollectionUtils.isNotEmpty(rdcPeopleDesList)) {
            vo.setOperatorDes(rdcPeopleDesList.stream().map(RdcPeopleDesDto::getNameDisplayLong).collect(Collectors.joining(PAUSE_MARK)));
        }
        return vo;
    }


}
