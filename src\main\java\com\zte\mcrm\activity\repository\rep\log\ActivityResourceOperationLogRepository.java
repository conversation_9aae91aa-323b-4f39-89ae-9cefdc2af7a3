package com.zte.mcrm.activity.repository.rep.log;

import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;

import java.util.List;
import java.util.Map;

/**
 * 客户活动资源操作日志DAO
 *
 * <AUTHOR>
 * @date 2024/11/13 上午10:36
 */
public interface ActivityResourceOperationLogRepository {

    /**
     * 根据业务id批量查询操作日志
     *
     * @param bizRelatedIdList  业务id
     * @param bizType           业务类型
     * @return {@link Map<String, List<ActivityResourceOperationLogDO>>}
     * <AUTHOR>
     * @date 2024/11/13 下午5:20
     */
    Map<String, List<ActivityResourceOperationLogDO>> selectByBizRelatedIdList(List<String> bizRelatedIdList, String bizType);

    /**
     * 批量新增操作日志
     *
     * @param list 操作日志
     * @return {@link int}
     */
    int batchInsert(List<ActivityResourceOperationLogDO> list);
}
