package com.zte.mcrm.activity.integration.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.http.HttpUtil;
import com.zte.mcrm.activity.common.http.param.PostHttpRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.MsaRpcResponseUtil;
import com.zte.mcrm.activity.integration.ai.dto.SummaryAbstractDTO;
import com.zte.mcrm.activity.integration.ai.param.SummaryAbstractParam;
import com.zte.mcrm.custcomm.common.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * AI生成摘要
 *
 * <AUTHOR>
 * @date 2025/3/4 下午1:44
 */
@Slf4j
@Component
public class AiAbstractService {

    @Value("${ai.activity.summary.abstract.generate.url:}")
    private String aiActivitySummaryAbstractGenerateUrl;

    /**
     * socket读取超时时间
     */
    private static final int MAX_SOCKET_TIMEOUT = 1000 * 60 * 5;

    /**
     * 生成会议纪要摘要
     *
     * @param request
     * @return {@link MsaRpcResponse<SummaryAbstractDTO>}
     * <AUTHOR>
     * @date 2025/3/4 下午5:13
     */
    public MsaRpcResponse<SummaryAbstractDTO> generateActivitySummaryAbstract(MsaRpcRequest<SummaryAbstractParam> request) {
        try {
            SummaryAbstractParam body = request.getBody();
            log.info("开始生成会议纪要摘要，入参: {}", JSON.toJSONString(body));
            PostHttpRequest post = PostHttpRequest.createWithJsonRestful(aiActivitySummaryAbstractGenerateUrl, body);
            post.setSocketTimeOut(MAX_SOCKET_TIMEOUT);
            post.addHeader(request.fetchHeaderMap());
            MsaRpcResponse<SummaryAbstractDTO> response = HttpUtil.postHttpWithStringRes(post, this::trans2SummaryAbstractDTO);
            log.info("开始生成会议纪要摘要，出参: {}", JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("生成活动会议纪要总结异常, param:{}", request.getBody(), e);
        }
        return MsaRpcResponseUtil.fail(RetCode.BUSINESSERROR_MSGID);
    }

    /**
     * 转换返回结果
     *
     * @param json
     * @return {@link MsaRpcResponse<SummaryAbstractDTO>}
     * <AUTHOR>
     * @date 2025/3/5 下午7:50
     */
    protected MsaRpcResponse<SummaryAbstractDTO> trans2SummaryAbstractDTO(String json) {
        json = JsonSanitizer.sanitize(json);
        ServiceData<SummaryAbstractDTO> serviceData = JSON.parseObject(json, new TypeReference<ServiceData<SummaryAbstractDTO>>() {});
        return MsaRpcResponseUtil.successRes(serviceData.getBo());
    }

}
