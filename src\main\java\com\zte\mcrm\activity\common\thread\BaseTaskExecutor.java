package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.*;

/**
 * 任务执行器基类
 * <pre>
 *     相关设计说明见：<a href="https://i.zte.com.cn/#/space/51c1f5f86d7e48cdaa8a7cc507dac49c/wiki/page/c2070c14792946b98090c897ac0acce5/view">设计</a>
 * </pre>
 *
 * <AUTHOR>
 */
public abstract class BaseTaskExecutor implements TaskExecutor {
    private String taskName;
    private int corePoolSize;
    private int maxPoolSize;
    private int keepAliveTimeSecond;
    private BlockingQueue<Runnable> taskList;
    private RejectedExecutionHandler rejectedHandler;
    private volatile ExecutorService executorService;

    public BaseTaskExecutor(String taskName, RejectedExecutionHandler rejectedHandler) {
        this.taskName = taskName;
        this.rejectedHandler = rejectedHandler;
    }

    @Override
    public void addTask(Runnable task) {
        init();
        if (task != null) {
            executorService.execute(task);
        }
    }

    @Override
    public <V> Future<V> addTask(Callable<V> task) {
        init();
        return executorService.submit(task);
    }

    /**
     * 初始化参数
     *
     * @param corePoolSize
     * @param maxPoolSize
     * @param keepAliveTimeSecond
     * @param taskList
     */
    protected void initParam(int corePoolSize, int maxPoolSize, int keepAliveTimeSecond, BlockingQueue<Runnable> taskList) {
        // 因为sonar扫描不允许超过五个参数，所以将构造器中的参数减少几个，由子类自行调用
        this.corePoolSize = corePoolSize;
        this.maxPoolSize = maxPoolSize;
        this.keepAliveTimeSecond = keepAliveTimeSecond;
        this.taskList = taskList;
    }


    /**
     * 初始化
     */
    private void init() {
        if (executorService == null) {
            synchronized (this) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTimeSecond,
                            TimeUnit.SECONDS, taskList, new TaskExecutorThreadFactory(taskName), rejectedHandler);
                }
            }
        }
    }

}
