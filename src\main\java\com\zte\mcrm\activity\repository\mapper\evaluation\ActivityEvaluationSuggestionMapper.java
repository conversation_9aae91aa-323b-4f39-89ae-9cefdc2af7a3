package com.zte.mcrm.activity.repository.mapper.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationSuggestionDO;

public interface ActivityEvaluationSuggestionMapper {
    /**
     * all field insert
     */
    int insert(ActivityEvaluationSuggestionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityEvaluationSuggestionDO record);

    /**
     * query by primary key
     */
    ActivityEvaluationSuggestionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityEvaluationSuggestionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityEvaluationSuggestionDO record);
}