package com.zte.mcrm.activity.application.exhibition.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.exhibition.ExhibitionCreateAppService;
import com.zte.mcrm.activity.application.exhibition.ExhibitionDetailsAppService;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.ExhibitionConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.HrInfoBlockEnum;
import com.zte.mcrm.activity.common.enums.PlaceResourceTypeEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionRoomTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.lookupapi.LookUpService;
import com.zte.mcrm.activity.integration.lookupapi.param.FastLookupSearchParamVO;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.model.PersonInfoModel;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDirectionDO;
import com.zte.mcrm.activity.service.exhibition.ExhibitionQueryService;
import com.zte.mcrm.activity.service.resource.ResourceBizExpertService;
import com.zte.mcrm.activity.service.resource.business.ResourceBusiness;
import com.zte.mcrm.activity.web.controller.exhibition.vo.*;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.MdmAdapter;
import com.zte.mcrm.adapter.constant.AreaConstant;
import com.zte.mcrm.adapter.constant.ExternalConstant;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.MdmAreaVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.adapter.constant.ExternalServiceConstant.SeparativeSign.COMMON;

/**
 * <AUTHOR>
 * @title: ExhibitionDetailsAppServiceImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会详情接口
 * @date 2023/9/1114:35
 */
@Service
public class ExhibitionDetailsAppServiceImpl implements ExhibitionDetailsAppService {
    private static final Logger logger = LoggerFactory.getLogger(ExhibitionDetailsAppServiceImpl.class);
    @Autowired
    private ExhibitionQueryService queryService;
    @Autowired
    private ExhibitionCreateAppService createAppService;
    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private LookUpService lookUpService;
    @Autowired
    private MdmAdapter mdmAdapter;
    @Autowired
    private ResourceBizExpertService resourceBizExpertService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;

    @Override
    public ExhibitionDetailsVO getExhibitionDetails(String exhibitionRowId) {
        //获取展会信息
        ExhibitionDetailsVO exhibitionDetails = queryService.getExhibitionDetails(exhibitionRowId);
        //完善展会信息
        perfectExhibition(exhibitionDetails);

        return exhibitionDetails;
    }

    /**
     * 完善展会详情信息
     * @param exhibitionDetails 展会详情
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    @Override
    public void perfectExhibition(ExhibitionDetailsVO exhibitionDetails) {
        //1、完善展角色
        Map<String, String> roleResponseMap = masRpcResponseMap(ExhibitionConstant.EXHIBITION_DIRECTOR_ROLE);
        //2、用户信息
        Map<String, PersonInfoDTO> userInfoMapByEmp = getUserInfoMapByEmp(exhibitionDetails);
        //3、展会基本信息
        perfectBasicInfo(exhibitionDetails);
        //4、展会负责人信息完善
        perfectDirectorDetails(exhibitionDetails.getDirectorDetailsList(), roleResponseMap, userInfoMapByEmp);
        //5、展会领导信息完善
        perfectLeaderDetails(exhibitionDetails.getLeaderDetailsList(), userInfoMapByEmp);
        //6、展会专家信息完善
        perfectExpertDetails(exhibitionDetails.getExpertDetailsList(), userInfoMapByEmp);
        //7、会议室信息完善
        perfectRoomDetails(exhibitionDetails,userInfoMapByEmp);
        // 酒店车辆资源信息
        perfectResourceDetail(exhibitionDetails);
    }

    /**
     * 完善酒店车辆资源信息
     *
     * @param exhibitionDetails
     * <AUTHOR>
     * @date 2024/10/25 下午4:40
     */
    private void perfectResourceDetail(ExhibitionDetailsVO exhibitionDetails) {
        Map<String, String> exhibitionResourceTypeMap = masRpcResponseMap(ExhibitionConstant.EXHIBITION_RESOURCE_TYPE);
        Map<String, List<ExhibitionRelationHotelDetailsVO>> hotelDetailsMap = exhibitionDetails.getHotelDetailsMap();
        Optional.ofNullable(hotelDetailsMap).ifPresent(map -> {
            map.forEach((key, value) -> {
                Optional.ofNullable(value).ifPresent(hotelDetails -> {
                    hotelDetails.forEach(item -> item.setResourceTypeName(exhibitionResourceTypeMap.get(item.getResourceType())));
                });
            });
        });
        List<ExhibitionRelationCarDetailsVO> carDetailsList = exhibitionDetails.getCarDetailsList();
        Optional.ofNullable(carDetailsList).ifPresent(carDetails -> {
            carDetails.forEach(item -> item.setResourceTypeName(exhibitionResourceTypeMap.get(item.getResourceType())));
        });
    }

    private void perfectRoomDetails(ExhibitionDetailsVO exhibitionDetails,Map<String, PersonInfoDTO> userInfoMapByEmp) {
        List<ExhibitionRelationRoomDetailsVO> roomDetailsList = exhibitionDetails.getRoomDetailsList();
        if (CollectionUtils.isNotEmpty(roomDetailsList)){
            roomDetailsList.forEach(room ->{
                room.setRoomTypeName(ExhibitionRoomTypeEnum.getDescByType(room.getRoomType()));
                room.setEmployeeList(getRoomMainLeaderList(userInfoMapByEmp,room));
            });
        }
    }

    /**
     * 获取员工信息
     * @param userInfoMapByEmp
     * @param roomDetailsVo
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    public List<ExhibitionEmployeeVO> getRoomMainLeaderList(Map<String, PersonInfoDTO> userInfoMapByEmp, ExhibitionRelationRoomDetailsVO roomDetailsVo) {
        List<ExhibitionEmployeeVO> employeeList = new ArrayList<>();
        String employeeNoStr = roomDetailsVo.getMainLeader();
        if (StringUtils.isNotBlank(employeeNoStr)){
            List<String> empNoList = Arrays.asList(employeeNoStr.split(CharacterConstant.COMMA));
            empNoList.forEach(empNo ->{
                PersonInfoDTO personInfoDTO = userInfoMapByEmp.get(empNo);
                if (Objects.nonNull(personInfoDTO)){
                    ExhibitionEmployeeVO exhibitionEmployeeVO = new ExhibitionEmployeeVO();
                    exhibitionEmployeeVO.setEmployeeName(personInfoDTO.getEmpName());
                    exhibitionEmployeeVO.setEmployeeNameEn(personInfoDTO.getEmpNameEN());
                    exhibitionEmployeeVO.setEmployeeNo(empNo);
                    exhibitionEmployeeVO.setEmployeeNoAndName((StringUtils.join(personInfoDTO.getEmpName(), empNo)));
                    exhibitionEmployeeVO.setPositionName(personInfoDTO.getPostName());
                    employeeList.add(exhibitionEmployeeVO);
                }
            });
        }
        return employeeList;
    }

    /**
     * 完善展会基本信息
     * @param exhibitionDetails
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    public void perfectBasicInfo(ExhibitionDetailsVO exhibitionDetails) {
        ExhibitionInfoDetailsVO infoDetailsVO = exhibitionDetails.getInfoDetailsVO();
        Map<String, String> levelResponseMap = masRpcResponseMap(getCurrentLevelType(infoDetailsVO.getPlaceResourceType()));
        infoDetailsVO.setExhibitionLevelName(levelResponseMap.get(infoDetailsVO.getExhibitionLevel()));
        Map<String, String> approveResponseMap = masRpcResponseMap(ExhibitionConstant.EXHIBITION_APPROVE_LEVEL);
        Map<String, String> exhibitionTypeMap = masRpcResponseMap(ExhibitionConstant.EXHIBITION_TYPE);
        infoDetailsVO.setApproveLevelName(approveResponseMap.get(infoDetailsVO.getApproveLevel().toString()));
        infoDetailsVO.setExhibitionTypeName(exhibitionTypeMap.get(infoDetailsVO.getExhibitionType()+""));
        List<MdmAreaDTO> mdmAreaCountryList = getMdmArea(infoDetailsVO.getCountryCode(), AreaConstant.AREA_TYPE_COUNTRY);
        if (CollectionUtils.isNotEmpty(mdmAreaCountryList)){
            infoDetailsVO.setCountryName(mdmAreaCountryList.get(NumberConstant.ZERO).getDesc3());
        }
        if (StringUtils.isNotBlank(infoDetailsVO.getCityCode())){
            List<MdmAreaDTO> mdmAreaCityList = getMdmArea(infoDetailsVO.getCityCode(),AreaConstant.AREA_TYPE_CITY);
            if (CollectionUtils.isNotEmpty(mdmAreaCityList)){
                infoDetailsVO.setCityName(mdmAreaCityList.get(NumberConstant.ZERO).getDesc3());
            }
        }
    }

    /**
     * 判断当前是大会还是展会
     * @param type
     * @return
     */
    public String getCurrentLevelType(String type) {
        if (PlaceResourceTypeEnum.CONFERENCE.isMe(type)) {
            return ExhibitionConstant.CONFERENCE_LEVEL;
        }
        //是展会
        return ExhibitionConstant.EXHIBITION_LEVEL;
    }

    /**
     * 根据MDM编码获取数据
     * @param areaType 编码级别
     * @param areaCode 编码
     * @return
     * <AUTHOR>
     * @date 2023/9/20
     */
    private List<MdmAreaDTO> getMdmArea(String areaCode,String areaType) {
        MdmAreaVO mdmAreaVO = new MdmAreaVO();
        mdmAreaVO.setAreaDefaultUrl(AreaConstant.QUERY_SERACH_GJDQ);
        mdmAreaVO.setSynCode(AreaConstant.QUERY_SERACH_GJDQ_SYS_CODE);
        mdmAreaVO.setCode(areaCode);
        mdmAreaVO.setDesc2(areaType);
        return mdmAdapter.queryAreaInfo(mdmAreaVO);
    }

    /**
     * 根据块码类型获取块码
     * @param lookupType
     * @return
     * <AUTHOR>
     * @date 2023/9/19
     */
    private Map<String, String> masRpcResponseMap(String lookupType){
        FastLookupSearchParamVO paramVO = new FastLookupSearchParamVO();
        paramVO.setLookupType(lookupType);
        paramVO.setLang(BizRequestUtil.createWithCurrentUser().getLangId());
        MsaRpcRequest<FastLookupSearchParamVO> request = MsaRpcRequestUtil.createWithCurrentUser(paramVO);
        MsaRpcResponse<Map<String, String>> msaRpcResponse = lookUpService.queryMapByLookupType(request);
        if (Objects.isNull(msaRpcResponse.getBo())){
            return Collections.emptyMap();
        } else {
            return msaRpcResponse.getBo();
        }
    }

    /**
     * 完善展会专家信息
     * @param expertDetailsList
     * @param userInfoMapByEmp
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private void perfectExpertDetails(List<ExhibitionExpertDetailsVO> expertDetailsList, Map<String, PersonInfoDTO> userInfoMapByEmp) {
        if (CollectionUtils.isNotEmpty(expertDetailsList)){
            for (ExhibitionExpertDetailsVO expertDetailsVO : expertDetailsList){
                PersonInfoDTO personInfoDTO = userInfoMapByEmp.get(expertDetailsVO.getEmployeeNo());
                if (Objects.nonNull(personInfoDTO)){
                    expertDetailsVO.setEmployeeName(personInfoDTO.getEmpName());
                    expertDetailsVO.setPositionName(personInfoDTO.getPostName());
                }
                if (StringUtils.isNotBlank(expertDetailsVO.getCommunicateDirection())){
                    List<String> communicates = Arrays.asList(expertDetailsVO.getCommunicateDirection().split(COMMON));
                    List<ResourceBizExpertDirectionDO> directionDOList = new ArrayList<>(communicates.size());
                    for (String communicate : communicates){
                        ResourceBizExpertDirectionDO directionDO = new ResourceBizExpertDirectionDO();
                        directionDO.setCommunicationDirection(communicate);
                        directionDOList.add(directionDO);
                    }
                    Map<String, String> directionsMap = resourceBizExpertService.getDirectionsMap();
                    expertDetailsVO.setCommunicateDirectionList(ResourceBusiness.combineRankedDirections(directionDOList,directionsMap));
                }
            }
        }
    }

    /**
     * 完善展会领导信息
     * @param leaderDetailsList
     * @param userInfoMapByEmp
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private void perfectLeaderDetails(List<ExhibitionLeaderDetailsVO> leaderDetailsList, Map<String, PersonInfoDTO> userInfoMapByEmp) {
        if (CollectionUtils.isNotEmpty(leaderDetailsList)){
            for (ExhibitionLeaderDetailsVO leaderDetailsVO : leaderDetailsList){
                PersonInfoDTO personInfoDTO = userInfoMapByEmp.get(leaderDetailsVO.getEmployeeNo());
                if (Objects.nonNull(personInfoDTO)){
                    leaderDetailsVO.setEmployeeName(personInfoDTO.getEmpName());
                    leaderDetailsVO.setPositionName(personInfoDTO.getPostName());
                }
            }
        }
    }

    /**
     * 完善展会负责人信息
     * @param directorDetailsList
     * @param roleResponseMap
     * @param userInfoMapByEmp
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private void perfectDirectorDetails(List<ExhibitionDirectorDetailsVO> directorDetailsList, Map<String, String> roleResponseMap, Map<String, PersonInfoDTO> userInfoMapByEmp) {
        if (CollectionUtils.isNotEmpty(directorDetailsList)){
            List<String> orgAuthStr = directorDetailsList.stream().filter(x -> StringUtils.isNotBlank(x.getOrgAuth())).map(ExhibitionDirectorDetailsVO::getOrgAuth).collect(Collectors.toList());
            Map<String, OrgInfoVO> orgInfoMap = getOrgInfoMap(orgAuthStr);
            for (ExhibitionDirectorDetailsVO director : directorDetailsList) {
                if (Objects.nonNull(roleResponseMap)){
                    director.setRoleTypeName(roleResponseMap.get(director.getRoleType()));
                }
                director.setEmployeeList(getExhibitionEmployeeList(userInfoMapByEmp, director));
                director.setOrgAuthList(getExhibitionOrgAuthList(orgInfoMap, director));
            }
        }
    }

    /**
     * 获取展会负责人数据权限
     * @param orgInfoMap
     * @param director
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private List<ExhibitionOrgAuthVO> getExhibitionOrgAuthList(Map<String, OrgInfoVO> orgInfoMap, ExhibitionDirectorDetailsVO director) {
        List<ExhibitionOrgAuthVO> exhibitionOrgAuthList = new ArrayList<>();
        String orgAuth = director.getOrgAuth();
        List<String> orgAuthList = Arrays.asList(orgAuth.split(CharacterConstant.COMMA));
        orgAuthList.forEach(auth ->{
            OrgInfoVO orgInfoVO = orgInfoMap.get(auth);
            if (Objects.nonNull(orgInfoVO)){
                ExhibitionOrgAuthVO exhibitionOrgAuthVO = new ExhibitionOrgAuthVO();
                exhibitionOrgAuthVO.setOrgAuth(auth);
                exhibitionOrgAuthVO.setOrgAuthName(orgInfoVO.getHrOrgName());
                exhibitionOrgAuthList.add(exhibitionOrgAuthVO);
            }
        });
        return exhibitionOrgAuthList;
    }

    /**
     * 获取组织数据信息
     * @param orgAuthStr
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private Map<String, OrgInfoVO> getOrgInfoMap(List<String> orgAuthStr) {
        if (CollectionUtils.isEmpty(orgAuthStr)){
            return Collections.emptyMap();
        }
        List<String> authList = getCommaStrToList(orgAuthStr);
        Map<String, OrgInfoVO> orgInfoMap;
        try {
            orgInfoMap = hrmUsercenterAdapter.getOrgInfoMap(ExternalConstant.IDTYPE_T0001, authList);
            logger.info("获取组织ORG获取组织信息={}", JSON.toJSONString(orgInfoMap));
        } catch (Exception e) {
            logger.error("获取组织ORG获取组织信息异常Message={}",e.getMessage());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE,"Get Organization ORG Get Organization Info Error");
        }
        return orgInfoMap;
    }

    /**
     * 获取员工信息
     * @param userInfoMapByEmp
     * @param director
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private List<ExhibitionEmployeeVO> getExhibitionEmployeeList(Map<String, PersonInfoDTO> userInfoMapByEmp, ExhibitionDirectorDetailsVO director) {
        List<ExhibitionEmployeeVO> employeeList = new ArrayList<>();
        String employeeNoStr = director.getEmployeeNo();
        List<String> empNoList = Arrays.asList(employeeNoStr.split(CharacterConstant.COMMA));
        empNoList.forEach(empNo ->{
            PersonInfoDTO personInfoDTO = userInfoMapByEmp.get(empNo);
            if (Objects.nonNull(personInfoDTO)){
                ExhibitionEmployeeVO exhibitionEmployeeVO = new ExhibitionEmployeeVO();
                exhibitionEmployeeVO.setEmployeeNo(empNo);
                exhibitionEmployeeVO.setEmployeeNoAndName((StringUtils.join(personInfoDTO.getEmpName(), empNo)));
                exhibitionEmployeeVO.setEmployeeName(personInfoDTO.getEmpName());
                exhibitionEmployeeVO.setEmployeeNameEn(personInfoDTO.getEmpNameEN());
                exhibitionEmployeeVO.setPositionName(personInfoDTO.getPostName());
                employeeList.add(exhibitionEmployeeVO);
            }
        });
        return employeeList;
    }

    /**
     * 获取展现详情所有的工号
     * @param exhibitionDetails
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private Map<String, PersonInfoDTO> getUserInfoMapByEmp(ExhibitionDetailsVO exhibitionDetails) {
        List<String> empNoStrList = new ArrayList<>(5);
        List<String> directorEmployeeList = CollectionUtils.isEmpty(exhibitionDetails.getDirectorDetailsList())
                ? Lists.newArrayList() :  exhibitionDetails.getDirectorDetailsList().stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeNo())).map(ExhibitionDirectorDetailsVO::getEmployeeNo).collect(Collectors.toList());
        empNoStrList.addAll(directorEmployeeList);
        List<String> leaderEmployeeList =  CollectionUtils.isEmpty(exhibitionDetails.getLeaderDetailsList())
                ? Lists.newArrayList() :  exhibitionDetails.getLeaderDetailsList().stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeNo())).map(ExhibitionLeaderDetailsVO::getEmployeeNo).collect(Collectors.toList());
        empNoStrList.addAll(leaderEmployeeList);
        List<String> expertEmployeeList = CollectionUtils.isEmpty(exhibitionDetails.getExpertDetailsList())
                ? Lists.newArrayList() : exhibitionDetails.getExpertDetailsList().stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeNo())).map(ExhibitionExpertDetailsVO::getEmployeeNo).collect(Collectors.toList());
        empNoStrList.addAll(expertEmployeeList);
        List<String> roomEmployeeList = CollectionUtils.isEmpty(exhibitionDetails.getRoomDetailsList())
                ? Lists.newArrayList() : exhibitionDetails.getRoomDetailsList().stream().filter(x -> StringUtils.isNotBlank(x.getMainLeader())).map(ExhibitionRelationRoomDetailsVO::getMainLeader).collect(Collectors.toList());
        empNoStrList.addAll(roomEmployeeList);
        if (CollectionUtils.isEmpty(empNoStrList)){
            return Collections.emptyMap();
        }
        List<String> empNoList = new ArrayList<>(5);
        empNoStrList.forEach(empNoStr ->{
            List<String> tempEmp= Arrays.asList(empNoStr.split(CharacterConstant.COMMA));
            empNoList.addAll(tempEmp);
        });
        List<String> targetEmpNoList = empNoList.stream().distinct().collect(Collectors.toList());
        PersonInfoModel model = new PersonInfoModel();
        model.setEmpNos(targetEmpNoList);
        model.setBlockList(Lists.newArrayList(HrInfoBlockEnum.PERSONAL_INFO, HrInfoBlockEnum.POSITION_INFO));
        MsaRpcResponse<Map<String, PersonInfoDTO>> mapMsaRpcResponse
                = hrmUserCenterSearchService.fetchPersonInfo(MsaRpcRequestUtil.createWithCurrentUser(model));
        if (MapUtils.isEmpty(mapMsaRpcResponse.getBo())) {
            return Maps.newHashMap();
        }
        return mapMsaRpcResponse.getBo();
    }

    /**
     * 将List转化符合条件的list
     * @param commaStrList
     * @return
     * <AUTHOR>
     * @date 2023/9/13
     */
    private List<String> getCommaStrToList(List<String> commaStrList) {
        List<String> targetList = new ArrayList<>();
        commaStrList.forEach(comma ->{
            List<String> commaList = Arrays.asList(comma.split(CharacterConstant.COMMA));
            targetList.addAll(commaList);
        });
        return targetList.stream().distinct().collect(Collectors.toList());
    }
}
