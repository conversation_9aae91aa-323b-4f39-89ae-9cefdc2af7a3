package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationSolutionDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCommunicationDirectionRepository;
import com.zte.mcrm.activity.repository.rep.event.ActivityRelationEventExtRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationProjectRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationSolutionRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationTalkRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryApRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryIssueRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRepository;
import com.zte.mcrm.activity.service.activity.ActivityDataAbilityService;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.common.util.StringExtUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * 活动数据能力服务类
 * <AUTHOR> 10333830
 * @date 2023-08-30 14:54
 */
@Service
public class ActivityDataAbilityServiceImpl implements ActivityDataAbilityService {
    @Autowired
    private ActivityCommunicationDirectionRepository communicationDirectionRepository;
    @Autowired
    private ActivityRelationProjectRepository projectRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository custPeopleRepository;
    @Autowired
    private ActivityRelationZtePeopleRepository ztePeopleRepository;
    @Autowired
    private ActivitySummaryRepository summaryRepository;
    @Autowired
    private ActivitySummaryIssueRepository summaryIssueRepository;
    @Autowired
    private ActivitySummaryApRepository summaryApRepository;
    @Autowired
    private ActivityRelationSolutionRepository solutionRepository;
    @Autowired
    private ActivityRelationAttachmentRepository relationAttachmentRepository;
    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;
    @Autowired
    private ActivityRelationTalkRepository talkRepository;
    @Autowired
    private ActivityRelationEventExtRepository eventExtRepository;

    /**
     * 删除活动除活动基本信息，客户信息外的所有信息
     *
     * @param activityRowId 活动Id
     * @return boolean
     * <AUTHOR>
     * date: 2023/8/30 15:18
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation= Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public boolean deleteActivityAllOtherInfo(String activityRowId) {
        String empNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        List<String> activityRowIds = Lists.newArrayList(activityRowId);
        // 删除交流方向
        communicationDirectionRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除客户联系人
        custPeopleRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除工程信息
        projectRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除我司参与人-除开被艾特人和被转发人
        deleteZtePeopleFilterTargetRole(empNo, activityRowId, ActivityPeopleTypeEnum.CONTACTS);
        // 删除谈参
        talkRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除方案
        solutionRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除附件
        relationAttachmentRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除会议纪要AP本地记录
        summaryApRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除会议纪要草稿
        summaryRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除关联事件
        eventExtRepository.deleteByActivityIds(empNo, activityRowIds);
        // 删除遗留问题
        summaryIssueRepository.deleteByActivityId(empNo, activityRowId);
        // 删除待办
        activityPendingNoticeRepository.deleteByActivityId(empNo, activityRowId);
        return true;
    }

    /**
     * 保存活动方案
     *
     * @param dataList      数据列表
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2024/1/19 13:55
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation= Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public int saveSolution(List<ActivityRelationSolutionDO> dataList, String activityRowId) {
        int res = ZERO;
        // 查询数据库已有数据
        List<ActivityRelationSolutionDO> dbList = solutionRepository.queryAllSolutionForActivity(activityRowId);
        Set<String> dbIdSet = CollectionExtUtils.getListOrDefaultEmpty(dbList).stream()
                .map(ActivityRelationSolutionDO::getRowId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<ActivityRelationSolutionDO> insertList = Lists.newArrayList();
        List<ActivityRelationSolutionDO> updateList = Lists.newArrayList();
        // 判断是更新还是新增
        dataList.forEach(e -> {
            if (!dbIdSet.contains(StringExtUtils.getOrEmpty(e.getRowId()))) {
                insertList.add(e);
            } else {
                updateList.add(e);
            }
        });
        res += solutionRepository.batchUpdate(updateList);
        res += solutionRepository.batchInsert(insertList);
        return res;
    }

    /**
     * 保存活动附件
     *
     * @param dataList      数据列表
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2024/1/19 13:55
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation= Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public int saveAttachment(List<ActivityRelationAttachmentDO> dataList, String activityRowId) {
        int res = ZERO;
        // 查询数据库已有数据
        List<ActivityRelationAttachmentDO> dbList = relationAttachmentRepository.queryAllByActivityRowId(activityRowId);
        Set<String> dbIdSet = CollectionExtUtils.getListOrDefaultEmpty(dbList).stream()
                .map(ActivityRelationAttachmentDO::getRowId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<ActivityRelationAttachmentDO> insertList = Lists.newArrayList();
        List<ActivityRelationAttachmentDO> updateList = Lists.newArrayList();
        // 判断是更新还是新增
        dataList.forEach(e -> {
            if (!dbIdSet.contains(StringExtUtils.getOrEmpty(e.getRowId()))) {
                insertList.add(e);
            } else {
                updateList.add(e);
            }
        });
        res += relationAttachmentRepository.batchUpdate(updateList);
        res += relationAttachmentRepository.batchInsert(insertList);
        return res;
    }


    /**
     * 删除中兴参与人除特定角色
     * @param empNo 操作人供货
     * @param activityRowId 活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 15:42
     */
    private void deleteZtePeopleFilterTargetRole(String empNo, String activityRowId, ActivityPeopleTypeEnum... peopleTypeEnums) {
        List<ActivityRelationZtePeopleDO> ztePeopleList = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        ztePeopleList = CollectionUtils.isEmpty(ztePeopleList) ? Lists.newArrayList() : ztePeopleList;
        List<String> rowIds = ztePeopleList.stream()
                .filter(e -> ActivityPeopleTypeEnum.in(e.getPeopleType(), peopleTypeEnums))
                .map(ActivityRelationZtePeopleDO::getRowId).collect(Collectors.toList());
        ztePeopleRepository.deleteByRowIds(empNo, rowIds);
    }
}
