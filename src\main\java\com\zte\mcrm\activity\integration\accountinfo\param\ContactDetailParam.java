package com.zte.mcrm.activity.integration.accountinfo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查客户联系人详细信息入参
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Getter
@Setter
public class ContactDetailParam {
    @ApiModelProperty("客户编码")
    private List<String> customerCodes;

    @ApiModelProperty("查询客户联系人信息类型（可选填dept和compliance）")
    private List<String> contactDetailTypes;
}
