package com.zte.mcrm.activity.repository.mapper.sample;


import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SampleVisitFeeConfigExtMapper extends SampleVisitFeeConfigMapper {
    /**
     * 查询样板参观费用配置所有有效数据
     * @return
     */
    List<SampleVisitFeeConfigDO> querySampleVisitFeeConfigAll();

    /**
     * 获取符合条件的实体列表
     * @param map
     * @return
     */
    List<SampleVisitFeeConfigDO> getSelectiveList(Map<String, Object> map);

    /**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param rowId 主键
     * @date 2024/01/30
     * @return 删除总数
     */
    int softDelete(@Param("rowId")String rowId,@Param("lastUpdatedBy") String empNo);

}