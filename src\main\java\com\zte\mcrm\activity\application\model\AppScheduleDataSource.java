package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10307200
 * @since 2024-01-09 上午10:47
 **/
@Setter
@Getter
public class AppScheduleDataSource {

    private String keyword;

    private boolean isAllSchedule;

    private ExhibitionInfoDO exhibitionInfoDO;

    private List<ActivityInfoDO> validActivityInfoList;

    private Map<String, ActivityInfoDO> activityId2EntityMaps;

    private List<ActivityScheduleItemDO> validScheduleItemList;

    private ActivityScheduleOrchestrationVersionDO version;

    private Map<String, List<ActivityScheduleItemDO>> activityId2ScheduleItemListMap;

    private Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemCustomerPeopleMap;

    private Map<String, List<ActivityScheduleItemPeopleDO>> scheduleItemZtePeopleMap;

    private Map<String, List<ActivityCustomerInfoDO>> activityId2CustomerInfoListMap;

    private Map<String, ActivityCustomerInfoDO> activityId2MainCustomerInfoMap;

    private Map<String, List<ActivityRelationAttachmentDO>> activityId2AttachmentListMap;

    private Map<String, List<ActivityRelationCustPeopleDO>> activityId2CustPeopleListMap;

    private Map<String, Map<String, ActivityRelationCustPeopleDO>> activityId2CustPeopleMaps;

    private Map<String, Set<String>> scheduleItem2OrchIdsMap;

}
