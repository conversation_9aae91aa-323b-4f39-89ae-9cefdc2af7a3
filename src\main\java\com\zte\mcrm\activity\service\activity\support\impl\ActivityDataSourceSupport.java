package com.zte.mcrm.activity.service.activity.support.impl;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityCostBudgetDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityCostBudgetRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.sample.SampleVisitFeeConfigRepository;
import com.zte.mcrm.activity.service.activity.param.ActivityDataSource;
import com.zte.mcrm.activity.service.activity.support.IActivityDataSourceSupport;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityApprovalParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 活动数据源支撑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActivityDataSourceSupport implements IActivityDataSourceSupport {
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityCostBudgetRepository activityCostBudgetRepository;
    @Autowired
    private ActivityRelationAttachmentRepository relationAttachmentRepository;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private SampleVisitFeeConfigRepository sampleVisitFeeConfigRepository;

    @Override
    public ActivityDataSource getActivitySamplePointDataSource(BizRequest<String> bizRequest) {
        ActivityDataSource activityDataSource = new ActivityDataSource();
        // 活动信息
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(bizRequest.getParam());
        if (Objects.isNull(activityInfoDO)) {
            return activityDataSource;
        }
        activityDataSource.setActivityInfoDO(activityInfoDO);
        // 活动成本预算
        Map<String, List<ActivityCostBudgetDO>> activityCostBudgetListMap = activityCostBudgetRepository.queryActivityCostBudgetByActivityRowIds(Collections.singletonList(activityInfoDO.getRowId()));
        activityDataSource.setActivityCostBudgetDOList(Optional.ofNullable(activityCostBudgetListMap).map(m -> m.get(activityInfoDO.getRowId())).orElse(Collections.emptyList()));
        // 活动成本预算配置
        List<SampleVisitFeeConfigDO> sampleVisitFeeConfigDOList = sampleVisitFeeConfigRepository.queryFeeConfigAll();
        activityDataSource.setSampleVisitFeeConfigDOList(sampleVisitFeeConfigDOList);
        // 活动附件
        List<ActivityRelationAttachmentDO> activityRelationAttachmentList = relationAttachmentRepository.queryAllByActivityRowId(activityInfoDO.getRowId());
        activityDataSource.setActivityRelationAttachmentDOList(Optional.ofNullable(activityRelationAttachmentList).orElse(Collections.emptyList()));
        // 活动审批列表
        List<ActivityApprovalParam> activityApprovalParamList = Optional.ofNullable(activityInfoDO.getApprovalText()).map(approvalText -> JSON.parseArray(activityInfoDO.getApprovalText(), ActivityApprovalParam.class)).orElse(Collections.emptyList());
        Set<String> empNoSet = activityApprovalParamList.stream().map(ActivityApprovalParam::getEmpNo).collect(Collectors.toSet());
        Map<String, String> empNoToNameMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(empNoSet)).getBo();
        activityApprovalParamList.forEach(approval -> {
            approval.setEmpName(empNoToNameMap.get(approval.getEmpNo()));
            approval.setEmpDesc(approval.getEmpName() + approval.getEmpNo());
        });
        activityDataSource.setActivityApprovalParamList(activityApprovalParamList);
        return activityDataSource;
    }
}
