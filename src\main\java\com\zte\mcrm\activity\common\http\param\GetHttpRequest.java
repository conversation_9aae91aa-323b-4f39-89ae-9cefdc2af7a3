package com.zte.mcrm.activity.common.http.param;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.StringUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.HttpEntity;
import org.springframework.http.MediaType;

import java.text.Normalizer;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * get请求
 *
 * <AUTHOR>
 */
public class GetHttpRequest extends BaseHttpRequest {
    /**
     * 参数
     */
    private final Map<String, String> paramMap = new HashMap<>();

    GetHttpRequest(String url) {
        super(url, "GET");
    }

    @Override
    public HttpEntity fetchHttpEntity() {
        // Get请求头没有请求提，这里返回空。或者抛异常也可以
        return null;
    }

    @Override
    public String getUrl() {
        String baseUrl = super.getUrl();
        if (paramMap.isEmpty()) {
            return baseUrl;
        }

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");

        Iterator<Map.Entry<String, String>> iterator = paramMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = entry.getKey();
            String value = entry.getValue();

            urlBuilder.append(key).append("=").append(value);

            if (iterator.hasNext()) {
                urlBuilder.append("&");
            }
        }

        return Normalizer.normalize(urlBuilder.toString().trim(), Normalizer.Form.NFKC);
    }

    /**
     * 添加参数
     *
     * @param name  参数名称
     * @param value 参数值
     */
    public GetHttpRequest addParam(String name, String value) {
        if (StringUtils.isAnyBlank(name, value)) {
            return this;
        }
        paramMap.put(name, value);
        return this;
    }

    /**
     * 删除参数
     *
     * @param name 参数名称
     */
    public GetHttpRequest removeParam(String name) {
        paramMap.remove(name);
        return this;
    }

    /**
     * 创建get请求 （备注：如果url和bizReq中的参数重复，以bizReq的为准）
     *
     * @param url
     * @param bizReq
     * @return
     */
    public static GetHttpRequest createWithJsonRestful(String url, BizRequest<Map<String, String>> bizReq) {
        GetHttpRequest get = createWithJsonRestful(url, bizReq.getParam());
        get.addHeader(bizReq.fetchHeaderMap());
        return get;
    }

    /**
     * 创建get请求 （备注：如果url和paramMap中的参数重复，以paramMap的为准）
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static GetHttpRequest createWithJsonRestful(String url, Map<String, String> paramMap) {
        String baseUrl = fetchBaseUrl(url);
        Map<String, String> param = fetchParamMap(url);

        GetHttpRequest get = new GetHttpRequest(baseUrl);
        get.addHeader(RequestHeaderConstant.X_ORIGIN_SERVICENAME, RequestHeaderConstant.SERVICE_NAME)
                .addHeader(RequestHeaderConstant.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
                .addHeader(RequestHeaderConstant.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        ;
        // 先将URL中的参数添加到参数中
        param.forEach(get::addParam);
        // 然后将传入的参数加入到参数中（即：指定参数的优先于URL中的）
        if (MapUtils.isNotEmpty(paramMap)) {
            paramMap.forEach(get::addParam);
        }

        return get;
    }

    /**
     * 获取基准URL（去除参数后的）
     *
     * @param url
     * @return
     */
    static String fetchBaseUrl(String url) {
        String[] arr = url.split("\\?");

        return arr[0];
    }

    /**
     * 获取URL中的参数
     *
     * @param url
     * @return
     */
    static Map<String, String> fetchParamMap(String url) {
        String[] arr = url.split("\\?");
        Map<String, String> map = new HashMap<>(NumberConstant.EIGHT);

        if (arr.length > 1) {
            String[] params = arr[1].split("&");
            for (String param : params) {
                String[] pair = param.split("=");
                if (pair.length == NumberConstant.TWO) {
                    map.put(pair[0], pair[1]);
                }
            }
        }

        return map;
    }
}
