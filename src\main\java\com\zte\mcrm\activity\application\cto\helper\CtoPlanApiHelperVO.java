package com.zte.mcrm.activity.application.cto.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.enums.LanguageEnum;
import com.zte.mcrm.activity.common.http.HttpUtil;
import com.zte.mcrm.activity.common.http.param.GetHttpRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.MsaRpcResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * {@code @description Api接口调用帮助类}}
 *
 * <AUTHOR>
 * @date 2024/12/24 下午2:42
 */
@Component
public class CtoPlanApiHelperVO {
    @Value("${zte.crm.api:http://test.ssc.zte.com.cn:8888/zte-crm-eva-demoapi/app}")
    public String crmApi;
    @Value("${zte.crm.api.appId:APP1033890729904046081}")
    public String crmAppId;
    @Value("${zte.crm.api.empNo:10318011}")
    public String crmEmpNo;


    public String getPlanName(String planId) {
        // 参数清理和转义
        String sanitizedPlanId = StringUtils.trim(planId);
        // 构建请求参数，使用不可变Map
        Map<String, String> paramMap = Collections.singletonMap("id", sanitizedPlanId);
        GetHttpRequest get = GetHttpRequest.createWithJsonRestful(crmApi + "/" + crmAppId + "/custom/getCtoDataById", paramMap);
        get.addHeader(RequestHeaderConstant.X_EMP_NO, crmEmpNo);
        get.addHeader(RequestHeaderConstant.X_LANG_ID, LanguageEnum.ZH_CN.getLanguage());
        get.addHeader(RequestHeaderConstant.X_APP_ID, crmAppId);
        get.addHeader(RequestHeaderConstant.X_ACCOUNT_ID, crmEmpNo);

        MsaRpcResponse<String> res = HttpUtil.getHttpWithStringRes(get, MsaRpcResponseUtil::successRes);
        if (MsaRpcResponse.SUCCESS_CODE.equals(res.getCode())) {
            String resBo = res.getBo();
            ServiceData<JSONObject> serviceData = JSON.parseObject(resBo, ServiceData.class);
            JSONObject boObj = Optional.ofNullable(serviceData.getBo()).orElse(new JSONObject());
            Object planNameObj = boObj.get("plan_name");
            if (Objects.nonNull(planNameObj)) {
                return planNameObj.toString();
            }
        }
        return "empty_plan";
    }
}
