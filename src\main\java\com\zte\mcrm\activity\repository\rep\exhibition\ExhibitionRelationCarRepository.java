package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午2:13
 **/
public interface ExhibitionRelationCarRepository {
    
    /***
     * <p>
     * 插入单条数据
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:32
     * @param record 展会关联车辆记录
     * @return int
     */
    int insertSelective(ExhibitionRelationCarDO record);
    
    /***
     * <p>
     * 更新单条记录的指定字段
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:34
     * @param record 更新的展会关联车辆记录
     * @return int
     */
    int updateByPrimaryKeySelective(ExhibitionRelationCarDO record);
    
    /***
     * <p>
     * 根据主键Id列表 获取 展会关联车辆记录列表
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:35
     * @param carRowIds 展会关联车辆记录主键Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO>
     */
    List<ExhibitionRelationCarDO> queryAllByCarRowIds(List<String> carRowIds);
    
    /***
     * <p>
     * 根据展会Id 获取 对应的关联车辆集合
     * 
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:35
     * @param exhibitionRowIds 展会Id集合
     * @return java.util.Map<java.lang.String,com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationCarDO>
     */
    Map<String, List<ExhibitionRelationCarDO>> getRelationCarListByExhibitionRowIds(Set<String> exhibitionRowIds);

    /***
     * <p>
     * 批量插入数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:37
     * @param records 展会关联车辆记录列表
     * @return int
     */
    int batchInsert(List<ExhibitionRelationCarDO> records);

    /***
     * <p>
     * 批量更新数据
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:38
     * @param records 展会关联车辆记录列表
     * @return int
     */
    int batchUpdate(List<ExhibitionRelationCarDO> records);

    /***
     * <p>
     * 根据展会Id 删除 对应展会ID的关联车辆记录列表
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:38
     * @param operator 操作人
     * @param exhibitionRowIds 展会Id
     * @return int
     */
    int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds);

    /***
     * <p>
     * 根据主键Id 删除 对应的关联车辆记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/16 下午2:39
     * @param operator 操作人
     * @param rowIds 展会关联车辆记录Id列表
     * @return int
     */
    int deleteByRowIds(String operator, List<String> rowIds);

}
