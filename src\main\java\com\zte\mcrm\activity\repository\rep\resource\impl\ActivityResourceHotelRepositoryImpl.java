package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityResourceHotelExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceHotelRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-16 17:19
 **/
@Component
public class ActivityResourceHotelRepositoryImpl implements ActivityResourceHotelRepository {
    @Autowired
    private IKeyIdService keyIdService;
    @Resource
    private ActivityResourceHotelExtMapper activityResourceHotelExtMapper;
    @Override
    public int insertSelective(List<ActivityResourceHotelDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityResourceHotelDO record : recordList) {
            record.setRowId(keyIdService.getKeyId());
            setDefaultValue(record);
            activityResourceHotelExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    private void setDefaultValue(ActivityResourceHotelDO resourceHotelDO) {
        resourceHotelDO.setCreatedBy(Optional.ofNullable(resourceHotelDO.getCreatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceHotelDO.setLastUpdatedBy(Optional.ofNullable(resourceHotelDO.getLastUpdatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceHotelDO.setCreationDate(new Date());
        resourceHotelDO.setLastUpdateDate(new Date());
        resourceHotelDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityResourceHotelDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return activityResourceHotelExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ActivityResourceHotelDO>> queryActivityResourceHotelsByActivityRowIds(List<String> activityRowIds) {
        return org.springframework.util.CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityResourceHotelExtMapper.queryActivityResourceHotelsByActivityRowIds(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityResourceHotelDO::getActivityRowId));
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return activityResourceHotelExtMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public void batchUpdate(List<ActivityResourceHotelDO> records){
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        for(ActivityResourceHotelDO record:records){
            updateByPrimaryKeys(record);
        }
    }

    private int updateByPrimaryKeys(ActivityResourceHotelDO record){
        if(StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return activityResourceHotelExtMapper.updateByPrimaryKeySelective(record);
    }
}
