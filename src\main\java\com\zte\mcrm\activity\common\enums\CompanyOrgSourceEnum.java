package com.zte.mcrm.activity.common.enums;

/* Started by AICoder, pid:861970855b828bd1467e09d20038ae8b1358115d */
import lombok.Getter;

/**
 * 公司组织来源枚举类
 */
@Getter
public enum CompanyOrgSourceEnum {

    /**
     * 股份组织
     */
    ZTE("T0001", "股份组织"),

    /**
     * 子公司
     */
    SUB_COMPANY("T0002", "子公司"),

    /**
     * 合作方公司
     */
    PARTNER("T0003", "合作方公司"),

    /**
     * 股份组织和子公司
     */
    ZTE_AND_SUB("T0001,T0002", "股份公司&子公司");

    private final String code;
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 代码
     * @param desc 描述
     */
    CompanyOrgSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 判断是否为当前枚举值
     *
     * @param type 类型
     * @return 是否为当前枚举值
     */
    public boolean isMe(String type) {
        return this.code.equalsIgnoreCase(type);
    }

    /**
     * 根据code获取枚举值
     *
     * @param code code
     * @return 枚举值
     */
    public static CompanyOrgSourceEnum getEnumByCode(String code) {
        for (CompanyOrgSourceEnum e : CompanyOrgSourceEnum.values()) {
            if (e.isMe(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 校验是否有效的枚举值
     *
     * @param code code
     * @return 是否有效
     */
    public static boolean valid(String code) {
        return getEnumByCode(code) != null;
    }

    /**
     * 判断code是否在指定的枚举值中
     *
     * @param code code
     * @param es   枚举值数组
     * @return 是否在指定的枚举值中
     */
    public static boolean in(String code, CompanyOrgSourceEnum... es) {
        for (CompanyOrgSourceEnum e : es) {
            if (e.isMe(code)) {
                return true;
            }
        }
        return false;
    }
}

/* Ended by AICoder, pid:861970855b828bd1467e09d20038ae8b1358115d */
