package com.zte.mcrm.activity.repository.rep.banner.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.banner.BannerConfigMapper;
import com.zte.mcrm.activity.repository.mapper.banner.BannerConfigNewExtMapper;
import com.zte.mcrm.activity.repository.model.banner.BannerConfigDO;
import com.zte.mcrm.activity.repository.model.banner.BannerConfigNewDO;
import com.zte.mcrm.activity.repository.rep.banner.BannerConfigRepository;
import com.zte.mcrm.activity.service.banner.util.BannerBusinessUtil;
import com.zte.mcrm.keyid.service.IKeyIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BannerConfigRepositoryImpl implements BannerConfigRepository {
    @Autowired
    private IKeyIdService iKeyIdService;
    @Autowired
    private BannerConfigMapper bannerConfigMapper;
    @Autowired
    private BannerConfigNewExtMapper bannerConfigNewExtMapper;

    @Override
    public int insertSelective(List<BannerConfigNewDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (BannerConfigNewDO record : recordList) {
            if (StringUtils.isBlank(record.getBannerConfigId())) {
                record.setBannerConfigId(iKeyIdService.getKeyId());
            }
            bannerConfigNewExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(BannerConfigNewDO record) {
        if (StringUtils.isBlank(record.getBannerConfigId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        return bannerConfigNewExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<BannerConfigNewDO>> fetchBannerConfigByBannerIds(List<String> bannerIds) {
        if (CollectionUtils.isEmpty(bannerIds)) {
            return Collections.emptyMap();
        }
        return bannerConfigNewExtMapper.fetchBannerConfigByBannerIds(bannerIds)
                .stream()
                .collect(Collectors.groupingBy(BannerConfigNewDO::getBannerId));
    }

    @Override
    public BannerConfigDO get(String bannerConfigId) {
        return bannerConfigMapper.get(bannerConfigId);
    }

    @Override
    public List<BannerConfigDO> getList(BannerConfigDO entity) {
        return bannerConfigMapper.getList(entity);
    }

    @Override
    public int delete(String bannerConfigId) {
        BannerConfigDO entity = new BannerConfigDO();
        entity.setBannerConfigId(bannerConfigId);
        entity.setEnabledFlag(CharacterConstant.N);
        return this.update(entity);
    }

    @Override
    public String insert(BannerConfigDO entity) {
        String emp = BannerBusinessUtil.getEmpNoOrSystem();
        Date now = new Date();
        entity.setCreatedBy(emp);
        entity.setLastUpdatedBy(emp);
        entity.setCreationDate(now);
        entity.setLastUpdateDate(now);
        if (StringUtils.isBlank(entity.getBannerConfigId())) {
            entity.setBannerConfigId(iKeyIdService.getKeyId());
        }
        int insertCount = bannerConfigMapper.insert(entity);
        log.info("===insert banner config, insert count = {}", insertCount);
        return entity.getBannerConfigId();

    }

    @Override
    public int insertByBatch(String bannerId, List<BannerConfigDO> list) {
        if(CollectionUtils.isEmpty(list)){
            return NumberConstant.ZERO;
        }
        String emp = BannerBusinessUtil.getEmpNoOrSystem();
        Date now = new Date();
        for(BannerConfigDO entity:list){
            entity.setBannerId(bannerId);
            entity.setCreatedBy(emp);
            entity.setLastUpdatedBy(emp);
            entity.setCreationDate(now);
            entity.setLastUpdateDate(now);
            entity.setEnabledFlag(CharacterConstant.Y);
            if (StringUtils.isBlank(entity.getBannerConfigId())) {
                entity.setBannerConfigId(iKeyIdService.getKeyId());
            }
        }
        return bannerConfigMapper.insertByBatch(list);
    }

    @Override
    public int update(BannerConfigDO entity) {
        entity.setLastUpdatedBy(BannerBusinessUtil.getEmpNoOrSystem());
        entity.setLastUpdateDate(new Date());
        return bannerConfigMapper.update(entity);
    }

    @Override
    public int deteleBatchByBannerId(String bannerId) {
        if(StringUtils.isBlank(bannerId)){
            return NumberConstant.ZERO;
        }
        BannerConfigDO entity = new BannerConfigDO();
        entity.setBannerId(bannerId);
        entity.setEnabledFlag(CharacterConstant.N);
        entity.setLastUpdatedBy(BannerBusinessUtil.getEmpNoOrSystem());
        entity.setLastUpdateDate(new Date());
        return bannerConfigMapper.updateByBannerId(entity);
    }

    @Override
    public long getCount(Map<String, Object> map) {
        return bannerConfigMapper.getCount(map);
    }

    @Override
    public List<BannerConfigDO> getPage(Map<String, Object> map) {
        return bannerConfigMapper.getPage(map);
    }

    /**
     * 根据banner id获取对应配置
     *
     * @param bannerIds bannerID集合
     * @return Map<String, List < BannerConfigNewDO>>
     */
    @Override
    public Map<String, List<BannerConfigNewDO>> matchBannerConfigByBannerIds(List<String> bannerIds) {
        /* Started by AICoder, pid:r5b2f8bca3cdc5914cbb09cbb08eb41556329ecd */
        if (CollectionUtils.isEmpty(bannerIds)) {
            return Collections.emptyMap();
        }
        Map<String, List<BannerConfigNewDO>> result = new HashMap<>(bannerIds.size());
        for (String bannerId : bannerIds) {
            result.put(bannerId, new ArrayList<>());
        }
        List<BannerConfigNewDO> bannerConfigs = bannerConfigNewExtMapper.fetchBannerConfigByBannerIds(bannerIds);
        if (CollectionUtils.isEmpty(bannerConfigs)) {
            return Collections.emptyMap();
        }
        for (BannerConfigNewDO config : bannerConfigs) {
            result.computeIfAbsent(config.getBannerId(), v -> Lists.newArrayList()).add(config);
        }
        return result;
        /* Ended by AICoder, pid:r5b2f8bca3cdc5914cbb09cbb08eb41556329ecd */
    }
}
