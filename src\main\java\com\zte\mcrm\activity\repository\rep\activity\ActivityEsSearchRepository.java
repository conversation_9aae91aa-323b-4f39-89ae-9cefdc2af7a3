package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityConvergenceInfoVO;

import java.util.List;

/**
 * 客户融合活动ES查询
 *
 * <AUTHOR>
 * @date 2023/12/20 下午2:25
 */
public interface ActivityEsSearchRepository {

    /**
     * 查询活动列表分页
     *
     * @param query
     * @return {@link PageRows< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2023/12/20 下午2:24
     */
    PageRows<ActivityInfoDO> searchPage(ActivityInfoQuery query);

    /**
     * 查询活动列表信息并返回按照活动类型的汇聚结果
     * @param query
     * @return
     */
    List<ActivityConvergenceInfoVO> searchConvergeInfo(ActivityInfoQuery query);

    /**
     * 查询活动列表分页
     *
     * @param query
     * @return {@link PageRows< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2023/12/20 下午2:24
     */
    PageRows<ActivityInfoDO> searchPageDownLoad(ActivityInfoQuery query);
}
