package com.zte.mcrm.activity.repository.rep.summary.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryIssueExtMapper;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryIssueRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
public class ActivitySummaryIssueRepositoryImpl implements ActivitySummaryIssueRepository {
    @Resource
    private ActivitySummaryIssueExtMapper activitySummaryIssueExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivitySummaryIssueDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivitySummaryIssueDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            activitySummaryIssueExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivitySummaryIssueDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activitySummaryIssueExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivitySummaryIssueDO> queryAllSummaryIssueForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activitySummaryIssueExtMapper.queryAllSummaryIssueForActivity(activityRowId);
    }

    @Override
    public Map<String, List<ActivitySummaryIssueDO>> queryAllByActivityRowId(List<String> activityRowId) {
        List<ActivitySummaryIssueDO> list = CollectionUtils.isEmpty(activityRowId) ? Collections.emptyList() : activitySummaryIssueExtMapper.queryAllByActivityRowId(activityRowId);

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream().collect(Collectors.groupingBy(ActivitySummaryIssueDO::getActivityRowId));
    }

    @Override
    public int deleteBatch(List<String> rowIdList) {
        return CollectionUtils.isEmpty(rowIdList) ? 0 : activitySummaryIssueExtMapper.deleteBatch(rowIdList);
    }


    /**
     * 删除指定活动下的所有待办
     * @param operator  操作者
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 16:17
     */
    @Override
    public int deleteByActivityId(String operator, String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return 0;
        }
        ActivitySummaryIssueDO record = new ActivitySummaryIssueDO();
        record.setLastUpdatedBy(operator);
        record.setLastUpdateDate(new Date());
        record.setActivityRowId(activityRowId);
        record.setEnabledFlag(BooleanEnum.N.getCode());
        return activitySummaryIssueExtMapper.updateByActivityId(record);
    }

}
