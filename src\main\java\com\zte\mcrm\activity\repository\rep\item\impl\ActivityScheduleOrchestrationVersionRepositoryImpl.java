package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleOrchestrationVersionExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationVersionRepository;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;
import com.zte.mcrm.activity.service.schedule.impl.support.ScheduleOrchestrationConstants;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-17 9:57
 **/
@Component
public class ActivityScheduleOrchestrationVersionRepositoryImpl implements ActivityScheduleOrchestrationVersionRepository {
    @Autowired
    private IKeyIdService keyIdService;
    @Resource
    private ActivityScheduleOrchestrationVersionExtMapper activityScheduleOrchestrationVersionExtMapper;
    @Override
    public int insertSelective(List<ActivityScheduleOrchestrationVersionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityScheduleOrchestrationVersionDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            activityScheduleOrchestrationVersionExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public ActivityScheduleOrchestrationVersionDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : activityScheduleOrchestrationVersionExtMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public ActivityScheduleOrchestrationVersionDO getLastScheduleOrchestrationVersion(ActivityScheduleOrchestrationVersionQuery query){
        return activityScheduleOrchestrationVersionExtMapper.getLastScheduleOrchestrationVersion(query);
    }

    @Override
    public Map<String, ActivityScheduleOrchestrationVersionDO> queryAfterPublishTimeVersion(String exhibitionRowId, Date afterPublishTime) {
        if (StringUtils.isBlank(exhibitionRowId)) {
            return Collections.emptyMap();
        }
        ActivityScheduleOrchestrationVersionQuery query = new ActivityScheduleOrchestrationVersionQuery();
        query.setExhibitionRowId(exhibitionRowId);
        query.setPublishTimeAfter(afterPublishTime);
        query.setVersionStatus(ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH);

        Map<String, List<ActivityScheduleOrchestrationVersionDO>> emp2Version = activityScheduleOrchestrationVersionExtMapper.queryActivityScheduleOrchestrationVersions(query)
                .stream().collect(Collectors.groupingBy(ActivityScheduleOrchestrationVersionDO::getCreatedBy));

        Map<String, ActivityScheduleOrchestrationVersionDO> res = new HashMap<>();
        emp2Version.forEach((empNo, versionList) ->
                res.put(empNo, versionList.stream().
                        max(Comparator.comparing(ActivityScheduleOrchestrationVersionDO::getPublishTime)).orElse(null)));
        return res;
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityScheduleOrchestrationVersionDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityScheduleOrchestrationVersionExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityScheduleOrchestrationVersionDO> queryActivityScheduleOrchestrationVersions(ActivityScheduleOrchestrationVersionQuery query) {
        return activityScheduleOrchestrationVersionExtMapper.queryActivityScheduleOrchestrationVersions(query);
    }
}
