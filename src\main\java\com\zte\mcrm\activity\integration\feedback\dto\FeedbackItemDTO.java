package com.zte.mcrm.activity.integration.feedback.dto;

import com.zte.mcrm.activity.integration.rdc.dto.DescriptionInfoDto;
import com.zte.mcrm.activity.integration.rdc.dto.RdcPeopleDesDto;
import com.zte.mcrm.activity.integration.rdc.dto.SystemStateDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> 10344346
 * @date 2023-12-11 16:11
 **/
@Getter
@Setter
public class FeedbackItemDTO {
    private boolean qfbIsPrivate;
    private String belongSubDomainName;
    private String systemCreatedDate;
    private boolean qfbEnableComment;
    private String qfbServiceDeskCnName;
    private String systemId;
    private String qfbServiceDeskEnName;
    private boolean qfbEnablePopularityRanking;
    private int qfbServiceDeskId;
    private RdcPeopleDesDto systemCreatedBy;
    private String id;
    private int viewCount;
    private String qfbStatusReqOrITSM;
    private int qfbBelongTypeId;
    private int viewUserCount;
    private String qfbBelongSubDomain;
    private boolean qfbEnableRecommendedRanking;
    private int commentCount;
    private String systemDescriptionHtml;
    private boolean qfbIsTop;
    private SystemStateDto systemState;
    private String systemTitle;
    private String systemWorkItemTypeKey;
    private List<RdcPeopleDesDto> systemAssignedTo;
    private List<RdcPeopleDesDto> qfbProposer;
    private String qfbBelongSubDomainEn;
    private String deskTypeColor;
    private DescriptionInfoDto descriptionInfo;
}
