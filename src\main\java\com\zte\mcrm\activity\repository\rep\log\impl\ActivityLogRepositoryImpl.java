package com.zte.mcrm.activity.repository.rep.log.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.log.ActivityLogMapper;
import com.zte.mcrm.activity.repository.model.log.ActivityLogDO;
import com.zte.mcrm.activity.repository.rep.log.ActivityLogRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 活动日志
 *
 * @author: 汤踊10285568
 * @date: 2023/9/2 21:48
 */
@Service
public class ActivityLogRepositoryImpl implements ActivityLogRepository {
    @Autowired
    private IKeyIdService iKeyIdService;

    @Autowired
    private ActivityLogMapper activityLogMapper;

    /**
     * 根据主键获取实体对象
     *
     * @param rowId 主键ID
     * @return
     * <AUTHOR>
     * @date 2023/09/02
     */
    @Override
    public ActivityLogDO get(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return null;
        }
        return activityLogMapper.get(rowId);
    }

    /**
     * 删除指定记录
     *
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2023/09/02
     */
    @Override
    public int delete(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return NumberConstant.ZERO;
        }
        return activityLogMapper.delete(rowId);
    }

    /**
     * 新增指定记录
     *
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2023/09/02
     */
    @Override
    public int insert(ActivityLogDO entity) {
        String emp = Optional.ofNullable(entity.getCreatedBy()).orElse(HeadersProperties.getXEmpNo());
        Date now = new Date();
        entity.setCreatedBy(emp);
        entity.setLastUpdatedBy(emp);
        entity.setCreationDate(now);
        entity.setLastUpdateDate(now);
        entity.setEnabledFlag(BooleanEnum.Y.getCode());
        if (StringUtils.isBlank(entity.getRowId())) {
            entity.setRowId(iKeyIdService.getKeyId());
        }
        return activityLogMapper.insert(entity);
    }

    /**
     * 修改指定记录
     *
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2023/09/02
     */
    @Override
    public int update(ActivityLogDO entity) {
        entity.setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
        entity.setLastUpdateDate(new Date());
        return activityLogMapper.update(entity);
    }

    /**
     * 获取符合条件的实体列表,按指定属性排序
     *
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2023/09/02
     */
    @Override
    public List<ActivityLogDO> getList(ActivityLogDO entity) {
        if (entity == null) {
            return Lists.newArrayList();
        }
        return activityLogMapper.getList(entity);
    }

}
