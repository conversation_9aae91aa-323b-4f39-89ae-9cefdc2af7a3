package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityScheduleItemAndPeopleVO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 活动日程安排信息
 */
public interface ActivityScheduleItemRepository {
    /**
     * 批量插入日程安排信息
     *
     * @param scheduleItemList
     * @return
     */
    int batchInsert(List<ActivityScheduleItemDO> scheduleItemList);

    /**
     * 根据活动id列表,批量获取日程安排信息集合
     *
     * @param activityRowIds
     * @return
     */
    Map<String, List<ActivityScheduleItemDO>> getRelationScheduleInfoIds(List<String> activityRowIds);

    /**
     * 根据活动id列表,批量获取日程安排信息列表
     *
     * @param activityRowIds
     * @return
     */
    List<ActivityScheduleItemDO> getRelationScheduleInfoList(List<String> activityRowIds);

    /***
     * <p>
     * 根据日程安排RowId 获取 对应记录
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 上午10:16
     * @param scheduleItemRowIds 日程安排RowId
     * @return java.util.List<com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO>
     */
    List<ActivityScheduleItemDO> getScheduleItemByRowIds(List<String> scheduleItemRowIds);

    /**
     * 批量更新数据
     *
     * @param records
     * @return
     */
    int batchUpdate(List<ActivityScheduleItemDO> records);

    /**
     * 根据activityRowId查日程安排列表
     * @param activityRowId
     * @return
     */
    List<ActivityScheduleItemDO> queryAllByActivityRowId(String activityRowId);


    /**
     * 根据rowId,批量软删除日程安排数据
     *
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 根据rowID获取日程安排信息
     * @param rowId
     * @return
     */
    ActivityScheduleItemDO selectByPrimaryKey(String rowId);

    /**
     * 通过版本ID批量查询数据
     * @param orchestrationVersionIdList
     * @return
     */
    Map<String, List<ActivityScheduleItemDO>> queryAllScheduleInfoByVersionIdList(List<String> orchestrationVersionIdList);

    /**
     * 查询日程
     * @param activityIdList
     * @param scheduleStatusList
     * @param scheduleDate
     * @return
     */
    List<ActivityScheduleItemDO> getScheduleListByActivityIdListAndStatusListAndScheduleDate(List<String> activityIdList, List<String> scheduleStatusList, Date scheduleDate);

    /**
     * 通过展会ID和人员查询所有日程
     * @param peopleNoList
     * @param exhibitionId
     * @return
     */
    List<ActivityScheduleItemAndPeopleVO> queryScheduleIdByExhibitionIdAndPeopleNoList(List<String> peopleNoList, String exhibitionId
            , String peopleType, List<String> activityStatusList);
}
