package com.zte.mcrm.activity.service.ai;

import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * AI处理策略
 *
 * @author: 汤踊10285568
 * @date: 2024/8/1 10:51
 */
@Component
public class AiBusinessStrategyComponent {
    @Autowired
    private ApplicationContext context;

    public String processBusinessStrategy(AiApplicationVO req) {
        Map<String, AiApplicationStrategy> serviceMap = context.getBeansOfType(AiApplicationStrategy.class);

        for (AiApplicationStrategy service : serviceMap.values()) {
            if (service.support(req.getBusinessType())) {
                return service.processBusiness(req);
            }
        }
        return StringUtils.EMPTY;
    }
}
