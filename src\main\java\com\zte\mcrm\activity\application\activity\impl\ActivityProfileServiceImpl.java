package com.zte.mcrm.activity.application.activity.impl;

import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.mcrm.activity.application.activity.ActivityProfileService;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.web.controller.activity.vo.ProfileActivityInfoDataVO;
import com.zte.mcrm.dataservice.dto.ProfileActivityApDTO;
import com.zte.mcrm.dataservice.dto.ProfileActivityInfoDTO;
import com.zte.mcrm.dataservice.model.ActivityApDataQuery;
import com.zte.mcrm.dataservice.model.ActivityDataQuery;
import com.zte.mcrm.dataservice.service.ActivityDataResultsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ActivityProfileServiceImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 客户画像接口
 * @date 2024/6/18 14:16
 */
@Service
public class ActivityProfileServiceImpl implements ActivityProfileService {
    @Autowired
    private ActivityDataResultsService activityDataResultsService;
    @Override
    public PageRows<ProfileActivityInfoDataVO> queryActivityDate(BizRequest<ActivityDataQuery> bizRequest) throws Exception {
        PageRows<ProfileActivityInfoDataVO> pageRows = new PageRows<>();
        QueryResult activityResult = activityDataResultsService.activityQueryResult(bizRequest);
        if (Objects.isNull(activityResult) || CollectionUtils.isEmpty(activityResult.getRows())){
            return pageRows;
        }
        List<ProfileActivityInfoDTO> activityInfoDTOList = activityResult.getRows(ProfileActivityInfoDTO.class);
        List<ProfileActivityInfoDataVO> activityInfoDataVOList = getProfileActivityInfoDataList(activityInfoDTOList,bizRequest.getParam().getApNumFlag());
        pageRows.setRows(activityInfoDataVOList);
        pageRows.setTotal(activityResult.getTotal());
        pageRows.setCurrent(bizRequest.getParam().getPageNum());
        pageRows.setPageSize(bizRequest.getParam().getPageSize());
        return pageRows;
    }

    /**
     * 封装活动和AP数据
     * @param activityInfoDTOList
     * @return
     * <AUTHOR>
     * @date 2024/6/18
     */
    public List<ProfileActivityInfoDataVO> getProfileActivityInfoDataList(List<ProfileActivityInfoDTO> activityInfoDTOList,Boolean apNumFlag) throws Exception {

        List<String> activityIds = activityInfoDTOList.stream().map(ProfileActivityInfoDTO::getActivityId).collect(Collectors.toList());

        List<ProfileActivityInfoDataVO> activityInfoDataVOList = new ArrayList<>(activityInfoDTOList.size());
        //查询AP
        Map<String, List<ProfileActivityApDTO>> activityIdGroupMap = getActivityIdGroupMap(apNumFlag, activityIds);

        for (ProfileActivityInfoDTO info : activityInfoDTOList){
            ProfileActivityInfoDataVO profileActivityInfo = new ProfileActivityInfoDataVO();
            BeanUtils.copyProperties(info,profileActivityInfo);
            profileActivityInfo.setActivityTypeName(ActivityTypeEnum.getDescByType(profileActivityInfo.getActivityType()));
            List<ProfileActivityApDTO> activityAps = activityIdGroupMap.get(info.getActivityId());
            //封装AP数量
            if (CollectionUtils.isNotEmpty(activityAps)){
                profileActivityInfo.setApNum(activityAps.size());
            }
            activityInfoDataVOList.add(profileActivityInfo);
        }
        return activityInfoDataVOList;
    }

    public Map<String, List<ProfileActivityApDTO>> getActivityIdGroupMap(Boolean apNumFlag, List<String> activityIds) throws Exception {
        Map<String, List<ProfileActivityApDTO>> activityIdGroupMap = new HashMap<>();
        if (apNumFlag){
            ActivityApDataQuery activityApDataQuery = new ActivityApDataQuery();
            activityApDataQuery.setActivityRowId(String.join(CharacterConstant.COMMA, activityIds));
            QueryResult activityApResult = activityDataResultsService.activityApQueryResult(BizRequestUtil.createWithCurrentUser(activityApDataQuery));
            activityApResult = Optional.ofNullable(activityApResult).orElse(new QueryResult());
            if (CollectionUtils.isNotEmpty(activityApResult.getRows())){
                List<ProfileActivityApDTO> activityApDTOList = activityApResult.getRows(ProfileActivityApDTO.class);
                activityIdGroupMap = activityApDTOList.stream().collect(Collectors.groupingBy(ProfileActivityApDTO::getActivityRowId));
            }
        }
        return activityIdGroupMap;
    }

    @Override
    public List<ProfileActivityApDTO> queryActivityApDate(BizRequest<ActivityApDataQuery> bizRequest) throws Exception {
        List<ProfileActivityApDTO> activityApDTOList = new ArrayList<>();
        QueryResult activityApResult = activityDataResultsService.activityApQueryResult(bizRequest);
        activityApResult = Optional.ofNullable(activityApResult).orElse(new QueryResult());
        if (CollectionUtils.isNotEmpty(activityApResult.getRows())){
            activityApDTOList =  activityApResult.getRows(ProfileActivityApDTO.class);
        }
        return activityApDTOList;
    }
}
