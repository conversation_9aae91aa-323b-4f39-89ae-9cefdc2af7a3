package com.zte.mcrm.activity.integration.ai.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/23 16:11
 */
@Setter
@Getter
@ToString
public class AiAssistantResponseDTO implements Serializable {
    private static final long serialVersionUID = 5125473074983759281L;

    /**
     * 活动内容
     */
    private String activityContent;

    /**
     * 活动地点
     */
    private String activityPlace;

    /**
     * 活动标题
     */
    private String activityTitle;

    /**
     * 交流方式
     */
    private String communicationWay;

    /**
     * 活动相关人员
     */
    private List<AiAssistantContentPeopleDTO> contentPeople;
    /**
     * 活动的国家城市
     */
    private List<AiAssistantCountryCityDTO> countryCity;
    /**
     * 日期
     */
    private String date;

    /**
     * 结束时间
     */
    private String endMoment;

    /**
     * 是否有主客户
     */
    private String hasMainCust;
    /**
     * 讲师人员
     */
    private List<String> lecturers;
    /**
     * 客户联系人
     */
    private List<AiAssistantCustPeopleInfoDTO> listCustPeopleInfo;
    /**
     * 我司参与人列表
     */
    private List<String> listZtePeopleInfo;
    /**
     * 主客户信息
     */
    private AiAssistantMainCustDTO mainCust;
    /**
     * 项目
     */
    private String project;

    /**
     * 开始时间点
     */
    private String startMoment;


}
