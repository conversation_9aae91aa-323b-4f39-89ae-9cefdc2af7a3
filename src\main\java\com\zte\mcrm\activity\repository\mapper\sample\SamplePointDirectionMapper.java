package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointDirectionDO;

public interface SamplePointDirectionMapper {
    /**
     * all field insert
     */
    int insert(SamplePointDirectionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(SamplePointDirectionDO record);

    /**
     * query by primary key
     */
    SamplePointDirectionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(SamplePointDirectionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(SamplePointDirectionDO record);
}