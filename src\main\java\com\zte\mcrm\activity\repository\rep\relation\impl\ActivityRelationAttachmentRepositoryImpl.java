package com.zte.mcrm.activity.repository.rep.relation.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.relation.ActivityRelationAttachmentExtMapper;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
@Service
public class ActivityRelationAttachmentRepositoryImpl implements ActivityRelationAttachmentRepository {
    @Autowired
    private ActivityRelationAttachmentExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityRelationAttachmentDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());

        return extMapper.insertSelective(record);
    }

    @Override
    public int insertSelectiveList(List<ActivityRelationAttachmentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityRelationAttachmentDO record : recordList) {
            setDefaultValue(record);
            extMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationAttachmentDO record) {
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationAttachmentDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public List<ActivityRelationAttachmentDO> queryAttachmentByActivityRowIds(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : extMapper.queryAttachmentByActivityRowIds(activityRowIds);
    }

    @Override
    public List<ActivityRelationAttachmentDO> queryActivityAttachmentList(List<String> sceneOriginRowIds) {
        return CollectionUtils.isEmpty(sceneOriginRowIds) ? Collections.emptyList()
                : extMapper.queryAttachmentBySceneOriginRowIds(sceneOriginRowIds);
    }

    @Override
    public ActivityRelationAttachmentDO queryAllBySceneOriginRowId(String sceneOriginRowId){
        return StringUtils.isBlank(sceneOriginRowId) ? null
                :extMapper.queryAllBySceneOriginRowId(sceneOriginRowId);
    }

    @Override
    public List<ActivityRelationAttachmentDO> queryByActivityRowIdAndSummaryId(String activityRowId, String activitySummaryRowId) {
        return StringUtils.isBlank(activityRowId) || StringUtils.isBlank(activitySummaryRowId)
                ? Collections.emptyList() : extMapper.queryByActivityRowIdAndSummaryId(activityRowId, activitySummaryRowId);
    }

    @Override
    public int deleteBatch(List<String> rowIdList) {
        return CollectionUtils.isEmpty(rowIdList) ? 0 : extMapper.deleteBatch(rowIdList);
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.softDeleteByActivityIds(operator, activityIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return extMapper.deleteByRowIds(operator, rowIds);

    }

    /**
     * 批量插入数据
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchInsert(List<ActivityRelationAttachmentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        recordList.forEach(this::setDefaultValue);
        return extMapper.batchInsert(recordList);
    }

    /**
     * 批量修改数据
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int batchUpdate(List<ActivityRelationAttachmentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityRelationAttachmentDO customerInfoDO : recordList) {
            this.updateByPrimaryKeySelective(customerInfoDO);
        }
        return recordList.size();
    }

    @Override
    public int batchUpdateWithoutLastUpdate(List<ActivityRelationAttachmentDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityRelationAttachmentDO customerInfoDO : recordList) {
            extMapper.updateByPrimaryKeySelective(customerInfoDO);
        }
        return recordList.size();
    }


    /**
     * 设置默认值
     *
     * @param attachmentDO 实体类
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    private void setDefaultValue(ActivityRelationAttachmentDO attachmentDO) {
       attachmentDO.setRowId(Optional.ofNullable(attachmentDO.getRowId()).orElse(keyIdService.getKeyId()));
       attachmentDO.setCreatedBy(Optional.ofNullable(attachmentDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
       attachmentDO.setLastUpdatedBy(Optional.ofNullable(attachmentDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
       attachmentDO.setCreationDate(new Date());
       attachmentDO.setLastUpdateDate(new Date());
       attachmentDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int deleteBySceneOriginRowIds(String operator, List<String> sceneOriginRowIds) {
        if (CollectionUtils.isEmpty(sceneOriginRowIds)) {
            return NumberConstant.ZERO;
        }
        return extMapper.deleteBySceneOriginRowIds(operator,sceneOriginRowIds);
    }

    @Override
    public List<ActivityRelationAttachmentDO> queryErrorType(List<String> errorTypeList) {
        return extMapper.queryErrorType(errorTypeList);
    }

    /**
     * 查询所有-包含无效数据
     * 增加enabled_flag = 'Y'，推送ES不需要无效附件
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationAttachmentDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityRelationAttachmentDO> queryAllActivityWithNotEnable(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllActivityWithNotEnable(activityRowId);
    }

    /**
     * 按照主键和类型更新
     *
     * @param record 附件
     * @return int
     * <AUTHOR>
     * date: 2024/1/18 20:12
     */
    @Override
    public int updateByPrimaryKeySelectiveAndType(ActivityRelationAttachmentDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return extMapper.updateByPrimaryKeySelectiveAndType(record);
    }

    /**
     * 根据条件查询附件信息
     *
     * @param param 附件信息
     * @return 附件信息
     */
    @Override
    public ActivityRelationAttachmentDO queryByCondition(ActivityRelationAttachmentDO param) {
        if (null == param) {
            return new ActivityRelationAttachmentDO();
        }
        return extMapper.queryByCondition(param);
    }

    /**
     * 根据业务id和附件类型查询单个附件
     *
     * @param attachmentSceneType
     * @param sceneOriginRowId
     * @return {@link ActivityRelationAttachmentDO}
     * <AUTHOR>
     * @date 2025/3/2 下午12:34
     */
    @Override
    public ActivityRelationAttachmentDO queryAttachmentBySceneOriginRowIdAndType(String attachmentSceneType, String sceneOriginRowId) {
        if (StringUtils.isAnyBlank(attachmentSceneType, sceneOriginRowId)) {
            return null;
        }
        return extMapper.queryAttachmentBySceneOriginRowIdAndType(attachmentSceneType, sceneOriginRowId);
    }
}
