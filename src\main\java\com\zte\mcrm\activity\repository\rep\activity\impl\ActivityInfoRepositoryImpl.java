package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityInfoExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityVisitingStatDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoListQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoPageQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoSearchPushQuery;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityConvergenceInfoVO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoFolVO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ConvenientInfoParam;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoVO;
import com.zte.mcrm.activity.web.controller.reservation.param.ActivityResourceReservationParam;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationVo;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.common.util.DateUtil;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ActivityInfoRepositoryImpl implements ActivityInfoRepository {
    @Autowired
    private ActivityInfoExtMapper extMapper;

    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityInfoDO record) {
        if(Objects.isNull(record)){
            return NumberConstant.ZERO;
        }
        setInsertDefaultValue(record);
        return extMapper.insertSelective(record);
    }

    @Override
    public ActivityInfoDO selectByPrimaryKey(String rowId) {
        return StringUtils.isBlank(rowId) ? null : extMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityInfoDO record) {
        if(Objects.isNull(record)){
            return NumberConstant.ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ActivityInfoDO record) {
        if(Objects.isNull(record)){
            return NumberConstant.ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 提供给预约看板的活动分页列表
     * @param param
     * @return
     */
    @Override
    public PageRows<ActivityResourceReservationVo> pageQueryToReservation(PageQuery<ActivityResourceReservationParam> param) {
        // 设置分页
        PageHelper.startPage(param.getPageNo(), param.getPageSize());
        List<ActivityInfoDO> infoDOList = extMapper.pageQueryToReservation(param.getParam());
        List<ActivityResourceReservationVo> reservationVos = batchCopyToInfo(infoDOList);
        PageInfo<ActivityInfoDO> pageInfo = new PageInfo<>(infoDOList);
        //返回分页数据
        PageRows<ActivityResourceReservationVo> pageRows = new PageRows<>();
        pageRows.setRows(reservationVos);
        //设置当前页数
        pageRows.setCurrent(param.getPageNo());
        //返回符合条件的总记录数
        pageRows.setTotal(pageInfo.getTotal());
        return pageRows;
    }

    /**
     * 转换VO
     * @param infoDOS
     * @return
     */
    private List<ActivityResourceReservationVo> batchCopyToInfo(List<ActivityInfoDO> infoDOS) {
        List<ActivityResourceReservationVo> reservationVos = Lists.newArrayList();
        infoDOS.forEach(e ->{
            ActivityResourceReservationVo reservationVo = new ActivityResourceReservationVo();
            BeanUtils.copyProperties(e, reservationVo);
            reservationVo.setActivityRowId(e.getRowId());
            reservationVos.add(reservationVo);
        });
        return reservationVos;
    }
    @Override
    public PageRows<ActivityInfoDO> searchByPage(ActivityInfoQuery query) {
        PageRows<ActivityInfoDO> activityInfoDoPageRows = new PageRows<>();
        if(!query.check()){
            return PageRowsUtil.buildEmptyPage(query);
        }
        Integer pageNo = query.getPageNo();
        Integer pageSize = Math.min(query.getPageSize(), NumberConstant.MAX_PAGE_SIZE);
        query.setStartRow((pageNo - 1) * pageSize);
        query.setRowSize(pageSize);
        List<ActivityInfoDO> activityInfoDOList = extMapper.getList(query);
        int total = extMapper.countList(query);
        long totalPage = pageSize == 0 ? 0 : (total%pageSize == 0 ? total/pageSize : total/pageSize + 1);
        activityInfoDoPageRows.setRows(activityInfoDOList);
        activityInfoDoPageRows.setTotal(total);
        activityInfoDoPageRows.setCurrent(pageNo);
        activityInfoDoPageRows.setPageSize(pageSize);
        activityInfoDoPageRows.setTotalPage(totalPage);
        return activityInfoDoPageRows;
    }

    @Override
    public List<ActivityConvergenceInfoVO> getActivityConvergenceInfoList(ActivityInfoQuery query) {
        return extMapper.getActivityConvergenceInfoList(query);
    }

    /**
     * 查询列表数量
     *
     * @param query
     * @return {@link long}
     * <AUTHOR>
     * @date 2023/8/17 下午8:05
     */
    @Override
    public long countList(ActivityInfoQuery query) {
        return extMapper.countList(query);
    }

    /**
     * 查询状态为已完成，已完成评价，进行中但是时间已经过了活动时间的单据数目
     * @param query
     * @return
     */
    public Integer countListForSpecialStatus(ActivityInfoQuery query) {
        return extMapper.countListForSpecialStatus(query);
    }

    @Override
    public List<ActivityInfoDO> getListForSpecialStatus(ActivityInfoQuery query) {
        return extMapper.getListForSpecialStatus(query);
    }

    @Override
    public int insertSelectiveWithoutDefaultValue(ActivityInfoDO record) {
        if(Objects.isNull(record)){
            return NumberConstant.ZERO;
        }
        record.setRowId(Optional.ofNullable(record.getRowId()).orElse(keyIdService.getKeyId()));
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }

    /**
     * 查询重复的活动编号
     *
     * @param limit
     * @return
     */
    @Override
    public List<String> getRepeatedActivityReqNoList(int limit) {
        return extMapper.getRepeatedActivityReqNoList(limit);
    }

    @Override
    public int batchUpdateByPrimaryKey(List<ActivityInfoDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        return extMapper.batchUpdateByPrimaryKey(updateList);
    }

    @Override
    public List<ActivityInfoDO> getCommunicationWayEmptyList(int limit) {
        return extMapper.getCommunicationWayNullList(limit);
    }

    @Override
    public List<ActivityInfoDO> searchByParam(ActivityInfoQuery query) {
        return extMapper.getList(query);
    }

    /**
     * 根据ISearch查询条件进行查询
     * @param query 查询条件
     * @return java.util.List<com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO>
     * <AUTHOR>
     * date: 2023/12/4 16:00
     */
    @Override
    public List<ActivityInfoDO> searchByISearchParam(ActivityInfoSearchPushQuery query) {
        return extMapper.searchByISearchParam(query);
    }

    /**
     * 模糊查询
     *
     * @param query 查询条件
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/5/23 17:28
     */
    @Override
    public List<ActivityInfoDO> fuzzyQueryByRowIds(ActivityInfoQuery query) {
        if (query == null || CollectionUtils.isEmpty(query.getActivityRowIdList()) || !query.check()) {
            return Lists.newArrayList();
        }
        List<ActivityInfoDO> activityInfoDOList = extMapper.fuzzyQueryByRowIds(query);
        return activityInfoDOList;
    }

    @Override
    public int deleteByIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        return extMapper.softDeleteByIds(operator, rowIds);
    }

    @Override
    public List<ActivityInfoDO> selectByIds(List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return Collections.emptyList();
        }
        return extMapper.selectByIds(rowIds);
    }

    @Override
    public List<ActivityInfoDO> selectByOriginRowIds(List<String> originRowIds) {
        if (CollectionUtils.isEmpty(originRowIds)) {
            return new ArrayList<>();
        }
        return extMapper.selectByOriginRowIds(originRowIds);
    }

    /**
     * 设置默认值
     *
     * @param activityInfoDO
     * <AUTHOR>
     * @date: 2023/5/24 19:14
     */
    private void setInsertDefaultValue(ActivityInfoDO activityInfoDO) {
        activityInfoDO.setRowId(Optional.ofNullable(activityInfoDO.getRowId()).orElse(keyIdService.getKeyId()));
        activityInfoDO.setCreatedBy(Optional.ofNullable(activityInfoDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        activityInfoDO.setLastUpdatedBy(Optional.ofNullable(activityInfoDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        activityInfoDO.setCreationDate(new Date());
        activityInfoDO.setLastUpdateDate(new Date());
        activityInfoDO.setEnabledFlag(BooleanEnum.Y.getCode());
        activityInfoDO.setActivityRequestNo(getActivityRequestNo(activityInfoDO));
    }


    /**
     * 线下活动按：年月日（8位）+主客户国家码+5位流水号；
     * @return
     */
    private String getActivityRequestNo(ActivityInfoDO record){
        StringBuilder sb = new StringBuilder();
        int todayTotalNum =  extMapper.countTodayTotalNum() + 1;
        String totalNum = String.format("%05d", todayTotalNum);
        String currentDay=  DateUtil.getDate();
        String countryName =Optional.ofNullable(record.getCountryShortName()).orElse(StringUtils.EMPTY);
        sb.append(currentDay).append(countryName).append(totalNum);
        return sb.toString();
    }

    /**
     * 查询所有-包含无效数据
     * @param rowIds 活动Id
     * @return java.util.List<ActivityInfoDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    @Override
    public List<ActivityInfoDO> selectByIdsWithNoEnable(List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return Collections.emptyList();
        }
        return extMapper.selectByIdsWithNoEnable(rowIds);
    }

    @Override
    public ActivityInfoDO selectLatestVisitingActivity(ActivityInfoQuery query) {
        return extMapper.selectLatestVisitingActivity(query);
    }

    @Override
    public ActivityInfoDO getLatestActivityInfo(ActivityInfoQuery query) {

        return extMapper.getLatestActivityInfo(query);
    }

    @Override
    public Map<String, Integer> countVisitingActivity(ActivityInfoQuery query) {
        List<ActivityVisitingStatDO> activityVisitingStatDOS = extMapper.queryActivityVisitingStat(query);
        if (CollectionUtils.isEmpty(activityVisitingStatDOS)) {
            return Maps.newHashMap();
        }
        return activityVisitingStatDOS.stream().collect(Collectors.toMap(ActivityVisitingStatDO::getOriginRowId,
                ActivityVisitingStatDO::getVisitCount, (k1,k2) -> k1));
    }
    
    @Override
    public List<String> getMigrateActivityIds(int offset){
        return extMapper.getMigrateActivityIds(offset);
    }

    @Override
    public ConvenientInfoVO selectLastCityAndCountry(ConvenientInfoParam param) {
        return extMapper.selectLastCityAndCountry(param);
    }

    @Override
    public PageRows<ActivityInfoFolVO> pageQuery4Fol(int pageNo, int pageSize, ActivityInfoPageQuery queryParam) {
        PageInfo<ActivityInfoFolVO> pageInfo = PageHelper.startPage(pageNo, pageSize, true).doSelectPageInfo(() -> extMapper.query4Fol(queryParam));
        List<ActivityInfoFolVO> activityInfoFolList = pageInfo.getList();
        return PageRowsUtil.buildPageRow(pageNo, pageSize, pageInfo.getTotal(), activityInfoFolList);
    }

    @Override
    public List<ActivityInfoFolVO> queryList4Fol(ActivityInfoListQuery queryParam) {
        return extMapper.queryList4Fol(queryParam);
    }

    /**
     * 根据使用id进行游标分页，用于全表等可能深度分页的大数据量处理
     *
     * @param param
     * @param limit
     * @return {@link List< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2024/10/24 下午6:58
     */
    @Override
    public List<ActivityInfoDO> selectListPageById(ActivityInfoDO param, int limit) {
        return extMapper.selectListPageById(param, limit);
    }

    /**
     * 查询参与人相关的展会活动，
     *
     * @param pageQuery
     * @return {@link List< ActivityInfoDO>}
     * <AUTHOR>
     * @date 2025/3/6 下午2:34
     */
    @Override
    public PageRows<ActivityInfoDO> selectExhibitionActivityWithRelationPeople(PageQuery<ActivityInfoQuery> pageQuery) {
        if (!pageQuery.validatePage()) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        PageInfo<ActivityInfoDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> extMapper.selectExhibitionActivityWithRelationPeople(pageQuery.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }
}
