package com.zte.mcrm.activity.common.cache.loader;

import com.zte.mcrm.activity.common.cache.model.AreaDataModel;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.adapter.MdmAdapter;
import com.zte.mcrm.adapter.constant.AreaConstant;
import com.zte.mcrm.adapter.dto.MdmAreaDTO;
import com.zte.mcrm.adapter.vo.MdmAreaVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【区域】数据加载器
 *
 * <AUTHOR>
 */
@Component
public class AreaDataLoader implements CacheDataLoader<String, AreaDataModel> {

    @Autowired
    private MdmAdapter mdmAdapter;

    @Override
    public AreaDataModel load(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        return loadAll(Collections.singleton(key)).get(key);
    }

    @Override
    public Map<String, AreaDataModel> loadAll(Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }

        List<String> areaCodeKeys = keys.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        List<MdmAreaDTO> res = fetchMdmArea(areaCodeKeys);

        return res.stream().map(AreaDataModel::new).collect(Collectors.toMap(e -> e.identityKey(), Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 查询区域信息
     *
     * @param areaCodeKey
     * @return
     */
    List<MdmAreaDTO> fetchMdmArea(List<String> areaCodeKey) {
        if (CollectionUtils.isEmpty(areaCodeKey)) {
            return Collections.emptyList();
        }

        String areaType = AreaDataModel.fetchAreaType(areaCodeKey.get(0));
        if (StringUtils.isBlank(areaType)) {
            return Collections.emptyList();
        }

        Set<String> areaCodes = areaCodeKey.stream().map(AreaDataModel::fetchAreaCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (areaCodes.isEmpty()) {
            return Collections.emptyList();
        }

        MdmAreaVO mdmAreaVO = new MdmAreaVO();
        mdmAreaVO.setAreaDefaultUrl(AreaConstant.QUERY_SERACH_GJDQ);
        mdmAreaVO.setSynCode(AreaConstant.QUERY_SERACH_GJDQ_SYS_CODE);
        mdmAreaVO.setCode(String.join(CharacterConstant.COMMA, areaCodes));
        mdmAreaVO.setDesc2(areaType);

        List<MdmAreaDTO> res = mdmAdapter.queryAreaInfo(mdmAreaVO);

        return CollectionUtils.isEmpty(res) ? Collections.emptyList() : res;
    }
}
