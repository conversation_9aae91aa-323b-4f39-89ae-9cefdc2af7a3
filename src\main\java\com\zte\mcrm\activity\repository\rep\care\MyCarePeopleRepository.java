package com.zte.mcrm.activity.repository.rep.care;

import com.zte.mcrm.activity.repository.model.care.MyCarePeopleDO;
import com.zte.mcrm.activity.web.controller.resource.param.MyCarePeopleParam;

import java.util.List;

/**
 * 我关注的人
 *
 * <AUTHOR>
 */
public interface MyCarePeopleRepository {

    /**
     * 添加我关注的人（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<MyCarePeopleDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(MyCarePeopleDO record);

    /**
     * 查询我关注的所有人
     *
     * @param param 员工编号
     * @return
     */
    List<MyCarePeopleDO> queryAllMyCarePeople(MyCarePeopleParam param);
}
