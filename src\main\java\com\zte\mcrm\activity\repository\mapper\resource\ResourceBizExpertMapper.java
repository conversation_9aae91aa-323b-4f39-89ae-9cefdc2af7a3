package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceReservationScheduleDO;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ResourceBizExpertMapper {
    /**
     * all field insert
     */
    int insert(ResourceBizExpertDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ResourceBizExpertDO record);

    /**
     * query by primary key
     */
    ResourceBizExpertDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ResourceBizExpertDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ResourceBizExpertDO record);

    /**
     * 新工号切换
     */
    List<ResourceBizExpertDO> queryEmpNoTransList(DataTransParam searchParam);
}