package com.zte.mcrm.activity.application.exhibition.business;

import com.zte.mcrm.activity.common.enums.item.ZtePeopleOrderEnum;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;

import java.util.Comparator;

/**
 * <AUTHOR> 10307200
 * @since 2024-02-19 下午4:48
 **/
public class ExhibitionZtePeopleComparator implements Comparator<ActivityScheduleItemPeopleDO> {

    @Override
    public int compare(ActivityScheduleItemPeopleDO o1, ActivityScheduleItemPeopleDO o2) {
        int orderNumber1 = ZtePeopleOrderEnum.getPositionOrderNum(o1.getPeopleLabel(), o1.getPosition());
        int orderNumber2 = ZtePeopleOrderEnum.getPositionOrderNum(o2.getPeopleLabel(), o2.getPosition());
        return Integer.compare(orderNumber1, orderNumber2);
    }
}
