package com.zte.mcrm.activity.service.approval.event.helper;

import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName ApprovalHelper
 * @description: 审批节点辅助类
 * @author: 李龙10317843
 * @create: 2023-05-25 14:23
 * @Version 1.0
 **/
@ToString
@Getter
@Setter
public class ApprovalHelper {
    /* ################# 读区 ################# */

    /**
     * 活动详情
     */
    private ActivityInfoDO activityInfo;

    /**
     * 审批人信息
     */
    private EmployeeInfoDTO approve;



    /* ################# 写区 ################# */
    /**
     * 活动更新对象
     */
    private ActivityInfoDO activityInfoUpdate;

    /**
     * 审批节点新增对象
     */
    private ActivityApprovalProcessDO approvalProcessAdd;

    /**
     * 审批节点详情新增对象
     */
    private ActivityApprovalProcessNodeDO approvalProcessNodeAdd;

    /**
     * 生命周期新增对象
     */
    private ActivityStatusLifecycleDO activityStatusLifecycleAdd;
}