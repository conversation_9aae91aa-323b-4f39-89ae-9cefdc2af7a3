package com.zte.mcrm.activity.integration.accountinfo.dto;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 最近浏览客户信息（常用客户信息卡片）
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel("最近浏览客户信息")
public class LatestVisitCustomerDTO {
    /** 客户ID */
    private String customerId;
    /** 客户编码 */
    private String customerCode;
    /** 客户名称 */
    private String customerName;
    /** 客户个英文名称 */
    private String customerEnglishName;
    /** 客户级别 */
    private String customerLevel;
    /** 客户MVC,Y-是，N-否 */
    private String mvcFlag;
    /**
     * 客户级别/是否MVC
     * 示例“S/MVC”；若无客户级别或无是否MVC数据则不展示，示例“S”、“MVC”；两个数据都为空则不展示。
     */
    private String customerLevelMvcDesc;

    /** 联系人数量 */
    private int contactPersonNum;
}
