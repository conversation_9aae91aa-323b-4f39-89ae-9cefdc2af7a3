package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-17 9:38
 **/
public interface ActivityScheduleOrchestrationDetailRepository {
    /**
     * description 批量添加活动日程编排详情信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:39
     */
    int insertSelective(List<ActivityScheduleOrchestrationDetailDO> recordList);

    /**
     * description 根据主键更新活动日程编排详情信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:39
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationDetailDO record);

    /**
     * 批量删除（逻辑删除）
     *
     * @param rowIdList 主键ID
     * @param operEmpNo 操作人员工编号
     * @return
     */
    int batchDelete(List<String> rowIdList, String operEmpNo);

    /**
     * description 根据版本ID批量查询活动日程编排详情信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/17 上午9:39
     */
    Map<String, List<ActivityScheduleOrchestrationDetailDO>> queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(List<String> orchestrationRowIds);

    /***
     * <p>
     * 根据日程编排版本RowId 获取 对应的详情信息
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 上午10:12
     * @param orchestrationRowIds 日程编码版本RowId
     * @return java.util.List<com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO>
     */
    List<ActivityScheduleOrchestrationDetailDO> getActivityScheduleOrchestrationDetailListByRelationVersionRowIds(List<String> orchestrationRowIds);

    /***
     * <p>
     * 根据日程ID批量查询活动日程编排详情信息
     *
     * </p>
     * <AUTHOR>
     * @since  2023/11/5 下午1:27
     * @param scheduleItemRowIds 日程Id列表
     * @return java.util.Map<java.lang.String,java.util.List<com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO>>
     */
    Map<String, ActivityScheduleOrchestrationDetailDO> queryActivityScheduleOrchestrationDetailsByScheduleItemRowIds(List<String> scheduleItemRowIds);

    /***
     * <p>
     * 根据日程编排版本RowId 获取 对应的详情信息
     *
     * </p>
     * <AUTHOR>
     * @since  2023/10/25 上午10:12
     * @param scheduleItemRowIds 日程Id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO>
     */
    List<ActivityScheduleOrchestrationDetailDO> getActivityScheduleOrchestrationDetailListByScheduleItemRowIds(List<String> scheduleItemRowIds);
}
