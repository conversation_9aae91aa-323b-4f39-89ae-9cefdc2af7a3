package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemModel;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemPeopleModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/1/14 下午2:08
 */
@Mapper(componentModel = "spring")
public interface ScheduleItemModelConvert {
    ScheduleItemModelConvert INSTANCE = Mappers.getMapper(ScheduleItemModelConvert.class);

    List<ActivityScheduleItemDO> scheduleItemModelToDO(List<ActivityScheduleItemModel> activityScheduleItemModels);

    ActivityScheduleItemModel scheduleItemDOToModel(ActivityScheduleItemDO activityScheduleItemDO);

    @Mapping(source = "peopleNo",target = "peopleCode")
    ActivityRelationZtePeopleDO scheduleItemPeopleModelToZtePeopleDO(ActivityScheduleItemPeopleModel activityScheduleItemPeopleModel);
    List<ActivityRelationZtePeopleDO> scheduleItemPeopleModelToZtePeopleDOList(List<ActivityScheduleItemPeopleModel> activityScheduleItemPeopleModels);

    List<ActivityScheduleItemPeopleDO> scheduleItemPeopleModelToDO(List<ActivityScheduleItemPeopleModel> activityScheduleItemPeopleModels);

    List<ActivityScheduleItemPeopleModel> scheduleItemPeopleDOToModel(List<ActivityScheduleItemPeopleDO> activityScheduleItemPeopleDOs);
}
