package com.zte.mcrm.activity.common.cache.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zte.mcrm.activity.common.cache.loader.CacheDataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * 本地内存缓存
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
public class LocalCache<V> extends BaseCacheWithStrKey<V> {
    private static final Logger logger = LoggerFactory.getLogger(LocalCache.class);
    /**
     * 本地缓存
     */
    private LoadingCache<String, V> localCache;

    /**
     * @param loader 缓存数据加载器
     * @param config 缓存配置
     */
    public LocalCache(CacheDataLoader<String, V> loader, CacheConfig config) {
        super(loader);
        init(config);
    }

    @Override
    public V fetchCache(String key) {
        return fetchCacheInner(key);
    }

    @Override
    public Map<String, V> fetchCachesOnlyExist(Set<String> keys) {
        return localCache.getAllPresent(keys);
    }

    @Override
    public void addCache(String key, V val) {
        localCache.put(key, val);
    }

    @Override
    public void invalidate(String key) {
        localCache.invalidate(key);
    }

    @Override
    public void invalidateAll() {
        localCache.invalidateAll();
    }

    /**
     * 获取缓存数据
     *
     * @param key
     * @return
     */
    private V fetchCacheInner(String key) {
        V val = null;
        try {
            val = localCache.get(key);
        } catch (ExecutionException e) {
            logger.error("LocalCache fetch key:{} ExecutionException", key, e);
        } catch (Exception biz) {
            logger.error("查询为空", biz);
        }

        return val;
    }

    /**
     * 初始化
     *
     * @param config
     */
    private void init(CacheConfig config) {
        localCache = CacheBuilder.newBuilder()
                .expireAfterWrite(config.getExpireTime(), config.getExpireTimeUnit())
                .maximumSize(config.getMaxSize())
                .initialCapacity(128)
                .build(new CacheLoader<String, V>() {
                    @Override
                    public V load(String key) {
                        return reloadCache(key);
                    }
                });
    }
}
