package com.zte.mcrm.activity.common.enums;

import lombok.Getter;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Getter
public enum BooleanEnum {

    /**
     * Y 表示布尔true。如：启用-必传-常用-同步等
     */
    Y("Y", new String[]{"yes", "true", "1", "正确", "对", "真", "是"}, "表示布尔true"),
    /**
     * N 表示布尔false。如：未启用-非必传-非常用-未同步等
     */
    N("N", new String[]{"no", "false", "0", "错误", "错", "假", "否"}, "表示布尔true");

    private String code;
    private String[] alias;
    private String desc;

    BooleanEnum(String code, String[] alias, String desc) {
        this.code = code;
        this.alias = alias;
        this.desc = desc;
    }

    /**
     * 是否当前枚举
     *
     * @param type PO字段类型
     * @return
     */
    public boolean isMe(String type) {
        boolean flag = this.code.equalsIgnoreCase(type);

        for (String alia : alias) {
            flag = flag || alia.equalsIgnoreCase(type);
        }

        return flag;
    }

    /**
     * 返回对应标准code
     * @param flag
     * @return
     */
    public static String getCode(boolean flag) {
        return flag ? BooleanEnum.Y.getCode() : BooleanEnum.N.getCode();
    }

    /**
     * 解析boolean
     * @param type
     * @return 符合BooleanEnum则返回对应boolean，否则返回null
     */
    public static Boolean parseBoolean(String type) {
        if (BooleanEnum.Y.isMe(type)) {
            return true;
        }

        if (BooleanEnum.N.isMe(type)) {
            return false;
        }
        return null;
    }

    /**
     * 是否有效PO字段类型
     * @param type po字段类型
     * @return true-有效，false-无效
     */
    public static boolean valid(String type) {
        for (BooleanEnum e : BooleanEnum.values()) {
            if (e.isMe(type)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 当前codes是否都属于enums？
     *
     * @param codes
     * @return
     */
    public static boolean in(List<String> codes) {
        for (String code : codes) {
            if (!valid(code)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 当前codes是否都为Y？
     * @param codes
     * @return
     */
    public static String and(List<String> codes) {
        for (String code : codes) {
            if (BooleanEnum.N.isMe(code)) {
                return BooleanEnum.N.code;
            }
        }
        return BooleanEnum.Y.code;
    }

}
