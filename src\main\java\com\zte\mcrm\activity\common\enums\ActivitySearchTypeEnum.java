package com.zte.mcrm.activity.common.enums;

import lombok.Getter;

/**
 * 日程授权类型
 *
 * <AUTHOR>
 * @date 2023/8/25 下午4:31
 */
@Getter
public enum ActivitySearchTypeEnum {

    /**
     * 使用权限查询-all
     */
    ALL("all"),

    /**
     * 与我有关
     */
    WITH_ME("related"),

    /**
     * 我关注的
     */
    FOLLOW("myCare"),

    /**
     * 待我处理
     */
    NEED_TO_HANDLE("toDo"),
    /**
     * 待我处理
     */
    NEED_TO_HANDLE_PC("toDoPc"),
    /**
     * 遗留AP相关
     */
    REMAINING("remainingAp"),

    ;

    private final String code;

    ActivitySearchTypeEnum(String code) {
        this.code = code;
    }

    /**
     * 是否当前枚举
     *
     * @param code 操作类型
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/25 下午4:35
     */
    public boolean isMe(String code) {
        return this.code.equalsIgnoreCase(code);
    }

    /**
     * 是否当前所列枚举
     *
     * @param code 操作类型
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/25 下午4:35
     */
    public static boolean in(String code, ActivitySearchTypeEnum... enums) {
        for (ActivitySearchTypeEnum searchTypeEnum : enums) {
            if (searchTypeEnum.isMe(code)) {
                return true;
            }
        }
        return false;
    }
}
