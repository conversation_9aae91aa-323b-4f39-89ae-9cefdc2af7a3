package com.zte.mcrm.activity.repository.mapper.notice;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityPendingNoticeMapper {
    /**
     * all field insert
     */
    int insert(ActivityPendingNoticeDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityPendingNoticeDO record);

    /**
     * query by primary key
     */
    ActivityPendingNoticeDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityPendingNoticeDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityPendingNoticeDO record);

    /**
     * 新工号切换
     */
    List<ActivityPendingNoticeDO> queryEmpNoTransList(DataTransParam searchParam);
}