package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationImportDO;

public interface ActivityScheduleOrchestrationImportMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleOrchestrationImportDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleOrchestrationImportDO record);

    /**
     * query by primary key
     */
    ActivityScheduleOrchestrationImportDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleOrchestrationImportDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationImportDO record);
}