package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityResourceFeeExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceFeeDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceFeeRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-16 17:04
 **/
@Component
public class ActivityResourceFeeRepositoryImpl implements ActivityResourceFeeRepository {
    @Autowired
    private IKeyIdService keyIdService;
    @Resource
    private ActivityResourceFeeExtMapper activityResourceFeeExtMapper;

    public void setSumFee(ActivityResourceFeeDO record) {
        BigDecimal zero = new BigDecimal("0");

        record.setAirTicketFee(
                record.getAirTicketFee() != null ? record.getAirTicketFee() : zero);
        record.setVisaFee(
                record.getVisaFee() != null ? record.getVisaFee() : zero);
        record.setBizReceptionFee(
                record.getBizReceptionFee() != null ? record.getBizReceptionFee() : zero);
        record.setHotelFee(
                record.getHotelFee() != null ? record.getHotelFee() : zero);
        record.setCarFee(
                record.getCarFee() != null ? record.getCarFee() : zero);
        record.setOtherFee(
                record.getOtherFee() != null ? record.getOtherFee() : zero);

        record.setSumFee(record.getAirTicketFee()
                .add(record.getVisaFee())
                .add(record.getBizReceptionFee())
                .add(record.getHotelFee())
                .add(record.getCarFee())
                .add(record.getOtherFee()));
    }

    @Override
    public int insertSelective(List<ActivityResourceFeeDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityResourceFeeDO record : recordList) {
            setSumFee(record);
            record.setRowId(keyIdService.getKeyId());
            setDefaultValue(record);
            activityResourceFeeExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    private void setDefaultValue(ActivityResourceFeeDO resourceFeeDO) {
        resourceFeeDO.setCreatedBy(Optional.ofNullable(resourceFeeDO.getCreatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceFeeDO.setLastUpdatedBy(Optional.ofNullable(resourceFeeDO.getLastUpdatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceFeeDO.setCreationDate(new Date());
        resourceFeeDO.setLastUpdateDate(new Date());
        resourceFeeDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityResourceFeeDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        setSumFee(record);
        record.setLastUpdateDate(new Date());
        return activityResourceFeeExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ActivityResourceFeeDO>> queryActivityResourceFeesByActivityRowIds(List<String> activityRowIds) {
        return org.springframework.util.CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityResourceFeeExtMapper.queryActivityResourceFeesByActivityRowIds(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityResourceFeeDO::getActivityRowId));
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return activityResourceFeeExtMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public void batchUpdate(List<ActivityResourceFeeDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (ActivityResourceFeeDO record : records) {
            setSumFee(record);
            updateByPrimaryKeys(record);
        }
    }

    private int updateByPrimaryKeys(ActivityResourceFeeDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return activityResourceFeeExtMapper.updateByPrimaryKeySelective(record);
    }
}
