package com.zte.mcrm.activity.application.exhibition.convert;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.mcrm.activity.application.exhibition.util.ScheduleItemDiffUtils;
import com.zte.mcrm.activity.application.model.ScheduleOrchestrationDataSource;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.DictConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionDirectorRoleTypeEnum;
import com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum;
import com.zte.mcrm.activity.common.export.model.ExcelExportParamSetModel;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.*;
import com.zte.mcrm.activity.integration.lookupapi.LookUpService;
import com.zte.mcrm.activity.integration.lookupapi.param.FastLookupSearchParamVO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionDirectorRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationDetailRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleOrchestrationVersionRepository;
import com.zte.mcrm.activity.repository.rep.item.param.ActivityScheduleOrchestrationVersionQuery;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.service.common.lookup.CommunicationDirectorComponent;
import com.zte.mcrm.activity.service.common.lookup.model.CommunicationDirectorTwoLevelModel;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.service.schedule.impl.support.ScheduleOrchestrationConstants;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleCustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.schedule.param.ScheduleOrchestrationExportParam;
import com.zte.mcrm.activity.web.controller.schedule.vo.LastScheduleOrchestrationVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationDetailVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationExportVO;
import com.zte.mcrm.activity.web.controller.schedule.vo.ScheduleOrchestrationVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.PAUSE_MARK;
import static com.zte.mcrm.activity.common.constant.ExhibitionConstant.ORCHESTRATION_NOTICE_ADMIN;
import static com.zte.mcrm.activity.common.constant.ExhibitionConstant.ORCHESTRATION_NOTICE_BUTTON;

/**
 * <AUTHOR>
 */
@Component
public class ScheduleOrchestrationQueryConvertor {

    @Autowired
    private CommunicationDirectorComponent communicationDirectorComponent;
    @Autowired
    private HrmOrgInfoSearchService hrmOrgInfoSearchService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private ActivityScheduleOrchestrationVersionRepository activityScheduleOrchestrationVersionRepository;
    @Autowired
    private ActivityScheduleOrchestrationDetailRepository activityScheduleOrchestrationDetailRepository;
    @Autowired
    private ActivityScheduleItemPeopleRepository activityScheduleItemPeopleRepository;
    @Autowired
    private ExhibitionDirectorRepository exhibitionDirectorRepository;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityApprovalProcessNodeRepository activityApprovalProcessNodeRepository;
    @Autowired
    private DictService dictService;
    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;
    @Autowired
    private LookUpService lookUpService;
    /**
     * 打包资源编排列表
     *
     * @param data
     * @return
     */
    public ScheduleOrchestrationVO toScheduleOrchestrationDataSource(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data) {
        ExhibitionInfoDO exhibitionInfo = data.getExhibitionInfo();

        ScheduleOrchestrationVO vo = new ScheduleOrchestrationVO();
        vo.setOrgType(getOrgType(request, data));
        vo.setType(exhibitionInfo.getOrchestrationControl());
        vo.setExhibitionRowId(exhibitionInfo.getRowId());
        vo.setExhibitionName(exhibitionInfo.getExhibitionName());
        vo.setExhibitionYear(DateFormatUtils.format(exhibitionInfo.getEndTime(), DateFormatUtil.DATE_YYYY));
        vo.setEntryOpenStatus(exhibitionInfo.getEntryOpenStatus());
        List<ScheduleOrchestrationDetailVO> detailList = packScheduleOrchestrationDetailVO(request, data);
        vo.setDetailList(detailList);
        vo.setUndealDataCount((int) detailList.stream().filter(e -> ResourceOrchestrationDealStatusEnum.WAIT.isMe(e.getDealStatus())).count());

        return vo;
    }

    @Nullable
    String getOrgType(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data) {
        return StringUtils.equals(data.getRoleTypeAll(), BooleanEnum.Y.getCode()) ?
                null : data.fetchScheduleOrchestrationType(request.getEmpNo());
    }

    /**
     * 最新发布资源编排列表
     *
     * @param request 请求参数
     * @param data    数据
     * @return
     */
    public LastScheduleOrchestrationVO toLastScheduleOrchestrationVO(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data) {
        LastScheduleOrchestrationVO lastVo = new LastScheduleOrchestrationVO();
        ScheduleOrchestrationVO tempVo = toScheduleOrchestrationDataSource(request, data);
        BeanUtils.copyProperties(tempVo, lastVo);

        ActivityScheduleOrchestrationVersionDO lastVersion = data.fetchLastPublishScheduleOrchestrationVersion();
        if (lastVersion == null) {
            return lastVo;
        }

        lastVo.setNoticeButton(orchestrationNoticeAdmin(lastVo.getExhibitionRowId(),request.getEmpNo()));

        lastVo.setPublishTime(DateFormatUtils.format(lastVersion.getPublishTime(), DateFormatUtil.DATE_YYYY_MM_DD_HH_MM_SS_PATTERN));
        Map<String, String> lastPublisherMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(
                Collections.singleton(lastVersion.getCreatedBy()))).getBo();
        lastVo.setPublisher(lastVersion.getCreatedBy());
        String lastPublisherName = lastPublisherMap.get(lastVersion.getCreatedBy());
        if (StringUtils.isNotBlank(lastPublisherName)) {
            lastVo.setPublisherDesc(lastPublisherName + lastVersion.getCreatedBy());
        } else {
            lastVo.setPublisherDesc(lastVersion.getCreatedBy());
        }


        ActivityScheduleOrchestrationVersionDO myVersion = fetchMyLastPublishVersion(request, data);
        // 只有当前登录人最后一个发布版本和数据源中的最后一个发布版本不是同一个，才需要比较
        boolean notSelf = myVersion != null && !StringUtils.equals(lastVersion.getRowId(), myVersion.getRowId());
        if (!notSelf) {
            return lastVo;
        }

        List<ActivityScheduleOrchestrationDetailDO> myDetail = fetchMyLastPublishVersionDetail(data, myVersion.getRowId());

        ScheduleItemDiffUtils.computeMyLastPublishDiff(myDetail, lastVo);

        return lastVo;
    }

    /**
     * 获取编排通知块码管理员（总营编排人员、或者DT自己通过客户协同快码配置ORCHESTRATION_NOTICE_BUTTON、ORCHESTRATION_NOTICE_ADMIN）
     * @param exhibitionRowId
     * @param empNo
     * @return
     */
    boolean orchestrationNoticeAdmin(String exhibitionRowId,String empNo){
    	//是否总营编排人员
    	boolean isResourceAdminRole = exhibitionDirectorRepository.queryDirectorByExhibitionRowIdAndEmpNo(exhibitionRowId, empNo)
        	.stream().anyMatch(item -> ExhibitionDirectorRoleTypeEnum.RESOURCE_ADMIN.isMe(item.getRoleType()));
    	if(isResourceAdminRole){
    		return true;
    	}
    	//是否配置在快码中
        FastLookupSearchParamVO paramVO = new FastLookupSearchParamVO();
        paramVO.setLookupType(ORCHESTRATION_NOTICE_BUTTON);
        paramVO.setLookupCode(ORCHESTRATION_NOTICE_ADMIN);
        paramVO.setLang(BizRequestUtil.createWithCurrentUser().getLangId());
        MsaRpcRequest<FastLookupSearchParamVO> request = MsaRpcRequestUtil.createWithCurrentUser(paramVO);
        MsaRpcResponse<Map<String, String>> msaRpcResponse = lookUpService.queryMapByLookupType(request);
        if (Objects.isNull(msaRpcResponse.getBo())){
            return false;
        }
        String meaning = msaRpcResponse.getBo().get(ORCHESTRATION_NOTICE_ADMIN);
        return StringUtils.contains(meaning, empNo);
    }

    /**
     * 导出资源编排列表
     *
     * @param vo
     * @return
     */
    public SimpleExcelExportModel exportScheduleOrchestrationData(ScheduleOrchestrationVO vo) {
        List<ScheduleOrchestrationExportVO> exportData = Collections.emptyList();

        if (CollectionUtils.isNotEmpty(vo.getDetailList())) {

            exportData = new ArrayList<>(vo.getDetailList().size());
            // 导出资源编排处理状态为，接受、拒绝、待处理的数据
            List<ScheduleOrchestrationDetailVO> tempList = vo.getDetailList();

            List<String> activityScheduleItemRowIds = tempList.stream().map(ScheduleOrchestrationDetailVO::getScheduleItemRowId).collect(Collectors.toList());
            List<String> activityRowIds = tempList.stream().map(ScheduleOrchestrationDetailVO::getActivityRowId).collect(Collectors.toList());
            // 根据日程id，获取日程相关人员信息
            Map<String, List<ActivityScheduleItemPeopleDO>> itemPeopleMap = activityScheduleItemPeopleRepository.getRelationSchedulePeopleInfoIds(activityScheduleItemRowIds);

            // 根据活动id，获取活动相关信息
            List<ActivityInfoDO> activityInfoDOList = activityInfoRepository.selectByIds(activityRowIds);
            Map<String, List<ActivityInfoDO>> activityInfoDOMap = activityInfoDOList.stream().collect(Collectors.groupingBy(ActivityInfoDO::getRowId));

            // 根据活动id，获取我司参与人信息
            Map<String, List<ActivityRelationZtePeopleDO>> ztePeopleDOMap = activityRelationZtePeopleRepository.getZtePeopleListByActivityRowIds(Sets.newHashSet(activityRowIds));

            // 根据活动id，获取客户参与人信息
            List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList = activityRelationCustPeopleRepository.queryAllCustPeopleForActivity(activityRowIds);
            Map<String, List<ActivityRelationCustPeopleDO>> customerPeopleDOMap = activityRelationCustPeopleDOList.stream().collect(Collectors.groupingBy(ActivityRelationCustPeopleDO::getActivityRowId));

            // 根据活动id，查询审批信息
            Map<String, List<ActivityApprovalProcessNodeDO>> approvalMap = activityApprovalProcessNodeRepository.queryAllByActivityRowIds(activityRowIds);
            for (int i = 0; i < tempList.size(); i++) {
                ScheduleOrchestrationDetailVO detail = tempList.get(i);
                ScheduleOrchestrationExportVO export = new ScheduleOrchestrationExportVO();
                BeanUtils.copyProperties(detail, export);

                export.setDealStatusName(ResourceOrchestrationDealStatusEnum.getDescByType(detail.getDealStatus()));
                export.setScheduleItemType(ScheduleItemTypeEnum.getDescByType(detail.getScheduleItemType()));
                export.setZteOtherDesc(getZteOtherDes(itemPeopleMap, detail));
                setActivityRequestNoAndTitle(export, activityInfoDOMap, detail.getActivityRowId());
                setApprovalInfo(export, approvalMap, detail.getActivityRowId());
                setLectureInfo(export, ztePeopleDOMap, detail.getActivityRowId());
                setAllContractDescInfo(export, customerPeopleDOMap, detail.getActivityRowId());

                if (CollectionUtils.isNotEmpty(detail.getContractDesc())) {
                    export.setContractDesc(String.join(PAUSE_MARK, detail.getContractDesc()));
                }
                if (CollectionUtils.isNotEmpty(detail.getSitePeopleDesc())) {
                    export.setSitePeopleDesc(String.join(PAUSE_MARK, detail.getSitePeopleDesc()));
                }

                exportData.add(export);
            }
            exportData = exportData.stream().sorted(Comparator.comparing(ScheduleOrchestrationExportVO::getActivityRequestNo, Comparator.nullsLast(String::compareTo))
                            .thenComparing(ScheduleOrchestrationExportVO::getScheduleDate, Comparator.nullsLast(String::compareTo))
                            .thenComparing(ScheduleOrchestrationExportVO::getScheduleTimeStart, Comparator.nullsLast(String::compareTo))
                            .thenComparing(ScheduleOrchestrationExportVO::getScheduleTimeEnd, Comparator.nullsLast(String::compareTo)))
                    .collect(Collectors.toList());
            for (int i = 0; i < exportData.size(); i++){
                exportData.get(i).setOrderNum(String.valueOf(i+1));
            }
        }

        // 如果是数据为空，也生成对应sheet页
        SimpleExcelExportModel model = new SimpleExcelExportModel(false);
        model.addSheetData(ScheduleOrchestrationConstants.SHEET_SCHEDULE_ITEM, exportData, ScheduleOrchestrationExportVO.class);
        model.setExportParamSetModelList(packExcelExportParamList());
        model.setModifiyLineList(packModifyLineList());
        return model;
    }

    /**
     * 填充可修改列
     * @return
     */
    List<String> packModifyLineList(){
        String exportColum = dictService.getDictValueByTypeAndKey(DictConstant.EXCEL_EXPORT, DictConstant.EXPORT_MODIFY_COLUM);
        return StringUtils.isBlank(exportColum) ? Lists.newArrayList() :
                Arrays.stream(exportColum.split(CharacterConstant.COMMA)).collect(Collectors.toList());
    }

    /**
     * 填充可选择数据
     * @return
     */
    List<ExcelExportParamSetModel> packExcelExportParamList(){
        List<ExcelExportParamSetModel> exportParamSetModelList = Lists.newArrayList();
        List<String> scheduleItemTypeList = ScheduleItemTypeEnum.getTypeList();
        exportParamSetModelList.add(new ExcelExportParamSetModel(NumberConstant.TEN, NumberConstant.TEN, scheduleItemTypeList.toArray(new String[0])));
        List<String> dealStatusTypeList = ResourceOrchestrationDealStatusEnum.getTypeList();
        exportParamSetModelList.add(new ExcelExportParamSetModel(NumberConstant.TWELVE, NumberConstant.TWELVE, dealStatusTypeList.toArray(new String[0])));

        return exportParamSetModelList;
    }

    /**
     * 设置讲师信息
     * @param export
     * @param ztePeopleDOMap
     * @param activityRowId
     */
    void setLectureInfo(ScheduleOrchestrationExportVO export,  Map<String, List<ActivityRelationZtePeopleDO>> ztePeopleDOMap, String activityRowId){
        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleList = ztePeopleDOMap.get(activityRowId);
        if(CollectionUtils.isEmpty(activityRelationZtePeopleList)){
            return;
        }
        List<ActivityRelationZtePeopleDO> lecturerInfoList = activityRelationZtePeopleList.stream().filter(item ->
                ActivityPeopleTypeEnum.LECTURER.isMe(item.getPeopleType())).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(lecturerInfoList)){
            export.setLecturerDesc(lecturerInfoList.stream().map(e -> StringUtils.isBlank(e.getPeopleName()) ? e.getPeopleCode()
                    : (e.getPeopleName() + e.getPeopleCode())).collect(Collectors.joining(PAUSE_MARK)));
        }
    }

    /**
     * 设置所有客户参与人信息
     * @param export
     * @param customerPeopleDOMap
     * @param activityRowId
     */
    void setAllContractDescInfo(ScheduleOrchestrationExportVO export,  Map<String, List<ActivityRelationCustPeopleDO>> customerPeopleDOMap, String activityRowId){
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleList = customerPeopleDOMap.get(activityRowId);
        if(CollectionUtils.isEmpty(activityRelationCustPeopleList)){
            return;
        }
        String contactNameStr = activityRelationCustPeopleList.stream().map(this::appendAllContactPeopleDesc).collect(Collectors.joining(PAUSE_MARK));
        export.setAllContractDesc(contactNameStr);
    }

    /**
     * 客户联系人打包
     *
     * @param custPeopleDO
     * @return
     */
    String appendAllContactPeopleDesc(ActivityRelationCustPeopleDO custPeopleDO) {
        if (StringUtils.isBlank(custPeopleDO.getPositionName())) {
            return custPeopleDO.getContactName();
        } else {
            return CharacterConstant.BRACKET_EN_LEFT + custPeopleDO.getPositionName() + CharacterConstant.BRACKET_EN_RIGHT + custPeopleDO.getContactName();
        }
    }

    /**
     * description 设置审批信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/12/12 下午8:15
     */
    void setApprovalInfo(ScheduleOrchestrationExportVO export, Map<String, List<ActivityApprovalProcessNodeDO>> approvalMap, String activityRowId) {
        List<ActivityApprovalProcessNodeDO> approvalList = approvalMap.get(activityRowId);
        if (CollectionUtils.isEmpty(approvalList)) {
            return;
        }

        // 合规经理审批，结果不为转交
        List<ActivityApprovalProcessNodeDO> complianceAuditorList = approvalList.stream()
                .filter(e -> ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.isMe(e.getNodeType()))
                .filter(e -> !ApproveResultEnum.transfer.isMe(e.getApproveResult()))
                .filter(e -> StringUtils.isNotBlank(e.getApproveResult()))
                .collect(Collectors.toList());

        // 四层领导审批，结果不为转交
        List<ActivityApprovalProcessNodeDO> level4ApprovalList = approvalList.stream()
                .filter(e -> ApproveNodeTypeEnum.LEVEL4_LEADER_AUDITOR_NODE_CODE.isMe(e.getNodeType()))
                .filter(e -> !ApproveResultEnum.transfer.isMe(e.getApproveResult()))
                .filter(e -> StringUtils.isNotBlank(e.getApproveResult()))
                .collect(Collectors.toList());

        // 三层领导审批，结果不为转交
        List<ActivityApprovalProcessNodeDO> level3ApprovalList = approvalList.stream()
                .filter(e -> ApproveNodeTypeEnum.LEVEL3_LEADER_AUDITOR_NODE_CODE.isMe(e.getNodeType()))
                .filter(e -> !ApproveResultEnum.transfer.isMe(e.getApproveResult()))
                .filter(e -> StringUtils.isNotBlank(e.getApproveResult()))
                .collect(Collectors.toList());

        // 二层领导审批，结果不为转交
        List<ActivityApprovalProcessNodeDO> level2ApprovalList = approvalList.stream()
                .filter(e -> ApproveNodeTypeEnum.LEVEL2_LEADER_AUDITOR_NODE_CODE.isMe(e.getNodeType()))
                .filter(e -> !ApproveResultEnum.transfer.isMe(e.getApproveResult()))
                .filter(e -> StringUtils.isNotBlank(e.getApproveResult()))
                .collect(Collectors.toList());

        export.setComplianceAuditorDes(getApprovalDes(complianceAuditorList));
        export.setLevel4AuditorDes(getApprovalDes(level4ApprovalList));
        export.setLevel3AuditorDes(getApprovalDes(level3ApprovalList));
        export.setLevel2AuditorDes(getApprovalDes(level2ApprovalList));
    }

    /**
     * description 获取审批详情
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/12/12 下午8:50
     */
    private String getApprovalDes(List<ActivityApprovalProcessNodeDO> approvalList) {
        String res = Strings.EMPTY;
        if (CollectionUtils.isEmpty(approvalList)){
            return res;
        }
        ActivityApprovalProcessNodeDO processNodeDO = approvalList.get(0);
        res += processNodeDO.getApproverName() + processNodeDO.getApproveBy();
        res += CharacterConstant.BRACKET_EN_LEFT;
        res += ApproveResultEnum.getNameByCode(processNodeDO.getApproveResult());
        if (StringUtils.isNotBlank(processNodeDO.getRemark())){
            res += CharacterConstant.COMMA;
            res += processNodeDO.getRemark();
        }
        res += CharacterConstant.BRACKET_EN_RIGHT;
        return res;
    }

    /**
     * description 设置活动编号和活动议题
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/12/12 上午9:12
     */
    void setActivityRequestNoAndTitle(ScheduleOrchestrationExportVO export, Map<String, List<ActivityInfoDO>> activityInfoDOMap, String activityRowId) {
        List<ActivityInfoDO> activityInfoDOList = activityInfoDOMap.get(activityRowId);
        if (CollectionUtils.isEmpty(activityInfoDOList)) {
            return;
        }
        export.setActivityRequestNo(activityInfoDOList.get(0).getActivityRequestNo());
        export.setActivityTitle(activityInfoDOList.get(0).getActivityTitle());
    }

    /**
     * description 获取其他我司参与人（除领导、专家、我司陪同人的其他我司参与人），形如林瑜10344346、唐震10329400
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/12/11 下午8:00
     */
    String getZteOtherDes(Map<String, List<ActivityScheduleItemPeopleDO>> itemPeopleMap, ScheduleOrchestrationDetailVO detail) {
        String zteOtherDes = Strings.EMPTY;
        List<ActivityScheduleItemPeopleDO> itemPeopleDOList = itemPeopleMap.get(detail.getScheduleItemRowId());
        if (CollectionUtils.isEmpty(itemPeopleDOList)){
            return zteOtherDes;
        }

        Set<String> zteLeaderNos = Optional.ofNullable(detail.getZteLeaderList()).orElse(new ArrayList<>()).stream().map(ScheduleOrchestrationDetailVO.EmpInfo::getEmpUIID).collect(Collectors.toSet());
        Set<String> zteExpertNos = Optional.ofNullable(detail.getZteExpertList()).orElse(new ArrayList<>()).stream().map(ScheduleOrchestrationDetailVO.EmpInfo::getEmpUIID).collect(Collectors.toSet());

        itemPeopleDOList = itemPeopleDOList.stream()
                .filter(e -> ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(e.getPeopleType()))
                .filter(e -> !zteLeaderNos.contains(e.getPeopleNo()))
                .filter(e -> !zteExpertNos.contains(e.getPeopleNo()))
                .collect(Collectors.toList());
        for (ActivityScheduleItemPeopleDO peopleDO : itemPeopleDOList){
            zteOtherDes += peopleDO.getPeopleName() + peopleDO.getPeopleNo();
            zteOtherDes += PAUSE_MARK;
        }
        return zteOtherDes.length() > 0 ? zteOtherDes.substring(0, zteOtherDes.length() - 1) : zteOtherDes;

    }

    /**
     * 获取当前登录人最新版本信息
     *
     * @param request
     * @param data
     * @return
     */
    ActivityScheduleOrchestrationVersionDO fetchMyLastPublishVersion(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data) {
        ActivityScheduleOrchestrationVersionDO myVersion = data.fetchScheduleOrchestrationVersion(request.getEmpNo());
        // 如果数据源中已经有了则直接取，否则查询本人最新发布的版本
        if (myVersion != null && ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH.equalsIgnoreCase(myVersion.getVersionStatus())) {
            return myVersion;
        }

        // 没有，则直接从数据库中查询最新的版本
        ActivityScheduleOrchestrationVersionQuery query = new ActivityScheduleOrchestrationVersionQuery();
        query.setExhibitionRowId(request.getParam().getExhibitionRowId());
        query.setVersionStatus(ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_PUBLISH);
        query.setCreatedBy(request.getEmpNo());

        return activityScheduleOrchestrationVersionRepository.getLastScheduleOrchestrationVersion(query);
    }

    /**
     * 获取当前登陆人最新发布版本编排数据
     *
     * @param data
     * @return
     */
    List<ActivityScheduleOrchestrationDetailDO> fetchMyLastPublishVersionDetail(ScheduleOrchestrationDataSource data, String myLastVersionRowId) {
        List<ActivityScheduleOrchestrationDetailDO> detailList = data.fetchScheduleItemDetailFromVersion(myLastVersionRowId);
        if (CollectionUtils.isNotEmpty(detailList)) {
            return detailList;
        }

        Map<String, List<ActivityScheduleOrchestrationDetailDO>> lastScheduleOrchestrationMap = activityScheduleOrchestrationDetailRepository.queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(
                Collections.singletonList(myLastVersionRowId));

        return lastScheduleOrchestrationMap.getOrDefault(myLastVersionRowId, Collections.emptyList());
    }

    /**
     * 打包对应资源编排明细
     *
     * @param request
     * @param data
     * @return
     */
    List<ScheduleOrchestrationDetailVO> packScheduleOrchestrationDetailVO(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data) {
        if (CollectionUtils.isEmpty(data.getActivityInfoList())) {
            return Collections.emptyList();
        }

        Map<String, OrgInfoVO> orgMap = hrmOrgInfoSearchService.getOrgInfoByOrgIds(data.getActivityInfoList()
                .stream()
                .map(ActivityInfoDO::getApplyDepartmentNo)
                .collect(Collectors.toList()));

        CommunicationDirectorTwoLevelModel activityDirectionModel = communicationDirectorComponent.fetchActivityCommunicationDirectorTwoLevelModel();
        List<ScheduleOrchestrationDetailVO> list = new ArrayList<>();
        for (ActivityInfoDO activityInfo : data.getActivityInfoList()) {
            list.addAll(packScheduleOrchestrationDetailVO(request, data, activityInfo, activityDirectionModel, orgMap));
        }

        // 整体填充我司人员信息
        fillZtePeopleName(list);

        // 按照日程时间进行排序
        list = list.stream().sorted(new ScheduleOrchestrationDetailVO()).collect(Collectors.toList());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setOrderNum(String.valueOf(i + 1));
        }
        return list;
    }

    /**
     * 打包对应资源编排明细
     *
     * @param request
     * @param data
     * @param activityInfo
     * @return
     */
    List<ScheduleOrchestrationDetailVO> packScheduleOrchestrationDetailVO(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data,
                                                                          ActivityInfoDO activityInfo, CommunicationDirectorTwoLevelModel activityDirectionModel,
                                                                          Map<String, OrgInfoVO> orgMap) {
        List<ActivityScheduleItemDO> itemList = data.fetchActivityScheduleItem(activityInfo.getRowId());
        List<ScheduleOrchestrationDetailVO> voList = new ArrayList<>(itemList.size());

        String directionDesc = fetchActivityDirection(data, activityInfo, activityDirectionModel);
        // 打包日程信息
        for (ActivityScheduleItemDO item : itemList) {
            if (needOrchestrationItemFilter(item, data)) {
                continue;
            }
            // 【1】 创建打包基本信息
            ScheduleOrchestrationDetailVO vo = createBaseVO(item);
            // 【2】打包活动相关信息
            packActivityInfo(activityInfo, orgMap, vo);
            vo.setCommunicateDirectionDesc(directionDesc);
            // 【3】打包客户信息
            packCustomerInfo(data, activityInfo, vo);
            // 【4】打包日程参与人信息
            packPeopleInfo(data, vo);
            // 【5】决策编排数据填充
            decideOrchestrationData(request, data, item, vo);
            // 【6】计算日程修改情况
            computeItemModifyCase(request, data, item, vo);

            voList.add(vo);
        }

        // 过滤数据
        return voList.stream().filter(e -> filterScheduleData(e, request.getParam()) ).collect(Collectors.toList());
    }

    /**
     * 过滤数据
     * @param vo
     * @param param
     * @return
     */
    boolean filterScheduleData(ScheduleOrchestrationDetailVO vo, ScheduleOrchestrationExportParam param) {
        // 说明是勾选的数据
        if (CollectionUtils.isNotEmpty(param.getScheduleRowIdList()) && !param.getScheduleRowIdList().contains(vo.getScheduleItemRowId())) {
            return false;
        }

        if (BooleanEnum.Y.isMe(param.getLast()) && ResourceOrchestrationDealStatusEnum.WAIT.isMe(vo.getDealStatus())) {
            return false;
        }

        if(StringUtils.isNotBlank(param.getDealStatus()) && !StringUtils.equals(param.getDealStatus(), vo.getDealStatus())){
            return false;
        }

        return validate(vo, param);
    }

    /**
     * 数据校验
     * @param vo
     * @param param
     * @return
     */
    boolean validate(ScheduleOrchestrationDetailVO vo, ScheduleOrchestrationExportParam param) {
        if (param.isPublishedFlag() && !StringUtils.equals(vo.getSubPublish(), BooleanEnum.Y.getCode())) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(param.getMeetingTypeAndNameList())) {
            String placeTypeAndName= BooleanEnum.Y.isMe(param.getLast()) ? vo.getPlaceName() : StringUtils.join(vo.getPlaceType(), vo.getPlaceName());
            if (!param.getMeetingTypeAndNameList().contains(placeTypeAndName)) {
                return false;
            }
        }

        return BooleanUtils.and(ArrayUtils.addAll(ArrayUtils.toArray(isIncludeEmpNo(param.getLeaderEmpNoList(), vo.getZteLeader())
                , isIncludeEmpNo(param.getExpertEmpNoList(), vo.getZteExpert())
                , isIncludeQueryCondition(param.getScheduleTypeList(), vo.getScheduleItemType())
                , isIncludeQueryCondition(param.getOrg3NameList(), vo.getOrg3Name())
                , isIncludeQueryCondition(param.getOrg2NameList(), vo.getOrg2Name())
                , isIncludeQueryCondition(param.getOrgNameList(), vo.getOrgName())
                , isIncludeQueryCondition(param.getScheduleDateTimeList(), vo.getScheduleDate())
                , isIncludeQueryCondition(param.getScheduleItemNameList(),vo.getScheduleItemName())
                , isIncludeQueryCondition(param.getActivityRequestNoList(),vo.getActivityRequestNo()))));
    }

    /**
     * 判断是否包含查询条件
     * @param filedValueList
     * @param filedValue
     * @return
     */
    boolean isIncludeQueryCondition(List<String> filedValueList, String filedValue){
        if(CollectionUtils.isEmpty(filedValueList)){
            return true;
        }
        return filedValueList.contains(filedValue);
    }

    /**
     * 判断工号是否包含
     * @param empNoList
     * @param empNo
     * @return
     */
    boolean isIncludeEmpNo(List<String> empNoList, String empNo) {
        if (CollectionUtils.isNotEmpty(empNoList)) {
            if (StringUtils.isEmpty(empNo)) {
                return empNoList.contains(empNo);
            }
            List<String> zteNoList = Arrays.stream(empNo.split(CharacterConstant.COMMA)).collect(Collectors.toList());
            Collection collection = CollectionUtils.retainAll(zteNoList, empNoList);
            return !CollectionUtils.isEmpty(collection);
        }
        return true;
    }

    /**
     * 是否
     *
     * @param item
     * @param data
     * @return true-过滤，false-不过滤
     */
    boolean needOrchestrationItemFilter(ActivityScheduleItemDO item, ScheduleOrchestrationDataSource data) {
        if (!data.isNeedOrchestrationFiler()) {
            return false;
        }

        List<ActivityScheduleItemPeopleDO> peopleList = data.fetchActivityScheduleItemPeople(item.getRowId());
        boolean hasLeader = peopleList.stream().anyMatch(e -> PeopleRoleLabelEnum.LEADER.isMe(e.getPeopleLabel()));
        boolean needRoom = ScheduleItemPlaceTypeEnum.ROOM.isMe(item.getPlaceType());

        // 如果会议室、领导同时为空，则不需要打包
        return !hasLeader && !needRoom;
    }

    /**
     * 填充我司人员名称信息
     *
     * @param voList
     */
    void fillZtePeopleName(List<ScheduleOrchestrationDetailVO> voList) {
        Set<String> empNos = voList.stream().map(e -> {
            List<String> nos = new ArrayList<>();
            if (StringUtils.isNotBlank(e.getZteLeader())) {
                nos.addAll(Arrays.stream(e.getZteLeader().split(CharacterConstant.COMMA)).collect(Collectors.toList()));
            }
            if (StringUtils.isNotBlank(e.getZteExpert())) {
                nos.addAll(Arrays.stream(e.getZteExpert().split(CharacterConstant.COMMA)).collect(Collectors.toList()));
            }
            nos.add(e.getApplyPeople());
            return nos;
        }).flatMap(Collection::stream).collect(Collectors.toSet());


        Map<String, String> empNameMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(empNos)).getBo();

        for (ScheduleOrchestrationDetailVO vo : voList) {
            fillZtePeopleName(vo, empNameMap);
        }
    }

    /**
     * 填充我司人员名称信息
     *
     * @param vo
     * @param empNameMap
     */
    void fillZtePeopleName(ScheduleOrchestrationDetailVO vo, Map<String, String> empNameMap) {
        if (StringUtils.isNotBlank(vo.getZteLeader())) {
            List<ScheduleOrchestrationDetailVO.EmpInfo> leaderList = Arrays.stream(vo.getZteLeader().split(CharacterConstant.COMMA)).map(empNo -> {
                ScheduleOrchestrationDetailVO.EmpInfo emp = new ScheduleOrchestrationDetailVO.EmpInfo();
                emp.setEmpUIID(empNo);
                emp.setEmpName(empNameMap.get(empNo));
                return emp;
            }).collect(Collectors.toList());

            vo.setZteLeaderList(leaderList);
            vo.setZteLeaderDesc(leaderList.stream()
                    .map(e -> StringUtils.isBlank(e.getEmpName()) ? e.getEmpUIID() : (e.getEmpName() + e.getEmpUIID())).
                    collect(Collectors.joining(CharacterConstant.COMMA)));
        }

        if (StringUtils.isNotBlank(vo.getZteExpert())) {
            List<ScheduleOrchestrationDetailVO.EmpInfo> expertList = Arrays.stream(vo.getZteExpert().split(CharacterConstant.COMMA)).map(empNo -> {
                ScheduleOrchestrationDetailVO.EmpInfo emp = new ScheduleOrchestrationDetailVO.EmpInfo();
                emp.setEmpName(empNameMap.get(empNo));
                emp.setEmpUIID(empNo);
                return emp;
            }).collect(Collectors.toList());

            vo.setZteExpertList(expertList);
            vo.setZteExpertDesc(expertList.stream()
                    .map(e -> StringUtils.isBlank(e.getEmpName()) ? e.getEmpUIID() : (e.getEmpName() + e.getEmpUIID())).
                    collect(Collectors.joining(CharacterConstant.COMMA)));
        }

        String name = empNameMap.get(vo.getApplyPeople());
        vo.setApplyPeopleDesc(StringUtils.isBlank(name) ? vo.getApplyPeople() : (name + vo.getApplyPeople()));
    }

    /**
     * 计算日程修改情况
     *
     * @param request
     * @param data
     * @param item
     * @param vo
     */
    void computeItemModifyCase(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data, ActivityScheduleItemDO item, ScheduleOrchestrationDetailVO vo) {
        // 如果日程还是待处理状态，说明没有任何人操作过
        if (ResourceOrchestrationDealStatusEnum.WAIT.isMe(item.getDealStatus())) {
            vo.setHasOtherChange(BooleanEnum.N.getCode());
            vo.setSubPublish(BooleanEnum.N.getCode());
            return;
        }

        // 日程最后修改人是否为当前登录人
        boolean self = StringUtils.equalsIgnoreCase(item.getLastUpdatedBy(), request.getEmpNo());
        vo.setHasOtherChange(self ? BooleanEnum.N.getCode() : BooleanEnum.Y.getCode());
        // 判断最后修改人的角色Y-总营，N-分营
        String admin = data.fetchScheduleOrchestrationType(item.getLastUpdatedBy());
        // 判断最后发布人是总营还是分营，决定是否分营发布过
        vo.setSubPublish(BooleanEnum.Y.isMe(admin) ? BooleanEnum.N.getCode() : BooleanEnum.Y.getCode());
    }


    /**
     * 决策编排数据填充
     *
     * @param request
     * @param data
     * @param item
     * @param vo
     */
    void decideOrchestrationData(BizRequest<ScheduleOrchestrationExportParam> request, ScheduleOrchestrationDataSource data, ActivityScheduleItemDO item, ScheduleOrchestrationDetailVO vo) {
        // 非编排模式，则只返回最新一线数据
        if (!data.isEditModel()) {
            return;
        }
        // 如果一线数据有变更多，则以一线为准（支持后续一线数据变更的情况）
        if (ResourceOrchestrationDealStatusEnum.WAIT.isMe(item.getDealStatus()) && BooleanEnum.Y.isMe(item.getHasChanged())) {
            return;
        }

        ActivityScheduleOrchestrationVersionDO version = data.fetchScheduleOrchestrationVersion(request.getEmpNo());
        // 如果编排人员在编排的时候，有暂存过数据，则以暂存数据为准
        if (version != null && ScheduleOrchestrationConstants.SCHEDULE_ORCHESTRATION_DRAFT.equalsIgnoreCase(version.getVersionStatus())) {
            ActivityScheduleOrchestrationDetailDO versionDetail = data.fetchScheduleItemFromVersion(version.getRowId(), item.getRowId());
            // 如果暂存数据是存在的  && 暂存的数据是在一线最后修改时间（被同步、变更）之后，则说明暂存的数据是最新的，以暂存的数据为准
            if (versionDetail != null && versionDetail.getLastUpdateDate().after(item.getLastUpdateDate())) {
                vo.setScheduleDate(DateFormatUtils.format(versionDetail.getScheduleDate(), DateFormatUtil.DATE_YYYY_MM_DD_PATTERN));
                String[] arr = versionDetail.getScheduleTime().split(CharacterConstant.WAVE);
                vo.setScheduleTimeStart(arr[0]);
                vo.setScheduleTimeEnd(arr[1]);
                vo.setScheduleTime(versionDetail.getScheduleTime());
                vo.setScheduleDateTime(versionDetail.getScheduleDate());
                vo.setZteLeader(versionDetail.getZteLeader());
                vo.setZteExpert(versionDetail.getZteExpert());
                vo.setDealStatus(versionDetail.getDealStatus());
                vo.setDealNote(versionDetail.getDealNote());
                vo.setPlaceType(versionDetail.getPlaceType());
                vo.setPlaceName(versionDetail.getPlaceName());
                vo.setRemark(versionDetail.getRemark());
                vo.setFromStaging(BooleanEnum.Y.getCode());
            }
        }
    }


    /**
     * 创建基本VO信息
     *
     * @param item
     * @return
     */
    ScheduleOrchestrationDetailVO createBaseVO(ActivityScheduleItemDO item) {
        ScheduleOrchestrationDetailVO vo = new ScheduleOrchestrationDetailVO();
        vo.setScheduleItemRowId(item.getRowId());
        vo.setScheduleItemType(item.getScheduleItemType());
        vo.setScheduleItemTypeName(ScheduleItemTypeEnum.getDescByType(item.getScheduleItemType()));
        vo.setScheduleItemName(item.getScheduleItemName());
        vo.setActivityRowId(item.getActivityRowId());

        vo.setScheduleDateTime(item.getScheduleDate());
        vo.setScheduleDate(DateFormatUtils.format(item.getScheduleDate(), DateFormatUtil.DATE_YYYY_MM_DD_PATTERN));
        String[] arr = item.getScheduleTime().split(CharacterConstant.WAVE);
        vo.setScheduleTimeStart(arr[0]);
        vo.setScheduleTimeEnd(arr[1]);
        vo.setScheduleTime(item.getScheduleTime());

        vo.setPlaceType(item.getPlaceType());
        vo.setPlaceName(item.getPlaceName());
        vo.setPlaceCapacityNum(item.getPlaceCapacityNum());
        vo.setDealStatus(item.getDealStatus());
        vo.setDealNote(item.getDealNote());
        vo.setRemark(item.getRemark());
        vo.setLastUpdatedBy(item.getLastUpdatedBy());

        return vo;
    }

    /**
     * 打包活动信息
     *
     * @param activityInfo
     * @param vo
     */
    void packActivityInfo(ActivityInfoDO activityInfo, Map<String, OrgInfoVO> orgMap, ScheduleOrchestrationDetailVO vo) {
        // 活动信息
        vo.setSubmitTime(DateFormatUtils.format(activityInfo.getSubmitTime(), DateFormatUtil.DATE_YYYY_MM_DD_PATTERN));
        vo.setActivitySubmitTime(DateFormatUtils.format(activityInfo.getSubmitTime(), DateFormatUtil.DATE_YYYY_MM_DD_HH_MM_SS_PATTERN));
        vo.setActivityRequestNo(activityInfo.getActivityRequestNo());
        vo.setActivityStatusName(ActivityStatusEnum.getNameByCode(activityInfo.getActivityStatus()));
        vo.setActivityTitle(activityInfo.getActivityTitle());
        vo.setApplyPeople(activityInfo.getApplyPeopleNo());
        vo.setApplyPeopleDesc(activityInfo.getApplyPeopleNo());
        OrgInfoVO org = orgMap.get(activityInfo.getApplyDepartmentNo());
        if (org != null) {
            vo.setOrg2Name(OrgUtil.getOrg2Name(org.getHrOrgNamePath()));
            vo.setOrg3Name(OrgUtil.getOrg3Name(org.getHrOrgNamePath()));
            vo.setOrgName(OrgUtil.getOrg4Name(org.getHrOrgNamePath()));
            vo.setOrgFullName(org.getHrOrgNamePath());
        }
    }

    /**
     * 打包客户信息
     *
     * @param data
     * @param activityInfo
     * @param vo
     */
    void packCustomerInfo(ScheduleOrchestrationDataSource data, ActivityInfoDO activityInfo, ScheduleOrchestrationDetailVO vo) {
        ActivityCustomerInfoDO mainCust = data.fetchActivityMainCustomerInfo(activityInfo.getRowId());

        // 实际允许这里必然是不会为空的，这里仅为防止CCA扫描
        if (mainCust != null) {
            // 把主客户放在最前面
            List<ActivityCustomerInfoDO> allCustList = data.fetchCustomerList(activityInfo.getRowId());
            List<String> custCodeList = Lists.newArrayList(mainCust.getCustomerCode());
            List<String> custNameList = Lists.newArrayList(mainCust.getCustomerName());
            List<String> mktCodeList = Lists.newArrayList(mainCust.getMktCode());
            List<String> mktNameList = Lists.newArrayList(mainCust.getMktName());
            List<String> mtoCodeList = Lists.newArrayList(mainCust.getMtoCode());
            List<String> mtoNameList = Lists.newArrayList(mainCust.getMtoName());
            List<String> localCodeList = Lists.newArrayList(mainCust.getLocalCode());
            List<String> localNameList = Lists.newArrayList(mainCust.getLocalName());
            for (ActivityCustomerInfoDO cust : allCustList) {
                if (!StringUtils.equals(mainCust.getCustomerCode(), cust.getCustomerCode())) {
                    custCodeList.add(cust.getCustomerCode());
                    custNameList.add(cust.getCustomerName());
                    mktCodeList.add(cust.getMktCode());
                    mktNameList.add(cust.getMktName());
                    mtoCodeList.add(cust.getMtoCode());
                    mtoNameList.add(cust.getMtoName());
                    localCodeList.add(cust.getLocalCode());
                    localNameList.add(cust.getLocalName());
                }
            }

            vo.setCustomerCode(String.join(PAUSE_MARK, custCodeList));
            vo.setCustomerName(StringUtils.append(custNameList, PAUSE_MARK, CharacterConstant.ZH_NULL, CharacterConstant.ZH_NULL));
            vo.setMktCode(String.join(PAUSE_MARK, mktCodeList));
            vo.setMktName(StringUtils.append(mktNameList, PAUSE_MARK, CharacterConstant.ZH_NULL, CharacterConstant.ZH_NULL));
            vo.setMtoCode(String.join(PAUSE_MARK, mtoCodeList));
            vo.setMtoName(StringUtils.append(mtoNameList, PAUSE_MARK, CharacterConstant.ZH_NULL, CharacterConstant.ZH_NULL));
            vo.setLocalCode(String.join(PAUSE_MARK, localCodeList));
            vo.setLocalName(StringUtils.append(localNameList, PAUSE_MARK, CharacterConstant.ZH_NULL, CharacterConstant.ZH_NULL));
        }
    }

    /**
     * 打包日程中所有参与人
     *
     * @param vo
     * @param data
     */
    void packPeopleInfo(ScheduleOrchestrationDataSource data, ScheduleOrchestrationDetailVO vo) {
        List<ActivityScheduleItemPeopleDO> peopleList = data.fetchActivityScheduleItemPeople(vo.getScheduleItemRowId());

        vo.setZteLeader(peopleList.stream().filter(e -> ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(e.getPeopleType()) && PeopleRoleLabelEnum.LEADER.isMe(e.getPeopleLabel()))
                .map(ActivityScheduleItemPeopleDO::getPeopleNo).filter(StringUtils::isNotBlank).collect(Collectors.joining(CharacterConstant.COMMA)));

        vo.setZteExpert(peopleList.stream().filter(e -> ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(e.getPeopleType()) && PeopleRoleLabelEnum.SAVANT.isMe(e.getPeopleLabel()))
                .map(ActivityScheduleItemPeopleDO::getPeopleNo).filter(StringUtils::isNotBlank).collect(Collectors.joining(CharacterConstant.COMMA)));

        vo.setContractDesc(setContractDesc(data, vo, peopleList));

        vo.setSitePeopleDesc(peopleList.stream()
                .filter(e -> ScheduleItemPeopleTypeEnum.in(e.getPeopleType(), ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE, ScheduleItemPeopleTypeEnum.OTHER_INTERFACE_PEOPLE))
                .map(this::appendSitePeopleDesc).collect(Collectors.toList()));

        vo.setZteSitePeopleList(peopleList.stream().filter(e -> ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE.isMe(e.getPeopleType()))
                .map(ActivityScheduleItemPeopleDO::getPeopleNo).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
    }

    /**
     * 初始化客户联系人
     * @param data
     * @param vo
     * @param peopleList
     * @return
     * <AUTHOR>
     * @date 2024/1/30
     */
    List<String> setContractDesc(ScheduleOrchestrationDataSource data, ScheduleOrchestrationDetailVO vo, List<ActivityScheduleItemPeopleDO> peopleList) {
        Map<String, Map<String, ActivityRelationCustPeopleDO>> relationPeopleMap = data.getRelationCustPeople();
        Map<String, ActivityRelationCustPeopleDO> activityRelationCustPeopleDOMap = relationPeopleMap.get(vo.getActivityRowId());
        if (Objects.isNull(activityRelationCustPeopleDOMap)){
            return peopleList.stream().filter(e -> ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.isMe(e.getPeopleType()))
                    .map(this::appendContactPeopleDesc).collect(Collectors.toList());
        } else {
            List<ExhibitionScheduleCustPeopleInfoVO> custPeopleDOList = getExhibitionScheduleContactPeopleInfoList(activityRelationCustPeopleDOMap, peopleList);
            return custPeopleDOList.stream().map(this::initContactPeopleDesc).collect(Collectors.toList());
        }
    }

    /**
     * 客户联系人打包
     *
     * @param people
     * @return
     */
    String initContactPeopleDesc(ExhibitionScheduleCustPeopleInfoVO people) {
        if (StringUtils.isBlank(people.getPositionName())) {
            return people.getContactName();
        } else {
            return CharacterConstant.BRACKET_EN_LEFT + people.getPositionName() + CharacterConstant.BRACKET_EN_RIGHT + people.getContactName();
        }
    }

    /**
     * 活动维度-客户联系人列表
     * @param activityRelationCustPeopleDOMap
     * @param peopleList
     * @return
     * <AUTHOR>
     * @date 2024/1/30
     */
    List<ExhibitionScheduleCustPeopleInfoVO> getExhibitionScheduleContactPeopleInfoList(Map<String, ActivityRelationCustPeopleDO> activityRelationCustPeopleDOMap, List<ActivityScheduleItemPeopleDO> peopleList) {
        List<ExhibitionScheduleCustPeopleInfoVO> custPeopleDOList = new ArrayList<>();
        peopleList.stream().filter(e -> ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.isMe(e.getPeopleType())).forEach(contact ->{
            ActivityRelationCustPeopleDO peopleDO = activityRelationCustPeopleDOMap.get(contact.getPeopleNo());
            if (Objects.nonNull(peopleDO)){
                ExhibitionScheduleCustPeopleInfoVO peopleInfoVO = new ExhibitionScheduleCustPeopleInfoVO();
                BeanUtils.copyProperties(peopleDO,peopleInfoVO);
                peopleInfoVO.setPositionName(contact.getPosition());
                peopleInfoVO.setContactLevelNo(ActivityContactLevelEnum.getDescByType(peopleInfoVO.getContactLevel()));
                custPeopleDOList.add(peopleInfoVO);
            }
        });
        return custPeopleDOList.stream().sorted(Comparator.comparing(ExhibitionScheduleCustPeopleInfoVO::getContactLevelNo)).collect(Collectors.toList());
    }

    /**
     * 现场陪同人员打包
     *
     * @param people
     * @return
     */
    public String appendSitePeopleDesc(ActivityScheduleItemPeopleDO people) {
        String name = people.getPeopleName();
        if (ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE.isMe(people.getPeopleType())) {
            name += people.getPeopleNo();
        }
        if (StringUtils.isBlank(people.getPhoneNum())) {
            return name;
        } else {
            return name + CharacterConstant.BRACKET_EN_LEFT + people.getPhoneNum() + CharacterConstant.BRACKET_EN_RIGHT;
        }
    }

    /**
     * 客户联系人打包
     *
     * @param people
     * @return
     */
    String appendContactPeopleDesc(ActivityScheduleItemPeopleDO people) {
        if (StringUtils.isBlank(people.getPosition())) {
            return people.getPeopleName();
        } else {
            return CharacterConstant.BRACKET_EN_LEFT + people.getPosition() + CharacterConstant.BRACKET_EN_RIGHT + people.getPeopleName();
        }
    }

    /**
     * 获取交流方向
     *
     * @param data
     * @param activityInfo
     * @param activityDirectionModel 活动交流方向模型
     * @return
     */
    String fetchActivityDirection(ScheduleOrchestrationDataSource data, ActivityInfoDO activityInfo, CommunicationDirectorTwoLevelModel activityDirectionModel) {
        List<ActivityCommunicationDirectionDO> directionList = data.fetchCommDirection(activityInfo.getRowId());
        if (CollectionUtils.isNotEmpty(directionList)) {
            return activityDirectionModel.fetchMeaningWithSplit(
                    directionList.stream().map(ActivityCommunicationDirectionDO::getCommunicationDirection).collect(Collectors.toSet()), PAUSE_MARK);
        }

        return CharacterConstant.EMPTY_STR;
    }

}
