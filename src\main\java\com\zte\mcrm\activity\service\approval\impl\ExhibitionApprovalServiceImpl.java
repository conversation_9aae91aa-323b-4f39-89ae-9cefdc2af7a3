package com.zte.mcrm.activity.service.approval.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.iss.approval.sdk.bean.FlowStartDTO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.mcrm.activity.common.cache.client.AreaDataCacheClient;
import com.zte.mcrm.activity.common.cache.model.AreaDataModel;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.AreaTypeEnum;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessNodeDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalInfoRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessNodeRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalProcessRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activity.impl.ActivityCustomerSearchServiceImpl;
import com.zte.mcrm.activity.service.approval.BaseApprovalBizService;
import com.zte.mcrm.activity.service.approval.param.ApprovalFlowStartParam;
import com.zte.mcrm.activity.service.approval.param.ApprovedByInfo;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.constant.ExternalConstant;
import com.zte.mcrm.adapter.vo.Account;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.customvisit.util.DateUtils;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.COLON_EN;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.SEMICOLON;
import static com.zte.mcrm.activity.common.constant.DateConstants.YYYY_MM_DD_HH_MM_SS;
import static com.zte.mcrm.activity.common.constant.I18Constants.NOTICE_APPROVAL_RESUBMIT;
import static com.zte.mcrm.activity.common.enums.activity.ApprovalFlowTypeEnum.EXHIBITION_APPROVAL;
import static com.zte.mcrm.cust.constants.Constants.FLAG_YES;
import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;
import static com.zte.mcrm.customvisit.util.DateUtils.convertDateToString;

/**
 * <AUTHOR>
 */
@Service
public class ExhibitionApprovalServiceImpl extends BaseApprovalBizService {

    private static final Logger logger = LoggerFactory.getLogger(ExhibitionApprovalServiceImpl.class);


    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;

    @Autowired
    private ActivityApprovalInfoRepository approvalInfoRepository;

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;

    @Autowired
    private ActivityApprovalProcessRepository approvalProcessRepository;

    @Autowired
    private ActivityApprovalProcessNodeRepository approvalProcessNodeRepository;

    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private ActivityUrlConfig activityUrlConfig;

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private AreaDataCacheClient areaDataCacheClient;


    private final String NEED_COMPLIANCE_AUDITOR = "needComplianceAuditor";

    private final String COMPLIANCE_MANAGER = "complianceManager";

    private final String NEED_LEVEL2_LEADER_AUDITOR = "needLevel2LeaderAuditor";

    private final String LEVEL2_LEADER = "Level2Leader";

    private final String NEED_LEVEL3_LEADER_AUDITOR = "needLevel3LeaderAuditor";

    private final String LEVEL3_LEADER = "Level3Leader";

    private final String NEED_LEVEL4_LEADER_AUDITOR = "needLevel4LeaderAuditor";

    private final String LEVEL4_LEADER = "Level4Leader";

    private final String MAIN_CUSTOMER = "mainCustomer";

    private final String ACT_NO_SUFFIX = "actNoSuffix";

    private final String ACT_TYPE = "actType";

    private final String COUNTRY = "country";

    @Override
    protected void checkParam(ApprovalFlowStartParam approvalFlowStartParam) {
        if (StringUtils.isBlank(approvalFlowStartParam.getBizId())) {
            // todo 详细报错
            throw new RuntimeException();
        }
        if (null == approvalFlowStartParam.getApprovalType()) {
            // todo 详细报错
            throw new RuntimeException();
        }
        // 已存在的未完成的审批活动不让重复启动
        List<ActivityApprovalInfoDO> approvalInfoDOList = approvalInfoRepository.queryAllByActivityRowId(approvalFlowStartParam.getBizId());
        approvalInfoDOList = approvalInfoDOList.stream().filter(e -> BooleanEnum.N.isMe(e.getInstanceStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(approvalInfoDOList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, NOTICE_APPROVAL_RESUBMIT);
        }
    }

    @Override
    public FlowStartDTO buildStartDTO(ApprovalFlowStartParam approvalFlowStartParam, String empNo) {
        FlowStartDTO flowStartDTO = new FlowStartDTO();
        flowStartDTO.setFlowCode(approvalFlowStartParam.getApprovalType().getCode());
        flowStartDTO.setBusinessId(keyIdService.getKeyId());
        flowStartDTO.setHandler(empNo);
        Map<String, Object> params = new HashMap<>(16);
        // 开始组装启动参数
        // 1.查询审批信息
        ActivityInfoDO activityInfoDO = activityInfoRepository.selectByPrimaryKey(approvalFlowStartParam.getBizId());
        ExhibitionInfoDO exhibitionInfoDo = exhibitionInfoRepository.selectByPrimaryKey(activityInfoDO.getOriginRowId());
        List<ApprovedByInfo> approvedByInfoList = JacksonJsonConverUtil.jsonToListBean(activityInfoDO.getApprovalText(), new TypeReference<List<ApprovedByInfo>>() {});
        if (CollectionUtils.isEmpty(approvedByInfoList)) {
            throw new RuntimeException();
        }
        // 1.1 查询合规审批
        List<ApprovedByInfo> complianceApprovedByList = approvedByInfoList.stream().filter(e -> ApprovalTypeEnum.COMPLIANCE_AUDITOR.isMe(e.getApprovalType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(complianceApprovedByList)) {
            params.put(NEED_COMPLIANCE_AUDITOR, BooleanEnum.Y.getCode());
            params.put(COMPLIANCE_MANAGER, complianceApprovedByList.get(0).getEmpNo());
        }
        // 1.2 查询业务审批
        List<ApprovedByInfo> bizLeaderApprovedByList = approvedByInfoList.stream().filter(e -> ApprovalTypeEnum.LEADER_AUDITOR.isMe(e.getApprovalType())).sorted(Comparator.comparingInt(ApprovedByInfo::getLevel)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bizLeaderApprovedByList)) {
            throw new RuntimeException();
        }
        // 如果相邻两个审批人相同，则保留层级更高的领导审批
        filterApprover(params, bizLeaderApprovedByList);
        // 1.3 封装通知信息
        buildInformInfo(params, activityInfoDO, exhibitionInfoDo);
        // 1.4 封装移动模板信息
        // 查询客户联系人信息信息
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList = activityRelationCustPeopleRepository.queryAllByActivityRowId(approvalFlowStartParam.getBizId());
        // 查询客户信息
        List<ActivityCustomerInfoDO> customerInfoDOList = activityCustomerInfoRepository.queryAllByActivityRowId(approvalFlowStartParam.getBizId());
        /* Started by AICoder, pid:n4792gb24e56fd714c24084d7015ed0e80348d76 */
        Optional<String> mainCustomerName = customerInfoDOList.stream()
                .filter(customer -> FLAG_YES.equals(customer.getMainCust()))
                .map(ActivityCustomerInfoDO::getCustomerName)
                .findAny();
        mainCustomerName.ifPresent(name -> params.put(MAIN_CUSTOMER, name));
        /* Ended by AICoder, pid:n4792gb24e56fd714c24084d7015ed0e80348d76 */
        List<ActivityCustomerInfoDO> sortedCustomerInfoDOList = customerInfoDOList.stream().sorted(Comparator.comparing(ActivityCustomerInfoDO::getSanctionedPatryCode, SanctionedPartyEnum::compare)).collect(Collectors.toList());
        params.put("activityContent", activityInfoDO.getActivityContent());
        String communicationWay = Optional.ofNullable(CommunicationWayEnum.getByCode(activityInfoDO.getCommunicationWay()))
                .orElse(CommunicationWayEnum.OFFLINE).getDesc();
        params.put("communicationWay", communicationWay);
        Map<String, OrgInfoVO> orgInfoMap = new HashMap<>(16);
        try {
            orgInfoMap = hrmUsercenterAdapter.getOrgInfoMap(ExternalConstant.IDTYPE_T0001, Lists.newArrayList(activityInfoDO.getApplyDepartmentNo()));
        } catch (Exception e) {
            logger.info("hrm service error!");
        }
        if ((null != orgInfoMap) && null != orgInfoMap.get(activityInfoDO.getApplyDepartmentNo())) {
            params.put("applyDepartment", orgInfoMap.get(activityInfoDO.getApplyDepartmentNo()).getHrOrgName());
        }
        StringBuilder sb = new StringBuilder();
        sortedCustomerInfoDOList.stream().forEach(e -> {
            sb.append(e.getMktName()).append(COLON_EN).append(SanctionedPartyEnum.getDescByCode(e.getSanctionedPatryCode())).append(SEMICOLON);
        });
        params.put("cusLimitStatus", sb.toString());
        StringBuilder sb1 = new StringBuilder();
        activityRelationCustPeopleDOList.stream().forEach(e -> {
            sb1.append(e.getContactName()).append(COLON_EN).append(SanctionedPartyEnum.getDescByCode(e.getSanctionedPatryCode())).append(SEMICOLON);
        });
        params.put("cusRelationLimitStatus", sb1.toString());
        String startTime = convertDateToString(activityInfoDO.getStartTime(), YYYY_MM_DD_HH_MM_SS);
        String endTime = convertDateToString(activityInfoDO.getEndTime(), YYYY_MM_DD_HH_MM_SS);
        params.put("communicationDate", startTime + "-" + endTime);
        flowStartDTO.setParams(params);
        return flowStartDTO;
    }

    private void filterApprover(Map<String, Object> params, List<ApprovedByInfo> bizLeaderApprovedByList) {
        for (int i = 0; i < bizLeaderApprovedByList.size(); i++) {
            if (i + 1 >= bizLeaderApprovedByList.size()) {
                break;
            }
            if (StringUtils.equals(bizLeaderApprovedByList.get(i).getEmpNo(), bizLeaderApprovedByList.get(i + 1).getEmpNo())) {
                bizLeaderApprovedByList.remove(i + 1);
                i = i - 1;
            }
        }
        for (ApprovedByInfo approvedByInfo : bizLeaderApprovedByList) {
            if (NumberConstant.TWO == approvedByInfo.getLevel()) {
                params.put(NEED_LEVEL2_LEADER_AUDITOR, BooleanEnum.Y.getCode());
                params.put(LEVEL2_LEADER, approvedByInfo.getEmpNo());
            }
            if (NumberConstant.THREE == approvedByInfo.getLevel()) {
                params.put(NEED_LEVEL3_LEADER_AUDITOR, BooleanEnum.Y.getCode());
                params.put(LEVEL3_LEADER, approvedByInfo.getEmpNo());
            }
            if (NumberConstant.FOUR == approvedByInfo.getLevel()) {
                params.put(NEED_LEVEL4_LEADER_AUDITOR, BooleanEnum.Y.getCode());
                params.put(LEVEL4_LEADER, approvedByInfo.getEmpNo());
            }
        }
    }

    private void buildInformInfo(Map<String, Object> params, ActivityInfoDO activityInfoDO, ExhibitionInfoDO exhibitionInfoDo) {
        String actLinkUrl = activityUrlConfig.fetchPendingNoticeUrl(activityInfoDO.getRowId());
        String actFlowUrl = activityUrlConfig.fetchDetailUrl(activityInfoDO.getRowId(), activityInfoDO.getActivityType(), "fifth");
        EmployeeInfoDTO approve = userCenterService.getUserInfo(activityInfoDO.getApplyPeopleNo());
        String applyPeopleName = null == approve ? StringUtils.EMPTY : approve.getEmpName();
        params.put("exhibitionTitle", exhibitionInfoDo.getExhibitionName());
        params.put("actLinkUrl", actLinkUrl);
        String countryCode = activityInfoDO.getCountryCode();
        Set<String> countryCodes = Sets.newHashSet(countryCode);
        Map<String, AreaDataModel> areaMap = areaDataCacheClient.fetchAllCache(AreaTypeEnum.COUNTRY, countryCodes);
        for (Map.Entry<String, AreaDataModel> entry : areaMap.entrySet()) {
            AreaDataModel areaData = entry.getValue();
            String countryName = StringUtils.equals(HeadersProperties.getXLangId(), LANGUAGE_ZH_CN)
                    ? areaData.getAreaNameZh()
                    : areaData.getAreaNameEn();
            params.put(COUNTRY, countryName);
        }
        String activityNo = activityInfoDO.getActivityRequestNo();
        params.put("actNo", activityNo);
        params.put(ACT_NO_SUFFIX, activityNo.length() > NumberConstant.SEVEN
                ? activityNo.substring(activityNo.length() - NumberConstant.SEVEN)
                : activityNo);
        params.put(ACT_TYPE, ActivityTypeEnum.getDescByType(activityInfoDO.getActivityType()));
        params.put("actTitle", activityInfoDO.getActivityTitle());
        params.put("applyPeople", applyPeopleName + activityInfoDO.getApplyPeopleNo());
        params.put("applyPeopleEmpNo", activityInfoDO.getApplyPeopleNo());
        params.put("submitTime", DateUtils.convertDateToString(activityInfoDO.getSubmitTime(), "yyyy-MM-dd HH:mm:ss"));
        params.put("actFlowUrl", actFlowUrl);
    }

    private String createApprovalProcess(ApprovalFlowStartParam approvalFlowStartParam, String empNo, ApprovalTypeEnum approvalTypeEnum, String approvalInfoRowId) {
        ActivityApprovalProcessDO data = new ActivityApprovalProcessDO();
        data.setActivityRowId(approvalFlowStartParam.getBizId());
        data.setRowId(keyIdService.getKeyId());
        data.setProcessType(approvalTypeEnum.getCode());
        data.setProcessStatus(ProcessStatusEnum.DEFAULT.getCode());
        data.setCreatedBy(empNo);
        data.setLastUpdatedBy(empNo);
        data.setActivityApprovalInfoRowId(approvalInfoRowId);
        approvalProcessRepository.insertSelective(data);
        return data.getRowId();
    }

    @Override
    protected String startFlow(FlowStartDTO flowStartDTO) {
        try {
            return ApprovalFlowClient.start(flowStartDTO);
        } catch (Exception e) {
            // todo 详细报错
            throw new RuntimeException();
        }
    }

    @Override
    protected String saveData(ApprovalFlowStartParam approvalFlowStartParam, FlowStartDTO flowStartDTO, String empNo) {
        ActivityApprovalInfoDO record = new ActivityApprovalInfoDO();
        record.setInstanceStatus(BooleanEnum.N.getCode());
        record.setInstanceId(StringUtils.EMPTY);
        record.setCreatedBy(empNo);
        record.setLastUpdatedBy(empNo);
        String approvalInfoRowId = keyIdService.getKeyId();
        record.setRowId(approvalInfoRowId);
        record.setApprovalNo(flowStartDTO.getBusinessId());
        record.setApprovalType(EXHIBITION_APPROVAL.getCode());
        record.setActivityRowId(approvalFlowStartParam.getBizId());
        approvalInfoRepository.insertSelective(record);

        Map<String, Object> paramsMap = flowStartDTO.getParams();
        if (MapUtils.isEmpty(paramsMap)) {
            throw new RuntimeException();
        }
        // 对应的process和code表都都先创建
        Object complianceManager = paramsMap.get(COMPLIANCE_MANAGER);
        if (null != complianceManager) {
            String processRowId = createApprovalProcess(approvalFlowStartParam, empNo, ApprovalTypeEnum.COMPLIANCE_AUDITOR, approvalInfoRowId);
            ActivityApprovalProcessNodeDO data = new ActivityApprovalProcessNodeDO();
            data.setActivityRowId(approvalFlowStartParam.getBizId());
            data.setApprovalProcessRowId(processRowId);
            data.setNodeStatus(ApproveNodeStatusEnum.DEFAULT.getCode());
            data.setNodeType(ApproveNodeTypeEnum.COMPLIANCE_MANAGER_AUDITOR_NODE_CODE.getCode());
            String approvedBy = complianceManager.toString();
            data.setApproveBy(approvedBy);
            data.setCreatedBy(empNo);
            EmployeeInfoDTO approve = userCenterService.getUserInfo(approvedBy);
            data.setApproverName(approve.getEmpName());
            approvalProcessNodeRepository.insertSelective(data);
        }
        String processRowId = StringUtils.EMPTY;
        Object level4Leader = paramsMap.get(LEVEL4_LEADER);
        Object level3Leader = paramsMap.get(LEVEL3_LEADER);
        Object level2Leader = paramsMap.get(LEVEL2_LEADER);
        if (null != level4Leader || null != level3Leader || null != level2Leader) {
            processRowId = createApprovalProcess(approvalFlowStartParam, empNo, ApprovalTypeEnum.LEADER_AUDITOR, approvalInfoRowId);
        }
        if (null != level4Leader) {
            ActivityApprovalProcessNodeDO data = new ActivityApprovalProcessNodeDO();
            data.setActivityRowId(approvalFlowStartParam.getBizId());
            data.setApprovalProcessRowId(processRowId);
            data.setNodeStatus(ApproveNodeStatusEnum.DEFAULT.getCode());
            data.setNodeType(ApproveNodeTypeEnum.LEVEL4_LEADER_AUDITOR_NODE_CODE.getCode());
            String approvedBy = level4Leader.toString();
            data.setApproveBy(approvedBy);
            EmployeeInfoDTO approve = userCenterService.getUserInfo(approvedBy);
            data.setApproverName(approve.getEmpName());
            data.setCreatedBy(empNo);
            // 初始状态
            approvalProcessNodeRepository.insertSelective(data);
        }
        if (null != level3Leader) {
            ActivityApprovalProcessNodeDO data = new ActivityApprovalProcessNodeDO();
            data.setActivityRowId(approvalFlowStartParam.getBizId());
            data.setNodeStatus(ApproveNodeStatusEnum.DEFAULT.getCode());
            data.setApprovalProcessRowId(processRowId);
            data.setNodeType(ApproveNodeTypeEnum.LEVEL3_LEADER_AUDITOR_NODE_CODE.getCode());
            String approvedBy = level3Leader.toString();
            data.setApproveBy(approvedBy);
            data.setCreatedBy(empNo);
            EmployeeInfoDTO approve = userCenterService.getUserInfo(approvedBy);
            data.setApproverName(approve.getEmpName());
            // 初始状态
            approvalProcessNodeRepository.insertSelective(data);
        }
        if (null != level2Leader) {
            ActivityApprovalProcessNodeDO data = new ActivityApprovalProcessNodeDO();
            data.setActivityRowId(approvalFlowStartParam.getBizId());
            data.setApprovalProcessRowId(processRowId);
            data.setNodeStatus(ApproveNodeStatusEnum.DEFAULT.getCode());
            data.setNodeType(ApproveNodeTypeEnum.LEVEL2_LEADER_AUDITOR_NODE_CODE.getCode());
            data.setCreatedBy(empNo);
            String approvedBy = level2Leader.toString();
            data.setApproveBy(approvedBy);
            EmployeeInfoDTO approve = userCenterService.getUserInfo(approvedBy);
            data.setApproverName(approve.getEmpName());
            // 初始状态
            approvalProcessNodeRepository.insertSelective(data);
        }
        return approvalInfoRowId;
    }

    @Override
    protected void updateFlowInfo(String flowId, String approvalRowId) {
        ActivityApprovalInfoDO record = new ActivityApprovalInfoDO();
        record.setInstanceId(flowId);
        record.setRowId(approvalRowId);
        approvalInfoRepository.updateByActivityRowIdSelective(record);
    }

    @Override
    public boolean support(ApprovalFlowStartParam param) {
        return ApprovalFlowTypeEnum.EXHIBITION_APPROVAL.isMe(param.getApprovalType().getCode());
    }
}
