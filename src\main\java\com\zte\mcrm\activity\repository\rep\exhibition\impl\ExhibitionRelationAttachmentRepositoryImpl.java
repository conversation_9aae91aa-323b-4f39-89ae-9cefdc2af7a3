package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationAttachmentExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationAttachmentRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ExhibitionRelationAttachmentRepositoryImpl
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会关联的附件信息业务接口
 * @date 2023/9/615:03
 */
@Component
public class ExhibitionRelationAttachmentRepositoryImpl implements ExhibitionRelationAttachmentRepository {
    @Resource
    private ExhibitionRelationAttachmentExtMapper exhibitionRelationAttachmentExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ExhibitionRelationAttachmentDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        return exhibitionRelationAttachmentExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationAttachmentDO record) {
        if (null == record || StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        return exhibitionRelationAttachmentExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String,List<ExhibitionRelationAttachmentDO>> queryAttachmentByExhibitionRowId(List<String> exhibitionRowIds) {
        return CollectionUtils.isEmpty(exhibitionRowIds) ? Collections.emptyMap() :
                exhibitionRelationAttachmentExtMapper.getAttachmentByExhibitionRowIds(exhibitionRowIds).stream().collect(
                        Collectors.groupingBy(ExhibitionRelationAttachmentDO::getExhibitionRowId));
    }
}
