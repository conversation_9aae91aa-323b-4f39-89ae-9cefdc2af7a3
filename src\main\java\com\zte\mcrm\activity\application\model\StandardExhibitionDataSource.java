package com.zte.mcrm.activity.application.model;

import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionAttachmentSceneTypeEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.exhibition.*;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionRelationRoomVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标准展会数据源
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class StandardExhibitionDataSource {
    /**
     * 分页展会
     */
    private PageRows<ExhibitionInfoDO> exhibitionPage;
    /**
     * 展会信息
     */
    private List<ExhibitionInfoDO> exhibitionList;
    /**
     * 关联专家信息
     */
    private Map<String, List<ExhibitionRelationExpertDO>> expertMap;
    /**
     * 关联领导信息
     */
    private Map<String, List<ExhibitionRelationLeaderDO>> leaderMap;
    /**
     * 关联附件信息
     */
    private Map<String, List<ExhibitionRelationAttachmentDO>> attachmentMap;
    /**
     * 会议室信息
     */
    private Map<String, List<ExhibitionRelationRoomDO>> roomMap;
    /**
     * 车辆信息
     */
    private Map<String, List<ExhibitionRelationCarDO>> carMap;
    /**
     * 酒店信息
     */
    private Map<String, List<ExhibitionRelationHotelDO>> hotelMap;

    /**
     * 获取展会信息
     *
     * @param exhibitionRowId 展会RowId
     * @return 展会信息
     */
    public ExhibitionInfoDO fetchExhibition(String exhibitionRowId) {
        List<ExhibitionInfoDO> list = exhibitionList == null ?
                (exhibitionPage == null ? null : exhibitionPage.getRows()) : exhibitionList;

        return list == null ? null : list.stream().filter(e -> StringUtils.equals(exhibitionRowId, e.getRowId())).findFirst().orElse(null);
    }

    /**
     * 获取展会对应专家资源列表
     *
     * @param exhibitionRowId 展会RowId
     * @return 关联专家资源
     */
    public List<ExhibitionRelationExpertDO> fetchExhibitionExpert(String exhibitionRowId) {
        List<ExhibitionRelationExpertDO> list = expertMap == null ? null : expertMap.get(exhibitionRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取展会对应领导资源列表
     *
     * @param exhibitionRowId 展会RowId
     * @return 关联L领导资源
     */
    public List<ExhibitionRelationLeaderDO> fetchExhibitionLeader(String exhibitionRowId) {
        List<ExhibitionRelationLeaderDO> list = leaderMap == null ? null : leaderMap.get(exhibitionRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取展会对应附件列表
     *
     * @param exhibitionRowId 展会RowId
     * @param types           附件类型
     * @return 关联L领导资源
     */
    public List<ExhibitionRelationAttachmentDO> fetchAttachment(String exhibitionRowId, ExhibitionAttachmentSceneTypeEnum... types) {
        List<ExhibitionRelationAttachmentDO> list = attachmentMap == null ? null : attachmentMap.get(exhibitionRowId);
        return list == null ? null : list.stream()
                .filter(e -> types.length == 0 || ExhibitionAttachmentSceneTypeEnum.in(e.getExhibitionAttachmentSceneType(), types))
                .collect(Collectors.toList());
    }

    /**
     * 获取会议室资源
     *
     * @param exhibitionRowId
     * @return
     */
    public List<ExhibitionRelationRoomDO> fetchRoom(String exhibitionRowId) {
        // getOrDefault这个方法如果value是null，返回的是null
        List<ExhibitionRelationRoomDO> list = roomMap == null ? null : roomMap.get(exhibitionRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取车辆资源
     *
     * @param exhibitionRowId
     * @return
     */
    public List<ExhibitionRelationCarDO> fetchCar(String exhibitionRowId) {
        // getOrDefault这个方法如果value是null，返回的是null
        List<ExhibitionRelationCarDO> list = carMap == null ? null : carMap.get(exhibitionRowId);
        return list == null ? Collections.emptyList() : list;
    }

    /**
     * 获取酒店资源
     *
     * @param exhibitionRowId
     * @return
     */
    public List<ExhibitionRelationHotelDO> fetchHotel(String exhibitionRowId) {
        // getOrDefault这个方法如果value是null，返回的是null
        List<ExhibitionRelationHotelDO> list = hotelMap == null ? null : hotelMap.get(exhibitionRowId);
        return list == null ? Collections.emptyList() : list;
    }
}
