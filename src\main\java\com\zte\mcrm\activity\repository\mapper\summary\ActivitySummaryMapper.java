package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;

public interface ActivitySummaryMapper {
    /**
     * all field insert
     */
    int insert(ActivitySummaryDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivitySummaryDO record);

    /**
     * query by primary key
     */
    ActivitySummaryDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivitySummaryDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivitySummaryDO record);
}