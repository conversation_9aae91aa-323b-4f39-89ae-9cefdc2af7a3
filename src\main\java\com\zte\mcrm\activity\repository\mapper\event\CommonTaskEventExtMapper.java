package com.zte.mcrm.activity.repository.mapper.event;

import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.service.event.param.CommonTaskEventQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommonTaskEventExtMapper extends CommonTaskEventMapper {

    /***
     * <p>
     * 批量更新定时任务信息
     *
     * </p>
     * <AUTHOR>
     * @since  2024/1/16 上午10:38
     * @param updateList 待更新的记录列表
     * @return int
     */
    int batchUpdateByPrimaryKey(@Param(value = "updateList") List<CommonTaskEventDO> updateList);

    /***
     * <p>
     * 获取符合条件的定时任务记录列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/1/16 上午11:05
     * @param query 查询条件
     * @return java.util.List<com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO>
     */
    List<CommonTaskEventDO> getListByQuery(CommonTaskEventQueryParam query);

    /***
     * <p>
     * 逻辑删除定时任务记录
     *
     * </p>
     * <AUTHOR>
     * @since  2024/1/16 上午10:39
     * @param operator 操作人
     * @param rowIds 待删除的id列表
     * @return int
     */
    int softDeleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

}