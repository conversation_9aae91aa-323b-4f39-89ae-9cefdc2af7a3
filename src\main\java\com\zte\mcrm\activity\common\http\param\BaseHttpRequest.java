package com.zte.mcrm.activity.common.http.param;

import com.zte.mcrm.activity.common.util.StringUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.HttpEntity;

import java.text.Normalizer;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class BaseHttpRequest {
    /**
     * 请求地址
     */
    private final String url;
    /**
     * 请求方式。如：get，post……
     */
    private final String method;
    /**
     * request请求头
     */
    private final Map<String, String> requestHeaderMap = new HashMap<>();
    /**
     * 是否为https请求
     */
    private final boolean https;

    /**
     * 链接超时时间，默认30秒
     */
    private int connectTimeOut = 60 * 1000;
    /**
     * socket读写超时时间，默认120秒
     */
    private int socketTimeOut = 120 * 1000;
    /**
     * 从连接池获取链接超时花四溅，默认30秒
     */
    private int connectReqTimeOut = 30 * 1000;


    public BaseHttpRequest(String url, String method) {
        this(url, method, null);
    }

    /**
     * @param url              请求地址
     * @param method           请求方式。如：get，post……
     * @param requestHeaderMap 请求头
     */
    public BaseHttpRequest(String url, String method, Map<String, String> requestHeaderMap) {
        this.method = method;
        this.url = Normalizer.normalize(url.trim(), Normalizer.Form.NFKC);
        this.https = this.url.startsWith("https");
        if (MapUtils.isNotEmpty(requestHeaderMap)) {
            this.requestHeaderMap.putAll(requestHeaderMap);
        }
    }

    /**
     * 获取请求体
     *
     * @return 请求体
     */
    public abstract HttpEntity fetchHttpEntity();

    /**
     * 添加请求头
     *
     * @param name  请求头名称，参考：{@link  com.zte.mcrm.activity.common.constant.RequestHeaderConstant}
     * @param value 请求头值
     * @return 返回this
     */
    public BaseHttpRequest addHeader(String name, String value) {
        // 不判断value是不是空的
        if (StringUtils.isNotBlank(name)) {
            this.requestHeaderMap.put(name, value);
        }
        return this;
    }

    /**
     * 添加请求头
     *
     * @param headerMap 请求头
     * @return 返回this
     */
    public BaseHttpRequest addHeader(Map<String, String> headerMap) {
        if (headerMap != null) {
            headerMap.forEach(this::addHeader);
        }
        return this;
    }

    /**
     * 移除请求头
     *
     * @param headerNames 需要被移除的请求头名称
     * @return 返回this
     */
    public BaseHttpRequest removeHeader(String... headerNames) {
        for (String headerName : headerNames) {
            this.requestHeaderMap.remove(headerName);
        }

        return this;
    }

}
