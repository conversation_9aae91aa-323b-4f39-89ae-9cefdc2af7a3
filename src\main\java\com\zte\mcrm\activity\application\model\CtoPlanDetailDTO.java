package com.zte.mcrm.activity.application.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数据库返回计划详情的数据
 * <AUTHOR>
 * @date 2025年02月14日9:48
 */
@Getter
@Setter
public class CtoPlanDetailDTO implements Serializable {

    /**
     * 活动Id，JSON数组
     */
    private String activityIds;

    /**
     * ran活动Id
     */
    private String ranActivityIds;

    /**
     * ccn活动Id
     */
    private String ccnActivityIds;

    /**
     * bn活动Id
     */
    private String bnActivityIds;

    /**
     * fm活动Id
     */
    private String fmActivityIds;

    /**
     * sn活动Id
     */
    private String snActivityIds;

    /**
     * 计划Id
     */
    private String ctoPlanInfoId;

    /**
     * account code
     */
    private String accountCodes;

    /**
     * account名称
     */
    private String accountNames;

    /**
     * 握手计划中的人
     */
    private String employeeNo;

    /**
     * 重点客户活动ID
     */
    private String accountActivityId;

}
