package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 任务执行器
 *
 * <AUTHOR>
 */
public interface TaskExecutor {

    /**
     * 添加异步任务
     *
     * @param task
     */
    void addTask(Runnable task);

    /**
     * 添加具有返回结果的任务
     *
     * @param task
     * @param <T>
     * @return
     */
    <T> Future<T> addTask(Callable<T> task);

}
