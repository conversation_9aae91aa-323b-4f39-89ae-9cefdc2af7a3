package com.zte.mcrm.activity.common.cache.client;

import com.zte.mcrm.activity.common.cache.cache.CacheConfig;
import com.zte.mcrm.activity.common.cache.loader.CacheDataLoader;
import com.zte.mcrm.activity.common.cache.model.AreaDataModel;
import com.zte.mcrm.activity.common.enums.AreaTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【区域】数据使用客户端
 *
 * <AUTHOR>
 */
public class AreaDataCacheClient extends BaseCacheClient<AreaDataModel> {
    /**
     * @param loader 缓存数据加载器
     * @param config 缓存配置
     */
    public AreaDataCacheClient(CacheDataLoader<String, AreaDataModel> loader, CacheConfig config) {
        super(loader, config);
    }

    /**
     * 获取地区信息
     *
     * @param type     地区类型
     * @param areaCode 地区code
     * @return
     */
    public AreaDataModel fetchCache(AreaTypeEnum type, String areaCode) {
        if (type == null || StringUtils.isBlank(areaCode)) {
            return null;
        }

        AreaDataModel m = new AreaDataModel();
        m.setAreaType(type.getType());
        m.setAreaCode(areaCode);

        return super.fetchCache(AreaDataModel.identityKey(m));
    }

    /**
     * 获取区域信息
     *
     * @param type      地区类型
     * @param areaCodes 地区code
     * @return
     */
    public Map<String, AreaDataModel> fetchAllCache(AreaTypeEnum type, Set<String> areaCodes) {
        if (type == null || CollectionUtils.isEmpty(areaCodes)) {
            return Collections.emptyMap();
        }

        return super.fetchAllCache(areaCodes.stream().map(code -> {
            AreaDataModel m = new AreaDataModel();
            m.setAreaType(type.getType());
            m.setAreaCode(code);
            return m.identityKey();
        }).collect(Collectors.toSet())).values().stream().collect(Collectors.toMap(AreaDataModel::getAreaCode, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 不允许直接调用该方法。请使用
     * {@link  AreaDataCacheClient#fetchCache(AreaTypeEnum, String)}
     *
     * @param key key
     * @return
     */
    @Override
    public AreaDataModel fetchCache(String key) {
        // 解决调用方无需关注key的组成问题
        throw new BizRuntimeException();
    }

    /**
     * 不允许直接调用该方法。请使用
     * {@link  AreaDataCacheClient#fetchAllCache(AreaTypeEnum, Set)}
     *
     * @param keys key
     * @return
     */
    @Override
    public Map<String, AreaDataModel> fetchAllCache(Set<String> keys) {
        // 解决调用方无需关注key的组成问题
        throw new BizRuntimeException();
    }
}
