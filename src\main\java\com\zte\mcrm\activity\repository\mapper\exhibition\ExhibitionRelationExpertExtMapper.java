package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface ExhibitionRelationExpertExtMapper extends ExhibitionRelationExpertMapper {
    /**
     * 查询展会对应的专家信息
     *
     * @param exhibitionRowIds 展会RowId列表
     * @return
     */
    List<ExhibitionRelationExpertDO> queryExpertsWithExhibitionRowId(@Param("exhibitionRowIds") List<String> exhibitionRowIds);
    /**
     * 查询常用专家信息
     *
     * @param size 返回的数据个数
     * @return
     */
    List<ExhibitionRelationExpertDO> queryCommonExperts(@Param("size") int size);
}

