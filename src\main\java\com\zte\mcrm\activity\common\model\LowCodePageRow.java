package com.zte.mcrm.activity.common.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 低代码列表/分页数据行规范
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class LowCodePageRow<T> {
    /**
     * 总条数
     */
    private int total;
    /**
     * 当前分页
     */
    private int pageNo;
    /**
     * 分页大小
     */
    private int pageSize;
    /**
     * 记录
     */
    private List<T> records;
}
