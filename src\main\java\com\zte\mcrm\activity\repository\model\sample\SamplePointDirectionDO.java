package com.zte.mcrm.activity.repository.model.sample;

import java.util.Date;

/**
 * table:sample_point_direction -- 
 */
public class SamplePointDirectionDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String samplePointRowId;

    /** 发展方向大类。存父快码COMMUNICATION_DIRECTOR_LEVEL1查出的快码类型，如：TERMINAL。TERMINAL-终端，NEW_BUSINESS-新业务等 */
    private String communicationType;

    /** 发展方向大类。存父快码COMMUNICATION_DIRECTOR_LEVEL1查出的快码编码，如：SCHEME_TM。SCHEME_TM-方案等 */
    private String communicationSubType;

    /** 发展方向code。以communication_sub_type的值作为快码类型查询出的快码编码。见：LookUpService#fetchCommunicationTree方法 */
    private String communicationDirection;

    /** 对应交流方向的描述内容 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getSamplePointRowId() {
        return samplePointRowId;
    }

    public void setSamplePointRowId(String samplePointRowId) {
        this.samplePointRowId = samplePointRowId == null ? null : samplePointRowId.trim();
    }

    public String getCommunicationType() {
        return communicationType;
    }

    public void setCommunicationType(String communicationType) {
        this.communicationType = communicationType == null ? null : communicationType.trim();
    }

    public String getCommunicationSubType() {
        return communicationSubType;
    }

    public void setCommunicationSubType(String communicationSubType) {
        this.communicationSubType = communicationSubType == null ? null : communicationSubType.trim();
    }

    public String getCommunicationDirection() {
        return communicationDirection;
    }

    public void setCommunicationDirection(String communicationDirection) {
        this.communicationDirection = communicationDirection == null ? null : communicationDirection.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}