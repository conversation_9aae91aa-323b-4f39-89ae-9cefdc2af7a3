package com.zte.mcrm.activity.repository.mapper.event;

import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 客户活动关联事件 数据访问接口类
 * <AUTHOR>
 * @date 2023/06/29
 */
public interface ActivityRelationEventMapper {
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2023/06/29
     * @return 实体
     */
	ActivityRelationEventDO get(@Param("rowId")String rowId);

    /**
     * 查询列表
     * <AUTHOR>
     * @param entity 查询条件
     * @date 2023/06/29
     * @return 实体集合
     */
	List<ActivityRelationEventDO> getList(ActivityRelationEventDO entity);

    /**
     * 删除
     * <AUTHOR>
     * @param rowId 主键
     * @date 2023/06/29
     * @return 删除总数
     */
	int delete(@Param("rowId")String rowId);

    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2023/06/29
     * @return 新增总数
     */
	int insert(ActivityRelationEventDO entity);

    /**
     * 批量新增
     * <AUTHOR>
     * @param list 新增实体集合
     * @date 2023/06/29
     * @return 新增总数
     */
	int insertByBatch(List<ActivityRelationEventDO> list);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2023/06/29
     * @return 更新影响总数
     */
	int update(ActivityRelationEventDO entity);

    /**
     * 动态更新
     * @param record
     * @return
     * <AUTHOR>
     * @date 2024/2/23
     */
    int updateByPrimaryKeySelective(ActivityRelationEventDO record);
}
