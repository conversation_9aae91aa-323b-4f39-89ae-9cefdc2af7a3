package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Getter
@Setter
@ToString
public class ExportActivityFeeVO {
    @Excel(name = "序号", orderNum = "1")
    private String index;

    @Excel(name = "提单人/申请人", orderNum = "2")
    private String applicantDesc;

    @Excel(name = "事业部", orderNum = "3")
    private String orgName2;

    @Excel(name = "片区", orderNum = "4")
    private String orgName3;

    @Excel(name = "代表处", orderNum = "5")
    private String orgName4;

    @Excel(name = "主客户对应国家/地区", orderNum = "6")
    private String mainCustomerCountryRegion;

    @Excel(name = "客户单位", orderNum = "7")
    private String customerName;

    @Excel(name = "客户单位级别", orderNum = "8")
    private String custUnionLevel;

    @Excel(name = "客户参与人人数", orderNum = "9")
    private String totalContactCount;

    @Excel(name = "客户参与人最高级别", orderNum = "10")
    private String highestContactLevel;

    @Excel(name = "我司参与人人数", orderNum = "11")
    private String totalZetPeopleCount;

    @Excel(name = "客户侧机票费用", orderNum = "12")
    private String contactTicketCost;

    @Excel(name = "我司机票费用", orderNum = "13")
    private String zteTicketCost;

    @Excel(name = "客户侧签证费用", orderNum = "14")
    private String contactVisaCost;

    @Excel(name = "我司签证费用", orderNum = "15")
    private String zteVisaCost;

    @Excel(name = "客户侧业务费用", orderNum = "16")
    private String contactBusinessCost;

    @Excel(name = "我司业务费用", orderNum = "17")
    private String zteBusinessCost;

    @Excel(name = "客户侧酒店费用", orderNum = "18")
    private String contactHotelCost;

    @Excel(name = "我司酒店费用", orderNum = "19")
    private String zteHotelCost;

    @Excel(name = "客户侧车辆费用", orderNum = "20")
    private String contactVehicleCost;

    @Excel(name = "我司车辆费用", orderNum = "21")
    private String zteVehicleCost;

    @Excel(name = "客户侧其他费用", orderNum = "22")
    private String contactOtherCost;

    @Excel(name = "我司其他费用", orderNum = "23")
    private String zteOtherCost;

    @Excel(name = "客户侧费用合计", orderNum = "24")
    private String contactSumCost;

    @Excel(name = "我司侧费用合计", orderNum = "25")
    private String zteSumCost;

    @Excel(name = "费用合计", orderNum = "26")
    private String totalCost;

    @Excel(name = "活动编号", orderNum = "27")
    private String activityRequestNo;

    @Excel(name = "单据状态", orderNum = "28")
    private String activityStatus;

    @Excel(name = "最后更新日期", orderNum = "29")
    private String lastUpdatedDate;
}
