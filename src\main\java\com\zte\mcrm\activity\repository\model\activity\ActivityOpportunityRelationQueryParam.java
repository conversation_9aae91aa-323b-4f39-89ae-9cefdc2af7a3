package com.zte.mcrm.activity.repository.model.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 10307200
 * @since 2024-06-04 下午5:04
 **/

@Setter
@Getter
public class ActivityOpportunityRelationQueryParam {

    @ApiModelProperty(value = "活动编号")
    private String activityRequestNo;

    @ApiModelProperty(value = "活动标题")
    private String activityTitle;

    @ApiModelProperty(value = "商机编号")
    private String opportunityCode;

    @ApiModelProperty(value = "通过商机查询需要过滤掉的活动列表")
    private List<String> filterActivityRowIdList;

    @ApiModelProperty(value = "活动状态列表")
    private List<String> activityStatusList;

    @ApiModelProperty(value = "活动开始时间")
    private Date dateStartTime;

    @ApiModelProperty(value = "活动结束时间")
    private Date dateEndTime;

    @ApiModelProperty(value = "当前登录人工号")
    private String empNo;

    @ApiModelProperty(value = "活动关联人员类型列表")
    private List<String> peopleTypeList;

}
