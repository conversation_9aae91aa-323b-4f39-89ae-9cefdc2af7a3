package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.resource.ResourceBizExpertDirectionExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDirectionDO;
import com.zte.mcrm.activity.repository.rep.resource.ResourceBizExpertDirectionRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * ClassName: ResourceBizExpertDirectionRepositoryImpl
 * Description:
 * date: 2023/5/26 09:27
 *
 * <AUTHOR>
 */
@Component
public class ResourceBizExpertDirectionRepositoryImpl implements ResourceBizExpertDirectionRepository {

    @Resource
    private ResourceBizExpertDirectionExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(List<ResourceBizExpertDirectionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ResourceBizExpertDirectionDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            record.setEnabledFlag(CharacterConstant.Y);
            record.setCreationDate(new Date());
            record.setLastUpdateDate(new Date());
            extMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public List<ResourceBizExpertDirectionDO> getByEmpNosAndDirections(List<String> empNos, List<String> directions) {
        if (CollectionUtils.isEmpty(empNos)) {
            return Collections.emptyList();
        }
        return extMapper.queryByEmpNosAndDirections(empNos, directions);
    }

    @Override
    public List<ResourceBizExpertDirectionDO> getByEmpNos(List<String> empNos) {
        if (CollectionUtils.isEmpty(empNos)) {
            return Collections.emptyList();
        }
        return extMapper.getByEmpNos(empNos);
    }

    @Override
    public int deleteByEmpNos(String operator, List<String> empNos) {
        if (CollectionUtils.isEmpty(empNos)) {
            return NumberConstant.ZERO;
        }
        return extMapper.softDeleteByEmpNos(operator, empNos);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return extMapper.softDeleteByRowIds(operator,rowIds);
    }
}
