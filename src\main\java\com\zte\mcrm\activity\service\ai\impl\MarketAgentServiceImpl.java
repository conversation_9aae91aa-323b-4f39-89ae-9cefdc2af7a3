package com.zte.mcrm.activity.service.ai.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.json.JsonSanitizer;
import com.zte.mcrm.activity.common.constant.AiConstant;
import com.zte.mcrm.activity.common.constant.DictConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.ai.MessageTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.service.ai.AiRobotService;
import com.zte.mcrm.activity.service.ai.MarketAgentService;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptReqVO;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataQueryVO;
import com.zte.mcrm.custcomm.common.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/10/31 13:53
 */
@Service
public class MarketAgentServiceImpl implements MarketAgentService {

    private static final Logger logger = LoggerFactory.getLogger(MarketAgentServiceImpl.class);

    @Autowired
    private MarketAgentStrategyComponent marketAgentStrategyComponent;

    @Autowired
    private AiRobotService aiRobotService;

    @Autowired
    private DictService dictService;

    /**
     * 获取并组装AI答案
     * @param request
     * @return
     */
    @Override
    public List<IgptRespVO> getAnswerList(BizRequest<IgptReqVO> request) {
        IgptReqVO igptReqVO = request.getParam();
        MarketDataQueryVO marketDataQueryVO = new MarketDataQueryVO();
        marketDataQueryVO.setQuestion(igptReqVO.getText());
        // 调用AI接口
        String result = aiRobotService.getMarketDataInfo(marketDataQueryVO);
        if(StringUtils.isEmpty(result)){
            String msg = dictService.getDictValueByTypeAndKey(DictConstant.MARKET_AGENT, DictConstant.AI_ERROR_MSG);
            return Lists.newArrayList(packIgptRespVO(igptReqVO.getChatUuid(), msg));
        }

        JSONObject jsonObject = JSON.parseObject(JsonSanitizer.sanitize(result));
        String code = jsonObject.getJSONObject(AiConstant.CODE).getString(AiConstant.CODE);
        if(!StringUtils.equals(RetCode.SUCCESS_CODE, code)){
            String msg = jsonObject.getJSONObject(AiConstant.CODE).getString(AiConstant.MSG);
            return Lists.newArrayList(packIgptRespVO(igptReqVO.getChatUuid(), msg));
        }

        // 获取具体数据
        AiApplicationRespVO respVO = jsonObject.getJSONObject(AiConstant.BO).toJavaObject(AiApplicationRespVO.class);
        respVO.setChatUuid(igptReqVO.getChatUuid());

        logger.info("AI请求-APIQuery返回结果:{}", JSONObject.toJSONString(respVO.getApiQuery()));
        // 根据接口类型处理不同数据
        List<IgptRespVO> igptRespVOList = marketAgentStrategyComponent.processBusinessStrategy(respVO);
        if(CollectionUtils.isEmpty(igptRespVOList)){
            String msg = dictService.getDictValueByTypeAndKey(DictConstant.MARKET_AGENT, DictConstant.NOT_SEARCH_ERROR_MSG);
            return Lists.newArrayList(packIgptRespVO(igptReqVO.getChatUuid(), msg));
        }
        return igptRespVOList;
    }

    /**
     * 打包数据
     * @param chatUuid
     * @param msg
     * @return
     */
    private IgptRespVO packIgptRespVO(String chatUuid, String msg){
        IgptRespVO igptRespVO = new IgptRespVO();
        igptRespVO.setChatUuid(chatUuid);
        igptRespVO.setStatus(NumberConstant.ZERO);
        JSONObject object = new JSONObject();
        object.put(AiConstant.CONTENT, msg);
        object.put(AiConstant.TYPE, MessageTypeEnum.TEXT.getType());
        igptRespVO.setResult(object.toJSONString());
        return igptRespVO;
    }
}
