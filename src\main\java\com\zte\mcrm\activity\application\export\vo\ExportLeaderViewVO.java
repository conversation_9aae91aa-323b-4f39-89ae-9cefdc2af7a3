package com.zte.mcrm.activity.application.export.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午2:12
 **/
@Setter
@Getter
public class ExportLeaderViewVO {

    @Excel(name = "事业部", orderNum = "1")
    private String org2Name;

    @Excel(name = "片区", orderNum = "2")
    private String org3Name;

    @Excel(name = "代表处", orderNum = "3")
    private String orgName;

    @Excel(name = "会见日期", orderNum = "4")
    private String scheduleDate;

    @Excel(name = "时间", orderNum = "5")
    private String scheduleTime;

    @Excel(name = "地点", orderNum = "6")
    private String placeName;

    @Excel(name = "日程名称", orderNum = "7")
    private String scheduleItemName;

    @Excel(name = "高层领导", orderNum = "8")
    private String zteLeader;

    @Excel(name = "专家", orderNum = "9")
    private String zteExpert;

    @Excel(name = "客户单位", orderNum = "10")
    private String customerName;

    @Excel(name = "Account", orderNum = "11")
    private String mktName;

    @Excel(name = "Group", orderNum = "12")
    private String mtoName;

    @Excel(name = "国家", orderNum = "13")
    private String localName;

    @Excel(name = "客户参与人及职务", orderNum = "14")
    private String contractDesc;

    @Excel(name = "交流方向", orderNum = "15")
    private String communicateDirectionDesc;

}
