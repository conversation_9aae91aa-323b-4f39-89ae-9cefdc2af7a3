package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.service.converter.activity.ActivityStatusLifecycleConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @ClassName ActivityStatusLifecycleAddParam
 * @description: 活动生命周期新增对象
 * @author: 李龙10317843
 * @create: 2023-05-22 15:20
 * @Version 1.0
 **/
@Getter
@Setter
@ToString
public class ActivityStatusLifecycleAddParam {

    /**
     * 拓展活动id
     */
    private String activityRowId;

    /**
     * 状态为status_to的时间
     */
    private Date enterTime;

    /**
     * 变更前状态。枚举：ActivityStatusEnum
     */
    private String statusFrom;

    /**
     * 变更后状态。枚举：ActivityStatusEnum
     */
    private String statusTo;

    /**
     * 记录创建人
     */
    private String createdBy;


}