package com.zte.mcrm.activity.common.http;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.http.param.BaseHttpRequest;
import com.zte.mcrm.activity.common.http.param.GetHttpRequest;
import com.zte.mcrm.activity.common.http.param.PostHttpRequest;
import com.zte.mcrm.activity.common.http.param.PutHttpRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.util.MsaRpcResponseUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.common.util.HttpClientUtil;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.function.Function;

/**
 * http统一请求处理工具类
 *
 * <AUTHOR>
 */
public class HttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 发送GET请求（响应结果可以转为字符串）
     *
     * @param request 请求参数
     * @param covert  报文转换
     * @param <R>
     * @return 结果
     */
    public static <R> MsaRpcResponse<R> getHttpWithStringRes(GetHttpRequest request, Function<String, MsaRpcResponse<R>> covert) {
        HttpRequestBase httpGet = new HttpGet(UriComponentsBuilder.fromUriString(request.getUrl()).build().toUri());

        dealBaseHttp(httpGet, request);

        return httpWithStringRes(request, covert, httpGet);
    }

    /**
     * 发送POST请求（响应结果可以转为字符串）
     *
     * @param request 请求参数
     * @param covert  报文转换
     * @param <R>
     * @return 结果
     */
    public static <R> MsaRpcResponse<R> postHttpWithStringRes(PostHttpRequest request, Function<String, MsaRpcResponse<R>> covert) {
        // 【1】初始化链接配置
        HttpPost httpPost = new HttpPost(UriComponentsBuilder.fromUriString(request.getUrl()).build().toUri());
        // 【2】基本http信息
        dealBaseHttp(httpPost, request);
        // 【3】设置请求参数信息
        httpPost.setEntity(request.fetchHttpEntity());

        return httpWithStringRes(request, covert, httpPost);
    }

    /**
     * 发送POST请求（响应结果可以转为字符串）
     *
     * @param request 请求参数
     * @param covert  报文转换
     * @param <R>
     * @return 结果
     */
    public static <R> MsaRpcResponse<R> putHttpWithStringRes(PutHttpRequest request, Function<String, MsaRpcResponse<R>> covert) {
        // 【1】初始化链接配置
        HttpPut httpPut = new HttpPut(UriComponentsBuilder.fromUriString(request.getUrl()).build().toUri());
        // 【2】基本http信息
        dealBaseHttp(httpPut, request);
        // 【3】设置请求参数信息
        httpPut.setEntity(request.fetchHttpEntity());

        return httpWithStringRes(request, covert, httpPut);
    }

    /**
     * http请求处理（响应结果可以转为字符串的请求处理）
     *
     * @param requestParam
     * @param covert
     * @param http
     * @param <R>
     * @return
     */
    static <R> MsaRpcResponse<R> httpWithStringRes(BaseHttpRequest requestParam, Function<String, MsaRpcResponse<R>> covert, HttpRequestBase http) {
        String result = null;
        long start = System.currentTimeMillis();
        try (CloseableHttpClient httpClient = createHttpClient(requestParam)) {

            CloseableHttpResponse response = httpClient.execute(http);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == NumberConstant.TWO_HUNDRED || statusCode == NumberConstant.TWO_HUNDRED_ONE) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                logger.error("http请求状态异常，status code: {}", statusCode);
                MsaRpcResponse<R> res = errorRes(response);
                response.close();
                return res;
            }

            logger.info("http请求:{}，响应结果：{}", requestParam.getUrl(), result);

            response.close();
        } catch (Exception e) {
            logger.error("POST request fail， url:{}", requestParam.getUrl(), e);
        } finally {
            logger.info("http请求：{}，耗时：{}ms", requestParam.getUrl(), (System.currentTimeMillis() - start));
        }

        if (StringUtils.isBlank(result)) {
            return MsaRpcResponseUtil.fail("post request fail");
        }

        return covert.apply(result);
    }

    /**
     * 处理基本http信息（请求配置、请求头）
     *
     * @param baseHttp
     * @param request
     * @return
     */
    static HttpRequestBase dealBaseHttp(HttpRequestBase baseHttp, BaseHttpRequest request) {
        // 【1】初始化链接配置
        RequestConfig requestConfig = buildRequestConfig(request);
        baseHttp.setConfig(requestConfig);

        // 【2】设置请求头信息（这里如果请求头存在会更新，如果不存在则添加）
        request.getRequestHeaderMap().forEach(baseHttp::setHeader);
        return baseHttp;
    }

    /**
     * 构建 请求配置
     *
     * @param request
     * @return
     */
    static RequestConfig buildRequestConfig(BaseHttpRequest request) {
        return RequestConfig.custom()
                .setConnectTimeout(request.getConnectTimeOut())
                .setConnectionRequestTimeout(request.getConnectReqTimeOut())
                .setSocketTimeout(request.getSocketTimeOut())
                // .setProxy(new HttpHost("proxynj.zte.com.cn", 80, "http")) // 如果没有开对外网关，本地测试这个开启,或proxyhk。哪个有权限用哪个
                .build();
    }

    /**
     * 创建http请求客户端
     *
     * @param request 请求参数
     * @return
     */
    static CloseableHttpClient createHttpClient(BaseHttpRequest request) {
        return request.isHttps() ? HttpClientUtil.createSSLClientDefault() : HttpClients.createDefault();
    }

    /**
     * 返回结果错误
     *
     * @param response
     * @param <R>
     * @return
     */
    static <R> MsaRpcResponse<R> errorRes(CloseableHttpResponse response) {
        int statusCode = response.getStatusLine().getStatusCode();
        String code = MsaRpcResponse.OUT_SERVERERROR_CODE;
        String msg = "out system error";
        if (statusCode >= NumberConstant.THREE_HUNDRED && statusCode < NumberConstant.FOUR_HUNDRED) {
            msg = "request redirection";
        } else if (statusCode == NumberConstant.FOUR_HUNDRED) {
            msg = "bad request";
        } else if (statusCode == NumberConstant.FOUR_HUNDRED_ONE || statusCode == NumberConstant.FOUR_HUNDRED_THREE) {
            code = "0003";
            msg = "no permission";
        } else if (statusCode == NumberConstant.FOUR_HUNDRED_FOUR) {
            msg = "not found";
        }

        MsaRpcResponse<R> res = MsaRpcResponseUtil.fail(msg);
        res.setCode(code);
        return res;
    }


}
