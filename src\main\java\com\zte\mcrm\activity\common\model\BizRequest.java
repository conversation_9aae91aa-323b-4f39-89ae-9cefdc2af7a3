package com.zte.mcrm.activity.common.model;

import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.enums.LanguageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务请求参数（建议所有定义的领域服务方法参数为这类）
 * <br/>
 * 快捷创建对象见：
 * 辅助创建BizRequest可以根据{@link com.zte.mcrm.activity.common.util.BizRequestUtil}
 * <AUTHOR>
 */
@Getter
@Setter
public class BizRequest<T> {
    /**
     * 客户编号
     */
    private String empNo;
    /**
     * 客户会话token
     */
    private String token;
    /**
     * 当前语言环境
     */
    private String langId;
    /**
     * 业务参数
     */
    private T param;


    public BizRequest() {
    }

    /**
     * 获取当前语言环境（如果没有，默认返回中文）
     * @return
     */
    public LanguageEnum fetchLanguage() {
        return LanguageEnum.getEnumByLanguage(langId);
    }

    /**
     * 获取请求头Map
     *
     * @return
     */
    public Map<String, String> fetchHeaderMap() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(RequestHeaderConstant.X_AUTH_VALUE, getToken());
        headerMap.put(RequestHeaderConstant.X_EMP_NO, getEmpNo());
        headerMap.put(RequestHeaderConstant.X_LANG_ID, getLangId());

        return headerMap;
    }
}
