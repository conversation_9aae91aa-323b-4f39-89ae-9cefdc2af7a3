package com.zte.mcrm.activity.repository.mapper.plancto;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-12
 */
@Mapper
public interface CtoPlanSaleDivisionMappingExtMapper extends CtoPlanSaleDivisionMappingMapper {

    /**
     * 获取所有有效关系
     *
     * @return
     */
    List<CtoPlanSaleDivisionMappingDO> listAll();

    List<CtoPlanSaleDivisionMappingDO> queryCtoPlanSaleDivisionMappingList(CtoPlanSaleDivisionMappingDO param);
}
