package com.zte.mcrm.activity.service.activity.param;

import com.zte.mcrm.activity.common.enums.activity.ApprovalTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum;
import com.zte.mcrm.activity.repository.model.activity.ActivityCostBudgetDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityApprovalParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityAttachmentInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityCostBudgetVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 活动数据源
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ActivityDataSource {
    /**
     * 活动信息
     */
    private ActivityInfoDO activityInfoDO;

    /**
     * 活动成本预算
     */
    private List<ActivityCostBudgetDO> activityCostBudgetDOList;

    /**
     * 活动成本预算配置
     */
    List<SampleVisitFeeConfigDO> sampleVisitFeeConfigDOList;

    /**
     * 活动附件
     */
    private List<ActivityRelationAttachmentDO> activityRelationAttachmentDOList;

    /**
     * 活动审批列表
     */
    private List<ActivityApprovalParam> activityApprovalParamList;

    /**
     * 获取活动审批列表
     *
     * @param approvalTypeEnum
     * @return
     */
    public List<ActivityApprovalParam> fetchActivityApprovalParamList(ApprovalTypeEnum approvalTypeEnum) {
        return Optional.ofNullable(activityApprovalParamList).filter(CollectionUtils::isNotEmpty).map(activityApprovalParams -> activityApprovalParams.stream().filter(activityApprovalParam -> approvalTypeEnum.isMe(activityApprovalParam.getApprovalType())).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    public List<ActivityCostBudgetVO> fetchActivityCostBudgetVOList() {
        return Optional.ofNullable(activityCostBudgetDOList).filter(CollectionUtils::isNotEmpty).map(acbDOList -> {
            List<ActivityCostBudgetVO> activityCostBudgetVOList = new ArrayList<>();
            acbDOList.forEach(activityCostBudgetDO -> {
                ActivityCostBudgetVO activityCostBudgetVO = new ActivityCostBudgetVO();
                BeanUtils.copyProperties(activityCostBudgetDO, activityCostBudgetVO);
                activityCostBudgetVOList.add(activityCostBudgetVO);
            });
            return activityCostBudgetVOList;
        }).orElse(Collections.emptyList());
    }

    /**
     * 获取活动成本预算变更标识
     *
     * @param international
     * @return
     */
    public Boolean fetchActivityCostBudgetChangeFlag(String international) {
        Set<String> sampleVisitFeeConfigSet = Optional.ofNullable(sampleVisitFeeConfigDOList).map(vfcDOList -> vfcDOList.stream().map(vfcDO -> vfcDO.getInternational() + vfcDO.getVisitModel() + vfcDO.getVisitFee() + vfcDO.getVisitFeeCurrency() + vfcDO.getApplyFeeRequire() + vfcDO.getReceiveFeeRequire()).collect(Collectors.toSet())).orElse(Collections.emptySet());
        return !Optional.ofNullable(activityCostBudgetDOList).orElse(Collections.emptyList()).stream().allMatch(acbDO -> sampleVisitFeeConfigSet.contains(international + acbDO.getFeeType() + acbDO.getFeeAmount() + acbDO.getCurrency() + acbDO.getApplyFeeRequire() + acbDO.getReceiveFeeRequire()));
    }

    /**
     * 获取活动附件
     *
     * @param attachmentSceneTypeEnum
     * @return
     */
    public List<ActivityAttachmentInfoVO> fetchActivityAttachmentInfoVOList(AttachmentSceneTypeEnum attachmentSceneTypeEnum) {
        return Optional.ofNullable(activityRelationAttachmentDOList).map(araDOList -> araDOList.stream().filter(ara -> attachmentSceneTypeEnum.isMe(ara.getAttachmentSceneType())).collect(Collectors.toList())).map(araDOList -> {
            List<ActivityAttachmentInfoVO> activityAttachmentInfoVOList = new ArrayList<>();
            araDOList.forEach(araDO -> {
                ActivityAttachmentInfoVO activityAttachmentInfoVO = new ActivityAttachmentInfoVO();
                BeanUtils.copyProperties(araDO, activityAttachmentInfoVO);
                activityAttachmentInfoVOList.add(activityAttachmentInfoVO);
            });
            return activityAttachmentInfoVOList;
        }).orElse(Collections.emptyList());
    }
}
