package com.zte.mcrm.activity.service.approval.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName ActivityApprovalFlowStartDTO
 * @description: 活动合规审批刘启动对象
 * @author: 李龙10317843
 * @create: 2023-05-16 16:33
 * @Version 1.0
 **/
@Data
public class ActivityApprovalFlowStartParam {

    @NotNull
    @ApiModelProperty("活动id")
    private String actRowId;

    @NotNull
    @ApiModelProperty("活动标题")
    private String actTitle;

    @NotEmpty
    @ApiModelProperty("通知提醒人集合")
    private String noticePeople;

    @NotNull
    @ApiModelProperty("活动编号（拓展活动申请单号 ）")
    private String actNo;

    @NotNull
    @ApiModelProperty("活动链接（拓展活动申请单号 ）")
    private String actLinkUrl;

    @NotNull
    @ApiModelProperty("部门编号")
    private String applyDepartmentNo;

    @ApiModelProperty("合规经理")
    private String complianceAuditor;

    @ApiModelProperty("责任部门领导")
    private String leaderAuditor;

    @NotNull
    @ApiModelProperty("交流类型")
    private String communicationWay;

    @NotNull
    @ApiModelProperty("交流时间")
    private String communicationDate;

    @NotNull
    @ApiModelProperty("申请人")
    private String applyPeople;

    @NotNull
    @ApiModelProperty("申请人工号")
    private String applyPeopleNo;

    @NotNull
    @ApiModelProperty("任务承接单位(申请人部门)")
    private String applyDepartment;

    @ApiModelProperty("详情描述(活动内容概要)")
    private String activityContent;

    @NotNull
    @ApiModelProperty("客户受限情况")
    private String cusLimitStatus;

    @NotNull
    @ApiModelProperty("联系人受限情况")
    private String cusRelationLimitStatus;

    @NotNull
    @ApiModelProperty("创建人")
    private String createdBy;

    @NotNull
    @ApiModelProperty("提交时间")
    private String submitTime;

    @NotNull
    @ApiModelProperty("最后更新人")
    private String lastUpdatedBy;

    @ApiModelProperty("是否需要合规经理审批 Y/N")
    private String needComplianceAuditor;
}