package com.zte.mcrm.activity.application.cto.convert;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.customvisit.util.DateUtils;

import java.util.Date;

public class CtoPlanProductFinishConvert {
    public static CtoPlanProductFinishDO buildUpdate(String rowId, int activityFinish, int accountFinish,
        String accountActivityId, String allActivityId){
        CtoPlanProductFinishDO obj = new CtoPlanProductFinishDO();
        Date now = new Date();
        obj.setRowId(rowId);
        obj.setActivityFinish(activityFinish);
        obj.setAccountFinish(accountFinish);
        obj.setAccountActivityId(accountActivityId);
        obj.setAllActivityId(allActivityId);
        // 执行时间设置为下一天凌晨1点
        obj.setExeWaitTime(DateUtils.adjustDate(0,1,1));
        obj.setExeFinishTime(now);
        obj.setLastUpdateDate(now);
        obj.setExeStatus(BooleanEnum.N.getCode());
        return obj;
    }

}
