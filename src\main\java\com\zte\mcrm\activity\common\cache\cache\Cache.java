package com.zte.mcrm.activity.common.cache.cache;

import java.util.Map;
import java.util.Set;

/**
 * 缓存
 * <pre>
 *     对于value暂时不使用Optional进行包装了
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
public interface Cache<K, V> {

    /**
     * 获取缓存数据（如果不存在，则自动加载数据）
     *
     * @param key
     * @return 缓存数据
     */
    V fetchCache(K key);

    /**
     * 获取缓存数据（仅仅只是从缓存中获取数据，如果没有不加载）
     *
     * @param keys
     * @return 仅仅缓存中存在则返回，缓存中都不存在则返回空map
     */
    Map<K, V> fetchCachesOnlyExist(Set<K> keys);

    /**
     * 添加缓存数据
     *
     * @param key 缓存key
     * @param val 缓存值
     */
    void addCache(K key, V val);

    /**
     * 失效（删除）缓存
     *
     * @param key 缓存key
     */
    void invalidate(K key);

    /**
     * 失效（删除）所有缓存
     */
    void invalidateAll();
}
