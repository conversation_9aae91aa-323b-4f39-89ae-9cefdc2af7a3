package com.zte.mcrm.activity.application.icrm.impl;

import com.zte.mcrm.activity.application.icrm.CustomerRelationActivityAppService;
import com.zte.mcrm.activity.application.icrm.convert.CustomerRelationActivityConvert;
import com.zte.mcrm.activity.application.model.StandardActivityDetailDataSource;
import com.zte.mcrm.activity.common.auth.CustomerIntegrationAuthModel;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.DateComputerUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.accountinfo.CustomerInfoQueryService;
import com.zte.mcrm.activity.integration.accountinfo.CustomerPersonService;
import com.zte.mcrm.activity.integration.accountinfo.dto.LatestVisitCustomerDTO;
import com.zte.mcrm.activity.integration.ap.ApSystemService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.repository.rep.relation.param.BaseActivityCustomerQuery;
import com.zte.mcrm.activity.service.activitylist.ActivityInfoListQueryService;
import com.zte.mcrm.activity.service.activitylist.param.ActivityDataSourceQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.web.controller.icrm.param.CustomerRelationParam;
import com.zte.mcrm.activity.web.controller.icrm.vo.*;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CustomerRelationActivityAppServiceImpl implements CustomerRelationActivityAppService {

    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;
    @Autowired
    private ActivityInfoListQueryService activityInfoListQueryService;
    @Autowired
    private CustomerPersonService customerPersonService;
    @Autowired
    private CustomerInfoQueryService customerInfoQueryService;
    @Autowired
    private ApSystemService apSystemService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private CustomerRelationActivityConvert customerRelationActivityConvert;


    @Override
    public LowCodePageRow<LatestVisitCustomerVO> latestVisitCustomer(BizRequest<CustomerRelationParam> request) {
        MsaRpcResponse<PageRows<LatestVisitCustomerDTO>> custRes = customerInfoQueryService.latestVisitCustomer(MsaRpcRequestUtil.createWithBizReq(request, CustomerRelationParam::getShowSize));
        List<LatestVisitCustomerDTO> custList = custRes.getBo().getRows();
        if (CollectionUtils.isEmpty(custList)) {
            return PageRowsUtil.buildEmptyLowCodePageRow(NumberConstant.ONE, request.getParam().getShowSize());
        }

        long total = custRes.getBo().getTotal();
        // 客户编码
        request.getParam().setCustomerCodeList(custList.stream().map(LatestVisitCustomerDTO::getCustomerCode).collect(Collectors.toList()));

        // 统计活动数据；商机 TODO 待外部提供接口。 项目、合同、订单、AP暂不做。 如果后续做了，这里要采用多线程并行查询。并将数据转换的放在转换类中
        Map<String, Integer> customer2Activity = countActivityForCustomer(request);

        List<LatestVisitCustomerVO> voList = new ArrayList<>(custList.size());
        for (LatestVisitCustomerDTO dto : custList) {
            LatestVisitCustomerVO vo = new LatestVisitCustomerVO();
            BeanUtils.copyProperties(dto, vo);
            vo.setContactPersonNum(String.valueOf(dto.getContactPersonNum()));
            vo.setActivityNum(String.valueOf(customer2Activity.get(dto.getCustomerCode())));

            voList.add(vo);
        }

        return PageRowsUtil.buildLowCodePageRow(NumberConstant.ONE, request.getParam().getShowSize(), (int) total, voList);
    }

    @Override
    public LowCodePageRow<CustomerRelationAllVO> allForCustomer(BizRequest<CustomerRelationParam> request) {
        CustomerRelationAllVO vo = new CustomerRelationAllVO();
        if (!request.getParam().valid()) {
            return PageRowsUtil.buildLowCodePageRow(Collections.singletonList(vo));
        }

        List<Future<Integer>> list = new ArrayList<>(NumberConstant.TEN);
        // 客户联系人数
        list.add(ThreadManager.submitToParallel(() -> {
            MsaRpcResponse<Integer> res = customerPersonService.countValidPerson(MsaRpcRequestUtil.createWithBizReq(request, e -> e.fetchCustomerCode(0)));
            vo.setContactPersonNum(String.valueOf(res.getBo()));
            return res.getBo();
        }));

        // 客户活动数、ap
        list.add(ThreadManager.submitToParallel(() -> {
            List<ActivityInfoDO> activityList = activityListWithCust(request, Arrays.asList(ActivityStatusEnum.PROGRESS.getCode(),
                    ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(), ActivityStatusEnum.BUSINESS_APPROVAL.getCode(), ActivityStatusEnum.FINISH.getCode()));
            vo.setActivityNum(String.valueOf(activityList.size()));

            List<ApTaskVO> apList = apSystemService.fetchApTaskInfo(MsaRpcRequestUtil.createWithBizReq(request, p ->
                    activityList.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toSet())));

            vo.setActivityApNum(String.valueOf(apList.size()));
            return apList.size();
        }));

        // TODO 本期其他字段暂时不做
        // 等待所有任务执行完毕
        ThreadManager.waitFuture(list);
        return PageRowsUtil.buildLowCodePageRow(Collections.singletonList(vo));
    }

    @Override
    public LowCodePageRow<CustomerRelationActivityVO> activityListForCustomer(BizRequest<CustomerRelationParam> request) {
        // 【1】获取活动列表
        List<ActivityInfoDO> activityList = activityListWithCust(request, Arrays.asList(ActivityStatusEnum.PROGRESS.getCode(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(), ActivityStatusEnum.BUSINESS_APPROVAL.getCode(), ActivityStatusEnum.DRAFT.getCode()));
        if (activityList.isEmpty()) {
            return PageRowsUtil.buildLowCodePageRow(Collections.emptyList());
        }
        int total = activityList.size();
        // 按照结束时间倒序排序，取xx条
        activityList = activityList.stream().sorted((o1, o2) -> DateComputerUtil.dateSortDesc(o1.getEndTime(), o2.getEndTime()))
                .limit(request.getParam().getShowSize())
                .collect(Collectors.toList());

        // 【2】打包活动需要的信息
        ActivityDataSourceQuery sourceQuery = new ActivityDataSourceQuery();
        sourceQuery.setActivityInfoList(activityList);
        sourceQuery.setNeedActivityDirection(true);
        sourceQuery.setNeedActivityCustomerInfo(true);
        sourceQuery.setNeedActivityCustomerPeople(true);
        sourceQuery.setNeedActivityZtePeople(true);
        StandardActivityDetailDataSource ds = activityInfoListQueryService.getStandardActivityDetailDataSource(sourceQuery);

        // 【3】活动信息转换
        return PageRowsUtil.buildLowCodePageRow(NumberConstant.ONE, request.getParam().getShowSize(), total,
                customerRelationActivityConvert.toCustomerRelationActivityVO(request, ds));
    }

    @Override
    public LowCodePageRow<CustomerRelationActivityApVO> apListForCustomer(BizRequest<CustomerRelationParam> request) {
        // 【1】获取活动列表
        List<ActivityInfoDO> activityList = activityListWithCust(request, Arrays.asList(ActivityStatusEnum.PROGRESS.getCode(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(), ActivityStatusEnum.BUSINESS_APPROVAL.getCode(), ActivityStatusEnum.DRAFT.getCode()));
        if (activityList.isEmpty()) {
            return PageRowsUtil.buildLowCodePageRow(Collections.emptyList());
        }
        // 【2】打包活动需要的信息
        ActivityDataSourceQuery sourceQuery = new ActivityDataSourceQuery();
        sourceQuery.setNeedAp(true);
        sourceQuery.setActivityInfoList(activityList);
        sourceQuery.setNeedActivityDirection(true);
        sourceQuery.setNeedActivityZtePeople(true);
        StandardActivityDetailDataSource ds = activityInfoListQueryService.getStandardActivityDetailDataSource(sourceQuery);

        // 【3】活动信息转换
        List<CustomerRelationActivityApVO> list = customerRelationActivityConvert.toCustomerRelationActivityApVO(request, ds);
        int showSize = request.getParam().getShowSize();
        return PageRowsUtil.buildLowCodePageRow(NumberConstant.ONE, showSize, list.size(), list.stream().limit(showSize).collect(Collectors.toList()));
    }


    @Override
    public LowCodePageRow<CustomerRelationActivityRdcVO> rdcListForCustomer(BizRequest<CustomerRelationParam> request) {
        // 【1】获取活动列表
        List<ActivityInfoDO> activityList = activityListWithCust(request, Arrays.asList(ActivityStatusEnum.PROGRESS.getCode(),
                ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(), ActivityStatusEnum.BUSINESS_APPROVAL.getCode(), ActivityStatusEnum.DRAFT.getCode()));
        if (activityList.isEmpty()) {
            return PageRowsUtil.buildLowCodePageRow(Collections.emptyList());
        }
        // 【2】打包活动需要的信息
        ActivityDataSourceQuery sourceQuery = new ActivityDataSourceQuery();
        sourceQuery.setNeedRdc(true);
        sourceQuery.setActivityInfoList(activityList);
        StandardActivityDetailDataSource ds = activityInfoListQueryService.getStandardActivityDetailDataSource(sourceQuery);

        // 【3】活动信息转换
        List<CustomerRelationActivityRdcVO> list = customerRelationActivityConvert.toCustomerRelationActivityRdcVO(request, ds);
        int showSize = request.getParam().getShowSize();
        return PageRowsUtil.buildLowCodePageRow(NumberConstant.ONE, showSize, list.size(), list.stream().limit(showSize).collect(Collectors.toList()));
    }

    /**
     * 获取客户关联的活动列表
     *
     * @param request
     * @param statusList
     * @return
     */
    List<ActivityInfoDO> activityListWithCust(BizRequest<CustomerRelationParam> request, List<String> statusList) {
        CustomerRelationParam param = request.getParam();
        if (!param.valid()) {
            return Collections.emptyList();
        }

        // 【1】查询客户相关的活动
        BaseActivityCustomerQuery query = new BaseActivityCustomerQuery();
        query.setCustomerCodeList(param.getCustomerCodeList());
        List<ActivityCustomerInfoDO> customerList = activityCustomerInfoRepository.baseActivityCustomerInfoQuery(query);
        if (CollectionUtils.isEmpty(customerList)) {
            return Collections.emptyList();
        }

        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(request.getEmpNo());

        ActivityInfoQuery acQuery = new ActivityInfoQuery();
        acQuery.setActivityRowIdList(customerList.stream().map(ActivityCustomerInfoDO::getActivityRowId).collect(Collectors.toList()));
        acQuery.setActivityStatus(statusList);
        acQuery.setAuthModel(authModel);
        return activityInfoRepository.searchByParam(acQuery);
    }

    /**
     * 客户参加的活动数量（最近访问客户）
     *
     * @param request
     * @return 《客户，活动数》
     */
    Map<String, Integer> countActivityForCustomer(BizRequest<CustomerRelationParam> request) {
        List<String> customerCodeList = request.getParam().getCustomerCodeList();
        // 【1】查询客户相关的活动
        BaseActivityCustomerQuery query = new BaseActivityCustomerQuery();
        query.setCustomerCodeList(customerCodeList);
        List<ActivityCustomerInfoDO> customerList = activityCustomerInfoRepository.baseActivityCustomerInfoQuery(query);
        Map<String, Integer> count = new HashMap<>();

        if (!customerList.isEmpty()) {
            Map<String, Set<String>> customer2Activity = customerList.stream().collect(Collectors.groupingBy(ActivityCustomerInfoDO::getCustomerCode,
                    Collectors.mapping(ActivityCustomerInfoDO::getActivityRowId, Collectors.toSet())));

            CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(request.getEmpNo());
            ActivityInfoQuery acQuery = new ActivityInfoQuery();
            acQuery.setActivityRowIdList(customerList.stream().map(ActivityCustomerInfoDO::getActivityRowId).collect(Collectors.toList()));
            acQuery.setActivityStatus(Arrays.asList(ActivityStatusEnum.PROGRESS.getCode(),
                    ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(), ActivityStatusEnum.BUSINESS_APPROVAL.getCode(), ActivityStatusEnum.DRAFT.getCode()));
            acQuery.setAuthModel(authModel);

            List<String> acList = activityInfoRepository.searchByParam(acQuery).stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());
            // 统计查询出来数据的部分
            customer2Activity.forEach((customerCode, acRowIdList) -> {
                int num = NumberConstant.ZERO;
                for (String rowId : acList) {
                    if (acRowIdList.contains(rowId)) {
                        num++;
                    }
                }

                count.put(customerCode, num);
            });
        }

        // 对从未参加过活动的客户进行赋值为0
        for (String customerCode : customerCodeList) {
            if (!count.containsKey(customerCode)) {
                count.put(customerCode, NumberConstant.ZERO);
            }
        }

        return count;
    }

}
