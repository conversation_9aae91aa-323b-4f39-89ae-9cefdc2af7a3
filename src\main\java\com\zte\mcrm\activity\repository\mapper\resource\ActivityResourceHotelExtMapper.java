package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityResourceHotelExtMapper extends ActivityResourceHotelMapper {
    /**
     * description 根据活动id批量查询活动相关酒店信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午3:57
     */
    List<ActivityResourceHotelDO> queryActivityResourceHotelsByActivityRowIds(@Param("activityRowIds") List<String> activityRowIds);

    /**
     * 批量删除
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<ActivityResourceHotelDO> records);
}