package com.zte.mcrm.activity.application.project;

import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityProjectSearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityProjectVO;

import java.util.List;

/**
 * 客户活动项目查询
 *
 * <AUTHOR>
 * @date 2023/5/28 下午2:44
 */
public interface ActivityProjectSearchAppService {

    /**
     * 查询项目
     *
     * @param pageQuery
     * @return {@link List< ActivityProjectVO>}
     * <AUTHOR>
     * @date 2023/5/28 下午2:58
     */
    List<ActivityProjectVO> listProject(PageQuery<ActivityProjectSearchParam> pageQuery);
}
