package com.zte.mcrm.activity.common.annotation;


import com.zte.mcrm.activity.common.annotation.impl.MemberOfValidator;
import com.zte.mcrm.activity.common.constant.ErrorMsgConstant;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验字段的值需要在定义的值里面
 *
 * @author: 汤踊10285568
 * @date: 2021/9/23 9:53
 */
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {MemberOfValidator.class})
public @interface MemberOf {

    /**
     * 默认错误信息
     *
     * @return 返回为空
     */
    String message() default ErrorMsgConstant.MEMBER_OF_MSG;

    /**
     * 字符串自定义值
     *
     * @return 结果
     */
    String[] strValues() default {};

    /**
     * 整数自定义值
     *
     * @return 结果
     */
    int[] intValues() default {};

    /**
     * 分组
     *
     * @return 结果
     */
    Class<?>[] groups() default {};

    /**
     * @return
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 指定多个时使用
     */
    @Target({FIELD, METHOD, PARAMETER, ANNOTATION_TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        MemberOf[] value();
    }
}
