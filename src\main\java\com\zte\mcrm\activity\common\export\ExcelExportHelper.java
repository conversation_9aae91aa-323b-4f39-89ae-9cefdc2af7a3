package com.zte.mcrm.activity.common.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.export.model.ExcelExportParamSetModel;
import com.zte.mcrm.activity.common.export.model.SimpleExcelExportModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * excel导出辅助类
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
public class ExcelExportHelper {

    /**
     * 简单Excel导出
     *
     * @param model 简单Excel数据模型
     * @return xxx.xls文档对象
     */
    public static Workbook simpleExportExcel(SimpleExcelExportModel model) {

        return ExcelExportUtil.exportExcel(model.toExportExcelParam(), ExcelType.HSSF);
    }

    /**
     * @param model    数据导出模型
     * @param fileName 仅仅只要文件名即可。如：活动数据
     * @return 生成fileName.xls（如：活动数据.xls）的文件的结果对象。
     * @throws IOException 异常
     */
    public static ByteArrayBody simpleExportExcel(SimpleExcelExportModel model, String fileName) throws IOException {
        try (Workbook wb = simpleExportExcel(model);
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            if (CollectionUtils.isNotEmpty(model.getModifiyLineList())) {
                ExcelSelectListUtil.setExcelHeaderColor(wb, model.getModifiyLineList());
                ExcelSelectListUtil.setExcelColumnStyle(wb, Lists.newArrayList(NumberConstant.FOUR, NumberConstant.FIVE, NumberConstant.SIX));
            }
            if (CollectionUtils.isNotEmpty(model.getExportParamSetModelList())) {
                for (ExcelExportParamSetModel excelExportParamSetModel : model.getExportParamSetModelList()) {
                    ExcelSelectListUtil.setSelectList(wb, excelExportParamSetModel);
                }
            }
            wb.write(out);
            return new ByteArrayBody(out.toByteArray(), fileName + ".xls");
        } catch (Exception e) {
            throw e;
        }
    }
}
