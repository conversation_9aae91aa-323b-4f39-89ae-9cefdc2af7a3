package com.zte.mcrm.activity.common.http.param;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.model.BizRequest;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.springframework.http.MediaType;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Post请求方式
 * <pre>
 *     在此提供快捷创建各类请求参数的静态方法。
 *     如果有其他类型的请求，应该在该方法中提供快捷创建对象的静态方法
 * </pre>
 *
 * <AUTHOR>
 */
public class PostHttpRequest extends BaseHttpRequest {
    private HttpEntity entity;

    PostHttpRequest(String url) {
        super(url, "POST");
    }

    @Override
    public HttpEntity fetchHttpEntity() {
        return entity;
    }

    /**
     * 创建POST请求，其中body是附件文件（字节流）。 —— 请求头由于文件类型不同，需自行决定
     *
     * @param url    请求地址
     * @param binary 文件字节流
     * @return POST请求对象
     */
    public static PostHttpRequest createWithBodyBinary(String url, byte[] binary) {
        PostHttpRequest post = new PostHttpRequest(url);
        post.entity = new ByteArrayEntity(binary);

        return post;
    }

    /**
     * 创建使用json的restful风格POST请求（其中body是json字符）
     *
     * @param url    请求地址
     * @param bizReq 请求参数体。（方法中会将bizReq中的param转为json字符串）
     * @return POST请求对象
     */
    public static PostHttpRequest createWithJsonRestful(String url, BizRequest<Object> bizReq) {
        PostHttpRequest post = createWithJsonRestful(url, bizReq.getParam());

        post.addHeader(bizReq.fetchHeaderMap());

        return post;
    }

    /**
     * 创建使用json的restful风格POST请求（其中body是json字符）
     *
     * @param url   请求地址
     * @param param 请求参数体。（方法中的param在在代码中会转为json字符串）
     * @return POST请求对象
     */
    public static PostHttpRequest createWithJsonRestful(String url, Object param) {
        PostHttpRequest post = new PostHttpRequest(url);

        String json = param instanceof String ? (String) param : JSON.toJSONString(param);
        post.entity = new StringEntity(json, StandardCharsets.UTF_8);

        post.addHeader(RequestHeaderConstant.X_ORIGIN_SERVICENAME, RequestHeaderConstant.SERVICE_NAME)
                .addHeader(RequestHeaderConstant.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
        ;

        return post;
    }

}
