/* Started by AICoder, pid:8e334550088e0c6144b5096d115a2e985410383b */
package com.zte.mcrm.activity.application.cto.impl;

import com.google.common.collect.Sets;
import com.zte.mcrm.activity.application.cto.CtoPlanActivityDataService;
import com.zte.mcrm.activity.application.cto.CtoPlanReportService;
import com.zte.mcrm.activity.application.cto.convert.CtoReportItemIndicatorVOConvert;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanSendEmailHelper;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanSheetDataHelper;
import com.zte.mcrm.activity.application.cto.helper.CtoPlanUploadFileHelper;
import com.zte.mcrm.activity.application.model.CtoReportIndicatorVO;
import com.zte.mcrm.activity.application.model.CtoReportItemIndicatorVO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityInfoResDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.CtoPlanProductTypeEnum;
import com.zte.mcrm.activity.common.enums.SaleDivisionEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanProductFinishDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanExeReportRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanOrgFinishRepository;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanProductFinishRepository;
import com.zte.mcrm.activity.service.common.dict.EmailAddressComponent;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import com.zte.mcrm.activity.web.controller.file.vo.FileInfoVO;
import com.zte.mcrm.common.util.DateUtil;
import com.zte.mcrm.customvisit.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CtoPlanReportServiceImpl implements CtoPlanReportService {

    @Autowired
    private CtoPlanProductFinishRepository ctoPlanProductFinishRepository;

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private CtoPlanExeReportRepository exeReportRepository;
    @Autowired
    private CtoPlanActivityDataService ctoPlanActivityDataService;

    @Autowired
    private EmailAddressComponent emailAddressComponent;
    @Autowired
    private CtoPlanOrgFinishRepository orgFinishRepository;
    @Autowired
    private CtoPlanProductFinishRepository productFinishRepository;
    @Autowired
    private CtoPlanUploadFileHelper ctoPlanUploadFileHelper;
    @Autowired
    private CtoPlanSendEmailHelper ctoPlanSendEmailHelper;

    /**
     * 生成盘活报表
     *
     * @param ctoPlanInfoId 计划信息ID
     * @param analysisDate  分析日期，若为 null，则默认为当前日期
     * @return 返回盘活报表数据列表
     */
    @Override
    public List<CtoReportItemIndicatorVO> generateActivationReport(String ctoPlanInfoId, Date analysisDate) {
        // 初始化分析日期，若为空则使用当前日期
        Date effectiveAnalysisDate = initializeAnalysisDate(analysisDate);

        // 计算当前月份的起始和结束日期
        DateUtils.DateRange dateRange = DateUtils.DateRange.of(effectiveAnalysisDate);

        // 获取产品完成列表
        List<CtoPlanProductFinishDO> productFinishList = fetchProductFinishList(ctoPlanInfoId);

        // 提取所有参与人的员工编号
        Set<String> employeeNos = extractEmployeeNos(productFinishList);

        // 获取员工详细信息
        Map<String, PersonInfoDTO> employeeInfoMap = fetchEmployeeDetails(employeeNos);
        // 名单盘活需要对人去重
        // 名单盘活需要对人去重，并保留原始列表顺序
        List<CtoPlanProductFinishDO> uniqueProductFinishList = new ArrayList<>(productFinishList.stream()
                .collect(Collectors.toMap(
                        item -> item.getProductCode()+ "_"+item.getEmployeeNo() ,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ))
                .values());

        // 遍历产品完成列表，处理每一个产品完成数据并生成报表
        return uniqueProductFinishList.stream()
                .map(productFinish -> processProductFinish(productFinish, employeeInfoMap, dateRange))
                .collect(Collectors.toList());
    }


    /* Started by AICoder, pid:451bep0e5f5e9e6140c30be9607b28254fb6841f */
    @Override
    public List<CtoReportIndicatorVO> generatePlanDetailReport(String planId, boolean isLeader) {
        // 获取事业部和产品的完成数据并初始化
        List<CtoPlanOrgFinishDO> ctoPlanOrgFinishDOS = orgFinishRepository.listOrgFinishByPlanInfoId(planId);
        List<CtoPlanProductFinishDO> ctoPlanProductFinishDOS = productFinishRepository.listProductFinish(planId);
        new CtoPlanSheetDataHelper().initSheetData(ctoPlanOrgFinishDOS, ctoPlanProductFinishDOS);

        // 结果列表
        List<CtoReportIndicatorVO> result = new ArrayList<>();

        // 统计维度：事业部，握手、国家/客户指标
        for (SaleDivisionEnum division : SaleDivisionEnum.values()) {
            CtoReportIndicatorVO ctoReportIndicatorVO = new CtoReportIndicatorVO();
            ctoReportIndicatorVO.buildIndicators(division, null, ctoPlanOrgFinishDOS, ctoPlanProductFinishDOS, isLeader);
            result.add(ctoReportIndicatorVO);
        }

        // 统计维度：产品，握手、国家/客户指标
        for (CtoPlanProductTypeEnum productType : CtoPlanProductTypeEnum.values()) {
            CtoReportIndicatorVO ctoReportIndicatorVO = new CtoReportIndicatorVO();
            ctoReportIndicatorVO.buildIndicators(null, productType, ctoPlanOrgFinishDOS, ctoPlanProductFinishDOS, isLeader);
            result.add(ctoReportIndicatorVO);
        }

        return result;
    }
    /* Ended by AICoder, pid:451bep0e5f5e9e6140c30be9607b28254fb6841f */


    @Override
    public void generateAndSendEmailCTOReport(BizRequest<CtoPlanExportParam> req) {
        //根据计划ID查询所有需要执行生成的报表，即待执行的exe_status=N且时间小于等于当前时间的计划,且不存在fileKey的记录
        List<CtoPlanExeReportDO> ctoPlanExeReportDOS = exeReportRepository.getByCtoPlanInfoId(req.getParam().getPlanId());
        List<CtoPlanExeReportDO> filteredPlanExeReportDOS = ctoPlanExeReportDOS.stream()
                .filter(t -> BooleanEnum.Y.isMe(t.getEnabledFlag()))
                .filter(t -> BooleanEnum.N.isMe(t.getExeStatus()))
                .filter(t -> t.getExeWaitTime().getTime() <= System.currentTimeMillis())
                .filter(t -> StringUtils.isBlank(t.getFileKey()))
                .collect(Collectors.toList());
        for (CtoPlanExeReportDO planExeReportDO : filteredPlanExeReportDOS) {
            try {
                //执行报表
                doExecuteReport(req, planExeReportDO);
            } catch (Exception e) {
                log.warn("[generateAndSendEmailCTOReport] execute report exception!", e);
            }
        }
    }

    /**
     * @param req             入参
     * @param planExeReportDO 执行报表对象
     */
    void doExecuteReport(BizRequest<CtoPlanExportParam> req, CtoPlanExeReportDO planExeReportDO) {
        // 1.生成报表数据
        req.getParam().setAnalysisDate(planExeReportDO.getScopeEnd());
        ByteArrayOutputStream arrayOutputStream = ctoPlanActivityDataService.generatePlanDetailFileData(req);
        if (arrayOutputStream == null) {
            log.warn("[generateAndSendEmailCTOReport] export plan detail sheet data failed");
            return;
        }

        // 2.上传文档云
        FileInfoVO fileInfoVO = ctoPlanUploadFileHelper.doUpload(req,planExeReportDO.getScopeEnd(), arrayOutputStream);
        if (fileInfoVO == null) {
            log.warn("[generateAndSendEmailCTOReport] upload failed: fileInfoVO is null for plan ID: {}", req.getParam().getPlanId());
            return;
        }

        // 3.更新fileKey、执行状态、执行时间
        req.getParam().setPlanExeReportRowId(planExeReportDO.getRowId());
        req.getParam().setFileToken(fileInfoVO.getFileToken());
        ctoPlanActivityDataService.updateCtoPlanExeReport(req);

        // 4.获取下载参数
        String downloadUrl = ctoPlanUploadFileHelper.getDownloadUrl(req.getEmpNo(), fileInfoVO);

        // 5.发送邮件
        ctoPlanSendEmailHelper.doSend(req, planExeReportDO.getReceiver(), planExeReportDO.getScopeEnd(), downloadUrl);
    }


    /**
     * 初始化分析日期，若传入日期为 null，则返回当前日期
     *
     * @param analysisDate 传入的分析日期
     * @return 初始化后的分析日期
     */
    private Date initializeAnalysisDate(Date analysisDate) {
        // 若传入日期为 null，则使用当前日期往前推一个月
        if (analysisDate == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1);  // 往前推一个月
            return calendar.getTime();
        }
        return analysisDate;
    }

    /**
     * 获取产品完成列表
     *
     * @param ctoPlanInfoId 计划信息ID
     * @return 产品完成列表
     */
    private List<CtoPlanProductFinishDO> fetchProductFinishList(String ctoPlanInfoId) {
        return ctoPlanProductFinishRepository.listProductFinishByRole(ctoPlanInfoId, null);
    }

    /**
     * 从产品完成列表中提取所有参与人的员工编号
     *
     * @param productFinishList 产品完成列表
     * @return 员工编号集合
     */
    private Set<String> extractEmployeeNos(List<CtoPlanProductFinishDO> productFinishList) {
        return productFinishList.stream()
                .map(CtoPlanProductFinishDO::getEmployeeNo)
                .collect(Collectors.toSet());
    }

    /**
     * 获取员工详细信息
     *
     * @param employeeNos 员工编号集合
     * @return 员工编号与详细信息的映射
     */
    private Map<String, PersonInfoDTO> fetchEmployeeDetails(Set<String> employeeNos) {
        MsaRpcRequest<Set<String>> request = MsaRpcRequestUtil.createWithCurrentUser(employeeNos);
        return hrmUserCenterSearchService.fetchPersonInfoAndPosition(request).getBo();
    }

    /**
     * 处理每个产品完成数据，生成相应的报表信息
     *
     * @param productFinish   产品完成信息
     * @param employeeInfoMap 员工详细信息的映射
     * @param dateRange       日期范围
     * @return 返回生成的报表对象
     */
    private CtoReportItemIndicatorVO processProductFinish(CtoPlanProductFinishDO productFinish,
                                                          Map<String, PersonInfoDTO> employeeInfoMap,
                                                          DateUtils.DateRange dateRange) {
        PersonInfoDTO person = employeeInfoMap.get(productFinish.getEmployeeNo());
        if (person == null) {
            throw new RuntimeException("processProductFinish failed! get personInfo failed, employeeNo:" + productFinish.getEmployeeNo());
        }

        List<SelectCtoActivityInfoResDTO> activities = fetchActivities(productFinish);
        // 对同场活动相同account去重
        Map<String, List<SelectCtoActivityInfoResDTO>> allActivityAccountMap = activities.stream().collect(Collectors.groupingBy(SelectCtoActivityInfoResDTO::getActivityAccountKey));
        List<SelectCtoActivityInfoResDTO> currentMonthActivities = filterActivitiesByDate(activities, dateRange);
        // 对同场活动相同account去重
        Map<String, List<SelectCtoActivityInfoResDTO>> currentMonthActivitiesMap = currentMonthActivities.stream().collect(Collectors.groupingBy(SelectCtoActivityInfoResDTO::getActivityAccountKey));
        CtoReportItemIndicatorVO reportRecord = initializeReportRecord(productFinish, person);
        setYearlyActivityCount(productFinish, allActivityAccountMap.size(), reportRecord);
        CtoReportItemIndicatorVOConvert.fillMonthCount(reportRecord, DateUtils.getMonthFromDate(dateRange.getAnalysisDate()), currentMonthActivitiesMap.size());

        return reportRecord;
    }

    /**
     * 初始化报表记录
     *
     * @param productFinish 产品完成信息
     * @param person        员工详细信息
     * @return 初始化后的报表记录
     */
    private CtoReportItemIndicatorVO initializeReportRecord(CtoPlanProductFinishDO productFinish, PersonInfoDTO person) {
        return CtoReportItemIndicatorVOConvert.buildListActivationInit(
                CtoPlanProductTypeEnum.nameByCode(productFinish.getProductCode(), null),
                person.personNameAndEmpNo(),
                person.getPostName()
        );
    }

    /**
     * 设置年累计活动数量
     *
     * @param productFinish 产品完成信息
     * @param count         次数
     * @param reportRecord  报表记录
     */
    private void setYearlyActivityCount(CtoPlanProductFinishDO productFinish,
                                        Integer count,
                                        CtoReportItemIndicatorVO reportRecord) {
        reportRecord.setYearSum(count);
    }

    /**
     * 获取员工的所有活动信息
     *
     * @param productFinish 产品完成信息
     * @return 活动信息列表
     */
    private List<SelectCtoActivityInfoResDTO> fetchActivities(CtoPlanProductFinishDO productFinish) {
        SelectCtoActivityParamDTO param = SelectCtoActivityParamDTO.buildByCount(
                productFinish.getScopeStart(),
                productFinish.getScopeEnd(),
                null, Sets.newHashSet(productFinish.getEmployeeNo())
        );
        return ctoPlanProductFinishRepository.selectCtoActivityInfo(param);
    }

    /**
     * 筛选出当前月份内的活动信息
     *
     * @param activities 活动信息列表
     * @param dateRange  日期范围
     * @return 当前月份的活动信息
     */
    private List<SelectCtoActivityInfoResDTO> filterActivitiesByDate(List<SelectCtoActivityInfoResDTO> activities, DateUtils.DateRange dateRange) {
        return activities.stream()
                .filter(activity -> DateUtils.isDateWithinRange(DateUtil.localDateTimeToDate(activity.getActivityEndTime()),
                        dateRange.getFirstDay(), dateRange.getLastDay()))
                .collect(Collectors.toList());
    }
}

/* Ended by AICoder, pid:8e334550088e0c6144b5096d115a2e985410383b */
