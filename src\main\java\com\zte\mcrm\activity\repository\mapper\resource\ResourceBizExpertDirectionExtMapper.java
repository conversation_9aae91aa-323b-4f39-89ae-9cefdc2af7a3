package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDirectionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ResourceBizExpertDirectionExtMapper extends ResourceBizExpertDirectionMapper {

    List<ResourceBizExpertDirectionDO> queryByEmpNosAndDirections(@Param("empNos") List<String> empNos, @Param("directions") List<String> directions);

    int softDeleteByEmpNos(@Param("operator") String operator, @Param("empNos") List<String> empNos);

    int softDeleteByRowIds(@Param("operator") String operator,@Param("rowIds") List<String> rowIds);

    List<ResourceBizExpertDirectionDO> getByEmpNos(@Param("empNos") List<String> empNos);
}