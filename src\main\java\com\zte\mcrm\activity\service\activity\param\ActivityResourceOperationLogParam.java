package com.zte.mcrm.activity.service.activity.param;

import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationBizTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date ：2024/11/20 16:25
 * @description ：活动资源操作日志批量新增对象
 */
@Getter
@Setter
@ToString
public class ActivityResourceOperationLogParam {
    /**
     * 活动id
     */
    private String activityId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务id
     */
    private String bizRelatedId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作前
     */
    private String operationBefore;

    /**
     * 操作前描述
     */
    private String operationBeforeDesc;

    /**
     * 操作后
     */
    private String operationAfter;

    /**
     * 操作后描述
     */
    private String operationAfterDesc;
}
