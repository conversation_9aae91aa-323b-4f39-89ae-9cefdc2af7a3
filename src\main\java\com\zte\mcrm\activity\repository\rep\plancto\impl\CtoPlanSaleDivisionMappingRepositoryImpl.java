package com.zte.mcrm.activity.repository.rep.plancto.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.plancto.CtoPlanSaleDivisionMappingExtMapper;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanSaleDivisionMappingRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-12
 */
@Component
public class CtoPlanSaleDivisionMappingRepositoryImpl implements CtoPlanSaleDivisionMappingRepository {

    @Autowired
    private CtoPlanSaleDivisionMappingExtMapper ctoPlanSaleDivisionMappingExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<CtoPlanSaleDivisionMappingDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        return ctoPlanSaleDivisionMappingExtMapper.batchInsert(list);
    }

    @Override
    public int updateByPrimaryKeySelective(CtoPlanSaleDivisionMappingDO record) {
        if (Objects.isNull(record) || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        return ctoPlanSaleDivisionMappingExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<CtoPlanSaleDivisionMappingDO> queryCtoPlanSaleDivisionMappingList(CtoPlanSaleDivisionMappingDO param) {
        return ctoPlanSaleDivisionMappingExtMapper.queryCtoPlanSaleDivisionMappingList(param);
    }

    @Override
    public int batchUpdate(List<CtoPlanSaleDivisionMappingDO> mappingDOList) {
        if (CollectionUtils.isEmpty(mappingDOList)) {
            return NumberConstant.ZERO;
        }
        return ctoPlanSaleDivisionMappingExtMapper.batchUpdate(mappingDOList);
    }

    @Override
    public List<CtoPlanSaleDivisionMappingDO> listAll() {
        return ctoPlanSaleDivisionMappingExtMapper.listAll();
    }
}
