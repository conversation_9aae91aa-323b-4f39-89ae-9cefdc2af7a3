package com.zte.mcrm.activity.application.export.convert;

import com.zte.mcrm.activity.application.export.vo.ExportLeaderViewVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionDataBoardViewVO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-25 下午3:18
 **/
@Component
@Mapper(componentModel = "spring")
public interface DataBoardViewConvert {

    ExportLeaderViewVO dataBoardView2ExportLeaderView(ExhibitionDataBoardViewVO dataBoardViewVo);

    List<ExportLeaderViewVO> dataBoardViewsToExportLeaderViews(List<ExhibitionDataBoardViewVO> dataBoardViewVos);

}
