package com.zte.mcrm.activity.repository.mapper.care;

import com.zte.mcrm.activity.repository.model.care.MyCarePeopleDO;

public interface MyCarePeopleMapper {
    /**
     * all field insert
     */
    int insert(MyCarePeopleDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(MyCarePeopleDO record);

    /**
     * query by primary key
     */
    MyCarePeopleDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(MyCarePeopleDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(MyCarePeopleDO record);
}