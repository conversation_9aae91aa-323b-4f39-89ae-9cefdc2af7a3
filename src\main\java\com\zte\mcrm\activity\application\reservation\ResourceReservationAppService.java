package com.zte.mcrm.activity.application.reservation;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.web.controller.reservation.param.ReplyReservationParam;
import com.zte.mcrm.activity.web.controller.reservation.param.ReserveScheduleResourceParam;
import com.zte.mcrm.activity.web.controller.reservation.vo.ReserveDealCommonVO;

import java.util.List;

/**
 * 预约资源应用服务 (对于所有资源来说，预约时资源个性化最大，这里单独抽出来。比如：领导、专家的日程资源)
 *
 * <AUTHOR>
 */
public interface ResourceReservationAppService {

    /**
     * 预约日程资源
     *
     * @param param
     * @return
     */
    ServiceData<ReserveDealCommonVO> reserveScheduleResource(ReserveScheduleResourceParam param);

    /**
     * 资源
     *
     * @param param
     * @return
     */
    ServiceData<ReserveDealCommonVO> replyReservation(ReplyReservationParam param);

}