package com.zte.mcrm.activity.repository.rep.summary.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryFeedbackExtMapper;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryFeedbackDO;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryFeedbackRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-12-08 14:16
 **/
@Component
public class ActivitySummaryFeedbackRepositoryImpl implements ActivitySummaryFeedbackRepository {
    @Resource
    private ActivitySummaryFeedbackExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;
    @Override
    public int insertSelective(List<ActivitySummaryFeedbackDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivitySummaryFeedbackDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            extMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivitySummaryFeedbackDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ActivitySummaryFeedbackDO>> queryAllFeedbackForActivity(List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap()
                : extMapper.queryAllFeedbackByActivityRowId(activityRowIds)
                .stream().collect(Collectors.groupingBy(ActivitySummaryFeedbackDO::getActivityRowId));
    }
}
