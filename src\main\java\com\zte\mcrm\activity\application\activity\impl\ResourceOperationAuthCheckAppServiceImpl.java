package com.zte.mcrm.activity.application.activity.impl;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.activity.ResourceOperationAuthCheckAppService;
import com.zte.mcrm.activity.common.constant.I18Constants;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.integration.zteKmCloudUdmCloudDisk.dto.FilePreviewParam;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.relation.impl.ActivityRelationAttachmentRepositoryImpl;
import com.zte.mcrm.activity.service.activity.ActivityResourceOperationLogService;
import com.zte.mcrm.activity.service.activity.param.ActivityResourceOperationLogParam;
import com.zte.mcrm.activity.service.attachment.ActivityAttachmentService;
import com.zte.mcrm.activity.service.attachment.impl.ActivityAttachmentServiceImpl;
import com.zte.mcrm.activity.service.attachment.param.ActivityAttachmentParam;
import com.zte.mcrm.activity.service.authority.ResourceOperationAuthService;
import com.zte.mcrm.activity.service.authority.model.OperationAuthModel;
import com.zte.mcrm.activity.service.authority.param.OperationAuthParam;
import com.zte.mcrm.activity.service.file.DownloadService;
import com.zte.mcrm.activity.service.file.PreviewService;
import com.zte.mcrm.activity.service.file.impl.DownloadServiceImpl;
import com.zte.mcrm.activity.service.file.param.FileDownLoadParam;
import com.zte.mcrm.activity.web.controller.authority.params.OperationAuthCheckParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceAddParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceDeleteParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourceDownloadParam;
import com.zte.mcrm.activity.web.controller.resource.param.AttachmentResourcePreviewParam;
import com.zte.mcrm.common.cloududm.service.FileInfoServiceImpl;
import com.zte.mcrm.common.util.FileValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 操作权限校验接口
 *
 * <AUTHOR>
 * @date 2024/11/19 下午4:07
 */
@Service
public class ResourceOperationAuthCheckAppServiceImpl implements ResourceOperationAuthCheckAppService {

    @Autowired
    private ResourceOperationAuthService resourceOperationAuthService;

    @Autowired
    private ActivityAttachmentService activityAttachmentService;

    @Autowired
    private ActivityResourceOperationLogService activityResourceOperationLogService;

    @Autowired
    private ActivityRelationAttachmentRepositoryImpl activityRelationAttachmentRepository;

    @Autowired
    private PreviewService previewService;

    @Autowired
    private DownloadService downloadService;


    /**
     * 操作权限校验，并记录日志
     * 仅用于附件查看等通用操作的前置校验，减少已有接口的修改，如果是比较复杂，涉及数据修改的操作，必须要在对应接口中处理，保证操作原子性
     *
     * @param request
     * @return {@link BizResult < Boolean>}
     * <AUTHOR>
     * @date 2024/11/19 下午3:54
     */
    @Override
    public BizResult<Boolean> checkAuth(BizRequest<OperationAuthCheckParam> request) {
        OperationAuthCheckParam checkParam = request.getParam();
        ActivityResourceOperationTypeEnum operationType = ActivityResourceOperationTypeEnum.getEnumByType(checkParam.getOperationType());
        if (StringUtils.isBlank(checkParam.getBizRelatedId()) || Objects.isNull(operationType)) {
            return BizResult.buildSuccessRes(Boolean.FALSE);
        }

        BizRequest<OperationAuthParam> authQueryRequest = BizRequestUtil.copyRequest(request, req -> {
            OperationAuthParam param = new OperationAuthParam();
            param.setActivityId(req.getActivityId());
            param.setBizType(req.getBizType());
            param.setBizRelatedIdList(Lists.newArrayList(req.getBizRelatedId()));
            return param;
        });
        OperationAuthModel operationAuthModel = resourceOperationAuthService.getOperationAuthValueList(authQueryRequest).getData();
        // 待增加操作日志记录

        return BizResult.buildSuccessRes(operationAuthModel.checkAuth(checkParam.getBizRelatedId(), operationType));
    }

    /* Started by AICoder, pid:pcde9p7245l418614b21096d4077684e0ff0a3e0 */
    /**
     * 附件新增
     * {@link ActivityAttachmentServiceImpl#insertSelective(BizRequest)}
     *
     * @param request 请求参数
     * @return 操作结果 {@link BizResult<String>}
     * <AUTHOR>
     * @date 2024/11/21 下午8:55
     */
    @Override
    public BizResult<String> addAttachment(BizRequest<AttachmentResourceAddParam> request) {
        AttachmentResourceAddParam param = request.getParam();
        param.setOperationType(ActivityResourceOperationTypeEnum.ADD.getCode());

        // 校验权限
        checkAuth(param);

        // 构建并处理业务请求
        BizRequest<ActivityAttachmentParam> attachmentReq = BizRequestUtil.copyRequest(request, req -> {
            ActivityAttachmentParam attachmentParam = new ActivityAttachmentParam();
            BeanUtils.copyProperties(req, attachmentParam);
            attachmentParam.setActivityRowId(param.getActivityId());
            attachmentParam.setSceneOriginRowId(param.getBizRelatedId());
            return attachmentParam;
        });

        String result = activityAttachmentService.insertSelective(attachmentReq);

        // 增加日志
        ActivityResourceOperationLogParam logParam = new ActivityResourceOperationLogParam();
        logParam.setActivityId(param.getActivityId());
        logParam.setBizType(param.getBizType());
        logParam.setBizRelatedId(param.getBizRelatedId());
        logParam.setOperationType(param.getOperationType());
        logParam.setOperationBefore(result);
        logParam.setOperationBeforeDesc(param.getFileName());
        logParam.setOperationAfter(result);
        logParam.setOperationAfterDesc(param.getFileName());
        activityResourceOperationLogService.saveActivityResourceOperationLog(Lists.newArrayList(logParam));

        return BizResult.buildSuccessRes(result);
    }

    /* Ended by AICoder, pid:pcde9p7245l418614b21096d4077684e0ff0a3e0 */


    /**
     * 校验操作权限
     *
     * @param param
     * <AUTHOR>
     * @date 2024/11/22 上午11:07
     */
    private void checkAuth(OperationAuthCheckParam param) {
        boolean isParamBlank = StringUtils.isAnyBlank(param.getActivityId(), param.getBizType(), param.getBizRelatedId(), param.getOperationType());
        ActivityResourceOperationTypeEnum typeEnum = ActivityResourceOperationTypeEnum.getEnumByType(param.getOperationType());
        if (isParamBlank) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18Constants.ACTIVITY_RESOURCE_OPERATION_TYPE_NOT_SUPPORT);
        }
        OperationAuthParam authParam = new OperationAuthParam();
        authParam.setActivityId(param.getActivityId());
        authParam.setBizType(param.getBizType());
        authParam.setBizRelatedIdList(Lists.newArrayList(param.getBizRelatedId()));
        OperationAuthModel operationAuthModel = resourceOperationAuthService.getOperationAuthValueList(BizRequestUtil.createWithCurrentUser(authParam)).getData();
        boolean checkResult = operationAuthModel.checkAuth(param.getBizRelatedId(), typeEnum);
        if (!checkResult) {
            throw new BizRuntimeException(RetCode.PERMISSIONDENIED_CODE, RetCode.PERMISSIONDENIED_MSGID);
        }
    }

    /**
     * 附件删除
     * {@link ActivityAttachmentServiceImpl#deleteByRowId(String)}
     *
     * @param request
     * @return {@link BizResult< Integer>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:31
     */
    /* Started by AICoder, pid:m3dd37b23fh5510148f50b7b501da229a379790f */
    @Override
    public BizResult<Integer> deleteAttachment(BizRequest<AttachmentResourceDeleteParam> request) {
        AttachmentResourceDeleteParam param = request.getParam();
        param.setOperationType(ActivityResourceOperationTypeEnum.DELETE.getCode());

        // 校验权限
        checkAuth(param);
        // 处理业务
        List<ActivityRelationAttachmentDO> attachmentDOList
                = activityRelationAttachmentRepository.queryActivityAttachmentList(Lists.newArrayList(param.getBizRelatedId()));
        Optional<ActivityRelationAttachmentDO> exist = attachmentDOList.stream().filter(item -> StringUtils.equals(item.getRowId(), param.getAttachmentId())).findAny();
        if (!exist.isPresent()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_NOT_EXIST);
        }
        int result = activityAttachmentService.deleteByRowId(param.getAttachmentId());

        // 增加日志
        ActivityRelationAttachmentDO attachmentDO = exist.get();
        ActivityResourceOperationLogParam logParam = new ActivityResourceOperationLogParam();
        logParam.setActivityId(param.getActivityId());
        logParam.setBizType(param.getBizType());
        logParam.setBizRelatedId(param.getBizRelatedId());
        logParam.setOperationType(param.getOperationType());
        logParam.setOperationBefore(attachmentDO.getRowId());
        logParam.setOperationBeforeDesc(attachmentDO.getFileName());
        logParam.setOperationAfter(attachmentDO.getRowId());
        logParam.setOperationAfterDesc(attachmentDO.getFileName());
        activityResourceOperationLogService.saveActivityResourceOperationLog(Lists.newArrayList(logParam));

        return BizResult.buildSuccessRes(result);
    }

    /* Ended by AICoder, pid:m3dd37b23fh5510148f50b7b501da229a379790f */

    /**
     * 附件预览
     * {@link FileInfoServiceImpl#getPreviewUrl(String, String, String)}
     *
     * @param request
     * @return {@link BizResult< String>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:38
     */
    /* Started by AICoder, pid:yaffbzca5bh986a1404b08b2d0399837d821a68b */
    @Override
    public BizResult<String> previewAttachment(BizRequest<AttachmentResourcePreviewParam> request) {
        AttachmentResourcePreviewParam param = request.getParam();
        param.setOperationType(ActivityResourceOperationTypeEnum.VIEW.getCode());
        // 校验权限
        checkAuth(param);
        // 处理业务
        List<ActivityRelationAttachmentDO> attachmentDOList
                = activityRelationAttachmentRepository.queryActivityAttachmentList(Lists.newArrayList(param.getBizRelatedId()));
        Optional<ActivityRelationAttachmentDO> exist = attachmentDOList.stream().filter(item -> StringUtils.equals(item.getRowId(), param.getAttachmentId())).findAny();
        if (!exist.isPresent()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_NOT_EXIST);
        }
        ActivityRelationAttachmentDO attachmentDO = exist.get();
        FileValidateUtils.validateFileName(attachmentDO.getFileName());

        FilePreviewParam previewParam = new FilePreviewParam();
        previewParam.setFileKey(attachmentDO.getFileToken());
        previewParam.setFileName(attachmentDO.getFileName());
        String result = previewService.getPreviewUrl(BizRequestUtil.copyRequest(request, req -> previewParam));
        // 增加日志
        ActivityResourceOperationLogParam logParam = new ActivityResourceOperationLogParam();
        logParam.setActivityId(param.getActivityId());
        logParam.setBizType(param.getBizType());
        logParam.setBizRelatedId(param.getBizRelatedId());
        logParam.setOperationType(param.getOperationType());
        logParam.setOperationBefore(attachmentDO.getRowId());
        logParam.setOperationBeforeDesc(attachmentDO.getFileName());
        logParam.setOperationAfter(attachmentDO.getRowId());
        logParam.setOperationAfterDesc(attachmentDO.getFileName());
        activityResourceOperationLogService.saveActivityResourceOperationLog(Lists.newArrayList(logParam));
        return BizResult.buildSuccessRes(result);
    }

    /* Ended by AICoder, pid:yaffbzca5bh986a1404b08b2d0399837d821a68b */

    /**
     * 附件下载
     * {@link DownloadServiceImpl#download(BizRequest, HttpServletResponse)}
     *
     * @param request
     * @return {@link BizResult<String>}
     * <AUTHOR>
     * @date 2024/11/22 上午10:39
     */
    /* Started by AICoder, pid:qc9b8j19e2m8a6214df30ab2b04f913555911eee */
    @Override
    public BizResult<Void> downloadAttachment(BizRequest<AttachmentResourceDownloadParam> request, HttpServletResponse response) {
        AttachmentResourceDownloadParam param = request.getParam();
        param.setOperationType(ActivityResourceOperationTypeEnum.DOWNLOAD.getCode());
        // 校验权限
        checkAuth(param);
        // 处理业务
        List<ActivityRelationAttachmentDO> attachmentDOList
                = activityRelationAttachmentRepository.queryActivityAttachmentList(Lists.newArrayList(param.getBizRelatedId()));
        Optional<ActivityRelationAttachmentDO> exist = attachmentDOList.stream().filter(item -> StringUtils.equals(item.getRowId(), param.getAttachmentId())).findAny();
        if (!exist.isPresent()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_NOT_EXIST);
        }
        ActivityRelationAttachmentDO attachmentDO = exist.get();
        FileDownLoadParam downLoadParam = new FileDownLoadParam();
        downLoadParam.setFileUrlToken(attachmentDO.getFileToken());
        downLoadParam.setFileName(attachmentDO.getFileName());
        downLoadParam.setEncryptionLevel(param.getEncryptionLevel());
        downloadService.download(BizRequestUtil.copyRequest(request, req -> downLoadParam), response);

        // 增加日志
        ActivityResourceOperationLogParam logParam = new ActivityResourceOperationLogParam();
        logParam.setActivityId(param.getActivityId());
        logParam.setBizType(param.getBizType());
        logParam.setBizRelatedId(param.getBizRelatedId());
        logParam.setOperationType(param.getOperationType());
        logParam.setOperationBefore(attachmentDO.getRowId());
        logParam.setOperationBeforeDesc(attachmentDO.getFileName());
        logParam.setOperationAfter(attachmentDO.getRowId());
        logParam.setOperationAfterDesc(attachmentDO.getFileName());
        activityResourceOperationLogService.saveActivityResourceOperationLog(Lists.newArrayList(logParam));
        return BizResult.buildSuccessRes(null);
    }

    /* Ended by AICoder, pid:qc9b8j19e2m8a6214df30ab2b04f913555911eee */
}
