package com.zte.mcrm.activity.repository.rep.exhibition.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.exhibition.ExhibitionRelationHotelExtMapper;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationHotelDO;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationHotelRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;

/**
 * <AUTHOR> 10307200
 * @since 2023-10-16 下午3:18
 **/
@Repository
public class ExhibitionRelationHotelRepositoryImpl implements ExhibitionRelationHotelRepository {

    @Autowired
    private ExhibitionRelationHotelExtMapper hotelExtMapper;

    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ExhibitionRelationHotelDO record) {
        setDefaultValue(record);
        return hotelExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ExhibitionRelationHotelDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }

        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return hotelExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ExhibitionRelationHotelDO> queryAllByHotelRowIds(List<String> hotelRowId) {
        return CollectionUtils.isEmpty(hotelRowId) ? Collections.emptyList()
                : hotelExtMapper.queryAllByHotelRowIds(hotelRowId);
    }

    @Override
    public Map<String, List<ExhibitionRelationHotelDO>> getRelationHotelListByExhibitionRowIds(Set<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return Collections.emptyMap();
        }

        return hotelExtMapper.queryAllByExhibitionIds(Lists.newArrayList(exhibitionRowIds))
                .stream().collect(Collectors.groupingBy(ExhibitionRelationHotelDO::getExhibitionRowId));
    }

    @Override
    public int batchInsert(List<ExhibitionRelationHotelDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        return hotelExtMapper.batchInsert(records.stream().map(record -> {
            this.setDefaultValue(record);
            return record;
        }).collect(Collectors.toList()));
    }

    @Override
    public int batchUpdate(List<ExhibitionRelationHotelDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return ZERO;
        }

        String empNo = HeadersProperties.getXEmpNo();
        Date updatedDate = new Date();
        List<ExhibitionRelationHotelDO> needUpdateRecords = records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getRowId()))
                .map(record -> {
                    record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(empNo));
                    record.setLastUpdateDate(updatedDate);
                    return record;
                }).collect(Collectors.toList());

        return CollectionUtils.isEmpty(needUpdateRecords) ? ZERO : hotelExtMapper.batchUpdate(needUpdateRecords);
    }

    @Override
    public int deleteByExhibitionRowIds(String operator, List<String> exhibitionRowIds) {
        if (CollectionUtils.isEmpty(exhibitionRowIds)) {
            return ZERO;
        }

        return hotelExtMapper.softDeleteByExhibitionRowIds(operator, exhibitionRowIds);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return ZERO;
        }

        List<ExhibitionRelationHotelDO> needDeletedRecords = rowIds.stream().map(rowId -> {
            ExhibitionRelationHotelDO record = new ExhibitionRelationHotelDO();
            record.setRowId(rowId);
            record.setLastUpdatedBy(operator);
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.N.getCode());
            return record;
        }).collect(Collectors.toList());
        return hotelExtMapper.batchUpdate(needDeletedRecords);
    }

    private void setDefaultValue(ExhibitionRelationHotelDO exhibitionRelationHotelDO) {
        exhibitionRelationHotelDO.setRowId(Optional.ofNullable(exhibitionRelationHotelDO.getRowId()).orElse(keyIdService.getKeyId()));
        exhibitionRelationHotelDO.setCreatedBy(Optional.ofNullable(exhibitionRelationHotelDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationHotelDO.setLastUpdatedBy(Optional.ofNullable(exhibitionRelationHotelDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        exhibitionRelationHotelDO.setCreationDate(new Date());
        exhibitionRelationHotelDO.setLastUpdateDate(new Date());
        exhibitionRelationHotelDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }
}
