package com.zte.mcrm.activity.repository.mapper.relation;

import com.zte.mcrm.activity.repository.model.relation.ActivityRelationTalkDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityRelationTalkMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationTalkDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationTalkDO record);

    /**
     * 批量插入
     * @param relationTalkDOList
     * @return
     */
    int batchInsert(@Param("list") List<ActivityRelationTalkDO> relationTalkDOList);

    /**
     * query by primary key
     */
    ActivityRelationTalkDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationTalkDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationTalkDO record);

    ActivityRelationTalkDO selectByActivityId(@Param("activityRowId")String activityRowId);
}