package com.zte.mcrm.activity.service.activity.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.distributedlock.lock.redis.annotation.RedisLock;
import com.zte.mcrm.activity.application.exhibition.convert.ExhibitionQuerySupportConvertor;
import com.zte.mcrm.activity.application.model.StandardExhibitionDataSource;
import com.zte.mcrm.activity.common.auth.CustomerIntegrationAuthModel;
import com.zte.mcrm.activity.common.config.ActivityUrlConfig;
import com.zte.mcrm.activity.common.config.CustomerIntegrationUppConfig;
import com.zte.mcrm.activity.common.constant.*;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.EvaluateTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionApproveLevelEnum;
import com.zte.mcrm.activity.common.enums.exhibition.ExhibitionAttachmentSceneTypeEnum;
import com.zte.mcrm.activity.common.enums.item.ResourceOrchestrationDealStatusEnum;
import com.zte.mcrm.activity.common.enums.item.ZtePeopleOrderEnum;
import com.zte.mcrm.activity.common.enums.resource.ExpenseSubjectEnum;
import com.zte.mcrm.activity.common.enums.resource.HotelPeopleSourceEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.model.DateTimePeriod;
import com.zte.mcrm.activity.common.util.*;
import com.zte.mcrm.activity.integration.accountinfo.AccountInfoCaller;
import com.zte.mcrm.activity.integration.accountinfo.dto.AccountInfoDTO;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.integration.forwardmessage.ForwardMessageComponent;
import com.zte.mcrm.activity.integration.forwardmessage.param.MessageBodyParam;
import com.zte.mcrm.activity.integration.infocenter.InfoCenterService;
import com.zte.mcrm.activity.integration.infocenter.vo.SendWorkOrderSimpleVO;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupDto;
import com.zte.mcrm.activity.integration.lookupapi.dto.FastLookupSearchDTO;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.integration.usercenter.dto.UserInfoDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.authority.ActivityResourceOperationAuthDO;
import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationAttachmentDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.log.ActivityLogDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationContactDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.model.relation.*;
import com.zte.mcrm.activity.repository.model.resource.*;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.authority.ActivityResourceOperationAuthRepository;
import com.zte.mcrm.activity.repository.rep.evaluation.ActivityEvaluationInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionInfoRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationAttachmentRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationExpertRepository;
import com.zte.mcrm.activity.repository.rep.exhibition.ExhibitionRelationLeaderRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.log.ActivityLogRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.*;
import com.zte.mcrm.activity.repository.rep.resource.*;
import com.zte.mcrm.activity.repository.rep.sample.SamplePointInfoRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryIssueRepository;
import com.zte.mcrm.activity.repository.rep.summary.ActivitySummaryRepository;
import com.zte.mcrm.activity.service.activity.*;
import com.zte.mcrm.activity.service.activity.convert.ActivityCustomerInfoConvert;
import com.zte.mcrm.activity.service.activity.convert.ScheduleItemModelConvert;
import com.zte.mcrm.activity.service.activity.helper.SyncUpScheduleItemHelper;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemModel;
import com.zte.mcrm.activity.service.activity.model.ActivityScheduleItemPeopleModel;
import com.zte.mcrm.activity.service.activity.model.ScheduleItemModel;
import com.zte.mcrm.activity.service.activity.param.ActivityDataSource;
import com.zte.mcrm.activity.service.activity.support.impl.ActivityDataSourceSupport;
import com.zte.mcrm.activity.service.activitylist.impl.ActivityInfoListQueryServiceImpl;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.activitylist.param.ActivityRelationZtePeopleQuery;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityInfoVO;
import com.zte.mcrm.activity.service.approval.ApprovalClient;
import com.zte.mcrm.activity.service.approval.impl.ActivityApprovalInfoServiceImpl;
import com.zte.mcrm.activity.service.approval.param.ApprovalFlowStartParam;
import com.zte.mcrm.activity.service.approval.param.ApprovedByInfo;
import com.zte.mcrm.activity.service.authority.ExhibitionAuthorityService;
import com.zte.mcrm.activity.service.authority.ResourceOperationAuthService;
import com.zte.mcrm.activity.service.authority.model.OperationAuthModel;
import com.zte.mcrm.activity.service.authority.param.OperationAuthParam;
import com.zte.mcrm.activity.service.comment.vo.SendWorkOrderContentVO;
import com.zte.mcrm.activity.service.common.timezone.ExhibitionTimeZoneComponent;
import com.zte.mcrm.activity.service.converter.ActivityInfoConverter;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.service.event.convert.ActivityEventConvert;
import com.zte.mcrm.activity.service.exhibition.param.ExhibitionBoardDataSource;
import com.zte.mcrm.activity.service.isearch.ActivityISearchService;
import com.zte.mcrm.activity.service.model.activity.ActivityBO;
import com.zte.mcrm.activity.service.notice.ActivityPendingNoticeService;
import com.zte.mcrm.activity.service.notice.param.ActivityPendingNoticeBO;
import com.zte.mcrm.activity.service.people.impl.ActivityZtePeopleServiceImpl;
import com.zte.mcrm.activity.service.person.convert.ZtePeopleLabelConvert;
import com.zte.mcrm.activity.service.reservation.ActivityResourceReservationService;
import com.zte.mcrm.activity.service.reservation.ResourceReleaseService;
import com.zte.mcrm.activity.service.reservation.ResourceReservationService;
import com.zte.mcrm.activity.service.reservation.param.ReserveResourceParam;
import com.zte.mcrm.activity.service.resource.model.ActivityScheduleConflictModel;
import com.zte.mcrm.activity.service.sample.SamplePointQueryService;
import com.zte.mcrm.activity.service.schedule.ScheduleOrchestrationQueryService;
import com.zte.mcrm.activity.service.schedule.support.synschedule.ScheduleOrchestrationSynScheduleService;
import com.zte.mcrm.activity.web.controller.activity.model.ActivityInvalidateModel;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityApprovalParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityConflictCheckParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityInvalidateParam;
import com.zte.mcrm.activity.web.controller.activity.param.ConvenientInfoParam;
import com.zte.mcrm.activity.web.controller.activity.vo.*;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ActivityBaseInfoVO;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoVO;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionAttachmentDetailsVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionRelationAttachmentVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.ExhibitionRelationExpertVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.SelectExhibitionVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleCustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleInfoVO;
import com.zte.mcrm.activity.web.controller.exhibition.vo.app.ExhibitionScheduleZtePeopleInfoVO;
import com.zte.mcrm.activity.web.controller.reception.vo.AccountVO;
import com.zte.mcrm.activity.web.controller.reception.vo.CustomerReceptionVO;
import com.zte.mcrm.activity.web.controller.reservation.param.ReserveScheduleInitiationParam;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationDetailVo;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationVo;
import com.zte.mcrm.activity.web.controller.reservation.vo.ReserveDealCommonVO;
import com.zte.mcrm.activity.web.controller.resource.vo.ActivityResourceCarVO;
import com.zte.mcrm.activity.web.controller.resource.vo.ActivityResourceFeeVO;
import com.zte.mcrm.activity.web.controller.resource.vo.ActivityResourceHotelVO;
import com.zte.mcrm.activity.web.controller.sample.vo.SamplePointInfoVO;
import com.zte.mcrm.adapter.AccountAdapter;
import com.zte.mcrm.adapter.HrmUsercenterAdapter;
import com.zte.mcrm.adapter.UserCenterPgAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.adapter.constant.ExternalConstant;
import com.zte.mcrm.adapter.vo.Account;
import com.zte.mcrm.adapter.vo.ContactVO;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.custcomm.access.vo.CustActivityCommPlanVO;
import com.zte.mcrm.custcomm.business.CustActivityCommPlanService;
import com.zte.mcrm.customvisit.access.dao.CustActivityHeaderDao;
import com.zte.mcrm.customvisit.domain.CustActivityHeaderVO;
import com.zte.mcrm.expansion.access.external.UserDetailVO;
import com.zte.mcrm.isearch.enums.AmountUnitTypeEnum;
import com.zte.mcrm.keyid.service.IKeyIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.*;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.*;
import static com.zte.mcrm.activity.common.constant.DictConstant.INTERNAL_CUSTOMER_TYPE;
import static com.zte.mcrm.activity.common.constant.DictConstant.JOINT_STOCK_COMPANY_ID;
import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.common.constant.LookupConstant.*;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.common.constant.NumberConstant.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.CONFERENCE;
import static com.zte.mcrm.activity.common.enums.activity.ActivityOriginTypeEnum.EXHIBITION;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum.DRAFT;
import static com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ApprovalTypeEnum.COMPLIANCE_AUDITOR;
import static com.zte.mcrm.activity.common.enums.activity.ApprovalTypeEnum.LEADER_AUDITOR;
import static com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.LEADER;
import static com.zte.mcrm.activity.common.enums.activity.PeopleRoleLabelEnum.SAVANT;
import static com.zte.mcrm.activity.service.converter.ActivityInfoConverter.*;
import static com.zte.mcrm.activity.service.schedule.param.ScheduleOrchestrationMailParam.SEVENTH;
import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;
import static com.zte.mcrm.customvisit.util.DateUtils.getYmdHmDate;
import static com.zte.mcrm.expansion.common.constant.CustExpansionConstant.*;
import static com.zte.mcrm.temp.common.constant.CommonConstant.COMMUNICATION;

/**
 * 查询活动信息
 *
 * @author: 汤踊********
 * @date: 2023/5/18 17:32
 */
@Service
@Slf4j
public class ActivityServiceImpl implements ActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityServiceImpl.class);

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private ActivityCommunicationDirectionRepository communicationDirectionRepository;
    @Autowired
    private ActivityRelationProjectRepository projectRepository;
    @Autowired
    private ActivityCustomerInfoRepository customerInfoRepository;
    @Autowired
    private ActivityRelationCustPeopleRepository custPeopleRepository;
    @Autowired
    private ActivityRelationZtePeopleRepository ztePeopleRepository;
    @Autowired
    private ActivityPendingNoticeService noticeService;
    @Autowired
    private ActivityUrlConfig activityUrlConfig;

    @Autowired
    private ActivitySummaryRepository summaryRepository;
    @Autowired
    private ActivitySummaryIssueRepository summaryIssueRepository;
    @Autowired
    private ActivityEvaluationInfoRepository evaluationInfoRepository;
    @Autowired
    private ActivityRelationSolutionRepository solutionRepository;
    @Autowired
    private IKeyIdService keyIdService;
    @Autowired
    private LocaleMessageSourceBean lmsb;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ActivityApprovalInfoServiceImpl activityApprovalInfoService;
    @Autowired
    private ApprovalClient approvalClient;
    @Autowired
    private ResourceReservationService reservationService;
    @Autowired
    private InfoCenterService infoCenterService;
    @Autowired
    private ActivityInfoRepository infoRepository;
    @Autowired
    private ExhibitionInfoRepository exhibitionInfoRepository;
    @Autowired
    private ExhibitionRelationExpertRepository exhibitionRelationExpertRepository;
    @Autowired
    private ExhibitionRelationLeaderRepository exhibitionRelationLeaderRepository;
    @Autowired
    private ExhibitionRelationAttachmentRepository exhibitionRelationAttachmentRepository;

    @Autowired
    private ActivityTagRepository tagRepository;
    @Autowired
    private ActivityRelationAttachmentRepository relationAttachmentRepository;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private LookUpExtService lookUpExtService;
    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;
    @Autowired
    private CustActivityCommPlanService custActivityCommPlanService;
    @Autowired
    private ActivityStatusLifecycleRepository lifecycleRepository;
    @Autowired
    private ActivityRelationTalkRepository talkRepository;
    @Autowired
    ActivityCustomerSearchService customerSearchService;
    @Autowired
    ActivityCustomerPeopleSearchService customerPeopleSearchService;
    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;
    @Autowired
    private ResourceBizExpertRepository resourceBizExpertRepository;
    @Autowired
    private ZtePeopleLabelConvert ztePeopleLabelConvert;
    @Autowired
    private AccountAdapter accountAdapter;
    @Autowired
    private HrmUsercenterAdapter hrmUsercenterAdapter;
    @Autowired
    private CustomerIntegrationUppConfig config;
    @Autowired
    private ActivityResourceReservationService resourceReservationService;
    @Autowired
    private ActivityZtePeopleServiceImpl ztePeopleService;

    @Autowired
    private ForwardMessageComponent forwardMessageComponent;
    @Autowired
    private ActivityInfoListQueryServiceImpl infoListQueryService;
    @Autowired
    private ActivityCustomerService customerService;
    @Autowired
    private ActivityDataAbilityService dataAbilityService;
    @Autowired
    private ActivityInfoService infoService;
    @Autowired
    private ActivityCheckService checkService;

    @Autowired
    private ExhibitionQuerySupportConvertor exhibitionQuerySupportConvertor;
    @Autowired
    private ResourceReleaseService resourceReleaseService;
    @Autowired
    private ActivityLogRepository logRepository;
    @Autowired
    private DictService dictService;
    @Autowired
    private UserCenterPgAdapter userCenterPgAdapter;

    @Autowired
    private CustActivityHeaderDao custActivityHeaderDao;
    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;
    @Autowired
    private ActivityScheduleItemPeopleRepository scheduleItemPeopleRepository;
    @Autowired
    private ActivityResourceHotelRepository activityResourceHotelRepository;

    @Autowired
    private ActivityResourceCarRepository activityResourceCarRepository;

    @Autowired
    private ActivityResourceFeeRepository activityResourceFeeRepository;

    @Autowired
    private ActivityReceptionMappingRepository activityReceptionMappingRepository;

    @Autowired
    private ActivityISearchService iSearchService;

    @Autowired
    private ExhibitionAuthorityService exhibitionAuthorityService;

    @Autowired
    private ActivityCostBudgetRepository activityCostBudgetRepository;
    @Autowired
    private SamplePointQueryService samplePointQueryService;
    @Autowired
    private ActivityResourceReservationScheduleRepository activityResourceReservationScheduleRepository;
    @Autowired
    private ScheduleOrchestrationQueryService scheduleOrchestrationQueryService;
    @Autowired
    private ScheduleOrchestrationSynScheduleService scheduleOrchestrationSynScheduleService;

    @Autowired
    private ExhibitionTimeZoneComponent exhibitionTimeZoneComponent;
    @Autowired
    private AccountInfoCaller accountInfoCaller;
    @Autowired
    private SamplePointInfoRepository samplePointInfoRepository;
    @Autowired
    private ActivityOpportunityRepository activityOpportunityRepository;

    @Autowired
    private ResourceOperationAuthService resourceOperationAuthService;

    @Autowired
    private ActivityResourceOperationAuthRepository activityResourceOperationAuthRepository;

    @Autowired
    private SyncUpScheduleItemHelper syncUpScheduleItemHelper;

    /**
     * 查询活动信息
     *
     * @param activityRowId 查询条件
     * @return 查询结果
     * @author: 汤踊********
     * @date: 2023/5/18 17:30
     */
    @Override
    public ActivityDetailInfoVO getActivityInfo(String activityRowId) {
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (Objects.isNull(activityInfoDO)) {
            return null;
        }
        ActivityDetailInfoVO detailInfoVO = new ActivityDetailInfoVO();
        // 样板点相关
        CompletableFuture<Void> querySamplePointFuture = CompletableFuture.runAsync(() -> querySamplePoint(detailInfoVO, activityInfoDO));
        if (ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), EXHIBITION, CONFERENCE)) {
            //查询酒店信息
            queryActivityResourceHotel(activityRowId, detailInfoVO);
            //查询车辆信息
            queryActivityResourceCar(activityRowId, detailInfoVO);
            //查询费用明细
            queryActivityResourceFee(activityRowId,detailInfoVO);
            //查询酒店资源、车辆资源附件
            queryListResourceAttachmentHotelAndCar(activityRowId,detailInfoVO);
        }
        List<ActivityRelationZtePeopleDO> listZtePeople = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        this.setPostNameByEmployeeInfo(listZtePeople);
        queryActivityBaseInfo(activityRowId, activityInfoDO, detailInfoVO, listZtePeople);
        //查询日程安排信息
        queryActivityScheduleItem(activityRowId,detailInfoVO);
        bindReservationStatus(detailInfoVO);
        // 会议纪要
        querySummaryInfo(activityRowId, detailInfoVO, listZtePeople);
        // 评价
        queryEvaluateInfo(activityRowId, detailInfoVO);
        // 操作权限
        queryOperatorAuthInfo(activityRowId, detailInfoVO);
        // 展会相关
        queryExhibition(detailInfoVO, activityInfoDO);
        // 接待信息相关
        queryReception(detailInfoVO, activityInfoDO);
        querySamplePointFuture.join();
        return detailInfoVO;
    }

    /**
     * 双语话配置，通过查询员工信息获取岗位信息，进行替换
     * @param listZtePeople
     */
    public void setPostNameByEmployeeInfo(List<ActivityRelationZtePeopleDO> listZtePeople){

        Set<String> peopleCodeList = Sets.newHashSet();
        for (ActivityRelationZtePeopleDO activityRelationZtePeopleDO : listZtePeople) {
            peopleCodeList.add(activityRelationZtePeopleDO.getPeopleCode());
        }

        if (!peopleCodeList.isEmpty()) {
            Map<String, PersonInfoDTO> userInfo = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(peopleCodeList)).getBo();
            for (ActivityRelationZtePeopleDO activityRelationZtePeopleDO : listZtePeople) {
                String peopleCode = activityRelationZtePeopleDO.getPeopleCode();
                PersonInfoDTO employeeInfoDTO = userInfo.get(peopleCode);
                if (employeeInfoDTO != null) {
                    activityRelationZtePeopleDO.setPositionName(employeeInfoDTO.getEmpPostNameMain());
                    activityRelationZtePeopleDO.setDeptFullName(employeeInfoDTO.getOrgFullName());
                }
            }
        }
    }

    @Autowired
    private ActivityDataSourceSupport activityDataSourceSupport;

    /**
     * 查询样板点信息
     * @param detailInfoVO
     * @param activityInfoDO
     */
    private void querySamplePoint(ActivityDetailInfoVO detailInfoVO, ActivityInfoDO activityInfoDO) {
        if (!ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), ActivityOriginTypeEnum.SAMPLE_POINT)) {
            // 设置审批人
            detailInfoVO.setApprovalList(Optional.ofNullable(activityInfoDO.getApprovalText()).map(approvalText -> JSON.parseArray(activityInfoDO.getApprovalText(), ActivityApprovalParam.class)).orElse(Collections.emptyList()));
            return;
        }
        ActivityDataSource activityDataSource = activityDataSourceSupport.getActivitySamplePointDataSource(BizRequestUtil.createWithCurrentUser(activityInfoDO.getRowId()));
        // 样板点信息
        detailInfoVO.setSamplePointInfo(samplePointQueryService.getSamplePointDetails(BizRequestUtil.createWithCurrentUser(activityInfoDO.getOriginRowId())).getData());
        // 审批人列表
        detailInfoVO.setApprovalList(activityDataSource.fetchActivityApprovalParamList(LEADER_AUDITOR));
        // 样板点活动成本预算
        detailInfoVO.setActivityCostBudgetList(activityDataSource.fetchActivityCostBudgetVOList());
        // 样板点活动成本预算变更标识
        detailInfoVO.setActivityCostBudgetChangeFlag(Optional.ofNullable(detailInfoVO.getSamplePointInfo()).map(SamplePointInfoVO::getInternational).map(activityDataSource::fetchActivityCostBudgetChangeFlag).orElse(Boolean.FALSE));
        // 样板点资金调整附件
        detailInfoVO.setFundingAdjustmentAttachmentList(activityDataSource.fetchActivityAttachmentInfoVOList(FUNDING_ADJUSTMENT));
    }

    /**
     * 转换完善日程--app
     * @param detailInfoVO
     */
    public void perfectScheduleItemInfoForApp(ActivityDetailInfoVO detailInfoVO, String activityRowId) {
        Map<String, List<ActivityScheduleItemVO>> mapScheduleInfo = detailInfoVO.getMapScheduleInfo();
        if (MapUtils.isEmpty(mapScheduleInfo)) {
            return;
        }

        SelectExhibitionVO exhibitionInfo = detailInfoVO.getExhibitionInfo();
        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        List<ActivityCustomerInfoDO> activityCustomerInfoDOList = customerInfoRepository.queryAllByActivityRowId(activityRowId);
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList = custPeopleRepository.queryAllByActivityRowId(activityRowId);

        Map<String, List<ExhibitionScheduleInfoVO>> exhibitionScheduleInfoMap = new TreeMap<>();
        for (String scheduleDate : mapScheduleInfo.keySet()) {
            List<ActivityScheduleItemVO> activityScheduleItemVOList = mapScheduleInfo.get(scheduleDate);
            List<ExhibitionScheduleInfoVO> exhibitionScheduleInfoVOList = Lists.newArrayList();
            for (ActivityScheduleItemVO activityScheduleItemVO : activityScheduleItemVOList) {
                ExhibitionScheduleInfoVO exhibitionScheduleInfoVO = getExhibitionScheduleInfoVO(activityScheduleItemVO
                        , activityRelationZtePeopleDOList, activityCustomerInfoDOList
                        , activityRelationCustPeopleDOList);
                exhibitionScheduleInfoVO.setExhibitionRowId(null != exhibitionInfo ? exhibitionInfo.getRowId() : null);
                exhibitionScheduleInfoVOList.add(exhibitionScheduleInfoVO);
            }
            exhibitionScheduleInfoMap.put(scheduleDate, exhibitionScheduleInfoVOList);
        }
        buildScheduleAuthority(detailInfoVO, exhibitionScheduleInfoMap, activityRowId);

        detailInfoVO.setExhibitionScheduleInfoMap(exhibitionScheduleInfoMap);
    }

    /**
     * 修改前：展会报名活动与样板点活动共用同一个接口，同一套权限
     * 修改后：样板点权限保持不变，展会报名活动权限使用新权限
     * 后续如果需要统一，删除样板点部分逻辑
     *
     * @param detailInfoVO
     * @param exhibitionScheduleInfoMap
     * @param activityRowId
     * <AUTHOR>
     * @date 2024/11/21 下午2:34
     */
    private void buildScheduleAuthority(ActivityDetailInfoVO detailInfoVO,
                                        Map<String, List<ExhibitionScheduleInfoVO>> exhibitionScheduleInfoMap,
                                        String activityRowId) {
        String activityType = Optional.ofNullable(detailInfoVO.getBaseInfo()).orElse(new BaseInfoVO()).getActivityType();
        if (JOIN_EXHIBITION.isMe(activityType)) {
            buildExhibitionScheduleAuthority(exhibitionScheduleInfoMap, activityRowId);
        } else {
            buildOtherScheduleAuthority(exhibitionScheduleInfoMap, activityRowId);
        }
    }

    /**
     * 展会报名活动日程权限
     * 兼容APP端一期修改，如果没有查看权限，fileToken置空
     *
     * @param exhibitionScheduleInfoMap 日程
     * @param activityRowId             活动id
     * <AUTHOR>
     * @date 2024/11/21 下午2:43
     */
    private void buildExhibitionScheduleAuthority(Map<String, List<ExhibitionScheduleInfoVO>> exhibitionScheduleInfoMap, String activityRowId) {
        for (Map.Entry<String, List<ExhibitionScheduleInfoVO>> entry : exhibitionScheduleInfoMap.entrySet()) {
            List<ExhibitionScheduleInfoVO> scheduleInfoVOList = entry.getValue();
            Optional.ofNullable(scheduleInfoVOList).orElse(Collections.emptyList())
                    .forEach(schedule -> {
                        ResourceOperationVO operation = Optional.ofNullable(schedule.getOperation()).orElse(new ResourceOperationVO());
                        boolean viewable = Boolean.TRUE.equals(operation.getViewable());
                        Optional.ofNullable(schedule.getScheduleAttachmentInfoList()).orElse(Collections.emptyList())
                                .forEach(attachment -> attachment.setFileToken(viewable ? attachment.getFileToken() : null));
                    });
        }
    }

    /**
     * 其他类型活动日程权限（样板点活动）
     *
     * @param exhibitionScheduleInfoMap 日程
     * @param activityRowId             活动id
     * <AUTHOR>
     * @date 2024/11/21 下午2:43
     */
    private void buildOtherScheduleAuthority(Map<String, List<ExhibitionScheduleInfoVO>> exhibitionScheduleInfoMap, String activityRowId) {
        List<String> userScheduleItemReferenceAuthNewList
                = exhibitionAuthorityService.getUserScheduleItemReferenceAuth(BizRequestUtil.createWithCurrentUserSecurity(activityRowId));
        for (Map.Entry<String, List<ExhibitionScheduleInfoVO>> entry : exhibitionScheduleInfoMap.entrySet()) {
            List<ExhibitionScheduleInfoVO> scheduleInfoVOList = entry.getValue();
            Optional.ofNullable(scheduleInfoVOList).orElse(Collections.emptyList())
                    .forEach(schedule -> {
                        boolean haveAuth = userScheduleItemReferenceAuthNewList.stream().anyMatch(item -> StringUtils.equals(item, schedule.getScheduleItemRowId()));
                        Optional.ofNullable(schedule.getScheduleAttachmentInfoList()).orElse(Collections.emptyList())
                                .forEach(attachment -> attachment.setFileToken(haveAuth ? attachment.getFileToken() : null));
                    });
        }
    }

    /**
     * 获取日程谈参操作权限
     *
     * @param activityRowId     活动id
     * @param scheduleIdList    日程id
     * @return {@link OperationAuthModel}
     * <AUTHOR>
     * @date 2024/11/20 下午3:07
     */
    private OperationAuthModel getOperationAuthModel(String activityRowId, List<String> scheduleIdList) {
        OperationAuthParam operationAuthParam = new OperationAuthParam();
        operationAuthParam.setActivityId(activityRowId);
        operationAuthParam.setBizType(ActivityResourceOperationBizTypeEnum.SCHEDULE_TALK.getCode());
        operationAuthParam.setBizRelatedIdList(scheduleIdList);
        return resourceOperationAuthService.getOperationAuthValueList(BizRequestUtil.createWithCurrentUser(operationAuthParam)).getData();
    }

    /**
     *
     * @param activityScheduleItemVO
     * @param activityRelationZtePeopleDOList
     * @param activityCustomerInfoDOList
     * @param activityRelationCustPeopleDOList
     * @return
     */
    private ExhibitionScheduleInfoVO getExhibitionScheduleInfoVO(ActivityScheduleItemVO activityScheduleItemVO
            , List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList
            , List<ActivityCustomerInfoDO> activityCustomerInfoDOList
            , List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList) {

        ExhibitionScheduleInfoVO exhibitionScheduleInfoVO = new ExhibitionScheduleInfoVO();
        exhibitionScheduleInfoVO.setScheduleItemRowId(activityScheduleItemVO.getRowId());
        exhibitionScheduleInfoVO.setActivityRowId(activityScheduleItemVO.getActivityRowId());
        exhibitionScheduleInfoVO.setScheduleItemType(activityScheduleItemVO.getScheduleItemType());
        exhibitionScheduleInfoVO.setScheduleItemName(activityScheduleItemVO.getScheduleItemName());
        exhibitionScheduleInfoVO.setScheduleDate(activityScheduleItemVO.getStrScheduleDate());
        exhibitionScheduleInfoVO.setScheduleTime(activityScheduleItemVO.getScheduleTime());
        exhibitionScheduleInfoVO.setPlaceType(activityScheduleItemVO.getPlaceType());
        exhibitionScheduleInfoVO.setRemark(activityScheduleItemVO.getRemark());
        exhibitionScheduleInfoVO.setDealStatus(activityScheduleItemVO.getDealStatus());
        exhibitionScheduleInfoVO.setDealStatusName(activityScheduleItemVO.getDealStatusName());
        String placeName = activityScheduleItemVO.getPlaceName();
        if (ScheduleItemPlaceTypeEnum.ROOM.isMe(activityScheduleItemVO.getPlaceType())) {
            exhibitionScheduleInfoVO.setPlaceName(StringUtils.isNotBlank(placeName) ? placeName : activityScheduleItemVO.getPlaceTypeName());
        } else {
            exhibitionScheduleInfoVO.setPlaceName(placeName);
        }

        if(StringUtils.isNotBlank(activityScheduleItemVO.getScheduleTime())){
            String scheduleTime = activityScheduleItemVO.getScheduleTime();
            String[] split = scheduleTime.split(WAVE);
            exhibitionScheduleInfoVO.setScheduleStartTime(split[0]);
            exhibitionScheduleInfoVO.setScheduleEndTime(split[split.length-1]);
        }
        ActivityCustomerInfoDO activityCustomerInfoDO = activityCustomerInfoDOList.stream()
                .filter(item -> BooleanEnum.Y.isMe(item.getMainCust())).findFirst().orElse(new ActivityCustomerInfoDO());
        exhibitionScheduleInfoVO.setLocalCode(activityCustomerInfoDO.getLocalCode());
        exhibitionScheduleInfoVO.setLocalName(activityCustomerInfoDO.getLocalName());
        exhibitionScheduleInfoVO.setMktCode(activityCustomerInfoDO.getMktCode());
        exhibitionScheduleInfoVO.setMktName(activityCustomerInfoDO.getMktName());
        exhibitionScheduleInfoVO.setCustomerCode(activityCustomerInfoDO.getCustomerCode());
        exhibitionScheduleInfoVO.setCustomerName(activityCustomerInfoDO.getCustomerName());
        exhibitionScheduleInfoVO.setSanctionedPatryCode(activityCustomerInfoDO.getSanctionedPatryCode());

        // 中兴参与人
        List<ActivityScheduleItemPeopleVO> listZtePeople = this.filterRepeatedZtePeople(activityScheduleItemVO.getListZtePeople());
        List<ActivityScheduleItemPeopleVO> sortedZtePeopleList = listZtePeople.stream().sorted(new Comparator<ActivityScheduleItemPeopleVO>() {
            @Override
            public int compare(ActivityScheduleItemPeopleVO o1, ActivityScheduleItemPeopleVO o2) {
                int orderNumber1 = ZtePeopleOrderEnum.getPositionOrderNum(o1.getPeopleLabel(), o1.getPosition());
                int orderNumber2 = ZtePeopleOrderEnum.getPositionOrderNum(o2.getPeopleLabel(), o2.getPosition());
                return Integer.compare(orderNumber1, orderNumber2);
            }
        }).collect(Collectors.toList());
        List<ExhibitionScheduleZtePeopleInfoVO> ztePeopleList = Lists.newArrayList();
        for (ActivityScheduleItemPeopleVO ztePeople : sortedZtePeopleList) {
            ztePeopleList.add(this.getExhibitionScheduleZtePeopleInfoVO(ztePeople, activityRelationZtePeopleDOList));
        }
        exhibitionScheduleInfoVO.setZtePeopleList(ztePeopleList);

        // 现场陪同人员
        List<ActivityScheduleItemPeopleVO> listInterfacePeople = activityScheduleItemVO.getListInterfacePeople();
        List<ExhibitionScheduleZtePeopleInfoVO> interfacePeopleList = Lists.newArrayList();
        for (ActivityScheduleItemPeopleVO interfacePeople : listInterfacePeople) {
            interfacePeopleList.add(this.getExhibitionScheduleZtePeopleInfoVO(interfacePeople, activityRelationZtePeopleDOList));
        }
        exhibitionScheduleInfoVO.setInterfacePeopleList(interfacePeopleList);

        // 谈参负责人
        List<ActivityScheduleItemPeopleVO> listReferenceControllerPeople = activityScheduleItemVO.getListReferenceControllerPeople();
        List<ExhibitionScheduleZtePeopleInfoVO> referenceControllerPeopleList = Lists.newArrayList();
        for (ActivityScheduleItemPeopleVO referenceControllerPeople : listReferenceControllerPeople) {
            referenceControllerPeopleList.add(this.getExhibitionScheduleZtePeopleInfoVO(referenceControllerPeople, activityRelationZtePeopleDOList));
        }
        exhibitionScheduleInfoVO.setReferenceControllerPeopleList(referenceControllerPeopleList);

        // 客户参与人
        List<ActivityScheduleItemPeopleVO> listClientParticipant = activityScheduleItemVO.getListClientParticipant();
        List<ExhibitionScheduleCustPeopleInfoVO> custPeopleList = Lists.newArrayList();
        for (ActivityScheduleItemPeopleVO activityScheduleItemPeopleVO : listClientParticipant) {
            custPeopleList.add(this.getExhibitionScheduleCustPeopleInfoVO(activityScheduleItemPeopleVO, activityCustomerInfoDOList
                    , activityRelationCustPeopleDOList));
        }
        exhibitionScheduleInfoVO.setCustPeopleList(custPeopleList);

        // 谈参信息
        ActivityAttachmentInfoVO scheduleRelationAttachment = activityScheduleItemVO.getScheduleRelationAttachment();
        List<ExhibitionAttachmentDetailsVO> scheduleAttachmentInfoList = Lists.newArrayList();
        if(null != scheduleRelationAttachment){
            scheduleAttachmentInfoList.add(this.getExhibitionAttachmentDetailsVO(scheduleRelationAttachment));
        }
        exhibitionScheduleInfoVO.setScheduleAttachmentInfoList(scheduleAttachmentInfoList);
        // 日程权限
        exhibitionScheduleInfoVO.setOperation(activityScheduleItemVO.getOperation());

        return exhibitionScheduleInfoVO;
    }

    List<ActivityScheduleItemPeopleVO> filterRepeatedZtePeople(List<ActivityScheduleItemPeopleVO> listZtePeople) {
        if (CollectionUtils.isEmpty(listZtePeople)) {
            return new ArrayList<>();
        }

        Map<String, ActivityScheduleItemPeopleVO> ztePeopleMap = new HashMap<>();
        listZtePeople.forEach(item -> {
            String peopleCode = item.getPeopleNo();
            String peopleLabel = item.getPeopleLabel();

            if (!ztePeopleMap.containsKey(peopleCode)) {
                ztePeopleMap.put(peopleCode, item);
                return;
            }
            // 如果我司参与人中同一个人添加多次 保留领导和专家标签的那条记录
            if (PeopleRoleLabelEnum.in(peopleLabel, LEADER, SAVANT)) {
                ztePeopleMap.put(peopleCode, item);
            }
        });

        return com.google.common.collect.Lists.newArrayList(ztePeopleMap.values());
    }

    /**
     * 谈参信息
     * @param scheduleRelationAttachment
     * @return
     */
    private ExhibitionAttachmentDetailsVO getExhibitionAttachmentDetailsVO(ActivityAttachmentInfoVO scheduleRelationAttachment) {
        ExhibitionAttachmentDetailsVO exhibitionAttachmentDetailsVO = new ExhibitionAttachmentDetailsVO();
        exhibitionAttachmentDetailsVO.setRowId(scheduleRelationAttachment.getRowId());
        exhibitionAttachmentDetailsVO.setActivityRowId(scheduleRelationAttachment.getActivityRowId());
        exhibitionAttachmentDetailsVO.setFileToken(scheduleRelationAttachment.getFileToken());
        exhibitionAttachmentDetailsVO.setFileName(scheduleRelationAttachment.getFileName());
        exhibitionAttachmentDetailsVO.setFileSize(scheduleRelationAttachment.getFileSize());
        return exhibitionAttachmentDetailsVO;
    }

    /**
     * 转换客户参与人信息
     * @param activityScheduleItemPeopleVO
     * @param activityCustomerInfoDOList
     * @param activityRelationCustPeopleDOList
     * @return
     */
    private ExhibitionScheduleCustPeopleInfoVO getExhibitionScheduleCustPeopleInfoVO(ActivityScheduleItemPeopleVO activityScheduleItemPeopleVO
            , List<ActivityCustomerInfoDO> activityCustomerInfoDOList, List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList) {
        ExhibitionScheduleCustPeopleInfoVO exhibitionScheduleCustPeopleInfoVO = new ExhibitionScheduleCustPeopleInfoVO();
        exhibitionScheduleCustPeopleInfoVO.setContactNo(activityScheduleItemPeopleVO.getPeopleNo());
        exhibitionScheduleCustPeopleInfoVO.setContactName(activityScheduleItemPeopleVO.getPeopleName());
        exhibitionScheduleCustPeopleInfoVO.setPositionName(activityScheduleItemPeopleVO.getPosition());
        exhibitionScheduleCustPeopleInfoVO.setCustomerCode(activityScheduleItemPeopleVO.getCustomerCode());
        ActivityCustomerInfoDO activityCustomerInfoDO = activityCustomerInfoDOList.stream().filter(item ->
                StringUtils.equals(item.getCustomerCode(), activityScheduleItemPeopleVO.getCustomerCode())).findFirst().orElse(new ActivityCustomerInfoDO());
        exhibitionScheduleCustPeopleInfoVO.setCustomerName(activityCustomerInfoDO.getCustomerName());
        ActivityRelationCustPeopleDO activityRelationCustPeopleDO = activityRelationCustPeopleDOList.stream().filter(item ->
                StringUtils.equals(item.getContactNo(), activityScheduleItemPeopleVO.getPeopleNo())).findFirst().orElse(new ActivityRelationCustPeopleDO());
        exhibitionScheduleCustPeopleInfoVO.setContactLevel(activityRelationCustPeopleDO.getContactLevel());
        exhibitionScheduleCustPeopleInfoVO.setSanctionedPatryCode(activityRelationCustPeopleDO.getSanctionedPatryCode());
        exhibitionScheduleCustPeopleInfoVO.setSanctionedPatryName(SanctionedPartyEnum.getDescByCode(activityRelationCustPeopleDO.getSanctionedPatryCode()));
        return exhibitionScheduleCustPeopleInfoVO;
    }

    /**
     * 转换我司人员信息
     *
     * @param activityScheduleItemPeopleVO
     * @param activityRelationZtePeopleDOList
     * @return
     */
    private ExhibitionScheduleZtePeopleInfoVO getExhibitionScheduleZtePeopleInfoVO(ActivityScheduleItemPeopleVO activityScheduleItemPeopleVO
            , List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList) {
        ExhibitionScheduleZtePeopleInfoVO exhibitionScheduleZtePeopleInfoVO = new ExhibitionScheduleZtePeopleInfoVO();
        exhibitionScheduleZtePeopleInfoVO.setPeopleCode(activityScheduleItemPeopleVO.getPeopleNo());
        exhibitionScheduleZtePeopleInfoVO.setPeopleName(activityScheduleItemPeopleVO.getPeopleName());
        exhibitionScheduleZtePeopleInfoVO.setPeopleLabel(activityScheduleItemPeopleVO.getPeopleLabel());
        exhibitionScheduleZtePeopleInfoVO.setPeopleType(activityScheduleItemPeopleVO.getPeopleType());
        exhibitionScheduleZtePeopleInfoVO.setPositionName(activityScheduleItemPeopleVO.getPosition());
        ActivityRelationZtePeopleDO activityRelationZtePeopleDO = activityRelationZtePeopleDOList.stream().filter(item ->
                StringUtils.equals(item.getPeopleCode(), activityScheduleItemPeopleVO.getPeopleNo())).findFirst().orElse(new ActivityRelationZtePeopleDO());
        exhibitionScheduleZtePeopleInfoVO.setDeptFullName(activityRelationZtePeopleDO.getDeptFullName());
        return exhibitionScheduleZtePeopleInfoVO;
    }

    /**
     * 完善展会相关信息--app
     * @param detailInfoVO
     */
    public void perfectAppExhibitionInfoForApp(ActivityDetailInfoVO detailInfoVO) {
        SelectExhibitionVO exhibitionInfo = detailInfoVO.getExhibitionInfo();
        if (null == exhibitionInfo) {
            return;
        }
        AppExhibitionVO appExhibitionVO = new AppExhibitionVO();
        appExhibitionVO.setRowId(exhibitionInfo.getRowId());
        appExhibitionVO.setExhibitionName(exhibitionInfo.getExhibitionName());
        detailInfoVO.setAppExhibitionVO(appExhibitionVO);
    }

    /**
     * 完善酒店信息--app
     *
     * @param detailInfoVO
     * @param activityRowId
     */
    public void perfectHotelInfoForApp(ActivityDetailInfoVO detailInfoVO, String activityRowId) {
        List<ActivityResourceHotelVO> listResourceHotel = detailInfoVO.getListResourceHotel();
        if (CollectionUtils.isEmpty(listResourceHotel)) {
            return;
        }

        List<ActivityCustomerInfoDO> activityCustomerInfoDOList = customerInfoRepository.queryAllByActivityRowId(activityRowId);
        List<ActivityRelationCustPeopleDO> activityRelationCustPeopleDOList = custPeopleRepository.queryAllByActivityRowId(activityRowId);
        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleDOList = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        for (ActivityResourceHotelVO activityResourceHotelVO : listResourceHotel) {
            String peopleNo = activityResourceHotelVO.getPeopleNo();
            if (HotelPeopleSourceEnum.CUSTOMER.isMe(activityResourceHotelVO.getPeopleSource())) {
                ActivityRelationCustPeopleDO activityRelationCustPeopleDO = activityRelationCustPeopleDOList.stream().filter(item ->
                        StringUtils.equals(item.getContactNo(), peopleNo)).findFirst().orElse(new ActivityRelationCustPeopleDO());
                ActivityCustomerInfoDO activityCustomerInfoDO = activityCustomerInfoDOList.stream().filter(item ->
                        StringUtils.equals(item.getCustomerCode(), activityRelationCustPeopleDO.getCustomerCode())).findFirst().orElse(new ActivityCustomerInfoDO());
                activityResourceHotelVO.setCustomerName(activityCustomerInfoDO.getCustomerName());
                activityResourceHotelVO.setContactLevel(activityRelationCustPeopleDO.getContactLevel());
                activityResourceHotelVO.setPositionName(activityRelationCustPeopleDO.getPositionName());

            } else if (HotelPeopleSourceEnum.CONTACT_PERSON.isMe(activityResourceHotelVO.getPeopleSource())) {
                ActivityRelationZtePeopleDO activityRelationZtePeopleDO = activityRelationZtePeopleDOList.stream()
                        .filter(item -> StringUtils.equals(item.getPeopleCode(), peopleNo)).findFirst().orElse(new ActivityRelationZtePeopleDO());
                activityResourceHotelVO.setDeptFullName(activityRelationZtePeopleDO.getDeptFullName());
                activityResourceHotelVO.setPositionName(activityRelationZtePeopleDO.getPositionName());
            }
        }
    }

    /**
     * 查询酒店资源、车辆资源附件
     * @param activityRowId
     * @param detailInfoVO
     */
    public void queryListResourceAttachmentHotelAndCar(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (activityInfoDO == null || StringUtils.isEmpty(activityInfoDO.getOriginRowId())) {
            detailInfoVO.setListResourceAttachmentHotel(Collections.emptyList());
            detailInfoVO.setListResourceAttachmentCar(Collections.emptyList());
            return;
        }
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(activityInfoDO.getOriginRowId());
        Map<String, List<ExhibitionRelationAttachmentDO>> mapRelationAttachmentDO = exhibitionRelationAttachmentRepository.queryAttachmentByExhibitionRowId(Collections.singletonList(exhibitionInfoDO.getRowId()));
        List<ExhibitionRelationAttachmentDO> listExhibitionRelationAttachment = mapRelationAttachmentDO.get(exhibitionInfoDO.getRowId());
        if (CollectionUtils.isEmpty(listExhibitionRelationAttachment)) {
            detailInfoVO.setListResourceAttachmentHotel(Collections.emptyList());
            detailInfoVO.setListResourceAttachmentCar(Collections.emptyList());
            return;
        }
        List<ExhibitionRelationAttachmentVO> listExhibitionRelationAttachmentHotel  =Lists.newArrayList();
        List<ExhibitionRelationAttachmentVO> listExhibitionRelationAttachmentCar  =Lists.newArrayList();

        for (ExhibitionRelationAttachmentDO relationAttachment: listExhibitionRelationAttachment){
            ExhibitionRelationAttachmentVO  exhibitionRelationAttachmentVO =new ExhibitionRelationAttachmentVO();
            if(ExhibitionAttachmentSceneTypeEnum.EXHIBITION_HOTEL.isMe(relationAttachment.getExhibitionAttachmentSceneType())){
                BeanUtils.copyProperties(relationAttachment,exhibitionRelationAttachmentVO);
                listExhibitionRelationAttachmentHotel.add(exhibitionRelationAttachmentVO);
            }
            if(ExhibitionAttachmentSceneTypeEnum.EXHIBITION_CAR.isMe(relationAttachment.getExhibitionAttachmentSceneType())){
                BeanUtils.copyProperties(relationAttachment,exhibitionRelationAttachmentVO);
                listExhibitionRelationAttachmentCar.add(exhibitionRelationAttachmentVO);
            }
        }
        detailInfoVO.setListResourceAttachmentHotel(listExhibitionRelationAttachmentHotel);
        detailInfoVO.setListResourceAttachmentCar(listExhibitionRelationAttachmentCar);
    }

    /**
     * 查询日程安排信息
     *
     * @param activityRowId
     * @param detailInfoVO
     */
    public void queryActivityScheduleItem(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        List<ActivityScheduleItemDO> listScheduleItem = scheduleItemRepository.queryAllByActivityRowId(activityRowId);

        // 按照日程时间进行排序
        List<ActivityScheduleItemDO> activityScheduleItemDOList = listScheduleItem.stream().sorted(new Comparator<ActivityScheduleItemDO>() {
            @Override
            public int compare(ActivityScheduleItemDO o1, ActivityScheduleItemDO o2) {
                return ExhibitionBoardDataSource.getOrderOfActivityScheduleItems(o1, o2);
            }
        }).collect(Collectors.toList());

        List<ActivityScheduleItemVO> listScheduleItemVO = Lists.newArrayList();

        for (ActivityScheduleItemDO scheduleItemDO : activityScheduleItemDOList) {
            ActivityScheduleItemVO scheduleItemVO = new ActivityScheduleItemVO();
            BeanUtils.copyProperties(scheduleItemDO, scheduleItemVO);

            //查询日程参与人
            List<ActivityScheduleItemPeopleDO> listScheduleItemPeople = scheduleItemPeopleRepository.queryAllByScheduleItemRowId(scheduleItemDO.getRowId());
            Map<String, Map<String, ActivityRelationCustPeopleDO>> relationCustPeopleMap = custPeopleRepository.getRelationCustPeopleMap(Collections.singleton(activityRowId));
            Map<String, ActivityRelationCustPeopleDO> activityRelationCustPeopleDOMap = relationCustPeopleMap.get(activityRowId);
            List<ActivityScheduleItemPeopleVO> listScheduleItemPeopleVO = Lists.newArrayList();

            listActivityScheduleItemPeopleDOToVo(listScheduleItemPeople, listScheduleItemPeopleVO);

            List<ActivityScheduleItemPeopleVO> listZtePeople = Lists.newArrayList();
            List<ActivityScheduleItemPeopleVO> listInterfacePeople = Lists.newArrayList();
            List<ActivityScheduleItemPeopleVO> listClientParticipant = Lists.newArrayList();
            List<ActivityScheduleItemPeopleVO> listReferenceControllerPeople = Lists.newArrayList();

            participantClassification(listScheduleItemPeopleVO, listZtePeople, listInterfacePeople, listReferenceControllerPeople);
            getListClientParticipant(listScheduleItemPeopleVO,listClientParticipant,activityRelationCustPeopleDOMap);

            scheduleItemVO.setScheduleItemTypeName(ScheduleItemTypeEnum.getDescByType(scheduleItemVO.getScheduleItemType()));
            if (StringUtils.isBlank(scheduleItemVO.getScheduleItemTypeName())) {
                Map<String, String> scheduleTypeMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_SCHEDULE_TYPE);
                scheduleItemVO.setScheduleItemTypeName(scheduleTypeMap.get(scheduleItemVO.getScheduleItemType()));
            }
            scheduleItemVO.setDealStatusName(ResourceOrchestrationDealStatusEnum.getDescByType(scheduleItemVO.getDealStatus()));

            if (ScheduleItemPlaceTypeEnum.ROOM.isMe(scheduleItemVO.getPlaceType())) {
                scheduleItemVO.setPlaceTypeName(ScheduleItemPlaceTypeEnum.getDescByType(scheduleItemVO.getPlaceType()));
            }

            //编辑要用
            scheduleItemVO.setListZtePeople(listZtePeople);
            scheduleItemVO.setListInterfacePeople(listInterfacePeople);
            scheduleItemVO.setListClientParticipant(listClientParticipant);
            scheduleItemVO.setListReferenceControllerPeople(listReferenceControllerPeople);

            scheduleItemVO.setStrScheduleDate(new SimpleDateFormat(DateConstants.YYYY_MM_DD).format(scheduleItemVO.getScheduleDate()));
            scheduleItemVO.setStrZtePeople(convertSchedulePeopleData(listZtePeople));
            scheduleItemVO.setStrInterfacePeople(convertSchedulePeopleData(listInterfacePeople));
            scheduleItemVO.setStrClientParticipant(convertSchedulePeopleData(listClientParticipant));
            scheduleItemVO.setStrReferenceControllerPeople(convertSchedulePeopleData(listReferenceControllerPeople));

            ActivityRelationAttachmentDO relationAttachment = relationAttachmentRepository.queryAllBySceneOriginRowId(scheduleItemVO.getRowId());
            if (relationAttachment != null) {
                ActivityAttachmentInfoVO attachmentInfoVO = new ActivityAttachmentInfoVO();
                BeanUtils.copyProperties(relationAttachment, attachmentInfoVO);
                scheduleItemVO.setScheduleRelationAttachment(attachmentInfoVO);
            }

            listScheduleItemVO.add(scheduleItemVO);
        }
        if (CollectionUtils.isNotEmpty(listScheduleItemVO)) {
            // 补充日程权限
            handleScheduleAuthority(listScheduleItemVO, activityRowId, Optional.ofNullable(detailInfoVO.getBaseInfo()).orElse(new BaseInfoVO()).getActivityType());
            Map<String, List<ActivityScheduleItemVO>> mapSortData = new TreeMap<>();
            mapSortData.putAll(listScheduleItemVO.stream().collect(Collectors.groupingBy(ActivityScheduleItemVO::getStrScheduleDate)));
            detailInfoVO.setMapScheduleInfo(mapSortData);
        }
    }

    /**
     * 处理展会报名活动日程权限
     *
     * @param listScheduleItemVO
     * @param activityRowId
     * @param activityType
     * <AUTHOR>
     * @date 2024/11/21 下午3:31
     */
    private void handleScheduleAuthority(List<ActivityScheduleItemVO> listScheduleItemVO, String activityRowId, String activityType) {
        if (!JOIN_EXHIBITION.isMe(activityType)) {
            return;
        }
        List<String> scheduleIdList = listScheduleItemVO.stream().map(ActivityScheduleItemVO::getRowId).collect(Collectors.toList());
        OperationAuthModel operationAuthModel = getOperationAuthModel(activityRowId, scheduleIdList);
        listScheduleItemVO.forEach(item -> item.setOperation(operationAuthModel.buildOperation(item.getRowId())));
    }

    public String convertSchedulePeopleData(List<ActivityScheduleItemPeopleVO> listPeople) {
        if (CollectionUtils.isEmpty(listPeople)) {
            return null;
        }
        Set<String> listPeopleData = listPeople.stream().map(elem -> {
            if (ScheduleItemPeopleTypeEnum.in(elem.getPeopleType(), ScheduleItemPeopleTypeEnum.ZTE_PEOPLE, ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE)) {
                return elem.getPeopleName() + elem.getPeopleNo();
            } else {
                return elem.getPeopleName();
            }
        }).collect(Collectors.toSet());
        return String.join(CharacterConstant.SEMICOLON, listPeopleData);
    }

    /**
     * 查询酒店信息
     *
     * @param activityRowId
     * @param detailInfoVO
     */
    public void queryActivityResourceHotel(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        Map<String, List<ActivityResourceHotelDO>> mapActivityResourceHotel = activityResourceHotelRepository.queryActivityResourceHotelsByActivityRowIds(Collections.singletonList(activityRowId));
        List<ActivityResourceHotelDO> listResourceHotel = mapActivityResourceHotel.get(activityRowId);
        List<ActivityResourceHotelVO> listResourceHotelVO = Lists.newArrayList();

        if (CollectionUtils.isEmpty(listResourceHotel)) {
            return;
        }
        for (ActivityResourceHotelDO resourceHotelDO : listResourceHotel) {
            ActivityResourceHotelVO resourceHotelVO = new ActivityResourceHotelVO();
            BeanUtils.copyProperties(resourceHotelDO, resourceHotelVO);
            resourceHotelVO.setPaySelfName(getCurrentPaySelfName(resourceHotelVO.getPaySelf()));
            resourceHotelVO.setPeopleSourceName(HotelPeopleSourceEnum.getDescByType(resourceHotelDO.getPeopleSource()));
            resourceHotelVO.setRoomCoefficientValue(getRoomCoefficientValue(resourceHotelDO.getRoomCoefficient()));
            listResourceHotelVO.add(resourceHotelVO);
        }
        detailInfoVO.setListResourceHotel(listResourceHotelVO);
    }

    public String getRoomCoefficientValue(String roomCoefficient){
        if(StringUtils.isBlank(roomCoefficient)){
            return roomCoefficient;
        }
        Map<String, String> roomCoefficientMap = lookUpExtService.getLookUpMapByType(LOOKUP_TYPE_ROOM_COEFFICIENT);
        return MapUtils.isEmpty(roomCoefficientMap) ? roomCoefficient : roomCoefficientMap.get(roomCoefficient);
    }

    public String getCurrentPaySelfName(String paySelf) {
        if (BooleanEnum.Y.parseBoolean(paySelf) == null) {
            return null;
        }
        return BooleanEnum.Y.parseBoolean(paySelf) ? ActivityConstant.YES
                : ActivityConstant.NO;
    }

    /**
     * 获取费用信息
     * @param activityRowId
     * @param detailInfoVO
     */
    public void queryActivityResourceFee(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        Map<String, List<ActivityResourceFeeDO>> mapActivityResourceFee = activityResourceFeeRepository.queryActivityResourceFeesByActivityRowIds(Collections.singletonList(activityRowId));
        List<ActivityResourceFeeDO> listResourceFee = mapActivityResourceFee.get(activityRowId);
        List<ActivityResourceFeeVO> listResourceFeeVO = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(listResourceFee)) {
            for (ActivityResourceFeeDO resourceFeeDO : listResourceFee) {
                ActivityResourceFeeVO resourceFeeVO = new ActivityResourceFeeVO();
                BeanUtils.copyProperties(resourceFeeDO, resourceFeeVO);
                resourceFeeVO.setCurrencyName(AmountUnitTypeEnum.getDescByType(resourceFeeVO.getCurrency()));
                resourceFeeVO.setFeeSourceName(ExpenseSubjectEnum.getDescByType(resourceFeeDO.getFeeSource()));
                listResourceFeeVO.add(resourceFeeVO);
            }
            detailInfoVO.setListResourceFee(listResourceFeeVO);
        }
    }

    /**
     * 查询车辆信息
     *
     * @param activityRowId
     * @param detailInfoVO
     */
    public void queryActivityResourceCar(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        Map<String, List<ActivityResourceCarDO>> mapActivityResourceCar = activityResourceCarRepository.queryActivityResourceCarsByActivityRowIds(Collections.singletonList(activityRowId));
        List<ActivityResourceCarDO> listResourceCar = mapActivityResourceCar.get(activityRowId);
        List<ActivityResourceCarVO> listResourceCarVO = Lists.newArrayList();
        if (CollectionUtils.isEmpty(listResourceCar)) {
            return;
        }

        for (ActivityResourceCarDO resourceCarDO : listResourceCar) {
            ActivityResourceCarVO resourceCarVO = new ActivityResourceCarVO();
            BeanUtils.copyProperties(resourceCarDO, resourceCarVO);
            resourceCarVO.setPaySelfName(getCurrentPaySelfName(resourceCarVO.getPaySelf()));
            listResourceCarVO.add(resourceCarVO);
        }
        detailInfoVO.setListResourceCar(listResourceCarVO);
    }

    public void listActivityScheduleItemPeopleDOToVo(List<ActivityScheduleItemPeopleDO> listScheduleItemPeople, List<ActivityScheduleItemPeopleVO> listScheduleItemPeopleVO) {
        if (CollectionUtils.isNotEmpty(listScheduleItemPeople)) {
            for (ActivityScheduleItemPeopleDO scheduleItemPeopleDO : listScheduleItemPeople) {
                ActivityScheduleItemPeopleVO scheduleItemPeopleVO = new ActivityScheduleItemPeopleVO();
                BeanUtils.copyProperties(scheduleItemPeopleDO, scheduleItemPeopleVO);
                scheduleItemPeopleVO.setParticipantType(scheduleItemPeopleDO.getPeopleType());
                listScheduleItemPeopleVO.add(scheduleItemPeopleVO);
            }
        }
    }

    public  void participantClassification(List<ActivityScheduleItemPeopleVO> listScheduleItemPeopleVO,
                                           List<ActivityScheduleItemPeopleVO> listZtePeople,
                                           List<ActivityScheduleItemPeopleVO> listInterfacePeople,
                                           List<ActivityScheduleItemPeopleVO> listReferenceControllerPeople){
        if(CollectionUtils.isNotEmpty(listScheduleItemPeopleVO)){
            listScheduleItemPeopleVO.stream().forEach(scheduleItemPeopleVO ->{
                if (ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(scheduleItemPeopleVO.getPeopleType())){
                    listZtePeople.add(scheduleItemPeopleVO);
                }
                if (ScheduleItemPeopleTypeEnum.in(scheduleItemPeopleVO.getPeopleType(),ScheduleItemPeopleTypeEnum.ZTE_INTERFACE_PEOPLE,ScheduleItemPeopleTypeEnum.OTHER_INTERFACE_PEOPLE)){
                    listInterfacePeople.add(scheduleItemPeopleVO);
                }
                if (ScheduleItemPeopleTypeEnum.REFERENCE_CONTROLLER.isMe(scheduleItemPeopleVO.getPeopleType())) {
                    listReferenceControllerPeople.add(scheduleItemPeopleVO);
                }
            });
        }
    }

    public void getListClientParticipant(List<ActivityScheduleItemPeopleVO>  listScheduleItemPeopleVO,List<ActivityScheduleItemPeopleVO> listClientParticipant,
                                           Map<String, ActivityRelationCustPeopleDO> activityRelationCustPeopleDOMap){
        if(CollectionUtils.isNotEmpty(listScheduleItemPeopleVO)){
            List<ActivityScheduleItemPeopleVO> contactCollect = listScheduleItemPeopleVO.stream().filter(a -> ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.isMe(a.getPeopleType())).collect(Collectors.toList());
            contactCollect.forEach(contact ->{
                setContactLevelNo(activityRelationCustPeopleDOMap, contact);
            });
            List<ActivityScheduleItemPeopleVO> peopleSorts = contactCollect.stream().sorted(Comparator.comparing(ActivityScheduleItemPeopleVO::getContactLevelNo)).collect(Collectors.toList());
            listClientParticipant.addAll(peopleSorts);
        }
    }

    /**
     * 初始化客户联系人等级序号
     * @param activityRelationCustPeopleDOMap
     * @param contact
     * @return
     * <AUTHOR>
     * @date 2024/2/20
     */
    public void setContactLevelNo(Map<String, ActivityRelationCustPeopleDO> activityRelationCustPeopleDOMap, ActivityScheduleItemPeopleVO contact) {
        if (Objects.nonNull(activityRelationCustPeopleDOMap)){
            ActivityRelationCustPeopleDO peopleDO = activityRelationCustPeopleDOMap.get(contact.getPeopleNo());
            if (Objects.nonNull(peopleDO)){
                contact.setContactLevel(peopleDO.getContactLevel());
                contact.setContactLevelNo(ActivityContactLevelEnum.getDescByType(peopleDO.getContactLevel()));
            } else {
                contact.setContactLevelNo(ActivityContactLevelEnum.getDescByType(EMPTY_STR));
            }
        } else {
            contact.setContactLevelNo(ActivityContactLevelEnum.getDescByType(EMPTY_STR));
        }
    }

    void queryReception(ActivityDetailInfoVO detailInfoVO, ActivityInfoDO activityInfoDO) {
        detailInfoVO.setActivityRelationReceptionFlag(false);

        if (!ActivityTypeEnum.in(activityInfoDO.getActivityType(), CUSTOMER_VISIT_ACTIVITY)) {
            return;
        }

        String activityRowId = activityInfoDO.getRowId();
        List<ActivityReceptionMappingDO> receptionMappingList = activityReceptionMappingRepository.listReceptionByActivityRowId(activityRowId);
        if (CollectionUtils.isEmpty(receptionMappingList)) {
            detailInfoVO.setActivityReceiveInfoList(new ArrayList<>());
            return;
        }

        detailInfoVO.setActivityRelationReceptionFlag(true);
        Map<String, ActivityReceptionMappingDO> receptionDoMap = receptionMappingList.stream()
                .collect(Collectors.toMap(ActivityReceptionMappingDO::getReceiveId, i -> i, (u, v) -> u));
        List<ActivityReceiveInfo> activityReceiveInfoList = activityReceptionMappingRepository.getDetailByReceiveIdList(com.google.common.collect.Lists.newArrayList(receptionDoMap.keySet()));
        detailInfoVO.setActivityReceiveInfoList(activityReceiveInfoList.stream().peek(item -> item.setActivityRowId(activityRowId)).collect(Collectors.toList()));
    }

    /**
     * 查询展会信息
     * @param detailInfoVO
     * @param activityInfoDO
     */
    private void queryExhibition(ActivityDetailInfoVO detailInfoVO, ActivityInfoDO activityInfoDO) {
        if (!ActivityOriginTypeEnum.in(activityInfoDO.getOriginType(), EXHIBITION, CONFERENCE)) {
            return;
        }

        ExhibitionInfoDO exhibitionInfo = exhibitionInfoRepository.selectByPrimaryKey(activityInfoDO.getOriginRowId());
        if (exhibitionInfo != null) {
            List<String> exhibitionRowIds = Collections.singletonList(exhibitionInfo.getRowId());
            Map<String, List<ExhibitionRelationExpertDO>> expertMap = exhibitionRelationExpertRepository.queryExpertsWithExhibitionRowId(exhibitionRowIds);
            Map<String, List<ExhibitionRelationAttachmentDO>> attachmentMap = exhibitionRelationAttachmentRepository.queryAttachmentByExhibitionRowId(exhibitionRowIds);
            Map<String, List<ExhibitionRelationLeaderDO>> leaderMap = exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(exhibitionRowIds);

            StandardExhibitionDataSource data = new StandardExhibitionDataSource();
            data.setExpertMap(expertMap);
            data.setLeaderMap(leaderMap);
            data.setAttachmentMap(attachmentMap);
            detailInfoVO.setExhibitionInfo(exhibitionQuerySupportConvertor.toSelectExhibitionVO(data, exhibitionInfo));
            appendExhibitionExpertLabel(detailInfoVO);

            if (StringUtils.isNotBlank(activityInfoDO.getApprovalText())) {
                // 由于展示是业务部门审批
                detailInfoVO.setApprovalList(JSON.parseArray(activityInfoDO.getApprovalText(), ActivityApprovalParam.class)
                        .stream().filter(e -> LEADER_AUDITOR.isMe(e.getApprovalType())).collect(Collectors.toList()));
                // 前台需要展示名称
                Map<String, String> emp2Name = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(
                        detailInfoVO.getApprovalList().stream().map(ActivityApprovalParam::getEmpNo).collect(Collectors.toSet()))).getBo();
                if (emp2Name != null) {
                    detailInfoVO.getApprovalList().forEach(e -> {
                        e.setEmpName(emp2Name.get(e.getEmpNo()));
                        e.setEmpDesc(e.getEmpName() + e.getEmpNo());
                        e.setLevelName(ExhibitionApproveLevelEnum.getDescByType(String.valueOf(e.getLevel())));
                    });
                }
            }
        }
    }

    /**
     * 附加展会的专家标签
     * @param detailInfoVO
     */
    public void appendExhibitionExpertLabel(ActivityDetailInfoVO detailInfoVO) {
        SelectExhibitionVO exhibitionVO = detailInfoVO.getExhibitionInfo();
        List<ExhibitionRelationExpertVO> exhibitionExpertList = exhibitionVO.getExpertList();
        RelationPeopleInfoVO peopleInfo = detailInfoVO.getRelationPeopleInfo();
        boolean needAppendExpertLabel = peopleInfo != null && CollectionUtils.isNotEmpty(peopleInfo.getListZtePeopleInfo()) && CollectionUtils.isNotEmpty(exhibitionExpertList);
        if (needAppendExpertLabel) {
            String landId = HeadersProperties.getXLangId();
            Set<String> bizExperts = resourceBizExpertRepository.selectByEmployeeNos(peopleInfo.getListZtePeopleInfo().stream().map(ZtePeopleVO::getPeopleCode).collect(Collectors.toList()))
                    .stream().filter(e-> BooleanEnum.Y.isMe(e.getEnabledStatus())).map(ResourceBizExpertDO::getEmployeeNo).collect(Collectors.toSet());

            Set<String> expertEmps = exhibitionExpertList.stream().map(ExhibitionRelationExpertVO::getEmployeeNo).collect(Collectors.toSet());

            // 清空老的标签
            peopleInfo.getListZtePeopleInfo().stream().filter(e -> PeopleRoleLabelEnum.SAVANT.isMe(e.getPeopleLabel())).forEach(e -> {
                e.setPeopleLabelName(null);
                e.setPeopleLabel(null);
            });
            // 先后附加业务线、展会/大会专家
            peopleInfo.getListZtePeopleInfo()
                    .stream()
                    .filter(e -> bizExperts.contains(e.getPeopleCode()))
                    .forEach(e -> {
                        e.appendPeopleLabelName(PeopleRoleLabelEnum.SAVANT.getLabelByLangId(landId), PAUSE_MARK);
                        // 如果当前人员是公司领导，优先领导标签
                        if (!PeopleRoleLabelEnum.LEADER.isMe(e.getPeopleLabel())) {
                            e.setPeopleLabel(PeopleRoleLabelEnum.SAVANT.getCode());
                        }
                    });
            String expertLabelName = exhibitionQuerySupportConvertor.getExpertPeopleLabelName(exhibitionVO.getPlaceResourceType());
            peopleInfo.getListZtePeopleInfo()
                    .stream()
                    .filter(e -> expertEmps.contains(e.getPeopleCode()))
                    .forEach(e -> {
                        e.appendPeopleLabelName(expertLabelName, PAUSE_MARK);
                        // 如果当前人员是公司领导，优先领导标签
                        if (!PeopleRoleLabelEnum.LEADER.isMe(e.getPeopleLabel())) {
                            e.setPeopleLabel(PeopleRoleLabelEnum.SAVANT.getCode());
                        }
                    });
        }
    }

    /**
     * 查询操作权限信息
     *
     * @param activityRowId
     * @param detailInfoVO
     * @return void
     * <AUTHOR>
     * date: 2023/9/4 7:16
     */
    private void queryOperatorAuthInfo(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        List<ActivityInfoVO> activityInfoList = infoListQueryService.getActivityDetailDataSource(activityRowId);
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return;
        }
        detailInfoVO.setOperatorFlagInfo(activityInfoList.get(ZERO).getOperatorFlagInfo());
    }

    /**
     * 查询待办信息
     *
     * @param activityRowId 活动Id
     * @param detailInfoVO  活动详情
     * @return void
     * <AUTHOR>
     * date: 2023/6/2 11:07
     */
    private void queryPendingNoticeStatus(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        List<ActivityPendingNoticeDO> pendingNoticeDOList = activityPendingNoticeRepository.getMyNoticeByActivityRowId(activityRowId);
        if (CollectionUtils.isEmpty(pendingNoticeDOList)) {
            return;
        }
        PendingNoticeInfoVO pendingNoticeInfoVO = new PendingNoticeInfoVO();
        BeanUtils.copyProperties(pendingNoticeDOList.get(ZERO), pendingNoticeInfoVO);
        detailInfoVO.setPendingNoticeInfo(pendingNoticeInfoVO);
    }

    /**
     * 查询评价信息
     *
     * @param activityRowId 活动主键
     * @param detailInfoVO  详细信息
     * @return void
     * <AUTHOR>
     * date: 2023/6/1 11:23
     */
    private void queryEvaluateInfo(String activityRowId, ActivityDetailInfoVO detailInfoVO) {
        List<ZtePeopleVO> listLecturer = detailInfoVO.getRelationPeopleInfo().getListZtePeopleInfo()
                .stream().filter(e -> LECTURER.isMe(e.getPeopleType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listLecturer)) {
            return;
        }
        EvaluationAllInfoVO evaluationAllInfoVO = new EvaluationAllInfoVO();
        List<String> planIdList = listLecturer.stream().map(ZtePeopleVO::getPlanId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Set<String> peopleRowIdSet = listLecturer.stream().map(ZtePeopleVO::getRowId).collect(Collectors.toSet());
        Map<String, CustActivityCommPlanVO> planMap = getSolutionPlanMap(planIdList);
        Map<String, ActivityRelationAttachmentDO> attachmentMap = getPeopleAttachmentMap(activityRowId, peopleRowIdSet);
        // 人员评价对象
        supplementLectureEvaluateInfo(evaluationAllInfoVO, listLecturer);
        // 方案评价对象
        supplementPlanEvaluateInfo(activityRowId, evaluationAllInfoVO, planMap);
        // 附件评价对象
        supplementAttachmentEvaluateInfo(activityRowId, evaluationAllInfoVO, attachmentMap);
        detailInfoVO.setEvaluationAllInfoVO(evaluationAllInfoVO);
    }

    /**
     * 补充附件评价信息
     *
     * @param activityRowId       活动主键
     * @param evaluationAllInfoVO 评价展示信息
     * @param attachmentMap       附件信息
     * @return void
     * <AUTHOR>
     * date: 2023/6/1 11:57
     */
    private void supplementAttachmentEvaluateInfo(String activityRowId, EvaluationAllInfoVO evaluationAllInfoVO,
                                                  Map<String, ActivityRelationAttachmentDO> attachmentMap) {
        if (MapUtils.isEmpty(attachmentMap)) {
            return;
        }
        List<EvaluationInfoVO> listAttachmentEvaluation = Lists.newArrayList();
        attachmentMap.forEach((key, value) -> {
            EvaluationInfoVO evaluationInfoVO = new EvaluationInfoVO();
            evaluationInfoVO.setEvaluateObjectRowId(key);
            evaluationInfoVO.setEvaluateObjectName(value.getFileName());
            evaluationInfoVO.setEvaluateObjectUrl(value.getFileToken());
            evaluationInfoVO.setEvaluateType(EvaluateTypeEnum.ATTACHMENT.getCode());
            evaluationInfoVO.setActivityRowId(activityRowId);
            listAttachmentEvaluation.add(evaluationInfoVO);
        });
        evaluationAllInfoVO.setListAttachmentEvaluation(listAttachmentEvaluation);
    }

    /**
     * 补充引用方案信息
     *
     * @param activityRowId       活动主键Id
     * @param evaluationAllInfoVO 评价信息
     * @param planMap             引用方案信息
     * @return void
     * <AUTHOR>
     * date: 2023/6/1 11:50
     */
    private void supplementPlanEvaluateInfo(String activityRowId, EvaluationAllInfoVO evaluationAllInfoVO,
                                            Map<String, CustActivityCommPlanVO> planMap) {
        if (MapUtils.isEmpty(planMap)) {
            return;
        }
        List<EvaluationInfoVO> listPlanEvaluation = Lists.newArrayList();
        planMap.forEach((key, value) -> {
            EvaluationInfoVO evaluationInfoVO = new EvaluationInfoVO();
            evaluationInfoVO.setEvaluateObjectRowId(key);
            evaluationInfoVO.setEvaluateObjectName(value.getPlanNameCn());
            evaluationInfoVO.setEvaluateObjectUrl(value.getPlanUrl());
            evaluationInfoVO.setEvaluateType(EvaluateTypeEnum.SCHEME.getCode());
            evaluationInfoVO.setActivityRowId(activityRowId);
            listPlanEvaluation.add(evaluationInfoVO);
        });
        evaluationAllInfoVO.setListPlanEvaluation(listPlanEvaluation);
    }

    /**
     * 补充讲师评价信息
     *
     * @param evaluationAllInfoVO 活动所有评价信息
     * @param listLecturer        讲师列表
     * @return void
     * <AUTHOR>
     * date: 2023/6/1 11:37
     */
    private void supplementLectureEvaluateInfo(EvaluationAllInfoVO evaluationAllInfoVO, List<ZtePeopleVO> listLecturer) {
        List<EvaluationInfoVO> listLecturerEvaluation = Lists.newArrayList();
        for (ZtePeopleVO peopleVO : listLecturer) {
            EvaluationInfoVO evaluationInfoVO = new EvaluationInfoVO();
            evaluationInfoVO.setEvaluateObjectRowId(peopleVO.getRowId());
            evaluationInfoVO.setEvaluateObjectName(peopleVO.getPeopleName() + peopleVO.getPeopleCode());
            evaluationInfoVO.setEvaluateType(EvaluateTypeEnum.LECTURER.getCode());
            evaluationInfoVO.setActivityRowId(peopleVO.getActivityRowId());
            evaluationInfoVO.setEvaluateObjectUrl(peopleVO.getHeadImageUrl());
            listLecturerEvaluation.add(evaluationInfoVO);
        }
        evaluationAllInfoVO.setListLecturerEvaluation(listLecturerEvaluation);
    }


    /**
     * 查询客户基本信息
     *
     * @param rowId          活动ID
     * @param activityInfoDO 基本信息
     * @param detailInfoVO   明细信息对象
     * @author: 汤踊********
     * @date: 2023/5/23 11:13
     */
    private void queryActivityBaseInfo(String rowId, ActivityInfoDO activityInfoDO, ActivityDetailInfoVO detailInfoVO,
                                       List<ActivityRelationZtePeopleDO> listZtePeople) {
        queryBaseInfo(rowId, activityInfoDO, detailInfoVO);
        queryParticipants(rowId, detailInfoVO, listZtePeople, activityInfoDO.getActivityType());
        queryPendingNoticeStatus(rowId, detailInfoVO);
        queryRelationTalkAndDataLink(rowId, detailInfoVO);
        //补充人员综合信息
        supplementBasePeopleInfo(detailInfoVO, listZtePeople);
    }

    private void queryRelationTalkAndDataLink(String rowId, ActivityDetailInfoVO detailInfoVO) {
        // 查询附件资料
        TalkInfoVO talkInfoVO = new TalkInfoVO();
        TalkInfoVO dataLinkVO = new TalkInfoVO();
        List<ActivityRelationTalkDO> talkDOList = talkRepository.queryAllByActivityRowId(rowId);
        Optional<ActivityRelationTalkDO> talkDO = talkDOList.stream().filter(n -> DataTypeEnum.TALK.isMe(n.getType())).findAny();
        Optional<ActivityRelationTalkDO> dataLinkDO = talkDOList.stream().filter(n -> DataTypeEnum.DATA_LINK.isMe(n.getType())).findAny();

        if (talkDO.isPresent()) {
             talkInfoVO = assembleTalkInfo(talkDO.get());
        }
        if (dataLinkDO.isPresent()) {
            dataLinkVO = assembleTalkInfo(dataLinkDO.get());
        }
        // 查询附件
        List<ActivityRelationAttachmentDO> listAttachment = relationAttachmentRepository.queryAllByActivityRowId(rowId);
        // 装填附件至附件资料
        assembleAttachment(dataLinkVO, listAttachment.stream()
                .filter(e -> DATA_LINK.isMe(e.getAttachmentSceneType()))
                .collect(Collectors.toList()));
        // 装填附件资料
        detailInfoVO.setDataLink(dataLinkVO);
        // 装填附件到谈参
        assembleAttachment(talkInfoVO, listAttachment.stream()
                .filter(e -> AttachmentSceneTypeEnum.TALK.isMe(e.getAttachmentSceneType()))
                .collect(Collectors.toList()));
        // 装填谈参
        detailInfoVO.setTalkInfo(talkInfoVO);
    }

    /**
     * 关联附件到附件资料
     * @param talkInfoVO
     * @param listAttachment
     */
    private static void assembleAttachment(TalkInfoVO talkInfoVO,
                                           List<ActivityRelationAttachmentDO> listAttachment) {
        if (CollectionUtils.isEmpty(listAttachment)) {
            return;
        }
        talkInfoVO.setListAttachmentInfo(assembleAttachmentInfo(listAttachment));
    }

    /**
     * 组装谈参信息
     *
     * @param talkDO
     * @return TalkInfoVO
     * <AUTHOR>
     * date: 2023/6/8 15:37
     */
    private TalkInfoVO assembleTalkInfo(ActivityRelationTalkDO talkDO) {
        TalkInfoVO talkInfoVO = new TalkInfoVO();
        BeanUtils.copyProperties(talkDO, talkInfoVO);
        talkInfoVO.setListAttachmentInfo(Lists.newArrayList());
        return talkInfoVO;
    }

    /**
     * 查询基础信息
     *
     * @param rowId        活动行ID
     * @param detailInfoVO 明细信息
     * @author: 汤踊********
     * @date: 2023/5/23 11:13
     */
    private void queryBaseInfo(String rowId, ActivityInfoDO activityInfoDO, ActivityDetailInfoVO detailInfoVO) {
        List<ActivityCommunicationDirectionDO> listCommunication = communicationDirectionRepository.queryAllByActivityRowId(rowId);
        List<ActivityRelationProjectDO> listProject = projectRepository.queryAllProjectForActivity(rowId);
        //组合活动基本信息
        assemActivityBaseInfo(activityInfoDO, detailInfoVO);
        assemDirectionInfo(listCommunication, detailInfoVO);
        assemProjectInfo(listProject, detailInfoVO);
        transCode2Name(detailInfoVO);
    }

    /**
     * 编码转展示信息
     *
     * @param detailInfoVO 活动详情
     * @return void
     * <AUTHOR>
     * date: 2023/5/29 9:37
     */
    private void transCode2Name(ActivityDetailInfoVO detailInfoVO) {
        BaseInfoVO baseInfoVO = detailInfoVO.getBaseInfo();
        if (baseInfoVO == null) {
            return;
        }
        List<FastLookupDto> listParentFastlookp = listCommunicationByParentType();
        Map<String, List<FastLookupDto>> mapByParentType = listParentFastlookp.stream().collect(Collectors.groupingBy(FastLookupDto::getParentLookupType));
        List<FastLookupDto> listFastlookp = listCommunicationByType();
        Map<String, String> fastlookupMap = listFastlookp.stream().collect(Collectors.toMap(FastLookupDto::getLookupCode, FastLookupDto::getMeaning, (o1, o2) -> o1));
        Map<String, String> extensionTypeMap = MapUtils.isEmpty(mapByParentType) ? Maps.newHashMap()
                : mapByParentType.get(PARENT_LOOKUP_TYPE_EXTENSION_TYPE).stream()
                .collect(Collectors.toMap(FastLookupDto::getLookupCode, FastLookupDto::getMeaning, (o1, o2) -> o1));
        List<FastLookupDto> listFastlookupParent = MapUtils.isEmpty(mapByParentType) ? Lists.newArrayList()
                : mapByParentType.get(COMMUNICATION_DIRECTOR_LEVEL1);
        List<FastLookupDto> listFastlookupChild = MapUtils.isEmpty(mapByParentType) ? Lists.newArrayList()
                : mapByParentType.get(PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR);
        List<UserInfoDTO> userInfoDTOList = getApplyPeopleUserInfo(baseInfoVO);
        List<String> orgIds = StringUtils.isNotBlank(baseInfoVO.getApplyDepartmentNo()) ?
                Collections.singletonList(baseInfoVO.getApplyDepartmentNo()) : Collections.emptyList();
        Map<String, OrgInfoVO> orgInfoMap = this.getOrgInfoMap(orgIds);
        // 活动状态
        baseInfoVO.setActivityStatusName(Optional.ofNullable(fastlookupMap.get(baseInfoVO.getActivityStatus()))
                .orElse(StringUtils.EMPTY));
        // 活动类型
        baseInfoVO.setActivityTypeName(Optional.ofNullable(extensionTypeMap.get(baseInfoVO.getActivityType()))
                .orElse(StringUtils.EMPTY));
        // 活动方式
        baseInfoVO.setCommunicationWayName(Optional.ofNullable(fastlookupMap.get(baseInfoVO.getCommunicationWay()))
                .orElse(StringUtils.EMPTY));
        // 活动级别
        baseInfoVO.setCommunicationLevelName(Optional.ofNullable(fastlookupMap.get(baseInfoVO.getCommunicationLevel()))
                .orElse(StringUtils.EMPTY));
        // 活动方向
        transCommunicateDirection(baseInfoVO, listFastlookupParent, listFastlookupChild);
        // 申请人
        transApplyPeopleInfo(baseInfoVO, userInfoDTOList, orgInfoMap);
        // 增加迁移活动对应场景描述
        baseInfoVO.setOldDataScene(this.getOldDataScene(baseInfoVO.getRowId(), baseInfoVO.getOldDataSource()));
    }

    String getOldDataScene(String activityRowId, String oldDataSource) {
        if (StringUtils.isBlank(oldDataSource)) {
            return null;
        }
        // 非iCenter交流活动暂不考虑
        if (!COMMUNICATION.equals(oldDataSource)) {
            return ActivitySceneEnum.OTHER.getCode();
        }

        CustActivityHeaderVO custActivityHeaderVO = custActivityHeaderDao.getById(activityRowId);
        return custActivityHeaderVO.getSceneCode();
    }

    /**
     * 根据部门id获取部门信息
     *
     * @param orgIds
     * @return {@link Map< String, OrgInfoVO>}
     * <AUTHOR>
     * @date 2023/7/19 下午5:49
     */
    private Map<String, OrgInfoVO> getOrgInfoMap(List<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Maps.newHashMap();
        }
        try {
            return hrmUsercenterAdapter.getOrgInfoMap(ExternalConstant.IDTYPE_T0001, orgIds);
        } catch (Exception e) {
            logger.error("获取部门信息异常", e);
        }
        return Maps.newHashMap();
    }

    private List<FastLookupDto> listCommunicationByParentType() {
        FastLookupSearchDTO searchDTOByParentType = new FastLookupSearchDTO();
        List<String> listParentLookupType = Arrays.asList(COMMUNICATION_DIRECTOR_LEVEL1, PARENT_LOOKUP_TYPE_COMMUNICATION_DIRECTOR, PARENT_LOOKUP_TYPE_EXTENSION_TYPE);
        searchDTOByParentType.setListParentLookupType(listParentLookupType);
        return lookUpExtService.batchSearch(searchDTOByParentType);
    }

    private List<FastLookupDto> listCommunicationByType() {
        FastLookupSearchDTO searchDTOByType = new FastLookupSearchDTO();
        List<String> listLookupType = Arrays.asList(LOOKUP_TYPE_ACTIVITY_STATUS, LOOKUP_TYPE_COMMUNICATION_WAY, LOOKUP_TYPE_COMMUNICATION_LEVEL);
        searchDTOByType.setListLookupType(listLookupType);
        return lookUpExtService.batchSearch(searchDTOByType);
    }


    /**
     * 申请人信息
     *
     * @param baseInfoVO 基本信息
     * @return List<UserInfoDTO>    申请人信息
     * <AUTHOR>
     * date: 2023/6/7 18:55
     */
    private List<UserInfoDTO> getApplyPeopleUserInfo(BaseInfoVO baseInfoVO) {
        List<String> listApplyPeopleNo = Lists.newArrayList();
        String applyPeopleNo = baseInfoVO.getApplyPeopleNo();
        if (StringUtils.isBlank(applyPeopleNo)) {
          return Collections.emptyList();
        }
        listApplyPeopleNo.add(applyPeopleNo);
        return userCenterService.queryUserByIds(listApplyPeopleNo);
    }

    /**
     * 转换申请人信息
     *
     * @param baseInfoVO
     * @param userInfoDTOList
     * @return void
     * <AUTHOR>
     * date: 2023/5/31 15:10
     */
    private static void transApplyPeopleInfo(BaseInfoVO baseInfoVO, List<UserInfoDTO> userInfoDTOList,
                                             Map<String, OrgInfoVO> orgInfoMap) {
        if (CollectionUtils.isNotEmpty(userInfoDTOList)) {
            UserInfoDTO userInfoDTO = userInfoDTOList.get(ZERO);
            baseInfoVO.setApplyPeopleName(userInfoDTO.getName());
        } else {
            baseInfoVO.setApplyPeopleName(StringUtils.EMPTY);
        }
        OrgInfoVO orgInfoVO = orgInfoMap.get(baseInfoVO.getApplyDepartmentNo());
        if (Objects.nonNull(orgInfoVO)) {
            baseInfoVO.setApplyDepartmentName(StringUtils.isNotBlank(orgInfoVO.getHrOrgNamePath()) ?
                    orgInfoVO.getHrOrgNamePath() : StringUtils.EMPTY);
        }
    }

    /**
     * 转换交流方向
     *
     * @param baseInfoVO           活动基本信息
     * @param listFastlookupParent 父类交流方向
     * @param listFastlookupChild  子类交流方向
     * @return void
     * <AUTHOR>
     * date: 2023/5/31 15:10
     */
    private static void transCommunicateDirection(BaseInfoVO baseInfoVO, List<FastLookupDto> listFastlookupParent, List<FastLookupDto> listFastlookupChild) {
        List<CommunicateDirectionVO> communicateDirectionVOList = baseInfoVO.getListCommunicationDirection();
        if (CollectionUtils.isEmpty(communicateDirectionVOList)) {
            return;
        }
        List<List<String>> listCommunicationResult = ActivityInfoConverter.buildCommunicationResult(communicateDirectionVOList, listFastlookupParent, listFastlookupChild);
        baseInfoVO.setListCommunicationLevelCode(listCommunicationResult);
        baseInfoVO.setCommunicationDirectionName(communicateDirectionVOList.stream()
                .map(CommunicateDirectionVO::getCommunicationDirectionName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(SEMICOLON_CN)));
    }

    /**
     * 查询活动参与人
     *
     * @param rowId        活动行ID
     * @param detailInfoVO 明细信息
     * @author: 汤踊********
     * @date: 2023/5/23 11:13
     */
    private void queryParticipants(String rowId, ActivityDetailInfoVO detailInfoVO, List<ActivityRelationZtePeopleDO> listZtePeople, String acType) {
        List<CustUnitInfoVO> listCustVO = ActivityCustomerInfoConvert.convert2InfoVO(customerInfoRepository.queryAllByActivityRowId(rowId));
        List<ActivityRelationCustPeopleDO> custPeopleList= custPeopleRepository.queryAllByActivityRowId(rowId);
        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        List<ActivityRelationCustPeopleDO> internalCustList = custPeopleList.stream().filter(n -> fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<ActivityRelationCustPeopleDO> externalCustList = custPeopleList.stream().filter(n -> !fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<CustPeopleInfoVO> listCustPeopleInfo = transCustPeopleDo2InfoVo(externalCustList);
        // 过滤掉异常状态内部员工
        List<ActivityRelationCustPeopleDO> filteredInternalCust = dealAbnormalInternalCustPeople(internalCustList);
        listCustPeopleInfo.addAll(transInternalCustPeopleDo2InfoVo(filteredInternalCust));
        List<ActivityRelationSolutionDO> listSolution = solutionRepository.queryAllSolutionForActivity(rowId);
        List<ActivityRelationAttachmentDO> listAttachment = relationAttachmentRepository.queryAllByActivityRowId(rowId);
        List<CustUnitInfoVO> listCustInfoVo = assembleCustPeopleInfo(listCustPeopleInfo, assembleCustInfo(listCustVO, detailInfoVO));
        List<ZtePeopleVO> listZtePeopleVo = assembleZtePeopleInfo(listZtePeople, listSolution, listAttachment);

        fillCustBelongType(listCustInfoVo);

        String custParticipan = joinCustName(listCustInfoVo);
        //组合参与人信息
        buildParticipantsInfo(detailInfoVO, listCustInfoVo, listZtePeopleVo, custParticipan);
    }

    /**
     * 处理状态异常员工
     * @param internalCustList
     */
    private List<ActivityRelationCustPeopleDO> dealAbnormalInternalCustPeople(List<ActivityRelationCustPeopleDO> internalCustList) {
        if (CollectionUtils.isEmpty(internalCustList)) {
            return new ArrayList<>();
        }
        List<String> internalEmpNoList = internalCustList.stream().map(ActivityRelationCustPeopleDO::getContactNo).collect(Collectors.toList());
        Map<String, UserDetailVO> empInfoDetails = userCenterPgAdapter.getEmpInfoDetails(internalEmpNoList);
        List<String> allStatusMsg = HrmEmpStatusEnum.getAllStatusMsg();
        List<String> deleteEmpList = new ArrayList<>();
        for (String empNo : empInfoDetails.keySet()) {
            UserDetailVO userDetailVO = empInfoDetails.get(empNo);
            if (allStatusMsg.contains(userDetailVO.getEmpStatus())) {
                deleteEmpList.add(empNo);
            }
        }
        if (CollectionUtils.isEmpty(deleteEmpList)) {
            return internalCustList;
        }
        // 复制、编辑时剔除状态异常员工
        return internalCustList.stream().filter(n -> !deleteEmpList.contains(n.getContactNo())).collect(Collectors.toList());
    }

    /**
     * 填充客户所属于哪类部门（三营，政企，海外，其他）
     *
     * @param listCustInfoVo
     */
    private void fillCustBelongType(List<CustUnitInfoVO> listCustInfoVo) {
        List<String> orgIds = listCustInfoVo.stream().map(CustUnitInfoVO::getBelongBuId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (orgIds.isEmpty()) {
            return;
        }

        try {
            Map<String, OrgInfoVO> orgInfoMap = hrmUsercenterAdapter.getOrgInfoMap(ExternalConstant.IDTYPE_T0001, orgIds);
            for (CustUnitInfoVO vo : listCustInfoVo) {
                OrgInfoVO org = orgInfoMap.get(vo.getBelongBuId());
                if (org != null) {
                    vo.setBelongBuFullPath(org.getHrOrgNamePath());
                    vo.setCustBelongType(CustBelongDepartmentTypeEnum.getEnumByDeptName(org.getHrOrgNamePath(), config).getType());
                }
            }
        } catch (Exception e) {
            logger.error("查询部门信息异常：{}", orgIds, e);
        }
    }

    /**
     * 更新受限制主体状态
     *
     * @param detailInfoVO 活动详情数据
     * @return void
     * <AUTHOR>
     * date: 2023/6/8 16:53
     */
    private void updateCustInfo(ActivityDetailInfoVO detailInfoVO) {
        if (detailInfoVO == null) {
            return;
        }
        RelationPeopleInfoVO relationPeopleInfoVO = detailInfoVO.getRelationPeopleInfo();
        if (relationPeopleInfoVO == null) {
            return;
        }
        List<CustUnitInfoVO> listCust = relationPeopleInfoVO.getListCustInfo();
        if (CollectionUtils.isEmpty(listCust)) {
            return;
        }
        customerService.fillCustomerInfo(listCust, detailInfoVO.getBaseInfo().getActivityType());
    }

    /**
     * 组装参与人信息
     *
     * @param detailInfoVO    活动详情
     * @param listCustVo      客户列表
     * @param listZtePeopleVo 我司相关人
     * @param custParticipan  客户参与人拼接名称
     * @return void
     * <AUTHOR>
     * date: 2023/6/2 13:03
     */
    private static void buildParticipantsInfo(ActivityDetailInfoVO detailInfoVO, List<CustUnitInfoVO> listCustVo,
                                              List<ZtePeopleVO> listZtePeopleVo, String custParticipan) {
        RelationPeopleInfoVO relationPeopleInfoVO = new RelationPeopleInfoVO();
        relationPeopleInfoVO.setCustAllName(custParticipan);
        relationPeopleInfoVO.setListCustInfo(listCustVo);
        relationPeopleInfoVO.setListZtePeopleInfo(listZtePeopleVo);
        detailInfoVO.setRelationPeopleInfo(relationPeopleInfoVO);
    }

    /**
     * 补充基本信息中的人员情况
     *
     * @param detailInfoVO  活动基本信息
     * @param listZtePeople 我司相关人
     * @return void
     * <AUTHOR>
     * date: 2023/6/2 12:59
     */
    private void supplementBasePeopleInfo(ActivityDetailInfoVO detailInfoVO, List<ActivityRelationZtePeopleDO> listZtePeople) {
        int noMainCustNum = countNoMainCustNum(detailInfoVO);
        BaseInfoVO baseInfo = detailInfoVO.getBaseInfo();
        String lpContactPeople = joinSameTypePeopleName(listZtePeople, LPCONTACTPEOPLE);
        String informers = joinSameTypePeopleName(listZtePeople, INFORMED);
        String organizers = joinSameTypePeopleName(listZtePeople, ORGANIZER);
        Boolean isFollow = checkFollowStatus(listZtePeople);

        baseInfo.setOrganizers(organizers);
        baseInfo.setLargeTeamPersons(lpContactPeople);
        baseInfo.setInformers(informers);
        baseInfo.setNoMainCustNum(noMainCustNum);
        baseInfo.setIsFollow(isFollow);
        detailInfoVO.setBaseInfo(baseInfo);
    }

    /**
     * 计算非主客户数量
     *
     * @param detailInfoVO 活动详情
     * @return int
     * <AUTHOR>
     * date: 2023/6/8 16:33
     */
    private int countNoMainCustNum(ActivityDetailInfoVO detailInfoVO) {
        List<CustUnitInfoVO> listCustVo = detailInfoVO.getRelationPeopleInfo().getListCustInfo();
        if (CollectionUtils.isEmpty(listCustVo)) {
            return ZERO;
        }
        return listCustVo.size() - NumberConstant.ONE;
    }

    /**
     * 拼接同一个类型的人员名字编码
     *
     * @param listZtePeople
     * @param peopleType
     * @return String
     * <AUTHOR>
     * date: 2023/6/2 12:38
     */
    private static String joinSameTypePeopleName(List<ActivityRelationZtePeopleDO> listZtePeople,
                                                 ActivityPeopleTypeEnum peopleType) {
        if (CollectionUtils.isEmpty(listZtePeople) || peopleType == null) {
            return StringUtils.EMPTY;
        }
        return listZtePeople.stream()
                .filter(e -> peopleType.isMe(e.getPeopleType()))
                .map(e -> e.getPeopleName() + e.getPeopleCode())
                .collect(Collectors.joining(SEMICOLON_CN));
    }

    /**
     * 校验关注状态
     *
     * @param listZtePeople 我司活动相关人列表
     * @return Boolean
     * <AUTHOR>
     * date: 2023/6/2 11:00
     */
    private Boolean checkFollowStatus(List<ActivityRelationZtePeopleDO> listZtePeople) {
        if (CollectionUtils.isEmpty(listZtePeople)) {
            return false;
        }
        String empNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        return CollectionUtils.isNotEmpty(
                listZtePeople.stream()
                        .filter(e -> FOLLOW.isMe(e.getPeopleType()) && StringUtils.equals(empNo, e.getPeopleCode()))
                        .collect(Collectors.toList()));
    }


    /**
     * 查询纪要反馈信息
     *
     * @param rowId         活动行ID
     * @param detailInfoVO  明细信息对象
     * @param listZtePeople 中兴参与人
     * @author: 汤踊********
     * @date: 2023/5/23 11:13
     */
    private void querySummaryInfo(String rowId, ActivityDetailInfoVO detailInfoVO,
                                  List<ActivityRelationZtePeopleDO> listZtePeople) {
        List<ZtePeopleVO> listParticipant = detailInfoVO.getRelationPeopleInfo().getListZtePeopleInfo();
        SummaryInfoVO summaryInfoVO = getSummaryInfoVO(rowId, listZtePeople, listParticipant);
        detailInfoVO.setSummaryInfo(summaryInfoVO);
        detailInfoVO.getRelationPeopleInfo().setListZtePeopleInfo(listParticipant);
    }

    /**
     * 查询会议纪要信息
     *
     * @param rowId           活动Id
     * @param listZtePeople   中兴活动有关人
     * @param listParticipant 中兴参与人
     * @return SummaryInfoVO
     * <AUTHOR>
     * date: 2023/5/26 19:04
     */
    private SummaryInfoVO getSummaryInfoVO(String rowId, List<ActivityRelationZtePeopleDO> listZtePeople,
                                           List<ZtePeopleVO> listParticipant) {
        List<ActivitySummaryDO> listSummary = summaryRepository.queryAllSummaryForActivity(rowId);
        if (CollectionUtils.isEmpty(listSummary)) {
            return null;
        }
        List<ActivitySummaryIssueDO> listSummaryIssue = summaryIssueRepository.queryAllSummaryIssueForActivity(rowId);
        List<ActivityRelationAttachmentDO> listAttachment = relationAttachmentRepository
                .queryByActivityRowIdAndSummaryId(rowId, listSummary.get(ZERO).getRowId());
        SummaryInfoVO summaryInfoVO = new SummaryInfoVO();
        assembleSummaryInfo(listSummary, summaryInfoVO);
        assembleSummaryIssueInfo(listSummaryIssue, summaryInfoVO);
        summaryInfoVO.setListAttachmentInfo(assembleAttachmentInfo(listAttachment));
        Map<String, UserInfoDTO> userInfoDTOMap = getAllUserInfoDTOS(summaryInfoVO, listZtePeople);
        supplementZtePeople(listParticipant, userInfoDTOMap);
        setRecipient(summaryInfoVO, listZtePeople, userInfoDTOMap);
        setSummaryInfoPeople(summaryInfoVO, userInfoDTOMap);
        setOrganizers(summaryInfoVO, listZtePeople);
        return summaryInfoVO;
    }

    /**
     * 补充组织者信息
     *
     * @param summaryInfoVO 会议纪要信息
     * @param listZtePeople 中兴参与人
     * @return void
     * <AUTHOR>
     * date: 2023/6/7 14:24
     */
    private void setOrganizers(SummaryInfoVO summaryInfoVO, List<ActivityRelationZtePeopleDO> listZtePeople) {
        String organizers = joinSameTypePeopleName(listZtePeople, ORGANIZER);
        summaryInfoVO.setOrganizers(organizers);
    }

    /**
     * 设置收件人
     *
     * @param summaryInfoVO   会议纪要详情
     * @param ztePeopleDOList 中兴活动相关人员
     * @param userInfoDTOMap  用户信息Map
     * @return void
     * <AUTHOR>
     * date: 2023/5/26 20:13
     */
    private void setRecipient(SummaryInfoVO summaryInfoVO, List<ActivityRelationZtePeopleDO> ztePeopleDOList,
                              Map<String, UserInfoDTO> userInfoDTOMap) {
        if (CollectionUtils.isEmpty(ztePeopleDOList)) {
            return;
        }
        List<ZtePeopleVO> listRecipient = Lists.newArrayList();
        Set<String> existSet = Sets.newHashSet();
        for (ActivityRelationZtePeopleDO ztePeopleDO : ztePeopleDOList) {
            if (!RECIPIENT.isMe(ztePeopleDO.getPeopleType())) {
                continue;
            }
            String peopleCode = ztePeopleDO.getPeopleCode();
            if (existSet.contains(peopleCode)) {
                continue;
            }
            existSet.add(peopleCode);
            ZtePeopleVO ztePeopleVO = new ZtePeopleVO();
            BeanUtils.copyProperties(ztePeopleDO, ztePeopleVO);
            UserInfoDTO userInfoDTO = userInfoDTOMap.get(peopleCode);
            if (userInfoDTO != null) {
                ztePeopleVO.setHeadImageUrl(userInfoDTO.getHeadIcon());
            }
            ztePeopleVO.setPeopleTypeName(ActivityPeopleTypeEnum.getDescByCode(ztePeopleDO.getPeopleType()));
            listRecipient.add(ztePeopleVO);
        }
        summaryInfoVO.setListRecipient(listRecipient);
    }

    /**
     * 补充中兴参与人信息
     *
     * @param summaryInfoVO 活动详情
     * @param listZtePeople 中兴参与人列表
     * @return void
     * <AUTHOR>
     * date: 2023/5/26 16:37
     */
    private Map<String, UserInfoDTO> getAllUserInfoDTOS(SummaryInfoVO summaryInfoVO, List<ActivityRelationZtePeopleDO> listZtePeople) {
        List<String> listEmpNo = Lists.newArrayList();
        List<String> listZtePeopleEmpNo = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(listZtePeople)) {
            listZtePeopleEmpNo = listZtePeople.stream()
                    .map(ActivityRelationZtePeopleDO::getPeopleCode).distinct().collect(Collectors.toList());
        }
        List<String> listResponsible = Lists.newArrayList();
        List<String> listQuestioner = Lists.newArrayList();
        for (SummaryIssueInfoVO issueInfoVO : summaryInfoVO.getListSummaryIssues()) {
            listResponsible.addAll(Arrays.asList(issueInfoVO.getIssueResponsible().split(COMMA)));
            listQuestioner.addAll(Arrays.asList(issueInfoVO.getIssueProposer().split(COMMA)));
        }
        listEmpNo.add(summaryInfoVO.getCreatedBy());
        listEmpNo.add(summaryInfoVO.getLastUpdatedBy());
        listEmpNo.addAll(listResponsible);
        listEmpNo.addAll(listQuestioner);
        listEmpNo.addAll(listZtePeopleEmpNo);
        listEmpNo = listEmpNo.stream().distinct().collect(Collectors.toList());
        List<UserInfoDTO> userInfoDTOList = userCenterService.queryUserByIds(listEmpNo);
        return userInfoDTOList.stream()
                .collect(Collectors.toMap(UserInfoDTO::getEmployeeShortId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 设置遗留问题责任人
     *
     * @param summaryInfoVO  会议纪要详情
     * @param userInfoDTOMap 用户信息Map
     * @return void
     * <AUTHOR>
     * date: 2023/5/26 18:09
     */
    private void setSummaryInfoPeople(SummaryInfoVO summaryInfoVO, Map<String, UserInfoDTO> userInfoDTOMap) {
        Map<String, String> peopleMap = Maps.newHashMap();
        if (MapUtils.isEmpty(userInfoDTOMap)) {
            return;
        }
        for (SummaryIssueInfoVO issueInfoVO : summaryInfoVO.getListSummaryIssues()) {
            String responsibleCode = issueInfoVO.getIssueResponsible();
            String proposerCode = issueInfoVO.getIssueProposer();
            String responsible = peopleMap.get(responsibleCode);
            String proposer = peopleMap.get(proposerCode);
            if (StringUtils.isBlank(responsible)) {
                List<String> listResponsibleCode = Arrays.asList(responsibleCode.split(COMMA));
                responsible = String.join(PAUSE_MARK, joinNameCode(userInfoDTOMap, listResponsibleCode));
                peopleMap.put(responsibleCode, responsible);
            }
            issueInfoVO.setIssueResponsible(responsible);
            if (StringUtils.isBlank(proposer)) {
                List<String> listQuestionerCode = Arrays.asList(proposerCode.split(COMMA));
                proposer = String.join(PAUSE_MARK, joinNameCode(userInfoDTOMap, listQuestionerCode));
                peopleMap.put(proposerCode, proposer);
            }
            issueInfoVO.setIssueProposer(proposer);
        }
        UserInfoDTO createdBy = userInfoDTOMap.get(summaryInfoVO.getCreatedBy());
        UserInfoDTO lastUpdatedBy = userInfoDTOMap.get(summaryInfoVO.getLastUpdatedBy());
        summaryInfoVO.setCreatedByName(createdBy == null ? StringUtils.EMPTY : createdBy.getName());
        summaryInfoVO.setLastUpdatedByName(lastUpdatedBy == null ? StringUtils.EMPTY : lastUpdatedBy.getName());
    }

    /**
     * 拼接名称编码
     *
     * @param userInfoDTOMap 用户信息
     * @param listCode       用户短工号
     * @return List<String>
     * <AUTHOR>
     * date: 2023/5/26 17:47
     */
    private static List<String> joinNameCode(Map<String, UserInfoDTO> userInfoDTOMap, List<String> listCode) {
        List<String> nameCodeList = Lists.newArrayList();
        for (String people : listCode) {
            UserInfoDTO userInfoDTO = userInfoDTOMap.get(people);
            if (userInfoDTO != null) {
                people = userInfoDTO.getName() + people;
                nameCodeList.add(people);
            }
        }
        return nameCodeList;
    }

    /**
     * 补充中兴参与人信息
     *
     * @param ztePeopleVOList 活动参与人
     * @param userInfoDTOMap  用户信息表
     * @return void
     * <AUTHOR>
     * date: 2023/5/26 17:55
     */
    private void supplementZtePeople(List<ZtePeopleVO> ztePeopleVOList, Map<String, UserInfoDTO> userInfoDTOMap) {
        if (CollectionUtils.isEmpty(ztePeopleVOList) || MapUtils.isEmpty(userInfoDTOMap)) {
            return;
        }
        for (ZtePeopleVO peopleVO : ztePeopleVOList) {
            UserInfoDTO userInfoDTO = userInfoDTOMap.get(peopleVO.getPeopleCode());
            if (userInfoDTO == null) {
                continue;
            }
            peopleVO.setHeadImageUrl(userInfoDTO.getHeadIcon());
        }
    }

    /**
     * 查询活动基本信息
     *
     * @param baseInfoVO 基本信息对象
     * @return String 活动Id
     * @author: 汤踊********
     * @date: 2023/5/23 11:13
     */
    @RedisLock(key = "'ACTIVITY_BASE_INFO_SAVE'", timeout = 60 * 1000)
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    @Override
    public String saveActivityBaseInfo(ActivityBaseInfoVO baseInfoVO) {
        if (StringUtils.isBlank(baseInfoVO.getActivityTitle())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_TITLE_CAN_NOT_BE_EMPTY));
        }

        if(StringUtils.isBlank(baseInfoVO.getRowId())){
            baseInfoVO.setIsNewCreateOrCopy(true);
        }
        String activityId = StringUtils.isNotBlank(baseInfoVO.getRowId()) ? baseInfoVO.getRowId() : (keyIdService.getKeyId());

        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityId);
        if (Objects.nonNull(activityInfoDO) && !ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), DRAFT)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_STATUS_ERROR));
        }
        //关联日程安排rowId和参与人activityScheduleItemRowId 和谈参的scene_origin_row_id
        assignScheduleAndPeopleInfoRowId(baseInfoVO, activityId, baseInfoVO.getIsNewCreateOrCopy());
        ActivityBO activityBO = activityInfoVoToDo(baseInfoVO, activityId, getCreatedByInfo());
        fillApplyFullDepartCode(activityBO.getActivityInfoDO());
        fillZtePeopleLabel(activityBO);
        fillApprovalInfo(baseInfoVO, activityBO);
        fillActivityOpportunityRelationInfo(baseInfoVO, activityBO);
        if (Objects.isNull(activityInfoDO)) {
            activityBO.getActivityInfoDO().setActivityStatus(DRAFT.getCode());
            insertActivityInfo(activityBO);
            saveActivityToLifeCycle(activityBO.getActivityInfoDO().getRowId(), StringUtils.EMPTY, DRAFT.getCode(), null);
        } else {
            activityBO.getActivityInfoDO().setActivityStatus(DRAFT.getCode());
            fillFromDbBeforeUpdate(activityBO, activityInfoDO);
            updateActivityInfo(activityBO);
        }
        return activityId;
    }

    public void assignScheduleAndPeopleInfoRowId(ActivityBaseInfoVO baseInfoVO, String activityId,Boolean isNewCreateOrCopy) {

        if (CollectionUtils.isEmpty(baseInfoVO.getListScheduleInfo())) {
            return;
        }
        //展会、大会获取专家和领导人员
        List<String> listExpertOrLeader = getExpertOrLeaderList(baseInfoVO);

        baseInfoVO.getListScheduleInfo().stream().forEach(item -> {
            //如果是新建/复制，则日程直接生成新的rowId。如果是编辑，则要么用原来的rowId，没有的话生成新的
            item.setRowId(isNewCreateOrCopy ? keyIdService.getKeyId() : (Optional.ofNullable(item.getRowId())
                    .orElse(keyIdService.getKeyId())));
            if (CollectionUtils.isNotEmpty(item.getListSchedulePeopleInfo())) {
                item.getListSchedulePeopleInfo().stream().forEach(e -> {
                    //活动类型是展会，日程参与人不是展会绑定的专家、领导。则置空当年item的peopleLabel
                    if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE) && !listExpertOrLeader.contains(e.getPeopleNo())) {
                        e.setPeopleLabel(null);
                    }
                    e.setActivityRowId(activityId);
                    e.setActivityScheduleItemRowId(item.getRowId());
                });
            }
            if (Objects.nonNull(item.getScheduleRelationAttachment()) && StringUtils.isNotEmpty(item.getScheduleRelationAttachment().getFileUrl())) {
                item.getScheduleRelationAttachment().setActivityRowId(activityId);
                item.getScheduleRelationAttachment().setSceneOriginRowId(item.getRowId());
            }
        });
    }

    private List<String> getExpertOrLeaderList(ActivityBaseInfoVO baseInfoVO) {
        List<String> listExpertOrLeader = Lists.newArrayList();
        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE)) {
            List<String> originRowIds = baseInfoVO.getOriginRowId() == null ? null : Collections.singletonList(baseInfoVO.getOriginRowId());
            Map<String, List<ExhibitionRelationExpertDO>> mapRelationExpert = exhibitionRelationExpertRepository.queryExpertsWithExhibitionRowId(originRowIds);
            List<ExhibitionRelationExpertDO> listRelationExpert = Optional.ofNullable(mapRelationExpert.get(baseInfoVO.getOriginRowId())).orElse(Collections.emptyList());

            Map<String, List<ExhibitionRelationLeaderDO>> mapRelationLeader = exhibitionRelationLeaderRepository.queryLeadersWithExhibitionRowId(originRowIds);
            List<ExhibitionRelationLeaderDO> listRelationLeader = Optional.ofNullable(mapRelationLeader.get(baseInfoVO.getOriginRowId())).orElse(Collections.emptyList());

            listExpertOrLeader = listRelationExpert.stream().map(ExhibitionRelationExpertDO::getEmployeeNo).collect(Collectors.toList());
            List<String> listLeader = listRelationLeader.stream().map(ExhibitionRelationLeaderDO::getEmployeeNo).collect(Collectors.toList());
            listExpertOrLeader.addAll(listLeader);
        }
        return listExpertOrLeader;
    }

    /**
     * 提交后的流程 1、发送活动提交的工作通知； 2、创建日程并发送工作通知。3、启动审批流或创建待办
     *
     * @param baseInfoVO 基本信息对象
     * @return String 活动Id
     */
    @RedisLock(key = "'ACTIVITY_BASE_INFO_SAVE'", timeout = 60 * 1000)
    @Override
    public BizResult<String> submitActivityBaseInfo(ActivityBaseInfoVO baseInfoVO) {
        checkService.checkActivitySubmitData(baseInfoVO);

        if(StringUtils.isBlank(baseInfoVO.getRowId())){
            baseInfoVO.setIsNewCreateOrCopy(true);
        }
        String activityId = StringUtils.isNotBlank(baseInfoVO.getRowId()) ? baseInfoVO.getRowId() : (keyIdService.getKeyId());
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityId);
        if (Objects.nonNull(activityInfoDO) && !ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), DRAFT)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(ACTIVITY_STATUS_ERROR));
        }
        setInternalCustPeoplePositionName(baseInfoVO.getListCustPeopleInfo(), baseInfoVO.getActivityType());
        //关联日程安排rowId和参与人activityScheduleItemRowId 和谈参的scene_origin_row_id
        assignScheduleAndPeopleInfoRowId(baseInfoVO, activityId,baseInfoVO.getIsNewCreateOrCopy());
        // 补充创建人信息
        ActivityBO activityBO = activityInfoVoToDo(baseInfoVO, activityId, getCreatedByInfo());
        //区分  其他项目、暂无关联项目、正常项目
        assembleProjectInfo(activityId, activityBO, baseInfoVO);
        fillApplyFullDepartCode(activityBO.getActivityInfoDO());
        fillZtePeopleLabel(activityBO);
        fillApprovalInfo(baseInfoVO, activityBO);
        fillActivityOpportunityRelationInfo(baseInfoVO, activityBO);
        /*
         由于启动审批后，审批中心那边已经启动审批，且可能会发送审批节点创建的事件。
         如果创建活动事务还未提交，则审批节点创建事件会接收失败，导致活动审批流程走不下去（测试环境的kafka不太稳定）
         故，活动应该先提交事务。
         */
        transactionTemplate.execute(st -> {
            try {
                if (Objects.isNull(activityInfoDO)) {
                    insertActivityInfo(activityBO);
                    submitActivityToLifeCycle(activityBO, Boolean.TRUE);
                } else {
                    fillFromDbBeforeUpdate(activityBO, activityInfoDO);
                    updateActivityInfo(activityBO);
                    submitActivityToLifeCycle(activityBO, Boolean.FALSE);
                }
                insertActivityLog(activityBO, activityId);
            } catch (Exception e) {
                st.setRollbackOnly();
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(TIPS_COMMON_BUSINESS_ERROR));
            }
            return true;
        });
        return submitNoticeExternal(baseInfoVO, activityBO);
    }

    /**
     * 填充职位信息
     * @param listCustPeopleInfo
     * @param acType
     */
    private void setInternalCustPeoplePositionName(List<CustPeopleInfoVO> listCustPeopleInfo, String acType) {
        Map<String, DictLanguageDTO> fieldDictMap = dictService.exactQueryMapByType(INTERNAL_CUSTOMER_TYPE + UNDER_LINE + acType);
        List<CustPeopleInfoVO> internalCustList = listCustPeopleInfo.stream().filter(n -> fieldDictMap.containsKey(n.getCustomerCode())).collect(Collectors.toList());
        List<String> internalEmpNoList = internalCustList.stream().map(CustPeopleInfoVO::getContactNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalEmpNoList)) {
            return;
        }
        Map<String, UserDetailVO> empInfoDetails = userCenterPgAdapter.getEmpInfoDetails(internalEmpNoList);
        if (MapUtils.isEmpty(empInfoDetails)) {
            return;
        }
        for (CustPeopleInfoVO internalPeople : listCustPeopleInfo) {
            if (empInfoDetails.containsKey(internalPeople.getContactNo())) {
                UserDetailVO userDetailVO = empInfoDetails.get(internalPeople.getContactNo());
                internalPeople.setPositionName(StringUtils.isEmpty(userDetailVO.getPostName()) ? StringUtils.EMPTY : userDetailVO.getPostName().split(REG)[0]);
            }
        }
    }

    /**
     * 填充审批部门全路径编码
     * @param activityInfo
     */
    private void fillApplyFullDepartCode(ActivityInfoDO activityInfo) {
        if (StringUtils.isBlank(activityInfo.getApplyDepartmentNo())) {
            return;
        }

        Map<String, OrgInfoVO> orgMap = getOrgInfoMap(Collections.singletonList(activityInfo.getApplyDepartmentNo()));
        OrgInfoVO vo = orgMap.get(activityInfo.getApplyDepartmentNo());
        if (vo != null) {
            activityInfo.setApplyFullDepartmentNo(vo.getOrgIDPath());
        }
    }

    /**
     * 填充我司参与人人员标签信息（如果外部没有传入，则自动识别。比如：APP那边是不会传入的）
     *
     * @param activityBO
     */
    void fillZtePeopleLabel(ActivityBO activityBO) {
        // 我司参与人
        List<ActivityRelationZtePeopleDO> zteList = activityBO.getListZtePeopleInfo().stream()
                .filter(e -> StringUtils.isBlank(e.getPeopleLabel()) && ActivityPeopleTypeEnum.isActivityParticipants(e.getPeopleType()))
                .collect(Collectors.toList());

        List<String> empNoList = zteList.stream().map(ActivityRelationZtePeopleDO::getPeopleCode).distinct().collect(Collectors.toList());

        // 先确认领导的标签2
        Map<String, PersonInfoDTO> personMap = hrmUserCenterSearchService.fetchPersonInfoAndPosition(MsaRpcRequestUtil.createWithCurrentUser(new HashSet<>(empNoList))).getBo();
        Set<String> leaderEmpNos = ztePeopleLabelConvert.fetchLeaderNoSet(personMap);
        for (ActivityRelationZtePeopleDO ztePeople : zteList) {
            if (leaderEmpNos.contains(ztePeople.getPeopleCode())) {
                ztePeople.setPeopleLabel(LEADER.getCode());
            }
        }

        // 剩余的确认专家的标签
        empNoList.removeAll(leaderEmpNos);

        // 查领导、查专家（业务/展会）
        Set<String> bizExperts = resourceBizExpertRepository.selectByEmployeeNos(empNoList).stream().map(ResourceBizExpertDO::getEmployeeNo).collect(Collectors.toSet());
        for (ActivityRelationZtePeopleDO ztePeople : zteList) {
            if (bizExperts.contains(ztePeople.getPeopleCode())) {
                ztePeople.setPeopleLabel(SAVANT.getCode());
            }
        }

        if (JOIN_EXHIBITION.isMe(activityBO.getActivityInfoDO().getActivityType())) {
            // 剩余的确认是否是展会/大会中的专家
            empNoList.removeAll(bizExperts);
            Map<String, ExhibitionRelationExpertDO> exhibitionExpertMap = ztePeopleLabelConvert.fetchExpertFromExhibition(activityBO.getActivityInfoDO().getOriginRowId());
            for (ActivityRelationZtePeopleDO ztePeople : zteList) {
                if (exhibitionExpertMap.containsKey(ztePeople.getPeopleCode())) {
                    ztePeople.setPeopleLabel(SAVANT.getCode());
                }
            }
        }
    }

    /**
     * 填充审批信息
     * @param bo
     */
    private void fillApprovalInfo(ActivityBaseInfoVO baseInfoVO, ActivityBO bo) {
        if (CollectionUtils.isEmpty(baseInfoVO.getApprovalList())) {
            return;
        }

        // 目前合规审批是自动带出来的，部门领导可以自选择
        List<ActivityApprovalParam> list = baseInfoVO.getApprovalList().stream().filter(
                e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getApprovalType())).collect(Collectors.toList());

        // 如果需要合规审批
        if (bo.getNeedApprove()) {
            // 和审批模块约定，approvalText存所有审批人信息
            EmployeeInfoDTO complianceManager = activityApprovalInfoService.queryComplianceManager(baseInfoVO.getApplyDepartmentNo());
            ActivityApprovalParam complianceApprover = new ActivityApprovalParam();
            complianceApprover.setEmpNo(complianceManager.getEmpUIID());
            complianceApprover.setApprovalType(COMPLIANCE_AUDITOR.getCode());
            list.add(complianceApprover);
        }
        // 为后续方法调用审批提供参数
        baseInfoVO.setApprovalList(list);

        ActivityInfoDO activityInfo = bo.getActivityInfoDO();
        activityInfo.setApprovalText(JSON.toJSONString(list));
    }

    private void fillActivityOpportunityRelationInfo(ActivityBaseInfoVO baseInfo, ActivityBO bo) {
        String activityOpportunityCode = baseInfo.getOpportunityCode();
        if (StringUtils.isBlank(activityOpportunityCode)) {
            return;
        }

        String activityRowId = bo.getActivityInfoDO().getRowId();
        Set<String> relationActivityIdSet = Optional.ofNullable(activityOpportunityRepository.getMapByOpportunityIds(com.google.common.collect.Lists.newArrayList(activityOpportunityCode))
                .get(activityOpportunityCode)).orElse(new HashSet<>());
        if (relationActivityIdSet.contains(activityRowId)) {
            return;
        }

        ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
        activityOpportunityInfoDO.setRowId(keyIdService.getKeyId());
        activityOpportunityInfoDO.setActivityRowId(activityRowId);
        activityOpportunityInfoDO.setOpportunityId(activityOpportunityCode);
        activityOpportunityInfoDO.setBindFlag(NumberConstant.ONE);
        activityOpportunityInfoDO.setActivityBindStatus(DRAFT.getCode());
        bo.setActivityOpportunityInfoDOList(com.google.common.collect.Lists.newArrayList(activityOpportunityInfoDO));
    }

    /**
     * 插入活动信息日志
     *
     * @param activityBO 活动信息
     * @param activityId
     * @return void
     * <AUTHOR>
     * date: 2023/9/4 7:05
     */
    private void insertActivityLog(ActivityBO activityBO, String activityId) {
        ActivityLogDO record = new ActivityLogDO();
        record.setData(JSON.toJSONString(activityBO));
        record.setContactPrimaryId(activityId);
        record.setContactType(ContactTypeEnum.ACTIVITY_BASE_INFO.getCode());
        logRepository.insert(record);
    }

    private void rollBackAcitivity(ActivityBO activityBO) {
        ActivityInfoDO activityInfoDO = activityBO.getActivityInfoDO();
        activityInfoDO.setActivityStatus(DRAFT.getCode());
        infoRepository.updateByPrimaryKey(activityInfoDO);
    }

    /**
     * 创建关联事件
     *
     * @param activityBO 活动信息BO
     * @return void
     * <AUTHOR>
     * date: 2023/6/29 14:18
     */
    private void createEvent(ActivityBO activityBO) {
        // 创建 删除提单人角色事件
        createDeleteCreatedByRoleEvent(activityBO);
    }

    /**
     * 创建删除提单人角色事件
     * @param activityBO
     * @return void
     * <AUTHOR>
     * date: 2023/8/11 9:57
     */
    private void createDeleteCreatedByRoleEvent(ActivityBO activityBO) {
        ActivityRelationEventVO eventVO = ActivityEventConvert.buildEventVoByActivityDo(activityBO.getActivityInfoDO(), new Date(), StringUtils.EMPTY);
        ztePeopleService.createDeleteCreatedByRoleEvent(BizRequestUtil.createWithCurrentUser(eventVO));
    }

    /**
     * 获取创建人信息
     *
     * @param
     * @return UserInfoDTO
     * <AUTHOR>
     * date: 2023/6/21 16:25
     */
    private UserInfoDTO getCreatedByInfo() {
        List<String> createBy = com.google.common.collect.Lists.newArrayList(BizRequestUtil.createWithCurrentUser().getEmpNo());
        List<UserInfoDTO> res = userCenterService.queryUserByIds(createBy);
        return CollectionUtils.isEmpty(res) ? new UserInfoDTO() : res.get(ZERO);
    }

    /**
     * 当前是否超时 结束时间 > 当前时间
     * @param baseInfoVO
     * @return
     */
    public boolean currTimeout( ActivityBaseInfoVO baseInfoVO) {
        // 不是展会大会 直接比较时间
        if(!NoticeUtil.isExhibitionOrConference(baseInfoVO.getActivityType())) {
            return new Date().after(baseInfoVO.getEndTime());
        }
        // 展会大会 获取时区后 比较是否超时
        ExhibitionInfoDO exhibitionInfoDO = exhibitionInfoRepository.selectByPrimaryKey(baseInfoVO.getOriginRowId());
        int offset = exhibitionTimeZoneComponent.getDifferenceTimeOfExhibitionPlace(exhibitionInfoDO.getTimezone());
        Date endTime = DateUtils.addHours(baseInfoVO.getEndTime(), offset);
        return new Date().after(endTime);
    }
    public void sendNotice(ActivityBO activityBO, ActivityBaseInfoVO baseInfoVO) {
        String url = activityUrlConfig.fetchDetailUrl(activityBO.getActivityInfoDO().getRowId(), activityBO.getActivityInfoDO().getActivityType(), "first");
        String talkInfoUrl = activityUrlConfig.fetchDetailUrl(activityBO.getActivityInfoDO().getRowId(), activityBO.getActivityInfoDO().getActivityType(), SEVENTH);

        // 2024/04/10 活动提交后给讲师发消息 提示上传交流方案
        SendWorkOrderSimpleVO sendWorkOrderSimpleVO = buildSubmitLecturerUploadNotice(activityBO, baseInfoVO.getListZtePeopleInfo(),url);
        if(Objects.nonNull(sendWorkOrderSimpleVO)){
            infoCenterService.sendActivityWorkNoticeMessage(sendWorkOrderSimpleVO);
        }

        //结束时间 在 当前时间之前 逻辑后台控制 需要时区转换
        if(currTimeout(baseInfoVO)) {
            return;
        }
        //发给 知会人、我司参与人中所有人
        SendWorkOrderSimpleVO workOrderVO = buildSubmitScheduleNotice(activityBO, baseInfoVO);
        if (Objects.nonNull(workOrderVO)) {
            // 跳详情-基础信息
            workOrderVO.setUrl(url);
            infoCenterService.sendActivityWorkNoticeMessage(workOrderVO);
        }
        // 给谈参负责人发送催办上传谈参的工作通知
        Map<String, SendWorkOrderSimpleVO> workNoticeMap = buildUploadTalkInfoNotice(activityBO, baseInfoVO);
        if (MapUtils.isNotEmpty(workNoticeMap)) {
            /* Started by AICoder, pid:4f2857157f1ef62148790ad970ea2b08b4245cc4 */
            workNoticeMap.values().forEach(workNotice -> {
                workNotice.setUrl(talkInfoUrl);
                infoCenterService.sendActivityWorkNoticeMessage(workNotice);
            });
            /* Ended by AICoder, pid:4f2857157f1ef62148790ad970ea2b08b4245cc4 */
        }
        //old 活动提交后给知会人和组织者发送工作通知
        //new 2024/04/12 活动提交后给申请人发送工作通知
        String submitBy = HeadersProperties.getXEmpNo();
        EmployeeInfoDTO employeeInfoDTO = userCenterService.getUserInfo(submitBy);
        SendWorkOrderSimpleVO simpleVO = buildActivitySubmit(activityBO, employeeInfoDTO, baseInfoVO.getApplyPeopleNo());
        // 跳详情-基础信息
        if (Objects.nonNull(simpleVO)) {
            simpleVO.setUrl(url);
            infoCenterService.sendActivityWorkNoticeMessage(simpleVO);
        }
    }

    /**
     * 2024/04/10
     * 构建讲师上传交流方案通知对象
     * @param activityBO
     * @param lecturerList
     * @return
     */

    /**
     * 批量新增
     *
     * @param activityBO 活动信息
     * @author: 汤踊********
     * @date: 2023/5/23 22:13
     */
    private void insertActivityInfo(ActivityBO activityBO) {
        infoRepository.insertSelective(activityBO.getActivityInfoDO());
        projectRepository.insertSelective(activityBO.getRelationProject());
        communicationDirectionRepository.batchInsert(activityBO.getListCommunicateDirection());
        customerInfoRepository.batchInsert(activityBO.getListCustInfo());
        //保存日程安排信息
        scheduleItemRepository.batchInsert(activityBO.getListScheduleInfo());
        //保存日程参与人信息
        scheduleItemPeopleRepository.batchInsert(activityBO.getListSchedulePeopleInfo());
        //保存酒店信息
        activityResourceHotelRepository.insertSelective(activityBO.getListActivityResourceHotel());
        //保存车辆信息
        activityResourceCarRepository.insertSelective(activityBO.getListActivityResourceCar());
        //保存费用预算信息
        activityResourceFeeRepository.insertSelective(activityBO.getListActivityResourceFee());
        //保存日程安排谈参
        relationAttachmentRepository.insertSelectiveList(activityBO.getListActivityRelationAttachment());
        custPeopleRepository.batchInsert(activityBO.getListCustPeopleInfo());
        ztePeopleRepository.batchInsert(activityBO.getListZtePeopleInfo());
        assembleSolutionAndAttachment(activityBO);
        solutionRepository.batchInsert(activityBO.getListSolution());
        //活动附件
        relationAttachmentRepository.batchInsert(activityBO.getListAttachment());
        talkRepository.insertSelective(activityBO.getData());

        //保存样板点活动费用
        activityCostBudgetRepository.insertSelective(activityBO.getListActivityCostBudget());

        // 保存当前活动与商机的关联关系
        activityOpportunityRepository.insertByBatch(activityBO.getActivityOpportunityInfoDOList());
    }

    /**
     * @param activityBO
     * @param insert
     */
    public void submitActivityToLifeCycle(ActivityBO activityBO, boolean insert) {
        // 这里修改为public只是为了能满足规范，覆盖代码。要是以前状态是写死的，通用应直接从对象中取就好了，
        if (insert) {
            if (activityBO.getNeedApprove()) {
                saveActivityToLifeCycle(activityBO.getActivityInfoDO().getRowId(), StringUtils.EMPTY, DRAFT.getCode(), null);
            } else {
                saveActivityToLifeCycle(activityBO.getActivityInfoDO().getRowId(), StringUtils.EMPTY, activityBO.getActivityInfoDO().getActivityStatus(), null);
            }
        } else {
            if (!activityBO.getNeedApprove()) {
                saveActivityToLifeCycle(activityBO.getActivityInfoDO().getRowId(), DRAFT.getCode(), activityBO.getActivityInfoDO().getActivityStatus(), null);
            }
        }
    }

    /**
     * 保存生命周期
     * @param activityId 活动Id
     * @param statusFrom 当前状态
     * @param statusTo   后续状态
     * @param remark     备注信息
     * @return void
     * <AUTHOR>
     * date: 2023/8/29 19:44
     */
    private void saveActivityToLifeCycle(String activityId, String statusFrom, String statusTo, String remark) {
        List<ActivityStatusLifecycleDO> list = Lists.newArrayList();
        ActivityStatusLifecycleDO lifecycleDO = new ActivityStatusLifecycleDO();
        lifecycleDO.setActivityRowId(activityId);
        lifecycleDO.setStatusFrom(statusFrom);
        lifecycleDO.setStatusTo(statusTo);
        lifecycleDO.setEnterTime(new Date());
        lifecycleDO.setRemark(remark);
        list.add(lifecycleDO);
        lifecycleRepository.insertSelective(list);
    }

    private void assembleSolutionAndAttachment(ActivityBO activityBO) {
        List<ActivityRelationSolutionDO> listSolution = CollectionUtils.isNotEmpty(activityBO.getListSolution()) ?
                activityBO.getListSolution() : Lists.newArrayList();
        List<ActivityRelationAttachmentDO> listAttachment = CollectionUtils.isNotEmpty(activityBO.getListAttachment()) ?
                activityBO.getListAttachment() : Lists.newArrayList();
        List<ActivityRelationZtePeopleDO> listZtePeopleInfo = activityBO.getListZtePeopleInfo();
        List<ActivityRelationZtePeopleDO> lecturerList = listZtePeopleInfo.stream()
                .filter(e -> LECTURER.isMe(e.getPeopleType()))
                .collect(Collectors.toList());
        List<ActivityRelationAttachmentDO> solutionAttachment = listAttachment.stream().filter(e -> SOLUTION.isMe(e.getAttachmentSceneType())).collect(Collectors.toList());
        Map<String, ActivityRelationZtePeopleDO> mapPeopleCode = lecturerList.stream().collect(Collectors.toMap(ActivityRelationZtePeopleDO::getReleatedId, Function.identity(), (key1, key2) -> key2));
        if (CollectionUtils.isNotEmpty(listSolution)) {
            for (ActivityRelationSolutionDO solutionDO : listSolution) {
                solutionDO.setRelationPeopleRowId(mapPeopleCode.get(solutionDO.getReleatedId()).getRowId());
            }
        }
        if (CollectionUtils.isNotEmpty(solutionAttachment)) {
            for (ActivityRelationAttachmentDO attachmentDO : solutionAttachment) {
                attachmentDO.setSceneOriginRowId(mapPeopleCode.get(attachmentDO.getReleatedId()).getRowId());
            }
        }
    }

    /**
     * 在全量更新之前将已经存在的必要信息填充下
     *
     * @param activityBO
     * @param activityInDb
     */
    private void fillFromDbBeforeUpdate(ActivityBO activityBO, ActivityInfoDO activityInDb) {
        activityBO.getActivityInfoDO().setActivityRequestNo(activityInDb.getActivityRequestNo());
        activityBO.getActivityInfoDO().setCreatedBy(activityInDb.getCreatedBy());
        activityBO.getActivityInfoDO().setCreationDate(activityInDb.getCreationDate());
        activityBO.getActivityInfoDO().setLastUpdateDate(new Date());
        activityBO.getActivityInfoDO().setLastUpdatedBy(activityInDb.getLastUpdatedBy());
        activityBO.getActivityInfoDO().setEnabledFlag(activityInDb.getEnabledFlag());
    }

    /**
     * 在全量更新之前将已经存在的必要信息填充下
     *
     * @param activityBO
     * @param activityInDb
     */
    private void fillFromDbBeforeChange(ActivityBO activityBO, ActivityInfoDO activityInDb) {
        activityBO.getActivityInfoDO().setActivityRequestNo(activityInDb.getActivityRequestNo());
        activityBO.getActivityInfoDO().setCreatedBy(activityInDb.getCreatedBy());
        activityBO.getActivityInfoDO().setCreationDate(activityInDb.getCreationDate());
        activityBO.getActivityInfoDO().setLastUpdateDate(new Date());
        activityBO.getActivityInfoDO().setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
        activityBO.getActivityInfoDO().setEnabledFlag(activityInDb.getEnabledFlag());
    }

    private void updateActivityInfo(ActivityBO activityBO) {
        // 活动信息全量更新
        infoRepository.updateByPrimaryKey(activityBO.getActivityInfoDO());
        updateProject(activityBO);
        updateCommunitcation(activityBO);
        updateCustInfo(activityBO);
        updateCustPeople(activityBO);
        updateScheduleItem(activityBO);
        updateActivityHotel(activityBO);
        updateActivityCar(activityBO);
        updateActivityFee(activityBO);
        updateZtePeople(activityBO);
        assembleSolutionAndAttachment(activityBO);
        updateSolution(activityBO);
        updateAttachment(activityBO);
        updateTalkAndDataLink(activityBO);

        //更新样板点活动费用，先删除在新增
        updateActivityCostBudget(activityBO);
    }

    private void updateActivityCostBudget(ActivityBO activityBO){
         List<ActivityCostBudgetDO> currentActivityCostBudgetList = activityBO.getListActivityCostBudget();
        //根据活动ID查询对应所有活动费用
        String activityRowId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityCostBudgetDO> localActivityCostBudget = activityCostBudgetRepository.queryCostBudgetByActivityRowIds(activityRowId);
        //删除当前活动下所有活动费用
        if(CollectionUtils.isNotEmpty(localActivityCostBudget)){
             List<String> rowIds = localActivityCostBudget.stream().map(ActivityCostBudgetDO::getRowId).collect(Collectors.toList());
             activityCostBudgetRepository.deleteByRowIds(HeadersProperties.getXEmpNo(),rowIds);
        }
        //新增当前活动费用
        activityCostBudgetRepository.insertSelective(currentActivityCostBudgetList);
    }

    private void updateTalkAndDataLink(ActivityBO activityBO) {
        List<ActivityRelationTalkDO> relationTalkDOList = talkRepository.queryAllByActivityRowId(activityBO.getActivityInfoDO().getRowId());
        Optional<ActivityRelationTalkDO> talkDO = relationTalkDOList.stream().filter(n -> DataTypeEnum.TALK.isMe(n.getType())).findAny();
        Optional<ActivityRelationTalkDO> dataLinkDO = relationTalkDOList.stream().filter(n -> DataTypeEnum.DATA_LINK.isMe(n.getType())).findAny();

        List<ActivityRelationTalkDO> data = activityBO.getData();
        Optional<ActivityRelationTalkDO> currentTalkDO = data.stream().filter(n -> DataTypeEnum.TALK.isMe(n.getType())).findAny();
        Optional<ActivityRelationTalkDO> currentDataLinkDO = data.stream().filter(n -> DataTypeEnum.DATA_LINK.isMe(n.getType())).findAny();

        talkDO.ifPresent(activityRelationTalkDO -> talkRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), Arrays.asList(activityRelationTalkDO.getRowId())));
        currentTalkDO.ifPresent(activityRelationTalkDO -> talkRepository.insertSelective(Arrays.asList(activityRelationTalkDO)));
        dataLinkDO.ifPresent(activityRelationTalkDO -> talkRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), Arrays.asList(activityRelationTalkDO.getRowId())));
        currentDataLinkDO.ifPresent(activityRelationTalkDO -> talkRepository.insertSelective(Arrays.asList(activityRelationTalkDO)));
    }

    /**
     * 更新日程安排+参与人+谈参
     * @param activityBO
     */
     void updateScheduleItem(ActivityBO activityBO) {
        List<ActivityScheduleItemDO> listScheduleItem = activityBO.getListScheduleInfo();
        List<ActivityScheduleItemPeopleDO> listScheduleItemPeople = activityBO.getListSchedulePeopleInfo();
        List<ActivityRelationAttachmentDO> listActivityRelationAttachment = activityBO.getListActivityRelationAttachment();
        String activityId = activityBO.getActivityInfoDO().getRowId();

        List<ActivityScheduleItemDO> listScheduleItemDo = scheduleItemRepository.getRelationScheduleInfoList(Collections.singletonList(activityId));
        List<String> listRowIdPrevious = listScheduleItemDo.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList());

        // 获取活动的日程谈参授权数据
        Map<String, List<ActivityResourceOperationAuthDO>> operationAuthMap
                = activityResourceOperationAuthRepository.selectByActivityIdList(Collections.singletonList(activityId),
                ActivityResourceOperationBizTypeEnum.SCHEDULE_TALK.getCode(), null);
        List<ActivityResourceOperationAuthDO> operationAuthList = operationAuthMap.getOrDefault(activityId, Collections.emptyList());

         //编辑后为空，之前不为空，则清空之前所有的日程安排、参与人、谈参数据
        if (CollectionUtils.isEmpty(listScheduleItem)) {
            if (CollectionUtils.isNotEmpty(listRowIdPrevious)) {
                // 存在非待处理的数据，不允许删除
                List<ActivityScheduleItemDO> notWaitScheduleItemDOList = listScheduleItemDo.stream().filter(item ->
                        !ResourceOrchestrationDealStatusEnum.WAIT.isMe(item.getDealStatus())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(notWaitScheduleItemDOList)){
                    return;
                }
                scheduleItemRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listRowIdPrevious);
                scheduleItemPeopleRepository.deleteByScheduleItemRowIds(HeadersProperties.getXEmpNo(), listRowIdPrevious);
                relationAttachmentRepository.deleteBySceneOriginRowIds(HeadersProperties.getXEmpNo(), listRowIdPrevious);
                // 清理多余的日程谈参授权数据
                List<String> operationAuthIdList = operationAuthList.stream().map(ActivityResourceOperationAuthDO::getRowId).collect(Collectors.toList());
                activityResourceOperationAuthRepository.deleteByIdList(operationAuthIdList, HeadersProperties.getXEmpNo());
            }
            return;
        }

        Set<String> listRowIdParam = listScheduleItem.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toSet());
        List<String> deleteList = listRowIdPrevious.stream().filter(e -> !listRowIdParam.contains(e)).collect(Collectors.toList());
        List<String> insertList = listRowIdParam.stream().filter(e -> !listRowIdPrevious.contains(e)).collect(Collectors.toList());
        List<String> sameList = listRowIdParam.stream().filter(e -> listRowIdPrevious.contains(e)).collect(Collectors.toList());
        List<String> deleteOperationAuthIdList = operationAuthList.stream()
                .filter(item -> !listRowIdParam.contains(item.getBizRelatedId()))
                .map(ActivityResourceOperationAuthDO::getRowId)
                .collect(Collectors.toList());
         //删除
        if (CollectionUtils.isNotEmpty(deleteList)) {
            // 获取需要删除的日程id ----- 日程状态待处理
            List<ActivityScheduleItemDO> waitScheduleItemDOList = listScheduleItemDo.stream().filter(item -> deleteList.contains(item.getRowId())
                    && ResourceOrchestrationDealStatusEnum.WAIT.isMe(item.getDealStatus())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(waitScheduleItemDOList)){
                List<String> needDeleteList = waitScheduleItemDOList.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList());
                scheduleItemRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), needDeleteList);
                scheduleItemPeopleRepository.deleteByScheduleItemRowIds(HeadersProperties.getXEmpNo(), needDeleteList);
                relationAttachmentRepository.deleteBySceneOriginRowIds(HeadersProperties.getXEmpNo(), needDeleteList);
            }
        }
        //插入
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<ActivityScheduleItemDO> insertListScheduleItem = listScheduleItem.stream().filter(e -> insertList.contains(e.getRowId())).collect(Collectors.toList());
            List<ActivityScheduleItemPeopleDO> insertListScheduleItemPeople = listScheduleItemPeople.stream().filter(e -> insertList.contains(e.getActivityScheduleItemRowId())).collect(Collectors.toList());
            List<ActivityRelationAttachmentDO> insertListRelationAttachment = listActivityRelationAttachment.stream().filter(e -> insertList.contains(e.getSceneOriginRowId())).collect(Collectors.toList());
            scheduleItemRepository.batchInsert(insertListScheduleItem);
            scheduleItemPeopleRepository.batchInsert(insertListScheduleItemPeople);
            relationAttachmentRepository.batchInsert(insertListRelationAttachment);
        }
        //更新
        if(CollectionUtils.isNotEmpty(sameList)){
            List<ActivityScheduleItemDO> updateListScheduleItem = listScheduleItem.stream().filter(e -> sameList.contains(e.getRowId())).collect(Collectors.toList());
            // 获取需要更新的日程id ----- 日程状态待处理
            List<ActivityScheduleItemDO> needUpdateListScheduleItem = updateListScheduleItem.stream().filter(item ->
                    ResourceOrchestrationDealStatusEnum.WAIT.isMe(item.getDealStatus())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needUpdateListScheduleItem)){
                List<String> needUpdateList = needUpdateListScheduleItem.stream().map(ActivityScheduleItemDO::getRowId).collect(Collectors.toList());

                List<ActivityScheduleItemPeopleDO> updateListScheduleItemPeople = listScheduleItemPeople.stream().filter(e -> needUpdateList.contains(e.getActivityScheduleItemRowId())).collect(Collectors.toList());
                List<ActivityRelationAttachmentDO> updateListRelationAttachment = listActivityRelationAttachment.stream().filter(e -> needUpdateList.contains(e.getSceneOriginRowId())).collect(Collectors.toList());
                scheduleItemRepository.batchUpdate(needUpdateListScheduleItem);
                scheduleItemPeopleRepository.deleteByScheduleItemRowIds(HeadersProperties.getXEmpNo(), needUpdateList);
                relationAttachmentRepository.deleteBySceneOriginRowIds(HeadersProperties.getXEmpNo(), needUpdateList);
                scheduleItemPeopleRepository.batchInsert(updateListScheduleItemPeople);
                relationAttachmentRepository.batchInsert(updateListRelationAttachment);
            }
        }
        // 清理失效日程的日程谈参授权数据
        activityResourceOperationAuthRepository.deleteByIdList(deleteOperationAuthIdList, HeadersProperties.getXEmpNo());
    }

    private void updateProject(ActivityBO activityBO) {
        List<ActivityRelationProjectDO> list = projectRepository.queryAllProjectForActivity(activityBO.getActivityInfoDO().getRowId());
        boolean exist = CollectionUtils.isNotEmpty(list);
        boolean insert = Objects.nonNull(activityBO.getRelationProject()) && StringUtils.isNotBlank(activityBO.getRelationProject().getProjectCode());
        if (insert) {
            if (exist) {
                activityBO.getRelationProject().setRowId(list.get(0).getRowId());
                projectRepository.updateByPrimaryKeySelective(activityBO.getRelationProject());
            } else {
                projectRepository.insertSelective(activityBO.getRelationProject());
            }
        } else {
            if (exist) {
                List<String> deleteList = list.stream().map(ActivityRelationProjectDO::getRowId).collect(Collectors.toList());
                projectRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
            }
        }
    }

    private void updateAttachment(ActivityBO activityBO) {
        List<ActivityRelationAttachmentDO> listAttachment = activityBO.getListAttachment();
        List<ActivityRelationZtePeopleDO> listZtePeople = activityBO.getListZtePeopleInfo();
        String activityRowId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityRelationAttachmentDO> listSolutionDb = relationAttachmentRepository.queryAllByActivityRowId(activityRowId);
        List<String> listIdDb = listSolutionDb.stream()
                .filter(e -> AttachmentSceneTypeEnum.isContains(e.getAttachmentSceneType()))
                .map(ActivityRelationAttachmentDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listIdDb)) {
            relationAttachmentRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
        }
        if (CollectionUtils.isNotEmpty(listAttachment)) {
            Map<String, ActivityRelationZtePeopleDO> map = listZtePeople.stream()
                    .filter(e -> LECTURER.isMe(e.getPeopleType()))
                    .collect(Collectors.toMap(ActivityRelationZtePeopleDO::getReleatedId, Function.identity(), (key1, key2) -> key2));
            listAttachment.forEach(e -> {
                ActivityRelationZtePeopleDO ztePeopleDO = (map.get(e.getReleatedId()));
                if (Objects.nonNull(ztePeopleDO)) {
                    e.setSceneOriginRowId(map.get(e.getReleatedId()).getRowId());
                }
            });
            relationAttachmentRepository.batchInsert(listAttachment);
        }
    }

    private void updateSolution(ActivityBO activityBO) {
        List<ActivityRelationSolutionDO> listSolution = activityBO.getListSolution();
        List<ActivityRelationZtePeopleDO> listZtePeople = activityBO.getListZtePeopleInfo();
        String activityRowId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityRelationSolutionDO> listSolutionDb = solutionRepository.queryAllSolutionForActivity(activityRowId);
        List<String> listIdDb = listSolutionDb.stream().map(ActivityRelationSolutionDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listIdDb)) {
            solutionRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
        }
        if (CollectionUtils.isNotEmpty(listSolution)) {
            Map<String, ActivityRelationZtePeopleDO> map = listZtePeople.stream()
                    .filter(e -> LECTURER.isMe(e.getPeopleType()))
                    .collect(Collectors.toMap(ActivityRelationZtePeopleDO::getReleatedId, Function.identity(), (key1, key2) -> key2));
            listSolution.forEach(e -> e.setRelationPeopleRowId(map.get(e.getReleatedId()).getRowId()));
            solutionRepository.batchInsert(listSolution);
        }
    }

    /**
     * 更新酒店资源
     * @param activityBO
     */
    private void updateActivityHotel(ActivityBO activityBO) {
        List<ActivityResourceHotelDO> listResourceHotelParam = activityBO.getListActivityResourceHotel();
        String activityId = activityBO.getActivityInfoDO().getRowId();

        Map<String, List<ActivityResourceHotelDO>> mapActivityResourceHotel = activityResourceHotelRepository.queryActivityResourceHotelsByActivityRowIds(Collections.singletonList(activityId));
        List<ActivityResourceHotelDO> listResourceHotelDO = mapActivityResourceHotel.get(activityId);

        if(CollectionUtils.isEmpty(listResourceHotelDO)){
            if(CollectionUtils.isNotEmpty(listResourceHotelParam)){
                activityResourceHotelRepository.insertSelective(listResourceHotelParam);
            }
            return;
        }
        List<String> listIdDb = listResourceHotelDO.stream().map(ActivityResourceHotelDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listResourceHotelParam)) {
            if (CollectionUtils.isNotEmpty(listIdDb)) {
                activityResourceHotelRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityResourceHotelDO> insertList = listResourceHotelParam.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listHasId = listResourceHotelParam.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityResourceHotelDO::getRowId)
                .collect(Collectors.toList());
        activityResourceHotelRepository.insertSelective(insertList);

        List<String> updateList =  listIdDb.stream().filter(e -> listHasId.contains(e)).collect(Collectors.toList());
        List<String> deleteList = listIdDb.stream().filter(e -> !listHasId.contains(e)).collect(Collectors.toList());
        List<String> newInsertList = listHasId.stream().filter(e -> !listIdDb.contains(e)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(newInsertList)) {
            List<ActivityResourceHotelDO> insertListResourceHotelDO = listResourceHotelParam.stream().filter(e -> newInsertList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceHotelRepository.insertSelective(insertListResourceHotelDO);
        }
        //删除
        if (CollectionUtils.isNotEmpty(deleteList)) {
            activityResourceHotelRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
        //更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<ActivityResourceHotelDO> updateListResourceHotel = listResourceHotelParam.stream().filter(e -> updateList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceHotelRepository.batchUpdate(updateListResourceHotel);
        }
    }

    private void updateActivityCar(ActivityBO activityBO) {
        List<ActivityResourceCarDO> listResourceCarParam = activityBO.getListActivityResourceCar();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        Map<String, List<ActivityResourceCarDO>> mapResourceCar = activityResourceCarRepository.queryActivityResourceCarsByActivityRowIds(Collections.singletonList(activityId));

        List<ActivityResourceCarDO> listResourceCarDO = mapResourceCar.get(activityId);
        if(CollectionUtils.isEmpty(listResourceCarDO)){
            if(CollectionUtils.isNotEmpty(listResourceCarParam)){
                activityResourceCarRepository.insertSelective(listResourceCarParam);
            }
            return;
        }
        List<String> listIdDb = listResourceCarDO.stream().map(ActivityResourceCarDO::getRowId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(listResourceCarParam)) {
            if (CollectionUtils.isNotEmpty(listIdDb)) {
                activityResourceCarRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityResourceCarDO> insertList = listResourceCarParam.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listHasId = listResourceCarParam.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityResourceCarDO::getRowId)
                .collect(Collectors.toList());
        activityResourceCarRepository.insertSelective(insertList);

        List<String> deleteList = listIdDb.stream().filter(e -> !listHasId.contains(e)).collect(Collectors.toList());
        List<String> newInsertList = listHasId.stream().filter(e -> !listIdDb.contains(e)).collect(Collectors.toList());
        List<String> updateList = listIdDb.stream().filter(e -> listHasId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            activityResourceCarRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }

        if (CollectionUtils.isNotEmpty(newInsertList)) {
            List<ActivityResourceCarDO> insertListResourceCar = listResourceCarParam.stream().filter(e -> newInsertList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceCarRepository.insertSelective(insertListResourceCar);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<ActivityResourceCarDO> updateListActivityResourceCar = listResourceCarParam.stream().filter(e -> updateList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceCarRepository.batchUpdate(updateListActivityResourceCar);
        }
    }

    private void updateActivityFee(ActivityBO activityBO){

        List<ActivityResourceFeeDO> listResourceFeeParam = activityBO.getListActivityResourceFee();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        Map<String, List<ActivityResourceFeeDO>> mapActivityResourceFee = activityResourceFeeRepository.queryActivityResourceFeesByActivityRowIds(Collections.singletonList(activityId));
        List<ActivityResourceFeeDO> listResourceFeeDO = mapActivityResourceFee.get(activityId);
        if(CollectionUtils.isEmpty(listResourceFeeDO)){
            if(CollectionUtils.isNotEmpty(listResourceFeeParam)){
                activityResourceFeeRepository.insertSelective(listResourceFeeParam);
            }
            return;
        }
        List<String> listIdDb = listResourceFeeDO.stream().map(ActivityResourceFeeDO::getRowId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(listResourceFeeParam)){
            if(CollectionUtils.isNotEmpty(listIdDb)){
                activityResourceFeeRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityResourceFeeDO> insertList = listResourceFeeParam.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listHasId = listResourceFeeParam.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityResourceFeeDO::getRowId)
                .collect(Collectors.toList());
        activityResourceFeeRepository.insertSelective(insertList);

        List<String> deleteList = listIdDb.stream().filter(e -> !listHasId.contains(e)).collect(Collectors.toList());
        List<String> newInsertList = listHasId.stream().filter(e -> !listIdDb.contains(e)).collect(Collectors.toList());
        List<String> updateList = listIdDb.stream().filter(e -> listHasId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newInsertList)) {
            List<ActivityResourceFeeDO> insertListResourceFee = listResourceFeeParam.stream().filter(e -> newInsertList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceFeeRepository.insertSelective(insertListResourceFee);
        }
        //删除
        if (CollectionUtils.isNotEmpty(deleteList)) {
            activityResourceFeeRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<ActivityResourceFeeDO> updateListResourceFee = listResourceFeeParam.stream().filter(e -> updateList.contains(e.getRowId())).collect(Collectors.toList());
            activityResourceFeeRepository.batchUpdate(updateListResourceFee);
        }
    }

    /**
     * 更新中兴参与人
     *
     * @param activityBO
     */
    private void updateZtePeople(ActivityBO activityBO) {
        List<ActivityRelationZtePeopleDO> listZtePeople = activityBO.getListZtePeopleInfo();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityRelationZtePeopleDO> listDb = ztePeopleRepository.queryAllZtePeopleForActivity(activityId);
        List<ActivityRelationZtePeopleDO> excludFollowPeople = listDb.stream()
                .filter(e -> !ActivityPeopleTypeEnum.in(e.getPeopleType(), FOLLOW, CONTACTS))
                .collect(Collectors.toList());
        List<String> listIdDb = excludFollowPeople.stream().map(ActivityRelationZtePeopleDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listZtePeople)) {
            if (CollectionUtils.isNotEmpty(listIdDb)) {
                ztePeopleRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityRelationZtePeopleDO> insertList = listZtePeople.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listCurrentId = listZtePeople.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityRelationZtePeopleDO::getRowId)
                .collect(Collectors.toList());
        List<String> deleteList = listIdDb.stream().filter(e -> !listCurrentId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            ztePeopleRepository.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            ztePeopleRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
    }


    /**
     * 更新客户联系人
     *
     * @param activityBO
     */
    private void updateCustPeople(ActivityBO activityBO) {
        List<ActivityRelationCustPeopleDO> listCustPeopleInfo = activityBO.getListCustPeopleInfo();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityRelationCustPeopleDO> listDb = custPeopleRepository.queryAllByActivityRowId(activityId);
        List<String> listIdDb = listDb.stream().map(ActivityRelationCustPeopleDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listCustPeopleInfo)) {
            if (CollectionUtils.isNotEmpty(listIdDb)) {
                custPeopleRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityRelationCustPeopleDO> insertList = listCustPeopleInfo.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listCurrentId = listCustPeopleInfo.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityRelationCustPeopleDO::getRowId)
                .collect(Collectors.toList());
        List<String> deleteList = listIdDb.stream().filter(e -> !listCurrentId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            custPeopleRepository.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            custPeopleRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
    }

    /**
     * 修改客户信息
     *
     * @param activityBO
     */
    private void updateCustInfo(ActivityBO activityBO) {
        List<ActivityCustomerInfoDO> listCustInfo = activityBO.getListCustInfo();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityCustomerInfoDO> listDb = customerInfoRepository.queryAllByActivityRowId(activityId);
        List<String> listIdDb = listDb.stream().map(ActivityCustomerInfoDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listCustInfo)) {
            if (CollectionUtils.isNotEmpty(listIdDb)) {
                customerInfoRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), listIdDb);
            }
            return;
        }
        List<ActivityCustomerInfoDO> insertList = listCustInfo.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listCurrentId = listCustInfo.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityCustomerInfoDO::getRowId)
                .collect(Collectors.toList());
        List<String> deleteList = listIdDb.stream().filter(e -> !listCurrentId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            customerInfoRepository.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            customerInfoRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
    }


    /**
     * 修改交流方向
     *
     * @param activityBO
     */
    private void updateCommunitcation(ActivityBO activityBO) {
        List<ActivityCommunicationDirectionDO> listCommunicateDirection = activityBO.getListCommunicateDirection();
        String activityId = activityBO.getActivityInfoDO().getRowId();
        List<ActivityCommunicationDirectionDO> listDb = communicationDirectionRepository.queryAllByActivityRowId(activityId);
        List<String> dbRowId = listDb.stream().map(ActivityCommunicationDirectionDO::getRowId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listCommunicateDirection)) {
            if (CollectionUtils.isNotEmpty(dbRowId)) {
                communicationDirectionRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), dbRowId);
            }
            return;
        }
        List<ActivityCommunicationDirectionDO> insertList = listCommunicateDirection.stream().filter(e -> StringUtils.isBlank(e.getRowId())).collect(Collectors.toList());
        List<String> listCurrentId = listCommunicateDirection.stream()
                .filter(e -> StringUtils.isNotBlank(e.getRowId()))
                .map(ActivityCommunicationDirectionDO::getRowId)
                .collect(Collectors.toList());
        List<String> deleteList = dbRowId.stream().filter(e -> !listCurrentId.contains(e)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            communicationDirectionRepository.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            communicationDirectionRepository.deleteByRowIds(HeadersProperties.getXEmpNo(), deleteList);
        }
    }

    /* Started by AICoder, pid:l8664m7bd8gf3f01475b09b590a4ed6ada26239e */
    /**
     * 处理活动审批或创建待办通知的逻辑
     *
     * @param activityBO 活动业务对象
     * @param baseInfoVO 基本信息视图对象
     */
    private void startApproveOrCreatePendingNotice(ActivityBO activityBO, ActivityBaseInfoVO baseInfoVO) {
        // 根据活动类型进行不同的处理
        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE)) {
            startExhibitionApprove(baseInfoVO, activityBO);
        } else if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), VISITING_SAMPLE)) {
            startSamplePointApprove(baseInfoVO, activityBO);
        } else {
            handleSeniorVisit(activityBO, baseInfoVO);
        }
    }

    /**
     * 处理高层拜访活动的审批逻辑
     *
     * @param activityBO 活动业务对象
     * @param baseInfoVO 基本信息视图对象
     */
    private void handleSeniorVisit(ActivityBO activityBO, ActivityBaseInfoVO baseInfoVO) {
        boolean needApprove = activityBO.getNeedApprove();
        String needComplianceAuditor = BooleanEnum.Y.getCode();
        activityBO.setLinkUrl(activityUrlConfig.fetchPendingNoticeUrl(activityBO.getActivityInfoDO().getRowId()));

        if (ActivityTypeEnum.in(baseInfoVO.getActivityType(), SENIOR_VISIT_EXPANSION)) {
            needComplianceAuditor = needApprove ? BooleanEnum.Y.getCode() : BooleanEnum.N.getCode();
            needApprove = true;
        }

        startApproval(activityBO, baseInfoVO, needApprove, needComplianceAuditor);
    }

    /**
     * 启动活动审批流程
     *
     * @param activityBO             活动业务对象
     * @param baseInfoVO             基本信息视图对象
     * @param needApprove            是否需要审批
     * @param needComplianceAuditor  是否需要合规扫描
     */
    private void startApproval(ActivityBO activityBO, ActivityBaseInfoVO baseInfoVO, boolean needApprove, String needComplianceAuditor) {
        if (needApprove) {
            try {
                activityApprovalInfoService.startApproval(buildApprovalFlowParam(activityBO, baseInfoVO, needComplianceAuditor));
            } catch (Exception e) {
                logger.error("活动提交时，启动评审出错", e);
                throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(SUBMIT_AFTER_APPROVE_ERROR));
            }
        } else {
            createPendingNoticeAndSendEmail(activityBO);
        }
    }

    /**
     * 创建纪要和评价两个待办，并发送邮件
     *
     * @param activityBO 活动业务对象
     */
    private void createPendingNoticeAndSendEmail(ActivityBO activityBO) {
        BizRequest<ActivityPendingNoticeBO> bizRequest = buildPendNotice(activityBO);
        noticeService.insertPendingNoticeAndCreateEvent(bizRequest);
    }
    /* Ended by AICoder, pid:l8664m7bd8gf3f01475b09b590a4ed6ada26239e */

    private void startSamplePointApprove(ActivityBaseInfoVO baseInfoVO, ActivityBO activityBO) {
        List<ActivityApprovalParam> approvalList = Optional.ofNullable(baseInfoVO.getApprovalList()).orElse(new ArrayList<>())
                .stream().filter(item -> ApprovalTypeEnum.in(item.getApprovalType(), COMPLIANCE_AUDITOR, LEADER_AUDITOR))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalList)) {
            //不需要审批的话，则需要创建纪要待办，并发送邮件
            BizRequest<ActivityPendingNoticeBO> bizRequest = buildPendNotice(activityBO);
            noticeService.insertPendingNoticeAndCreateEvent(bizRequest);
            return;
        }
        ActivityInfoDO activityInfo = activityBO.getActivityInfoDO();
        // 审批需要统一的调用，目前审批还未统一，同时原代码未统一管理代码结构，
        ApprovalFlowStartParam param = new ApprovalFlowStartParam();
        param.setBizId(activityInfo.getRowId());
        param.setApprovalType(ApprovalFlowTypeEnum.SAMPLE_POINT_APPROVAL);
        param.setActTitle(activityInfo.getActivityTitle());
        param.setActNo(activityInfo.getOriginRowId());
        param.setActLinkUrl(activityUrlConfig.fetchPendingNoticeUrl(activityInfo.getRowId()));
        param.setApprovedByInfoList(baseInfoVO.getApprovalList().stream().map(e -> {
            ApprovedByInfo info = new ApprovedByInfo();
            info.setApprovalType(e.getApprovalType());
            info.setEmpNo(e.getEmpNo());
            info.setApprovalNodeType(e.getApprovalNodeType());
            return info;
        }).collect(Collectors.toList()));

        approvalClient.start(BizRequestUtil.createWithCurrentUser(param));
    }

    /**
     * @param baseInfoVO
     * @param activityBO
     */
    private void startExhibitionApprove(ActivityBaseInfoVO baseInfoVO, ActivityBO activityBO) {
        List<ActivityApprovalParam> approvalList = Optional.ofNullable(baseInfoVO.getApprovalList()).orElse(new ArrayList<>())
                .stream().filter(item -> ApprovalTypeEnum.in(item.getApprovalType(), COMPLIANCE_AUDITOR, LEADER_AUDITOR))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(approvalList)) {
            //不需要审批的话，则需要创建纪要待办，并发送邮件
            BizRequest<ActivityPendingNoticeBO> bizRequest = buildPendNotice(activityBO);
            noticeService.insertPendingNoticeAndCreateEvent(bizRequest);
            return;
        }

        ActivityInfoDO activityInfo = activityBO.getActivityInfoDO();
        // 审批需要统一的调用，目前审批还未统一，同时原代码未统一管理代码结构，
        ApprovalFlowStartParam param = new ApprovalFlowStartParam();
        param.setBizId(activityInfo.getRowId());
        param.setApprovalType(ApprovalFlowTypeEnum.EXHIBITION_APPROVAL);
        param.setActTitle(activityInfo.getActivityTitle());
        param.setActNo(activityInfo.getOriginRowId());
        param.setActLinkUrl(activityUrlConfig.fetchPendingNoticeUrl(activityInfo.getRowId()));
        param.setApprovedByInfoList(baseInfoVO.getApprovalList().stream().map(e -> {
            ApprovedByInfo info = new ApprovedByInfo();
            info.setApprovalType(e.getApprovalType());
            info.setEmpNo(e.getEmpNo());
            info.setLevel(e.getLevel());
            return info;
        }).collect(Collectors.toList()));

        approvalClient.start(BizRequestUtil.createWithCurrentUser(param));
    }


    private void createReservation(ActivityBO activityBO, ActivityBaseInfoVO baseInfoVO, boolean activityChangeFlag) {
        boolean notNeedSchedule = ActivityTypeEnum.in(baseInfoVO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE,VISITING_SAMPLE)
                || activityBO.getActivityInfoDO().getEndTime().before(new Date());
        // 如果时间是过去的，则说明是录入已经发生的活动。是不需要预约的。或者是日常拜访也不用预约日程
        if (notNeedSchedule) {
            return;
        }
        BizRequest<ReserveResourceParam> bizRequest = buildReserveParamWithAccredit(activityBO, baseInfoVO, null);
        if (Objects.isNull(bizRequest)) {
            return;
        }
        startReserveSeparateResource(activityChangeFlag, bizRequest);
    }

    /**
     * 开始预约日常
     * @param activityChangeFlag 活动变更标识
     * @param bizRequest
     * @return void
     * <AUTHOR>
     * date: 2023/9/7 11:27
     */
    private void startReserveSeparateResource(boolean activityChangeFlag, BizRequest<ReserveResourceParam> bizRequest) {
        bizRequest.getParam().setActivityChangeFlag(activityChangeFlag);
        ServiceData<ReserveDealCommonVO> result = reservationService.reserveSeparateResource(bizRequest);
        if (!ServiceDataUtils.isSuccess(result)) {
            logger.error("活动提交时，日程预约失败:{}", result.getBo());
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, SUBMIT_AFTER_RESERVATION_ERROR);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    public int deleteActivity(BizRequest<List<String>> activityIdsParams) {
        List<String> activityIds = activityIdsParams.getParam();
        String empNo = activityIdsParams.getEmpNo();
        List<ActivityInfoDO> activityInfoDOS = infoRepository.selectByIds(activityIds);

        List<ActivityInfoDO> draftActivitys = checkDeletePermission(empNo, activityInfoDOS);

        activityIds = draftActivitys.stream().map(ActivityInfoDO::getRowId).collect(Collectors.toList());

        // activity_tag
        tagRepository.deleteByActivityIds(empNo, activityIds);

        // activity_communication_direction
        communicationDirectionRepository.deleteByActivityIds(empNo, activityIds);

        // activity_status_lifecycle
        lifecycleRepository.deleteByActivityIds(empNo, activityIds);

        // activity_customer_info
        customerInfoRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_cust_people
        custPeopleRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_zte_people
        ztePeopleRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_talk
        talkRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_solution
        solutionRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_project
        projectRepository.deleteByActivityIds(empNo, activityIds);

        // activity_relation_attachment
        relationAttachmentRepository.deleteByActivityIds(empNo, activityIds);

        // activity_info
        int deleteNum = infoRepository.deleteByIds(empNo, activityIds);

        return deleteNum;
    }

    private static List<ActivityInfoDO> checkDeletePermission(String empNo, List<ActivityInfoDO> activityInfoDOS) {
        List<ActivityInfoDO> poweredDeleteActivitys = activityInfoDOS.stream().filter(
                item -> StringUtils.equals(empNo, item.getCreatedBy())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(poweredDeleteActivitys)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ErrorMsgConstant.ACTIVITY_NOT_CREATOR);
        }

        List<ActivityInfoDO> draftActivitys = poweredDeleteActivitys.stream().filter(
                item -> DRAFT.getCode().equals(item.getActivityStatus())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(draftActivitys)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, ErrorMsgConstant.ACTIVITY_NOT_ALLOWED);
        }
        return draftActivitys;
    }

    /**
     * 查询活动评价信息
     *
     * @param activityRowId 活动主键Id
     * @return List<EvaluationInfoVO>   活动评价信息列表
     * <AUTHOR>
     * date: 2023/5/26 10:40
     */
    @Override
    public EvaluationDetailsVO getEvaluationInfo(String activityRowId) {
        EvaluationDetailsVO evaluationDetailsVO = new EvaluationDetailsVO();
        //获取当前登录人工号
        String myEmpNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        // 判断当前登录人是否有权限评价
        boolean canEvaluate = isNotEvaluate(activityRowId);
        //查询这个活动所有评价信息
        List<ActivityEvaluationInfoDO> listEvaluation = evaluationInfoRepository.queryAllByActivityRowId(activityRowId);
        //当前登录人是否有评价记录
        List<ActivityEvaluationInfoDO> currentPeopleEva = listEvaluation.stream()
                .filter(e -> StringUtils.equals(e.getCreatedBy(), myEmpNo))
                .collect(Collectors.toList());
        if (canEvaluate) {
            String authStr = CollectionUtils.isEmpty(currentPeopleEva) ? AUTH_ING : AUTH_END;
            evaluationDetailsVO.setEvaluationButtonShow(authStr);
        } else {
            evaluationDetailsVO.setEvaluationButtonShow(AUTH_NONE);
            return evaluationDetailsVO;
        }
        if (CollectionUtils.isEmpty(listEvaluation)) {
            return evaluationDetailsVO;
        }
        String evaluationButtonShow = evaluationDetailsVO.getEvaluationButtonShow();
        // 设置评估人全称
        resetEvaluatedBy(listEvaluation);
        // 分类装填整体效果、讲师、方案等评价信息
        evaluationDetailsVO = ActivityInfoConverter.assembleEvaluationInfo(listEvaluation);
        // 补充讲师和方案的名称
        evaluationDetailsVO.setListLecturerEvaluation(supplementLectureInfo(evaluationDetailsVO.getListLecturerEvaluation()));
        supplementPlanInfo(evaluationDetailsVO);
        // 兼容迁移数据无整体评价导致前端报错
        EvaluationDetailsInfoVO overallEvaluation = evaluationDetailsVO.getOverallEvaluation();
        if (null == overallEvaluation) {
            evaluationDetailsVO.setOverallEvaluation(new EvaluationDetailsInfoVO());
        }
        evaluationDetailsVO.setEvaluationButtonShow(evaluationButtonShow);
        return evaluationDetailsVO;
    }

    /**
     * 补充方案信息
     *
     * @param evaluationDetailsVO 评价详情
     * <AUTHOR>
     * date: 2023/5/29 16:57
     */
    private void supplementPlanInfo(EvaluationDetailsVO evaluationDetailsVO) {
        List<EvaluationDetailsInfoVO> planEvaluationList = evaluationDetailsVO.getListPlanEvaluation();
        List<EvaluationDetailsInfoVO> lecturerEvaluationList = evaluationDetailsVO.getListLecturerEvaluation();
        if (CollectionUtils.isEmpty(planEvaluationList)) {
            return;
        }
        List<EvaluationDetailsInfoVO> listPlanEvaluation = Lists.newArrayList();
        List<EvaluationDetailsInfoVO> listAttachmentEvaluation = Lists.newArrayList();
        String activityRowId = planEvaluationList.get(ZERO).getActivityRowId();
        // 查询活动方案信息
        List<ActivityRelationSolutionDO> solutionDOList = solutionRepository.queryAllSolutionForActivity(activityRowId);
        if (CollectionUtils.isEmpty(solutionDOList)) {
            return;
        }
        // 获取方案信息，以planId为维度
        List<String> listPlanId = solutionDOList.stream().filter(e -> StringUtils.isNotBlank(e.getPlanId()))
                .map(ActivityRelationSolutionDO::getPlanId).distinct().collect(Collectors.toList());
        Set<String> peopleRowIdSet = lecturerEvaluationList.stream()
                .map(EvaluationDetailsInfoVO::getEvaluateTargetCode)
                .collect(Collectors.toSet());
        Map<String, CustActivityCommPlanVO> planMap = getSolutionPlanMap(listPlanId);
        // 获取附件信息
        Map<String, ActivityRelationAttachmentDO> attachmentMap = getPeopleAttachmentMap(activityRowId, peopleRowIdSet);
        // 装填名称和URL
        planEvaluationList.forEach(e -> {
            if (MapUtils.isNotEmpty(planMap) && EvaluateTypeEnum.SCHEME.isMe(e.getEvaluateType())) {
                EvaluationDetailsInfoVO detailsInfoVO = supplementPlanEvaluationInfo(planMap, e);
                if (detailsInfoVO != null) {
                    listPlanEvaluation.add(detailsInfoVO);
                }
            } else if (MapUtils.isNotEmpty(attachmentMap) && EvaluateTypeEnum.ATTACHMENT.isMe(e.getEvaluateType())) {
                EvaluationDetailsInfoVO detailsInfoVO = supplementAttachmentEvaluationInfo(attachmentMap, e);
                if (detailsInfoVO != null) {
                    listAttachmentEvaluation.add(detailsInfoVO);
                }
            }
        });
        listPlanEvaluation.addAll(listAttachmentEvaluation);
        evaluationDetailsVO.setListPlanEvaluation(listPlanEvaluation);
    }

    /**
     * 获取活动方案附件信息
     *
     * @param activityRowId  活动Id
     * @param peopleRowIdSet 关联人Id集合
     * @return Map<String, ActivityRelationAttachmentDO>
     * <AUTHOR>
     * date: 2023/5/31 13:52
     */
    private Map<String, ActivityRelationAttachmentDO> getPeopleAttachmentMap(String activityRowId, Set<String> peopleRowIdSet) {
        List<ActivityRelationAttachmentDO> listAttachment = relationAttachmentRepository.queryAllByActivityRowId(activityRowId);
        if (CollectionUtils.isEmpty(listAttachment)) {
            return Maps.newHashMap();
        }
        return listAttachment.stream()
                .filter(e -> peopleRowIdSet.contains(e.getSceneOriginRowId()) && SOLUTION.isMe(e.getAttachmentSceneType()))
                .collect(Collectors.toMap(ActivityRelationAttachmentDO::getRowId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 获取讲师方案Map
     *
     * @param listPlanId 引用方案Id列表
     * @return Map<CustActivityCommPlanVO>
     * <AUTHOR>
     * date: 2023/5/31 13:47
     */
    public Map<String, CustActivityCommPlanVO> getSolutionPlanMap(List<String> listPlanId) {
        if (CollectionUtils.isEmpty(listPlanId)) {
            return Maps.newHashMap();
        }
        List<CustActivityCommPlanVO> listPlan = custActivityCommPlanService.getPlanDataByIds(listPlanId);
        return listPlan.stream().collect(Collectors
                .toMap(CustActivityCommPlanVO::getId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 补充讲师信息
     *
     * @param listLecturerEvaluation 讲师评价信息列表
     * @return List<EvaluationDetailsInfoVO>
     * <AUTHOR>
     * date: 2023/5/29 16:42
     */
    private List<EvaluationDetailsInfoVO> supplementLectureInfo(List<EvaluationDetailsInfoVO> listLecturerEvaluation) {
        if (CollectionUtils.isEmpty(listLecturerEvaluation)) {
            return Lists.newArrayList();
        }
        List<EvaluationDetailsInfoVO> lecturerEvaluationList = Lists.newArrayList();
        Map<String, ActivityRelationZtePeopleDO> lecturerNameCodeMap = getLecturerInfoMap(listLecturerEvaluation);
        List<String> listEmpNo = Lists.newArrayList();
        lecturerNameCodeMap.forEach((v, k) -> {
            listEmpNo.add(k.getPeopleCode());
        });
        List<UserInfoDTO> userInfoDTOList = userCenterService.queryUserByIds(listEmpNo);
        Map<String, UserInfoDTO> userInfoDTOMap = userInfoDTOList.stream()
                .collect(Collectors.toMap(UserInfoDTO::getEmployeeShortId, v -> v, (v1, v2) -> v1));
        listLecturerEvaluation.forEach(e -> {
            ActivityRelationZtePeopleDO peopleDO = lecturerNameCodeMap.get(e.getEvaluateTargetCode());
            if (peopleDO == null) {
                return;
            }
            UserInfoDTO userInfo = userInfoDTOMap.get(peopleDO.getPeopleCode());
            if (userInfo == null) {
                return;
            }
            String nameCn = userInfo.getName();
            String nameEn = userInfo.getNameEn();
            if (StringUtils.isBlank(nameCn) && StringUtils.isBlank(nameEn)) {
                return;
            }
            if (StringUtils.isBlank(nameEn)) {
                nameEn = nameCn;
            }
            e.setEvaluateTargetNameCn(nameCn + userInfo.getEmployeeShortId());
            e.setEvaluateTargetNameEn(nameEn + userInfo.getEmployeeShortId());
            e.setEvaluateTargetUrl(userInfo.getHeadIcon());
            lecturerEvaluationList.add(e);
        });
        return lecturerEvaluationList;
    }

    /**
     * 获取讲师信息Map
     *
     * @param listLecturerEvaluation 讲师评价列表
     * @return Map<String, ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/5/31 13:58
     */
    private Map<String, ActivityRelationZtePeopleDO> getLecturerInfoMap(List<EvaluationDetailsInfoVO> listLecturerEvaluation) {
        ActivityRelationZtePeopleQuery query = new ActivityRelationZtePeopleQuery();
        query.setActivityRowId(listLecturerEvaluation.get(ZERO).getActivityRowId());
        query.setPeopleType(LECTURER.getCode());
        List<ActivityRelationZtePeopleDO> lecturers = ztePeopleRepository.queryZtePeople(query);
        return lecturers.stream().collect(Collectors
                .toMap(ActivityRelationZtePeopleDO::getRowId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 判断是否未评价当前活动，已经该活动是否处于待评价状态
     *
     * @param activityRowId 活动主键Id
     * @return boolean  是否评价
     * <AUTHOR>
     * date: 2023/5/29 16:11
     */
    private boolean isNotEvaluate(String activityRowId) {
        String myEmpNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (ActivityTypeEnum.in(activityInfoDO.getActivityType(), VISITING_SAMPLE)) {
            SamplePointInfoDO samplePointInfoDO = samplePointInfoRepository.selectByPrimaryKey(activityInfoDO.getOriginRowId());
            Assert.notNull(samplePointInfoDO, "samplePointInfoDO is null, originRowId="+ activityInfoDO.getOriginRowId());
            Set<String> peopleSet = Sets.newHashSet();
            peopleSet.add(activityInfoDO.getApplyPeopleNo());
            peopleSet.add(samplePointInfoDO.getAdminEmpNo());
            return peopleSet.contains(myEmpNo);
        }
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = ztePeopleRepository.queryZtePeopleByActivityRowId(activityRowId, NEGATIVE_ONE);
        boolean canEvaluate = CollectionUtils.isNotEmpty(ztePeopleDOList.stream()
                .filter(e -> ActivityPeopleTypeEnum.in(e.getPeopleType(), APPLICANT, LECTURER, ORGANIZER, PARTICIPANTS,SITE_PEOPLE)
                        && StringUtils.equals(e.getPeopleCode(), myEmpNo))
                .collect(Collectors.toList()));
        return canEvaluate;
    }

    /**
     * 只显示自己评价的评价人名称，如果是匿名评价则显示匿名评价
     *
     * @param listEvaluation 评价列表
     * @param employeeInfo   人员信息
     * @param myEmpNo        查看人工号
     * @return void
     * <AUTHOR>
     * date: 2023/5/29 16:19
     */
    private void removeOtherName(List<ActivityEvaluationInfoDO> listEvaluation, EmployeeInfoDTO employeeInfo, String myEmpNo) {
        String myName = StringUtils.EMPTY;
        boolean isCn = StringUtils.equals(BizRequestUtil.createWithCurrentUser().getLangId(), LANGUAGE_ZH_CN);
        if (employeeInfo != null) {
            myName = (isCn ? employeeInfo.getEmpName() : employeeInfo.getEmpNameEN()) + myEmpNo;
        }
        for (ActivityEvaluationInfoDO evaluationInfoDO : listEvaluation) {
            boolean isMe = StringUtils.equals(evaluationInfoDO.getCreatedBy(), myEmpNo);
            if (!isMe) {
                evaluationInfoDO.setEvaluatedBy(StringUtils.EMPTY);
            } else if (BooleanEnum.Y.isMe(evaluationInfoDO.getAnonymous())) {
                evaluationInfoDO.setEvaluatedBy(isCn ? ANONTMOUS_EVALUATION_CN : ANONTMOUS_EVALUATION_EN);
            } else if (BooleanEnum.N.isMe(evaluationInfoDO.getAnonymous())) {
                evaluationInfoDO.setEvaluatedBy(myName);
            }
        }
    }

    /**
     * 设置评估人全称
     * @param listEvaluation
     */
    private void resetEvaluatedBy(List<ActivityEvaluationInfoDO> listEvaluation) {
        if (CollectionUtils.isEmpty(listEvaluation)) {
            return;
        }
        String activityType = Optional.ofNullable(infoRepository.selectByPrimaryKey(listEvaluation.get(0).getActivityRowId())).map(ActivityInfoDO::getActivityType).orElse(StringUtils.EMPTY);
        boolean isCn = StringUtils.equals(BizRequestUtil.createWithCurrentUser().getLangId(), LANGUAGE_ZH_CN);
        if (ActivityTypeEnum.in(activityType, VISITING_SAMPLE)) {
            List<String> evaluatedByList = listEvaluation.stream().map(ActivityEvaluationInfoDO::getEvaluatedBy).distinct().collect(Collectors.toList());
            List<EmployeeInfoDTO> employeeInfoDTOList = userCenterService.getUserInfoList(evaluatedByList);
            Map<String, EmployeeInfoDTO> employeeInfoDTOMap = Optional.ofNullable(employeeInfoDTOList).map(eiDTOList -> eiDTOList.stream().collect(Collectors.toMap(EmployeeInfoDTO::getEmpUIID, Function.identity(), (v1, v2) -> v1))).orElse(Maps.newHashMap());
            listEvaluation.forEach(aeiDO -> {
                EmployeeInfoDTO employeeInfoDTO = employeeInfoDTOMap.getOrDefault(aeiDO.getEvaluatedBy(), new EmployeeInfoDTO());
                aeiDO.setEvaluatedBy((isCn ? employeeInfoDTO.getEmpName() : employeeInfoDTO.getEmpNameEN()) + employeeInfoDTO.getEmpUIID());
            });
            return;
        }
        String empNo = BizRequestUtil.createWithCurrentUser().getEmpNo();
        EmployeeInfoDTO employeeInfoDTO = userCenterService.getUserInfo(empNo);
        removeOtherName(listEvaluation, employeeInfoDTO, empNo);
    }

    /**
     * 通过活动主键Id查询会议纪要详情
     *
     * @param activityRowId 活动Id主键
     * @return SummaryInfoVO    会议纪要详情
     * <AUTHOR>
     * date: 2023/5/26 19:13
     */
    @Override
    public SummaryInfoVO getSummaryInfo(String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return null;
        }
        List<ActivityRelationZtePeopleDO> listZtePeople = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        return getSummaryInfoVO(activityRowId, listZtePeople, Lists.newArrayList());
    }

    @Override
    public CustomerReceptionVO getActivityInfoForInitReception(String activityRowId) {
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (Objects.isNull(activityInfoDO)) {
            return null;
        }
        ActivityDetailInfoVO detailInfoVO = new ActivityDetailInfoVO();
        // 查询活动基本信息
        List<ActivityRelationZtePeopleDO> listZtePeople = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        queryActivityBaseInfo(activityRowId, activityInfoDO, detailInfoVO, listZtePeople);
        // 获取account-info 客户信息
        List<String> customerCodes = detailInfoVO.getRelationPeopleInfo().getListCustInfo()
                .stream().map(CustUnitInfoVO::getCustomerCode).collect(Collectors.toList());
        String activityOrgCode = Optional.ofNullable(activityInfoDO.getApplyDepartmentNo()).orElse(JOINT_STOCK_COMPANY_ID);
        // 获取客户信息
        List<AccountVO> accountList = queryCustomerListByCustomerCodes(customerCodes, activityOrgCode);
        // 获取客户联系人信息
        List<ContactVO> custPeopleList = getContactPeopleList(detailInfoVO);

        // 转换为客户接待预填充数据
        return ActivityInfoConverter.assembleCustomerReceptionInfo(detailInfoVO, accountList, custPeopleList);

    }

    /**
     * 查询客户联系人信息
     *
     * @param detailInfoVO
     * @return
     */
    private List<ContactVO> getContactPeopleList(ActivityDetailInfoVO detailInfoVO) {
        List<ContactVO> custPeopleList = Lists.newArrayList();
        List<String> customerPeopleCodes = Lists.newArrayList();
        for (CustUnitInfoVO custUnitInfoVO : detailInfoVO.getRelationPeopleInfo().getListCustInfo()) {
            customerPeopleCodes.addAll(custUnitInfoVO.getListCustPeople()
                    .stream().map(CustPeopleInfoVO::getContactNo).collect(Collectors.toList()));
        }
        custPeopleList.addAll(fetchContactPerson(customerPeopleCodes));
        return custPeopleList;
    }

    /**
     * 获取客户信息
     *
     * @param customerCodes
     * @return
     */
    List<AccountVO> queryCustomerListByCustomerCodes(List<String> customerCodes, String activityOrgCode) {
        Account account = new Account();
        account.setAccntNumList(customerCodes);
        // 处理kw问题，因为copyList有返回null的分支，导致这里方法调用给外部也有返回null。但实际代码肯定不会空，这里避免null的CCA扫描返回
        List<AccountVO> resList = null;
        try {
            ServiceData<List<Account>> serviceData = accountAdapter.authorityAccountFetchTenant(account,
                    AccountInfoAuthTypeEnum.ALL.getCode(), NumberConstant.ONE, customerCodes.size(), activityOrgCode);
            if (CollectionUtils.isNotEmpty(serviceData.getBo())) {
                resList = CopyUtil.copyList(serviceData.getBo(), AccountVO.class);
            }
        } catch (Exception e) {
            logger.error("获取客户信息异常, {}", customerCodes, e);
        }
        return resList == null ? Collections.emptyList() : resList;
    }

    /**
     * 根据编号批量查询联系人
     *
     * @param conPerNums
     * @return {@link List<ContactVO>}
     */
    private List<ContactVO> fetchContactPerson(List<String> conPerNums) {
        try {
            List<ContactVO> contactVOList = accountAdapter.getContactNums(conPerNums);
            return CollectionUtils.isEmpty(contactVOList) ? Collections.emptyList() : contactVOList;
        } catch (Exception e) {
            logger.error("获取客户联系人异常, {}", conPerNums, e);
        }
        return Collections.emptyList();
    }

    /**
     * 编辑、赋值活动
     *
     * @param activityRowId 活动Id
     * @return ActivityDetailInfoVO
     * <AUTHOR>
     * date: 2023/6/15 11:13
     */
    @Override
    public ActivityDetailInfoVO getActivityDetailDraftInfo(String activityRowId) {
        ActivityDetailInfoVO detailInfoVO = getActivityInfo(activityRowId);
        updateCustInfo(detailInfoVO);
        return detailInfoVO;
    }

    /**
     * 获取活动基本信息-APP端使用（中兴参与人绑定预约状态）
     *
     * @param activityRowId 活动Id
     * @return ActivityDetailInfoVO
     * <AUTHOR>
     * date: 2023/6/21 9:17
     */
    @Override
    public ActivityDetailInfoVO getActivityDetailInfoToApp(String activityRowId) {
        ActivityDetailInfoVO detailInfoVO = getActivityInfo(activityRowId);
        if (null != detailInfoVO && ActivityOriginTypeEnum.in(detailInfoVO.getBaseInfo().getOriginType(), EXHIBITION, CONFERENCE)) {
            // 完善展会相关信息
            this.perfectAppExhibitionInfoForApp(detailInfoVO);
            // 转换酒店信息
            this.perfectHotelInfoForApp(detailInfoVO, activityRowId);
            // 转换日程信息
            this.perfectScheduleItemInfoForApp(detailInfoVO, activityRowId);
        }
        return detailInfoVO;
    }

    /**
     * 转发到icenter，加入鉴权
     *
     * @param messageBodyParam
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/17 下午8:02
     */
    @Override
    public boolean forwardMessageToICenter(MessageBodyParam messageBodyParam) {
        if (StringUtils.isBlank(messageBodyParam.getId())) {
            return false;
        }
        ActivityInfoQuery query = new ActivityInfoQuery();
        CustomerIntegrationAuthModel authModel = hrmUserCenterSearchService.getAuthModel(HeadersProperties.getXEmpNo());
        query.setAuthModel(authModel);
        query.setActivityRowIdList(Collections.singletonList(messageBodyParam.getId()));
        long count = infoRepository.countList(query);
        if (count <= ZERO) {
            throw new BizRuntimeException(RetCode.PERMISSIONDENIED_CODE, RetCode.PERMISSIONDENIED_MSGID);
        }
        return forwardMessageComponent.forwardMessageToICenter(messageBodyParam);
    }

    /**
     * 作废活动
     *
     * @param param 作废活动入参
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/10 13:56
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public String invalidate(BizRequest<ActivityInvalidateParam> param) {
        ActivityInvalidateParam invalidateParam = param.getParam();
        // 参数校验
        invalidateParam.check();
        ActivityInvalidateModel invalidateModel = new ActivityInvalidateModel();
        String activityRowId = invalidateParam.getActivityRowId();
        // 业务校验
        invalidateBizCheck(invalidateModel, activityRowId);
        // 活动作废
        updateActivityStatus(activityRowId, ABOLISH.getCode());
        saveActivityToLifeCycle(activityRowId, invalidateModel.getActivityInfo().getActivityStatus(),
                ABOLISH.getCode(), invalidateParam.getInvalidatedReason());
        // 取消所有待办
        cancelAllPendingNotice(activityRowId);
        // 推送至ISearch
        return activityRowId;
    }

    /**
     * 更新活动状态
     * @param activityRowId  活动Id
     * @param activityStatus 活动状态
     * @return void
     * <AUTHOR>
     * date: 2023/8/14 15:40
     */
    private void updateActivityStatus(String activityRowId, String activityStatus) {
        ActivityInfoDO infoDO = ActivityInfoConverter.buildOf(activityRowId, activityStatus);
        infoRepository.updateByPrimaryKeySelective(infoDO);
    }

    /**
     * 取消所有待办
     * @param activityRowId 活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/8/10 17:35
     */
    private void cancelAllPendingNotice(String activityRowId) {
        activityPendingNoticeRepository.updateTargetStatusByActivityRowId(activityRowId,
                PendingNoticeStatusEnum.WAIT_DEAL.getStatus(), PendingNoticeStatusEnum.DELETE.getStatus());
    }

    /**
     * 作废操作校验
     * @param invalidateModel 作废数据源
     * @return void
     * <AUTHOR>
     * date: 2023/8/10 16:53
     */
    private void invalidateBizCheck(ActivityInvalidateModel invalidateModel, String actRowId) {
        // 业务校验：是否符合作废要求，活动状态正确：已完成/已评价
        invalidateModel.checkActivityInfo(infoRepository.selectByPrimaryKey(actRowId));
        // 权限校验：是否具备作废权限
        List<ActivityInfoVO> voList = infoListQueryService.getActivityAuthInfo(
                BizRequestUtil.createWithCurrentUser(Collections.singletonList(actRowId)));
        if (CollectionUtils.isEmpty(voList)) {
            throw new BizRuntimeException(RetCode.PERMISSIONDENIED_CODE, RetCode.PERMISSIONDENIED_MSGID);
        }
        invalidateModel.checkAndSetActivityInfo(voList.get(ZERO));
    }

    /**
     * 绑定中兴参与人的预约状况
     *
     * @param detailInfoVO 活动详情
     * @return void
     * <AUTHOR>
     * date: 2023/6/21 9:19
     */
    private void bindReservationStatus(ActivityDetailInfoVO detailInfoVO) {
        // 草稿活动不具备预约情况，仅更新参与人信息
        if (DRAFT.isMe(detailInfoVO.getBaseInfo().getActivityStatus())) {
            updateCustInfo(detailInfoVO);
            return;
        }
        RelationPeopleInfoVO relationPeopleInfo = detailInfoVO.getRelationPeopleInfo();
        if (relationPeopleInfo == null || CollectionUtils.isEmpty(relationPeopleInfo.getListZtePeopleInfo())) {
            return;
        }
        List<ZtePeopleVO> listZtePeopleInfo = relationPeopleInfo.getListZtePeopleInfo();
        // 查询活动预约详情
        ActivityResourceReservationVo reservationVo = resourceReservationService
                .queryReservationDetail(detailInfoVO.getBaseInfo().getRowId());
        if (reservationVo == null || CollectionUtils.isEmpty(reservationVo.getReservationDetails())) {
            return;
        }
        List<ActivityResourceReservationDetailVo> reservationDetails = reservationVo.getReservationDetails();
        Map<String, ActivityResourceReservationDetailVo> reservationDetailVoMap = reservationDetails.stream()
                .collect(Collectors.toMap(ActivityResourceReservationDetailVo::getReserveDealerEmpNo, v -> v, (v1, v2) -> v1));
        // 绑定每个中兴参与人的预约情况
        listZtePeopleInfo.forEach(e -> {
            ActivityResourceReservationDetailVo resourceReservationDetailVo = reservationDetailVoMap.get(e.getPeopleCode());
            if (resourceReservationDetailVo == null) {
                return;
            }
            e.setReserveStatus(resourceReservationDetailVo.getReserveStatus());
            e.setReserveStatusName(resourceReservationDetailVo.getReserveStatusName());
        });
    }


    /**
     * 活动变更接口
     *
     * @param request 变更请求
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/29 21:46
     */
    @Override
    public String changeActivity(BizRequest<ActivityBaseInfoVO> request) {
        checkService.checkActivityChangePermissionAndStatusIsValid(request);
        checkService.checkActivitySubmitData(request.getParam());

        ActivityBaseInfoVO newBaseInfo = request.getParam();
        String activityRowId = newBaseInfo.getRowId();
        // 兼容历史数据编辑活动修改成内部活动
        setInternalCustPeoplePositionName(newBaseInfo.getListCustPeopleInfo(), newBaseInfo.getActivityType());

        //关联日程安排rowId和参与人activityScheduleItemRowId 和谈参的scene_origin_row_id
        assignScheduleAndPeopleInfoRowId(newBaseInfo, activityRowId, newBaseInfo.getIsNewCreateOrCopy());

        ActivityBO newActivityBO = activityInfoVoToDo(newBaseInfo, activityRowId, getCreatedByInfo());
        fillApplyFullDepartCode(newActivityBO.getActivityInfoDO());
        //区分  其他项目、暂无关联项目、正常项目
        assembleProjectInfo(activityRowId, newActivityBO, newBaseInfo);
        ActivityBO oldActivityBO = infoService.getActivityBaseInfoById(activityRowId);
        // 设置一个字段是否改变的字段
        infoService.compareActivityInfo(newActivityBO, oldActivityBO);

        // 设置审批自动 根据字段后过滤审批人即可
        changeApproveInfo(newBaseInfo, newActivityBO,oldActivityBO);
        /*
         由于启动审批后，审批中心那边已经启动审批，且可能会发送审批节点创建的事件。
         如果创建活动事务还未提交，则审批节点创建事件会接收失败，导致活动审批流程走不下去（测试环境的kafka不太稳定）
         故，活动应该先提交事务。后续应该考虑增加版本号进行数据管控
         */
        transactionTemplate.execute(st -> changeActivityInfo(newBaseInfo, newActivityBO, oldActivityBO, st));
        changeNoticeExternal(newBaseInfo, newActivityBO, oldActivityBO);
        iSearchService.asyncSendActivityData2ISearch(activityRowId);
        return activityRowId;
    }

    private void changeApproveInfo(ActivityBaseInfoVO newBaseInfo, ActivityBO newActivityBO,ActivityBO oldActivityBO) {
        ActivityInfoDO activityInfo = newActivityBO.getActivityInfoDO();
        if (!ActivityTypeEnum.in(activityInfo.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE, VISITING_SAMPLE)) {
            return;
        }
        // 增加公司变化场景也需要走业务审批
        if (newActivityBO.buildNeedApprove()) {
            fillApprovalInfo(newBaseInfo, newActivityBO);
        } else {
            newBaseInfo.setApprovalList(Lists.newArrayList());
            activityInfo.setActivityStatus(PROGRESS.getCode());
        }
    }

    /**
     * 变更活动信息
     * @param newBaseInfo   新活动基本信息
     * @param newActivityBO 新活动信息
     * @param oldActivityBO 旧活动信息
     * @param st
     * @return void
     * <AUTHOR>
     * date: 2023/9/7 20:16
     */
    private boolean changeActivityInfo(ActivityBaseInfoVO newBaseInfo, ActivityBO newActivityBO,
                                       ActivityBO oldActivityBO, TransactionStatus st) {
        try {
            String activityRowId = newBaseInfo.getRowId();
            dataAbilityService.deleteActivityAllOtherInfo(activityRowId);
            fillFromDbBeforeChange(newActivityBO, oldActivityBO.getActivityInfoDO());
            // 暂时沿用活动的更新
            updateActivityInfo(newActivityBO);
            //插入活动日志表
            insertActivityLog(newActivityBO, activityRowId);
            changeActivityToLifeCycle(newActivityBO, oldActivityBO, activityRowId);
            //参观样板点，变更活动后释放历史预约日程资源 补充：如果活动结束时间修改为当前时间之前，则需要将当前活动存在的预约日程释放
            this.resourceReleaseAfterActivityChange(newBaseInfo, oldActivityBO);
            //预约日程
            createReservation(newActivityBO, newBaseInfo, true);
        } catch (Exception e) {
            st.setRollbackOnly();
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, lmsb.getMessage(TIPS_COMMON_BUSINESS_ERROR));
        }
        return true;
    }

    private void resourceReleaseAfterActivityChange(ActivityBaseInfoVO newBaseInfo, ActivityBO oldActivityBO) {
        // 展会/大会不涉及活动日程
        if (ActivityTypeEnum.in(newBaseInfo.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE)) {
            return;
        }
        // 样板点保持之前的逻辑不变
        String activityRowId = newBaseInfo.getRowId();
        if (ActivityTypeEnum.in(newBaseInfo.getActivityType(), VISITING_SAMPLE)) {
            resourceReleaseService.releaseResource(BizRequestUtil.createWithCurrentUser(activityRowId));
            return;
        }
        // 四大融合活动需要校验该部分功能
        Date currentTime = new Date();
        boolean isNewEndTimeBeforeNow = newBaseInfo.getEndTime().before(currentTime);
        boolean isOldEndTimeAfterNow = oldActivityBO.getActivityInfoDO().getEndTime().after(currentTime);
        if (isNewEndTimeBeforeNow && isOldEndTimeAfterNow) {
            resourceReleaseService.releaseAllExistActivityScheduleResource(BizRequestUtil.createWithCurrentUser(activityRowId));
        }
    }

    /**
     * 活动变更发送外部通知
     * @param baseInfo      基本信息
     * @param newActivityBO 新活动信息
     * @param oldActivityBO 旧活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/3 20:02
     */
    private void changeNoticeExternal(ActivityBaseInfoVO baseInfo, ActivityBO newActivityBO, ActivityBO oldActivityBO) {
        //启动审批或创建待办事项。如果这里失败了怎么续上？虽然概率非常小，但是后续要考虑下，至少要有个一个问题补偿处理
        startApproveOrCreatePendingNotice(newActivityBO, baseInfo);
        //发送工作通知
        sendChangeNotice(baseInfo, newActivityBO, oldActivityBO);
        //创建事件
        createEvent(newActivityBO);
    }

    /**
     * 发送变更通知
     * 勾选了【通知所有人员】：申请人、知会人、我司参与人里面的所有人（去重，非新增的人，注意：新增的人收到的通知同1和2 ）
     * 勾选了【通知变更人员】：比较上面前后的变更人员（非新增的人，注意：新增的人收到的通知同1和2 ）
     * 勾选了【不通知】：无需发送icenter工作通知
     * @param newBaseInfo   新基础信息
     * @param newActivityBO 新活动
     * @param oldActivityBO 旧活动
     * @return void
     * <AUTHOR>
     * date: 2023/8/31 17:14
     */
    public void sendChangeNotice(ActivityBaseInfoVO newBaseInfo, ActivityBO newActivityBO, ActivityBO oldActivityBO) {
        String detailUrl = activityUrlConfig.fetchDetailUrl(newActivityBO.getActivityInfoDO().getRowId(), newActivityBO.getActivityInfoDO().getActivityType(), "first");
        String talkInfoUrl = activityUrlConfig.fetchDetailUrl(newActivityBO.getActivityInfoDO().getRowId(), newActivityBO.getActivityInfoDO().getActivityType(), SEVENTH);
        // 2024/04/10 变更提交 新增讲师没有上传交流方案
        List<String> oldLecturerCode = oldActivityBO.getListZtePeopleInfo().stream().filter(e->LECTURER.getCode().equals(e.getPeopleType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toList());

        List<ZtePeopleVO> lectureNoticeList = newBaseInfo.getListZtePeopleInfo().stream()
                .filter(e -> !oldLecturerCode.contains(e.getPeopleCode()))
                .collect(Collectors.toList());
        SendWorkOrderSimpleVO lectureNoticeVO = buildSubmitLecturerUploadNotice(newActivityBO, lectureNoticeList,detailUrl);
        if (Objects.nonNull(lectureNoticeVO)) {
            infoCenterService.sendActivityWorkNoticeMessage(lectureNoticeVO);
        }
        // 给谈参负责人发送催办上传谈参的工作通知
        Map<String, SendWorkOrderSimpleVO> workNoticeMap = buildUploadTalkInfoNotice(newActivityBO, newBaseInfo);
        if (MapUtils.isNotEmpty(workNoticeMap)) {
            /* Started by AICoder, pid:4f2857157f1ef62148790ad970ea2b08b4245cc4 */
            workNoticeMap.values().forEach(workNotice -> {
                workNotice.setUrl(talkInfoUrl);
                infoCenterService.sendActivityWorkNoticeMessage(workNotice);
            });
            /* Ended by AICoder, pid:4f2857157f1ef62148790ad970ea2b08b4245cc4 */
        }
        //活动变更后给提单人、申请人、知会人、参与人、组织者和专家发送活动变更工作通知
        String changeBy = HeadersProperties.getXEmpNo();
        EmployeeInfoDTO employeeInfoDTO = userCenterService.getUserInfo(changeBy);
        // 勾选不通知
        String changeNoticeFlag = newBaseInfo.getChangeNoticeFlag();
        if(!NoticeUtil.isEffectiveFlag(changeNoticeFlag) || NoticeUtil.NO_NOTICE.equals(changeNoticeFlag) || currTimeout(newBaseInfo)){
            return;
        }

        SendWorkOrderSimpleVO sendAddNotice;
        SendWorkOrderSimpleVO sendChangeNotice;
        // 勾选通知变更人 或者 勾选通知所有人
        // 通知新增人-我司参与人
        List<String> addNoticePeopleList = NoticeUtil.getAddNoticePeopleList(oldActivityBO.getListZtePeopleInfo(), newActivityBO.getListZtePeopleInfo(),
                NoticeUtil.isExhibitionOrConference(newBaseInfo.getActivityType()));
        // 通知新增人-客户参与人
        List<String> addCustomNoticePeopleList = NoticeUtil.getAddOrRemoveCustomPeopleList(oldActivityBO.getListCustPeopleInfo(), newActivityBO, true,false);
        addNoticePeopleList.addAll(addCustomNoticePeopleList);
        log.info("[变更活动]新增通知人：{}", addNoticePeopleList);

        sendAddNotice = ActivityInfoConverter.buildScheduleNoticeModel(newActivityBO, newBaseInfo, addNoticePeopleList);

        // 变更通知
        List<String> changeNoticePeopleList = NoticeUtil.getChangeNoticePeopleList(oldActivityBO.getListZtePeopleInfo(),
                newActivityBO.getListZtePeopleInfo(), NoticeUtil.ALL_NOTICE.equals(changeNoticeFlag),
                NoticeUtil.isExhibitionOrConference(newBaseInfo.getActivityType()));
        // 变更通知-移除的客户参与人，如果是通知所有人则直接返回原有的全部人员
        List<String> removeCustomNoticePeopleList = NoticeUtil.getAddOrRemoveCustomPeopleList(oldActivityBO.getListCustPeopleInfo(), newActivityBO, false,NoticeUtil.ALL_NOTICE.equals(changeNoticeFlag));
        changeNoticePeopleList.addAll(removeCustomNoticePeopleList);
        log.info("[变更活动]变更通知人：{}", changeNoticePeopleList);

        sendChangeNotice = ActivityInfoConverter.buildActivityChange(newBaseInfo, employeeInfoDTO, changeNoticePeopleList);

         // 申请人变更发送提交通知
         if(NoticeUtil.isChangeApplicant(oldActivityBO.getListZtePeopleInfo(),newBaseInfo.getApplyPeopleNo())) {
             SendWorkOrderSimpleVO simpleVO = buildActivitySubmit(newActivityBO, employeeInfoDTO, newBaseInfo.getApplyPeopleNo());
             if (Objects.nonNull(simpleVO)) {
                 simpleVO.setUrl(detailUrl);
                 infoCenterService.sendActivityWorkNoticeMessage(simpleVO);
             }
         }

        if (Objects.nonNull(sendAddNotice)) {
            // 跳详情-基础信息
            sendAddNotice.setUrl(detailUrl);
            infoCenterService.sendActivityWorkNoticeMessage(sendAddNotice);
        }
        if (Objects.nonNull(sendChangeNotice)) {
            sendChangeNotice.setUrl(detailUrl);
            infoCenterService.sendActivityWorkNoticeMessage(sendChangeNotice);
        }
    }

    /**
     * 变更活动生成生命周期
     * @param newActivityBO 新活动信息
     * @param activityRowId 活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 17:15
     */
    private void changeActivityToLifeCycle(ActivityBO newActivityBO, ActivityBO oldActivityBO, String activityRowId) {
        String oldStatus = oldActivityBO.getActivityInfoDO().getActivityStatus();
        String changeStatus = CHANGE.getCode();
        // 插入进行中至变更中
        saveActivityToLifeCycle(activityRowId, oldStatus, changeStatus, null);

        ActivityInfoDO activityInfoDO = newActivityBO.getActivityInfoDO();
        if (ActivityTypeEnum.in(activityInfoDO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE, VISITING_SAMPLE)) {
            saveActivityToLifeCycle(activityRowId, changeStatus, activityInfoDO.getActivityStatus(), null);
            return;
        }

        if (Boolean.TRUE.equals(newActivityBO.getNeedApprove())) {
            // 插入至合规审批中
            saveActivityToLifeCycle(activityRowId, changeStatus, COMPLIANCE_APPROVAL.getCode(), null);
            return;
        }
        // 插入至进行中
        saveActivityToLifeCycle(activityRowId, changeStatus, PROGRESS.getCode(), null);
    }

    /**
     * 通知外部
     * @param baseInfo
     * @param activityBO
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 16:46
     */
    public BizResult<String> submitNoticeExternal(ActivityBaseInfoVO baseInfo, ActivityBO activityBO) {
        // 如果是依赖的其他的失败，还是对外返回成功，并提供描述（方法修改为public仅仅为了覆盖，因为之前都没有覆盖分支）
        BizResult<String> res = BizResult.buildSuccessRes(activityBO.getActivityInfoDO().getRowId());
        try {
            //预约日程
            createReservation(activityBO, baseInfo, false);
            //启动审批或创建待办事项。如果这里失败了怎么续上？虽然概率非常小，但是后续要考虑下，至少要有个一个问题补偿处理
            startApproveOrCreatePendingNotice(activityBO, baseInfo);
            //发送工作通知
            sendNotice(activityBO, baseInfo);
            //创建事件
            createEvent(activityBO);
        } catch (BizRuntimeException bizException) {
            logger.error("客户拓展活动提交异常", bizException);
            //回滚活动状态到草稿
            rollBackAcitivity(activityBO);
            Locale locale = LocaleContextHolder.getLocale();
            String msg = messageSource.getMessage(bizException.getMsg(), bizException.getArgs(), bizException.getMsg(), locale);
            res.setMsg(msg);
        } catch (Exception e) {
            logger.error("客户拓展活动提交异常", e);
            //回滚活动状态到草稿
            rollBackAcitivity(activityBO);
            res.setMsg(lmsb.getMessage(TIPS_COMMON_BUSINESS_ERROR));
        } finally {
            // 因为数据已经落库，无论对外广播发生了什么，必须要推送Es
            iSearchService.sendActivityDataToISearch(res.getData());
        }
        return res;
    }

    @Override
    public Boolean scheduleReservationAndWorkNotice(BizRequest<ReserveScheduleInitiationParam> bizRequest) {

        ReserveScheduleInitiationParam bizRequestParam = Optional.ofNullable(bizRequest.getParam()).orElse(new ReserveScheduleInitiationParam());
        String activityId = bizRequestParam.getActivityRowId();
        ActivityInfoDO activityInfo = infoRepository.selectByPrimaryKey(activityId);
        if (!checkParam(activityInfo)) {
            return false;
        }
        // 填充申请人姓名 ==ActivityInfoDO对象中为什么有非数据库字段？？？
        List<EmployeeInfoDTO> listUser = userCenterService.getUserInfoList(Collections.singletonList(activityInfo.getApplyPeopleNo()));
        Map<String, EmployeeInfoDTO> map = listUser.stream().collect(Collectors.toMap(EmployeeInfoDTO::getEmpUIID, Function.identity(), (key1, key2) -> key2));
        EmployeeInfoDTO applyUser = map.getOrDefault(activityInfo.getApplyPeopleNo(), new EmployeeInfoDTO());
        activityInfo.setApplyPeopleName(applyUser.getEmpName());
        // 创建日程安排
        scheduleOrchestrationSynScheduleService.createIcenterSchedule(activityInfo);

        if (!bizRequestParam.getNeedSendIcenter()) {
            return true;
        }
        // 发送工作通知：给我司参与人、知会人发，标题有区别
        this.sendNotice(activityInfo);
        return Boolean.TRUE;
    }

    @Override
    public CountContactVisitVO countAllContactVisits(String startTime, String endTime) {
        CountContactVisitVO countContactVisitVO = CountContactVisitVO.getNewCountContactVisitVO();

        List<AccountInfoDTO> accountInfoDTOList = accountInfoCaller.queryABAccountContacts(null);
        Map<String, List<AccountInfoDTO>> accountInfoDTOMap = accountInfoDTOList.stream().collect(Collectors.groupingBy(AccountInfoDTO::getPersonLevel));

        Map<String, Integer> contactNumMap = countContactVisitVO.getContactNumMap();
        Map<String, Integer> noContactNumMap = countContactVisitVO.getNoContactNumMap();
        Map<String, Integer> visitNumMap = countContactVisitVO.getVisitNumMap();
        List<String> allActivityRowIdList = new ArrayList<>();

        for (String personLevel : accountInfoDTOMap.keySet()) {
            contactNumMap.put(personLevel, accountInfoDTOMap.get(personLevel).size());

            List<ActivityRelationContactDO> activityRelationCustPeopleDOList = custPeopleRepository.queryAllContactVisits(
                    accountInfoDTOMap.get(personLevel).stream().map(AccountInfoDTO::getContactNo).collect(Collectors.toList()), startTime, endTime);
            List<String> activityRowIdList = activityRelationCustPeopleDOList.stream().map(ActivityRelationContactDO::getActivityRequestNo).distinct().collect(Collectors.toList());
            visitNumMap.put(personLevel, activityRowIdList.size());
            allActivityRowIdList.addAll(activityRowIdList);

            List<String> contactList = accountInfoDTOMap.get(personLevel).stream().map(AccountInfoDTO::getContactNo).distinct().collect(Collectors.toList());
            List<String> visitContactList = activityRelationCustPeopleDOList.stream().map(ActivityRelationCustPeopleDO::getContactNo).distinct().collect(Collectors.toList());
            contactList.removeAll(visitContactList);
            noContactNumMap.put(personLevel, contactList.size());
        }
        countContactVisitVO.setContactSum(accountInfoDTOList.size());
        countContactVisitVO.setVisitSum((int) allActivityRowIdList.stream().distinct().count());
        countContactVisitVO.setNoContactSum(noContactNumMap.values().stream().mapToInt(i -> i).sum());
        return countContactVisitVO;
    }

    @Override
    public ActivityRelationReceptionVO getRelationReceptionInfo(String activityRowId) {
        ActivityRelationReceptionVO relationReceptionVO = new ActivityRelationReceptionVO();
        relationReceptionVO.init(activityRowId);
        if (StringUtils.isBlank(activityRowId)) {
            return relationReceptionVO;
        }

        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (Objects.isNull(activityInfoDO) || !BooleanEnum.Y.isMe(activityInfoDO.getEnabledFlag())) {
            return relationReceptionVO;
        }

        if (!CUSTOMER_VISIT_ACTIVITY.isMe(activityInfoDO.getActivityType())) {
            return relationReceptionVO;
        }

        // 查询接待单绑定信息
        List<ActivityReceptionMappingDO> receptionMappingList = activityReceptionMappingRepository.listReceptionByActivityRowId(activityRowId);
        if (CollectionUtils.isEmpty(receptionMappingList)) {
            return relationReceptionVO;
        }

        // 根据绑定的接待单详情
        Map<String, ActivityReceptionMappingDO> receptionDoMap = receptionMappingList.stream().collect(Collectors.toMap(ActivityReceptionMappingDO::getReceiveId, i -> i, (u, v) -> u));
        Map<String, ActivityReceiveInfo> activityReceiveInfoMap =
            activityReceptionMappingRepository.getDetailByReceiveIdList(com.google.common.collect.Lists.newArrayList(receptionDoMap.keySet())).stream().filter(item -> {
                if (EXPANSION_STATE_15.equalsIgnoreCase(item.getSubApplyState())) {
                    return false;
                }
                if (EXPANSION_STATE_16.equalsIgnoreCase(item.getSubApplyState())) {
                    return false;
                }
                return !EXPANSION_STATE_79.equalsIgnoreCase(item.getSubApplyState());
            }).collect(Collectors.toMap(ActivityReceiveInfo::getReceiveId, i -> i, (u, v) -> u));

        Set<String> needDeletedReceptionMappingSet = receptionMappingList.stream().filter(item ->
                !activityReceiveInfoMap.containsKey(item.getReceiveId())).map(ActivityReceptionMappingDO::getRowId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(needDeletedReceptionMappingSet)) {
            // 接待单已失效 需要解绑
            activityReceptionMappingRepository.softDeletedByBatch(null, needDeletedReceptionMappingSet);
        }

        if (MapUtils.isEmpty(activityReceiveInfoMap)) {
            return relationReceptionVO;
        }

        relationReceptionVO.setActivityReceiveInfoList(new ArrayList<>(activityReceiveInfoMap.values()));
        relationReceptionVO.setActivityRelationReceptionFlag(true);
        return relationReceptionVO;
    }

    private Boolean checkParam(ActivityInfoDO activityInfo) {
        if (activityInfo == null) {
            return false;
        }
        // 如果活动已结束，那么无需预约
        if (Objects.isNull(activityInfo.getEndTime()) || new Date().after(activityInfo.getEndTime())) {
            log.info("活动已结束,无需预约日程,activityRowId:{}", activityInfo.getRowId());
            return false;
        }
        // 如果已经预约，则无需再次预约
        BizResult<Boolean> bizResult = scheduleOrchestrationQueryService.queryReservationScheduleStatus(BizRequestUtil.createWithCurrentUser(activityInfo.getRowId()));
        if (!bizResult.getData()) {
            log.info("活动已经预约过,无需再次预约日程,activityRowId:{}", activityInfo.getRowId());
            return false;
        }
        return true;
    }

    private void sendNotice(ActivityInfoDO activityInfo) {

        Boolean changeFlag = activityResourceReservationScheduleRepository.checkIfScheduleHasChanged(activityInfo.getRowId());

        List<ActivityRelationZtePeopleDO> activityRelationZtePeopleList = ztePeopleRepository.queryAllZtePeopleForActivity(activityInfo.getRowId());
        List<String> ztePeopleList = activityRelationZtePeopleList.stream()
                .filter(e -> ActivityPeopleTypeEnum.in(e.getPeopleType(), ActivityPeopleTypeEnum.allParticipantsType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode)
                .collect(Collectors.toList());
        List<String> informerList = activityRelationZtePeopleList.stream()
                .filter(e -> ActivityPeopleTypeEnum.INFORMED.isMe(e.getPeopleType())).map(ActivityRelationZtePeopleDO::getPeopleCode)
                .collect(Collectors.toList());
        // 给我司参与人发工作通知
        SendWorkOrderSimpleVO workOrderVO = buildScheduleNoticeModel(activityInfo, getZtePeopleNoticeSubject(activityInfo, changeFlag), ztePeopleList);
        infoCenterService.sendCommonWorkNoticeMessage(workOrderVO);
        // 给知会人发工作通知
        if (CollectionUtils.isNotEmpty(informerList)) {
            SendWorkOrderSimpleVO informedNoticeMsg = buildScheduleNoticeModel(activityInfo, getInformedNoticeSubject(activityInfo), informerList);
            infoCenterService.sendCommonWorkNoticeMessage(informedNoticeMsg);
        }
    }

    public SendWorkOrderSimpleVO buildScheduleNoticeModel(ActivityInfoDO activityInfo, SendWorkOrderContentVO subject, List<String> to) {
        SendWorkOrderContentVO contend = new SendWorkOrderContentVO();
        contend.setZh(ACTIVITY_TITLE_DESC + activityInfo.getActivityTitle() + "\n" + ACTIVITY_TIME + getYmdHmDate(activityInfo.getStartTime()) + "-" + getYmdHmDate(activityInfo.getEndTime()));
        contend.setEn("title:" + activityInfo.getActivityTitle() + "\n" + "TIME:" + activityInfo.getStartTime() + "-" + activityInfo.getEndTime());
        SendWorkOrderSimpleVO sendWorkOrderVO = new SendWorkOrderSimpleVO();
        sendWorkOrderVO.setId(activityInfo.getRowId());
        sendWorkOrderVO.setFrom(HeadersProperties.getXEmpNo());
        sendWorkOrderVO.setSummary(contend);
        sendWorkOrderVO.setUrl(activityUrlConfig.fetchDetailUrl(activityInfo.getRowId(), activityInfo.getActivityType(), "first"));
        sendWorkOrderVO.setTo(to);
        sendWorkOrderVO.setSubject(subject);
        return sendWorkOrderVO;
    }


    private static SendWorkOrderContentVO getZtePeopleNoticeSubject(ActivityInfoDO activityInfo, Boolean changeFlag) {
        SendWorkOrderContentVO subject = new SendWorkOrderContentVO();
        subject.setZh(activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + ActivityConstant.ACTIVITY_INVITE_TITLE_PRE + activityInfo.getActivityTitle() + ACTIVITY_INVITE_TITLE_END);
        subject.setEn(activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + " invite you to participate [" + activityInfo.getActivityTitle() + "] activity");
        if (changeFlag) {
            subject.setZh(ActivityConstant.CHANGE_ACTIVITY_NOTICE_TITLE_CN + activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + ActivityConstant.ACTIVITY_INVITE_TITLE_PRE + activityInfo.getActivityTitle() + ACTIVITY_INVITE_TITLE_END);
            subject.setEn(ActivityConstant.CHANGE_ACTIVITY_NOTICE_TITLE_EN + activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + " invite you to participate [" + activityInfo.getActivityTitle() + "] activity");
        }
        return subject;
    }

    private static SendWorkOrderContentVO getInformedNoticeSubject(ActivityInfoDO activityInfo) {
        SendWorkOrderContentVO subject = new SendWorkOrderContentVO();
        subject.setZh(activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + SUBMIT_SAMPLE_POINT_WORK_NOTICE_TITLE_CN);
        subject.setEn(activityInfo.getApplyPeopleName() + activityInfo.getApplyPeopleNo() + SUBMIT_SAMPLE_POINT_WORK_NOTICE_TITLE_EN);
        return subject;
    }

    @Override
    public ConvenientInfoVO getConvenientInfo(ConvenientInfoParam pram) {
        return infoRepository.selectLastCityAndCountry(pram);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncUpScheduleItem(BizRequest<ScheduleItemModel> scheduleRequest) {
        //同步一线活动
        syncUpScheduleItemHelper.syncUpActivity(scheduleRequest);
        //同步一线日程
        syncUpScheduleItemHelper.syncUpActivityScheduleItem(scheduleRequest);
        return true;
    }

    /* Ended by AICoder, pid:ba208k0be53758e14252093530e53241589905c6 */

        /* Started by AICoder, pid:f915d95bb2w40c914f7909b0f0363440c544652b */
    @Override
    public List<ActivityConflictCheckVO> checkExhibitonConflict(BizRequest<ActivityConflictCheckParam> param) {
        ActivityConflictCheckParam activityConflictCheckParam = param.getParam();
        String exhibitionRowId = activityConflictCheckParam.getOriginRowId();
        String activityRowId = activityConflictCheckParam.getActivityRowId();
        List<ActivityScheduleItemVO> listScheduleInfo = activityConflictCheckParam.getListScheduleInfo();
        List<ActivityConflictCheckVO> activityConflictCheckVOList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(listScheduleInfo) || StringUtils.isBlank(exhibitionRowId)){
            return activityConflictCheckVOList;
        }

        // 查询冲突日程
        Set<String> ztePeopleAllSet = new HashSet<>();
        Set<String> clientPeopleAllSet = new HashSet<>();
        listScheduleInfo.forEach(scheduleItemVO -> {
            ztePeopleAllSet.addAll(scheduleItemVO.getListSchedulePeopleInfo().stream().filter(item -> ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.isMe(item.getParticipantType()))
                    .map(ActivityScheduleItemPeopleVO::getPeopleNo).collect(Collectors.toList()));
            clientPeopleAllSet.addAll(scheduleItemVO.getListSchedulePeopleInfo().stream().filter(item -> ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.isMe(item.getParticipantType()))
                    .map(ActivityScheduleItemPeopleVO::getPeopleNo).collect(Collectors.toList()));
        });
        List<String> activityStatusList = com.google.common.collect.Lists.newArrayList(PROGRESS.getCode(), COMPLIANCE_APPROVAL.getCode(),
                BUSINESS_APPROVAL.getCode(), FINISH.getCode(), EVALUATED.getCode());
        List<ActivityScheduleItemAndPeopleVO> zteScheduleItemAndPeopleVOList = scheduleItemRepository.queryScheduleIdByExhibitionIdAndPeopleNoList(new ArrayList<>(ztePeopleAllSet), exhibitionRowId, ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.getCode(),activityStatusList)
                .stream().filter(item -> !StringUtils.equals(activityRowId, item.getActivityRowId())).collect(Collectors.toList());
        List<ActivityScheduleItemAndPeopleVO> clientScheduleItemAndPeopleVOList = scheduleItemRepository.queryScheduleIdByExhibitionIdAndPeopleNoList(new ArrayList<>(clientPeopleAllSet), exhibitionRowId, ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.getCode(),activityStatusList)
                .stream().filter(item -> !StringUtils.equals(activityRowId, item.getActivityRowId())).collect(Collectors.toList());;


        if(BooleanUtils.and(new boolean[]{CollectionUtils.isEmpty(zteScheduleItemAndPeopleVOList), CollectionUtils.isEmpty(clientScheduleItemAndPeopleVOList)})){
            return activityConflictCheckVOList;
        }
        // 客户参与人--所有日程信息
        Map<String, List<ActivityScheduleItemAndPeopleVO>> client2ScheduleItemAndPeopleMap = clientScheduleItemAndPeopleVOList.stream().collect(Collectors.groupingBy(ActivityScheduleItemAndPeopleVO::getPeopleNo));
        // 我司参与人--所有日程信息
        Map<String, List<ActivityScheduleItemAndPeopleVO>> zte2ScheduleItemAndPeopleMap = zteScheduleItemAndPeopleVOList.stream().collect(Collectors.groupingBy(ActivityScheduleItemAndPeopleVO::getPeopleNo));

        // 计算冲突
        Map<String, Set<String>> clientConfilctMap = Maps.newHashMap();
        Map<String, Set<String>> zteConfilctMap = Maps.newHashMap();
        for (ActivityScheduleItemVO activityScheduleItemVO : listScheduleInfo) {
            // 客户参与人冲突
            computeConflict(clientConfilctMap, activityScheduleItemVO, client2ScheduleItemAndPeopleMap,  ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.getCode());
            // ZTE冲突
            computeConflict(zteConfilctMap, activityScheduleItemVO, zte2ScheduleItemAndPeopleMap, ScheduleItemPeopleTypeEnum.ZTE_PEOPLE.getCode());
        }

        if(MapUtils.isEmpty(clientConfilctMap) && MapUtils.isEmpty(zteConfilctMap)){
            return activityConflictCheckVOList;
        }

        // 合并所有日程信息并填充冲突结果
        List<ActivityScheduleItemAndPeopleVO> allPeopleList = Lists.newArrayList();
        allPeopleList.addAll(zteScheduleItemAndPeopleVOList);
        allPeopleList.addAll(clientScheduleItemAndPeopleVOList);

        this.packConflictVO(zteConfilctMap, clientConfilctMap, allPeopleList, activityConflictCheckVOList);
        this.perfectEmpName(activityConflictCheckVOList);

        return activityConflictCheckVOList;
    }

    @Override
    public RelationPeopleInfoVO getActivityDetailRelationPeopleInfoToApp(String activityRowId) {
        ActivityInfoDO activityInfoDO = infoRepository.selectByPrimaryKey(activityRowId);
        if (Objects.isNull(activityInfoDO)) {
            return null;
        }
        ActivityDetailInfoVO detailInfoVO = new ActivityDetailInfoVO();

        List<ActivityRelationZtePeopleDO> listZtePeople = ztePeopleRepository.queryAllZtePeopleForActivity(activityRowId);
        this.setPostNameByEmployeeInfo(listZtePeople);
        queryParticipants(activityRowId, detailInfoVO, listZtePeople, activityInfoDO.getActivityType());

        return detailInfoVO.getRelationPeopleInfo();
    }

    void perfectEmpName(List<ActivityConflictCheckVO> activityConflictCheckVOList) {
        List<String> applyPeopleNoList = activityConflictCheckVOList.stream().map(ActivityConflictCheckVO::getApplyPeopleNo)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, EmployeeInfoDTO> employeeInfoMap = userCenterService.getUserInfoMapByShortNo(applyPeopleNoList);
        activityConflictCheckVOList.forEach(item ->{
            EmployeeInfoDTO employeeInfoDTO = employeeInfoMap.get(item.getApplyPeopleNo());
            if(null != employeeInfoDTO){
                item.setApplyPeopleNoName(employeeInfoDTO.getEmpName() + item.getApplyPeopleNo());
            }
        });
    }

    void packConflictVO(Map<String, Set<String>> zteConfilctMap, Map<String, Set<String>> clientConfilctMap
            , List<ActivityScheduleItemAndPeopleVO> allPeopleList, List<ActivityConflictCheckVO> activityConflictCheckVOList) {
        Set<String> idSet = new HashSet<>(zteConfilctMap.keySet());
        idSet.addAll(clientConfilctMap.keySet());

        idSet.forEach(key -> {
            ActivityConflictCheckVO activityConflictCheckVO = new ActivityConflictCheckVO();
            ActivityScheduleItemAndPeopleVO activityScheduleItemAndPeopleVO = allPeopleList.stream().filter(item -> StringUtils.equals(item.getActivityRowId(), key))
                    .findFirst().orElse(new ActivityScheduleItemAndPeopleVO());
            activityConflictCheckVO.setActivityTitle(activityScheduleItemAndPeopleVO.getActivityTitle());
            activityConflictCheckVO.setApplyPeopleNo(activityScheduleItemAndPeopleVO.getApplyPeopleNo());

            Set<String> clientSet = clientConfilctMap.get(key);
            Set<String> zteSet = zteConfilctMap.get(key);
            if (CollectionUtils.isNotEmpty(zteSet)) {
                activityConflictCheckVO.setZteConflictPeopleName(String.join(PAUSE_MARK, zteSet));
            }
            if (CollectionUtils.isNotEmpty(clientSet)) {
                activityConflictCheckVO.setCustConflictPeopleName(String.join(PAUSE_MARK, clientSet));
            }
            activityConflictCheckVOList.add(activityConflictCheckVO);
        });
    }
    /* Ended by AICoder, pid:f915d95bb2w40c914f7909b0f0363440c544652b */

    /* Started by AICoder, pid:reb62e8e3bq19b6143c5090e503dce40c3e0af8c */
    void computeConflict(Map<String, Set<String>> confilctMap, ActivityScheduleItemVO activityScheduleItemVO
            , Map<String, List<ActivityScheduleItemAndPeopleVO>> scheduleItemAndPeopleMap, String peopleType){
        // 时间和日期
        Date scheduleDate = activityScheduleItemVO.getScheduleDate();
        String scheduleTime = activityScheduleItemVO.getScheduleTime();
        List<ActivityScheduleItemPeopleVO> listSchedulePeopleInfo = activityScheduleItemVO.getListSchedulePeopleInfo();
        List<ActivityScheduleItemPeopleVO> activityScheduleItemPeopleVOList = listSchedulePeopleInfo.stream().filter(item ->
                StringUtils.equals(peopleType, item.getParticipantType())).collect(Collectors.toList());

        for (ActivityScheduleItemPeopleVO activityScheduleItemPeopleVO : activityScheduleItemPeopleVOList) {
            String peopleNo = activityScheduleItemPeopleVO.getPeopleNo();
            List<ActivityScheduleItemAndPeopleVO> scheduleItemAndPeopleVOList = scheduleItemAndPeopleMap.get(peopleNo);
            if(CollectionUtils.isEmpty(scheduleItemAndPeopleVOList)){
                continue;
            }

            List<ActivityScheduleItemAndPeopleVO> conflictPeopleList = scheduleItemAndPeopleVOList.stream().filter(item ->
                    ActivityScheduleConflictModel.computeConflict(packDateTimePeriod(scheduleTime, scheduleDate),
                            packDateTimePeriod(item.getScheduleTime(), item.getScheduleDate()))).collect(Collectors.toList());

            conflictPeopleList.forEach(conflictItem -> {
                String activityRowId = conflictItem.getActivityRowId();
                Set<String> conflictSet = confilctMap.computeIfAbsent(activityRowId, k -> new HashSet<>());
                // 根据参与者类型添加名字或名字+编号
                String nameToAdd = ScheduleItemPeopleTypeEnum.CLIENT_PARTICIPANT.isMe(peopleType) ? activityScheduleItemPeopleVO.getPeopleName()
                        : activityScheduleItemPeopleVO.getPeopleName() + peopleNo;
                conflictSet.add(nameToAdd);
            });
        }
    }
    /* Ended by AICoder, pid:reb62e8e3bq19b6143c5090e503dce40c3e0af8c */

    /* Started by AICoder, pid:vccfb81525c4cd7140a50a309037a033f5c13d0d */
    DateTimePeriod packDateTimePeriod(String scheduleTime, Date scheduleDate) {
        String[] split = scheduleTime.split(WAVE);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DateConstants.YYYY_MM_DD);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateConstants.HH_MM);
        LocalTime parseStart = LocalTime.parse(split[0], timeFormatter);
        LocalTime parseEnd = LocalTime.parse(split[1], timeFormatter);

        SimpleDateFormat dateFormat = new SimpleDateFormat(DateConstants.YYYY_MM_DD);
        LocalDate dateTime = LocalDate.parse(dateFormat.format(scheduleDate), dateFormatter);
        LocalDateTime dateTimesStart = LocalDateTime.of(dateTime, parseStart);
        LocalDateTime dateTimesEnd = LocalDateTime.of(dateTime, parseEnd);
        Date timeStart = Date.from(dateTimesStart.atZone(ZoneId.systemDefault()).toInstant());
        Date timeEnd = Date.from(dateTimesEnd.atZone(ZoneId.systemDefault()).toInstant());

        return new DateTimePeriod(timeStart, timeEnd);
    }
    /* Ended by AICoder, pid:vccfb81525c4cd7140a50a309037a033f5c13d0d */
}

