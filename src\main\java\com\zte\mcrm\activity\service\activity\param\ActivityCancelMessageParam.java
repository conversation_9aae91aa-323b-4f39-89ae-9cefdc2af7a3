package com.zte.mcrm.activity.service.activity.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 活动取消 icenter 消息参数
 *
 * <AUTHOR>
 * @date 2023/9/1 下午4:35
 */
@Getter
@Setter
@ToString
public class ActivityCancelMessageParam {

    /**
     * 操作人工号
     */
    private String operatorEmpNo;

    /**
     * 接收人工号
     */
    private List<String> sendToEmpNos;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 活动议题
     */
    private String activityTitle;

    /**
     *  拓展活动类型。枚举：ActivityTypeEnum，
     *  快码：Activity_Type_Enum
     *  */
    private String activityType;

    /** 活动交流城市名称 */
    private String cityCodeName;

    /** 活动交流城市所在国家 */
    private String countryCodeName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动地点
     */
    private String activityPlace;

    /**
     * 详情链接
     */
    private String detailUrl;


}
