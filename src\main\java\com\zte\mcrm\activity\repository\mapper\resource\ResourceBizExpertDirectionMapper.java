package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDirectionDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ResourceBizExpertDirectionMapper {
    /**
     * all field insert
     */
    int insert(ResourceBizExpertDirectionDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ResourceBizExpertDirectionDO record);

    /**
     * query by primary key
     */
    ResourceBizExpertDirectionDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ResourceBizExpertDirectionDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ResourceBizExpertDirectionDO record);

    /**
     * 新工号切换
     */
    List<ResourceBizExpertDirectionDO> queryEmpNoTransList(DataTransParam searchParam);
}