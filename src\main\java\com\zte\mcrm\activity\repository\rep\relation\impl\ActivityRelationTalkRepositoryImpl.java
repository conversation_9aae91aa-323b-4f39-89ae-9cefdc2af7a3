package com.zte.mcrm.activity.repository.rep.relation.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.relation.ActivityRelationTalkExtMapper;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationTalkDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationTalkRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 活动相关的方案信息
 *
 * <AUTHOR>
 */
@Component
public class ActivityRelationTalkRepositoryImpl implements ActivityRelationTalkRepository {

    @Resource
    private ActivityRelationTalkExtMapper activityRelationTalkExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return activityRelationTalkExtMapper.softDeleteByActivityIds(operator, activityIds);
    }

    /**
     * 新增多条数据
     *
     * @param record 谈参数据
     * @return 返回结果
     */
    @Override
    public int insertSelective(List<ActivityRelationTalkDO> record) {
        if(CollectionUtils.isEmpty(record)){
            return NumberConstant.ZERO;
        }
        List<ActivityRelationTalkDO> updatedTalkList = new ArrayList<>();
        for (ActivityRelationTalkDO activityRelationTalkDO : record) {
            if(StringUtils.isBlank(activityRelationTalkDO.getRowId())){
                activityRelationTalkDO.setRowId(keyIdService.getKeyId());
            }
            activityRelationTalkDO.setCreatedBy(HeadersProperties.getXEmpNo());
            activityRelationTalkDO.setLastUpdatedBy(HeadersProperties.getXEmpNo());
            activityRelationTalkDO.setCreationDate(new Date());
            activityRelationTalkDO.setLastUpdateDate(new Date());
            activityRelationTalkDO.setEnabledFlag(BooleanEnum.Y.getCode());
            updatedTalkList.add(activityRelationTalkDO);
        }
        return activityRelationTalkExtMapper.batchInsert(updatedTalkList);
    }

    /**
     * 查询数据
     *
     * @param activityRowId 谈参数据
     * @return 返回结果
     */
    @Override
    public ActivityRelationTalkDO queryByActivityId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? null : activityRelationTalkExtMapper.selectByActivityId(activityRowId);
    }

    /**
     * 查询活动下所有的谈参
     *
     * @param activityRowId 活动Id
     * @return List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/6/8 11:29
     */
    @Override
    public List<ActivityRelationTalkDO> queryAllByActivityRowId(String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return Lists.newArrayList();
        }
        return activityRelationTalkExtMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return activityRelationTalkExtMapper.deleteByRowIds(operator, rowIds);
    }

    /**
     * 查询活动下所有的谈参
     * 增加enabled_flag = 'Y'，推送ES不需要无效谈参
     *
     * @param activityRowId 活动Id
     * @return List<ActivityRelationTalkDO>
     * <AUTHOR>
     * date: 2023/6/8 11:29
     */
    @Override
    public List<ActivityRelationTalkDO> queryAllActivityWithNotEnable(String activityRowId) {
        if (StringUtils.isBlank(activityRowId)) {
            return Lists.newArrayList();
        }
        return activityRelationTalkExtMapper.queryAllActivityWithNotEnable(activityRowId);
    }
}
