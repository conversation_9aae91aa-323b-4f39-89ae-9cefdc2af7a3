package com.zte.mcrm.activity.repository.rep.authority;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO;
import com.zte.mcrm.activity.repository.rep.authority.param.TalkAuthorityQuery;
import com.zte.mcrm.activity.web.controller.authority.params.TalkAuthorityChangeParam;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/12/27 16:18
 */
public interface TalkAuthorityInfoRepository {

    /***
     * <p>
     * 批量插入谈参权限记录
     *
     * </p>
     * <AUTHOR>
     * @date 2023/12/28 下午5:07
     * @param recordList 谈参权限记录
     * @return int
     */
    int batchInsertSelective(List<ActivityTalkAuthorityDO> recordList);

    /***
     * <p>
     * 根据人员编码获取对应的谈参权限记录
     *
     * </p>
     * <AUTHOR>
     * @date 2023/12/28 下午5:07
     * @param peopleCode 人员编码
     * @return java.util.List<com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO>
     */
    List<ActivityTalkAuthorityDO> getListByPeopleCode(String peopleCode);

    List<ActivityTalkAuthorityDO> listAllTalkAuthorityInfo();

    /**
     * 查询谈参权限列表
     * @param talkAuthorityQuery
     * @return
     */
    List<ActivityTalkAuthorityDO> queryTalkAuthorityInfoList(TalkAuthorityQuery talkAuthorityQuery);

    /**
     * 批量更新权限列表
     * @param recordList
     * @return
     */
    int batchUpdateSelective(List<ActivityTalkAuthorityDO> recordList);

    /**
     * 根据条件查询对应的谈参权限记录
     *
     * @param queryParam 查询条件
     * @return java.util.List<com.zte.mcrm.activity.repository.model.authority.ActivityTalkAuthorityDO>
     */
    List<ActivityTalkAuthorityDO> getListCondition(TalkAuthorityChangeParam queryParam);

}
