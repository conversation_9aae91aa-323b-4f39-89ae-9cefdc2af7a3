package com.zte.mcrm.activity.repository.rep.event;

import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.service.event.param.CommonTaskEventQueryParam;

import java.util.List;

/**
 * <AUTHOR> 10307200
 * @since 2024-01-16 下午1:46
 **/
public interface CommonTaskEventRepository {

    /***
     * <p>
     * 新增一条定时任务记录
     *
     * </p>
     * <AUTHOR>
     * @since  2024/1/22 上午9:28
     * @param commonTaskEventDO
     * @return int
     */
    int insert(CommonTaskEventDO commonTaskEventDO);

    /***
     * <p>
     * 批量新增记录
     *
     * </p>
     * <AUTHOR>
     * @since 2024/1/22 上午9:29
     * @param recordList
     * @return int
     */
    int insertSelective(List<CommonTaskEventDO> recordList);

    /***
     * <p>
     * 查询符合条件的定时任务
     *
     * </p>
     * <AUTHOR>
     * @since 2024/1/22 上午9:29
     * @param queryParam
     * @return java.util.List<com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO>
     */
    List<CommonTaskEventDO> listCommonTaskEventByCondition(CommonTaskEventQueryParam queryParam);

    /***
     * <p>
     * 批量修改定时任务
     *
     * </p>
     * <AUTHOR>
     * @since 2024/1/22 上午9:30
     * @param commonTaskEventList
     * @return int
     */
    int batchUpdateByPrimaryKey(List<CommonTaskEventDO> commonTaskEventList);

    /***
     * <p>
     * 批量逻辑删除记录列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/1/22 上午9:30
     * @param operator
     * @param rowIds
     * @return int
     */
    int softDeleteByRowIds(String operator, List<String> rowIds);

}
