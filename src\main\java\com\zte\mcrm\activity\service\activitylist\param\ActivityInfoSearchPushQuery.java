package com.zte.mcrm.activity.service.activitylist.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 推送ISearch查询参数
 * <AUTHOR>
 * date: 2023/12/6 10:52
 */
@Setter
@Getter
@ToString
public class ActivityInfoSearchPushQuery extends ActivityInfoQuery {

    /***
     * 创建活动起止时间
     */
    private Date createdStartTime;

    /***
     * 创建活动结束
     */
    private Date createdEndTime;

}
