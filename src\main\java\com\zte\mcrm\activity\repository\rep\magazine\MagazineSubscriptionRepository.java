package com.zte.mcrm.activity.repository.rep.magazine;

import com.zte.mcrm.activity.repository.model.magazine.MagazineSubscriptionDO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 杂志订阅Repository接口
 * 负责杂志订阅数据的访问和持久化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MagazineSubscriptionRepository {
    
    /**
     * 根据主键查询杂志订阅信息
     * 
     * @param rowId 主键ID
     * @return 杂志订阅对象
     */
    MagazineSubscriptionDO selectByPrimaryKey(String rowId);
    
    /**
     * 按联系人编号分页查询（去重）
     * 用于SUBSCRIBED维度查询
     * 
     * @param contactPersonNo 联系人编号（模糊查询）
     * @param magazineName 杂志名称（模糊查询）
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageInfo<String> selectDistinctContactPersonNoWithPage(String contactPersonNo, String magazineName, 
                                                          int pageNo, int pageSize);
    
    /**
     * 根据联系人编号列表查询订阅记录
     * 用于根据联系人编号查询对应的所有订阅记录
     * 
     * @param contactPersonNos 联系人编号列表
     * @return 订阅记录列表
     */
    List<MagazineSubscriptionDO> selectByContactPersonNos(List<String> contactPersonNos);
    
    /**
     * 动态插入杂志订阅记录
     * 
     * @param record 杂志订阅对象
     * @return 影响行数
     */
    int insertSelective(MagazineSubscriptionDO record);
    
    /**
     * 根据主键动态更新杂志订阅记录
     * 
     * @param record 杂志订阅对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(MagazineSubscriptionDO record);
} 