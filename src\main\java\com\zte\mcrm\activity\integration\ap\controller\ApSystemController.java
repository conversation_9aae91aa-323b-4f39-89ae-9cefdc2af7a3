package com.zte.mcrm.activity.integration.ap.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.integration.ap.IApSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：2024/6/26 14:54
 * @description ：老活动AP信息同步
 */

@Api(tags = "老活动AP信息同步-API")
@RestController
@RequestMapping("/apSystem")
public class ApSystemController {

    @Autowired
    private IApSystemService apSystemService;

    /**
     * 客户扩展活动列表
     *
     * @return
     */
    @ApiOperation("初始化老的Ap信息-post")
    @PostMapping("/queryAp")
    public ServiceData initOldAp() {
        return ServiceDataUtils.success(apSystemService.initOldAp());
    }


}
