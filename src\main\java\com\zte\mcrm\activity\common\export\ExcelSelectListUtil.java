package com.zte.mcrm.activity.common.export;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.export.model.ExcelExportParamSetModel;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/1/15 10:37
 */
public class ExcelSelectListUtil {

    /**
     * firstRow 开始行号根据此项目，默认为1(下标0开始)
     * lastRow  根据此项目，默认为最大65535
     * firstCol 区域中第一个单元格的列号 (下标0开始)
     * lastCol 区域中最后一个单元格的列号
     * strings 下拉内容
     * */
    public static void setSelectList(Workbook workbook, ExcelExportParamSetModel excelExportParamSetModel){
        int firstCol = excelExportParamSetModel.getFirstCol();
        int lastCol = excelExportParamSetModel.getLastCol();
        String[] selectList = excelExportParamSetModel.getSelectList();
        Sheet sheet = workbook.getSheetAt(0);
        //  生成下拉列表
        //  只对(x，x)单元格有效
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(NumberConstant.ONE, NumberConstant.MAX_VALUE, firstCol, lastCol);
        //  生成下拉框内容
        DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(selectList);
        HSSFDataValidation dataValidation = new HSSFDataValidation(cellRangeAddressList, dvConstraint);
        //  对sheet页生效
        sheet.addValidationData(dataValidation);
    }

    public static void setExcelHeaderColor(Workbook workbook, List<String> modifiyLineList){
        // 获取工作簿中的第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Row titleRow = sheet.getRow(0);
        for (int i = 0; i < modifiyLineList.size(); i++) {
            Cell cell = titleRow.getCell(Integer.parseInt(modifiyLineList.get(i)));
            cell.setCellStyle(cellStyle);
        }
    }

    /**
     * 将byte[]转换为Excel对象的方法
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/1/19
     */
    public static Workbook convertByteArrayToWorkbook(byte[] excelBytes) {
        try (InputStream inputStream = new ByteArrayInputStream(excelBytes)) {
            return WorkbookFactory.create(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert byte array to workbook", e);
        }
    }

    /**
     * 将Excel对象转换回byte[]的方法
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/1/19
     */
    public static byte[] convertWorkbookToByteArray(Workbook workbook) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert workbook to byte array", e);
        }
    }


    /**
     * 设置具体列的格式
     * @param workbook
     * @param columnIndexList 表示第几列
     */
    public static void setExcelColumnStyle(Workbook workbook, List<Integer> columnIndexList){
        // 获取工作簿中的第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 设置单元格格式为文本格式
        CellStyle style = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        //设置单元格格式为"文本"
        style .setDataFormat(format.getFormat(CharacterConstant.AT));
        for (Integer columnIndex: columnIndexList) {
            sheet.setDefaultColumnStyle(columnIndex, style);
        }
    }
}
