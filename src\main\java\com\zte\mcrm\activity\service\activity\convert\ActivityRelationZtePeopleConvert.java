package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityZtePeopleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户活动中兴参与人转换
 *
 * <AUTHOR>
 * @date 2023/5/17 下午4:57
 */
public class ActivityRelationZtePeopleConvert {

    private ActivityRelationZtePeopleConvert() {
    }

    /**
     * 转换成VO
     *
     * @param ztePeopleDOList
     * @return {@link List< ActivityZtePeopleVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午4:59
     */
    public static List<ActivityZtePeopleVO> convert2VO(List<ActivityRelationZtePeopleDO> ztePeopleDOList) {
        if (CollectionUtils.isEmpty(ztePeopleDOList)) {
            return Collections.emptyList();
        }
        return ztePeopleDOList.stream().map(item -> {
            ActivityZtePeopleVO vo = new ActivityZtePeopleVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(ActivityZtePeopleVO::getPeopleCode))), ArrayList::new));
    }
}
