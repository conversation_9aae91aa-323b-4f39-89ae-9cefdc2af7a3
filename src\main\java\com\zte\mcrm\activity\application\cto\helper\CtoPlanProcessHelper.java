package com.zte.mcrm.activity.application.cto.helper;

import com.google.common.collect.Maps;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * CTO握手计划处理辅助类
 */
@Getter
@Setter
public class CtoPlanProcessHelper {




    /**
     * 营销部门集合
     */
    Map<String, List<CtoPlanSaleDivisionMappingDO>> saleDivisionMap = Maps.newHashMap();


}
