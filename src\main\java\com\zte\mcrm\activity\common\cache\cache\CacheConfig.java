package com.zte.mcrm.activity.common.cache.cache;

import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.TimeUnit;

/***
 * 缓存配置
 *
 * <AUTHOR>
 * @date 2022-09-20
 */
@Getter
@Setter
public class CacheConfig {
    /**
     * 最大缓存容量
     */
    private int maxSize;
    /**
     * 过期时间
     */
    private int expireTime;
    /**
     * 过期时间单位
     */
    private TimeUnit expireTimeUnit;

    /**
     * 缓存策略。默认为：本地缓存。0-本地缓存，1-分布式redis缓存（暂未实现）
     */
    private String cacheStrategy = "0";
}
