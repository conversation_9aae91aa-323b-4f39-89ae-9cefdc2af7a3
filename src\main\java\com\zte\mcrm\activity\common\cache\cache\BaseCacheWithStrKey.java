package com.zte.mcrm.activity.common.cache.cache;

import com.zte.mcrm.activity.common.cache.loader.CacheDataLoader;

/**
 * 使用String作为key的缓存基类
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
public abstract class BaseCacheWithStrKey<V> implements Cache<String, V> {
    /**
     * 缓存数据加载器
     */
    private CacheDataLoader<String,V> loader;

    public BaseCacheWithStrKey(CacheDataLoader<String,V> loader) {
        this.loader = loader;
    }

    protected V reloadCache(String key) {
        return loader.load(key);
    }
}
