package com.zte.mcrm.activity.repository.model.resource;


import java.util.Date;

/**
 * table:activity_resource_reservation -- 
 */
public class ActivityResourceReservationDO {
    /** 主键 */
    private String rowId;

    /** 活动rowid */
    private String activityRowId;

    /** 资源类型。如：专家、领导、其他资源……。枚举：ResourceTypeEnum */
    private String resourceType;

    /** 资源编号。如：专家资源表的row_id，领导的员工编码 */
    private String resourceNo;

    /** 预约处理人员工编号。比如：预约的专家日程，这里就是对应的专家员工编号 */
    private String reserveDealer;

    /** 预约状态。待处理，已接受，已拒绝，转交。枚举：ReserveStatusEnum */
    private String reserveStatus;

    /** 如果是转交过来的，则该有值，转交来源id（本表的row_id） */
    private String transFrom;

    /** 预约占用时间开始 */
    private Date reserveFrom;

    /** 预约占用结束时间 */
    private Date reserveTo;

    /** 预约处理时间 */
    private Date replyTime;

    /** 备注 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    public String getResourceNo() {
        return resourceNo;
    }

    public void setResourceNo(String resourceNo) {
        this.resourceNo = resourceNo == null ? null : resourceNo.trim();
    }

    public String getReserveDealer() {
        return reserveDealer;
    }

    public void setReserveDealer(String reserveDealer) {
        this.reserveDealer = reserveDealer == null ? null : reserveDealer.trim();
    }

    public String getReserveStatus() {
        return reserveStatus;
    }

    public void setReserveStatus(String reserveStatus) {
        this.reserveStatus = reserveStatus == null ? null : reserveStatus.trim();
    }

    public String getTransFrom() {
        return transFrom;
    }

    public void setTransFrom(String transFrom) {
        this.transFrom = transFrom == null ? null : transFrom.trim();
    }

    public Date getReserveFrom() {
        return reserveFrom;
    }

    public void setReserveFrom(Date reserveFrom) {
        this.reserveFrom = reserveFrom;
    }

    public Date getReserveTo() {
        return reserveTo;
    }

    public void setReserveTo(Date reserveTo) {
        this.reserveTo = reserveTo;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}