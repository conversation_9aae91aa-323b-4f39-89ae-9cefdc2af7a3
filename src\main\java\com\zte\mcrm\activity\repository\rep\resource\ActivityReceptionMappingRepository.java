package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityReceptionMappingDO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityReceiveInfo;

import java.util.List;
import java.util.Set;

public interface ActivityReceptionMappingRepository {

    /**
     * 根据活动id查询关联的接待信息映射
     * @param activityRowId 活动id
     * @return 接待信息列表
     */
    List<ActivityReceptionMappingDO> listReceptionByActivityRowId(String activityRowId);

    /**
     * 根据接待id从t_cust_receive表获取接待相关信息列表
     * @param receiveIds
     * @return
     */
    List<ActivityReceiveInfo> getDetailByReceiveIdList(List<String> receiveIds);

    /***
     * <p>
     * 批量逻辑删除
     *
     * </p>
     * <AUTHOR>
     * @since 2024/5/30 下午8:10
     * @param empNo 操作人
     * @param rowIds 单据id列表
     * @return int
     */
    int softDeletedByBatch(String empNo, Set<String> rowIds);

}
