package com.zte.mcrm.activity.repository.rep.event.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.event.CommonTaskEventExtMapper;
import com.zte.mcrm.activity.repository.mapper.event.CommonTaskEventMapper;
import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;
import com.zte.mcrm.activity.repository.rep.event.CommonTaskEventRepository;
import com.zte.mcrm.activity.service.event.param.CommonTaskEventQueryParam;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR> 10307200
 * @since 2024-01-16 下午1:46
 **/
@Repository
public class CommonTaskEventRepositoryImpl implements CommonTaskEventRepository {

    @Autowired
    private IKeyIdService iKeyIdService;

    @Autowired
    private CommonTaskEventMapper commonTaskEventMapper;

    @Autowired
    private CommonTaskEventExtMapper commonTaskEventExtMapper;

    @Override
    public int insert(CommonTaskEventDO commonTaskEventDO) {
        String operator = HeadersProperties.getXEmpNo();
        if (StringUtils.isBlank(commonTaskEventDO.getRowId())) {
            commonTaskEventDO.setRowId(iKeyIdService.getKeyId());
        }
        if (StringUtils.isBlank(commonTaskEventDO.getCreatedBy())) {
            commonTaskEventDO.setCreatedBy(operator);
        }
        if (StringUtils.isBlank(commonTaskEventDO.getLastUpdatedBy())) {
            commonTaskEventDO.setLastUpdatedBy(operator);
        }
        Date currentTime = new Date();
        commonTaskEventDO.setCreationDate(currentTime);
        commonTaskEventDO.setLastUpdateDate(currentTime);
        commonTaskEventDO.setEnabledFlag(BooleanEnum.Y.getCode());
        return commonTaskEventMapper.insert(commonTaskEventDO);
    }

    @Override
    public int insertSelective(List<CommonTaskEventDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (CommonTaskEventDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(iKeyIdService.getKeyId());
            }
            record.setCreatedBy(Optional.ofNullable(record.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setCreationDate(new Date());
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.Y.getCode());
            commonTaskEventMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public List<CommonTaskEventDO> listCommonTaskEventByCondition(CommonTaskEventQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return new ArrayList<>();
        }

        return commonTaskEventExtMapper.getListByQuery(queryParam);
    }

    @Override
    public int batchUpdateByPrimaryKey(List<CommonTaskEventDO> commonTaskEventList) {
        if (CollectionUtils.isEmpty(commonTaskEventList)) {
            return NumberConstant.ZERO;
        }

        String operator = HeadersProperties.getXEmpNo();
        Date currentTime = new Date();
        for(CommonTaskEventDO commonTaskEvent : commonTaskEventList) {
            if (StringUtils.isBlank(commonTaskEvent.getLastUpdatedBy())) {
                commonTaskEvent.setLastUpdatedBy(operator);
            }
            commonTaskEvent.setLastUpdateDate(currentTime);
        }

        return commonTaskEventExtMapper.batchUpdateByPrimaryKey(commonTaskEventList);
    }

    @Override
    public int softDeleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        if (StringUtils.isBlank(operator)) {
            operator = HeadersProperties.getXEmpNo();
        }

        return commonTaskEventExtMapper.softDeleteByRowIds(operator, rowIds);
    }
}
