package com.zte.mcrm.activity.service.activitylist.convert;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityFlowNodeEnum;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;

import java.util.Date;

/**
 * <AUTHOR> 10333830
 * @date 2023-09-01 9:44
 **/
public class ActivityFlowConvert {

    /**
     * 枚举转VO
     * @param nodeEnum  节点枚举
     * @param highLightEnum 点亮枚举
     * @param finishStatus  阶段状态
     * @return com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO
     * <AUTHOR>
     * date: 2023/9/4 9:20
     */
    public static ActivityFlowInfoVO enumToVo(ActivityFlowNodeEnum nodeEnum, BooleanEnum highLightEnum, String finishStatus) {
        ActivityFlowInfoVO vo = new ActivityFlowInfoVO();
        vo.setNodeCode(nodeEnum.getCode());
        vo.setNodeName(nodeEnum.getDesc());
        vo.setNodeNameEn(nodeEnum.getEnDesc());
        vo.setHighlight(highLightEnum.getCode());
        vo.setStageStatus(finishStatus);
        return vo;
    }

    /**
     * 填充点亮节点触发信息
     * @param vo    节点
     * @param trigger   触发人
     * @param triggerTime   触发时间
     * @param finishStatus  结束状态
     * @return void
     * <AUTHOR>
     * date: 2023/9/1 9:52
     */
    public static void fillLightTriggerInfo(ActivityFlowInfoVO vo, String trigger, Date triggerTime, String finishStatus, String log) {
        vo.setTrigger(trigger);
        vo.setTriggerTime(triggerTime);
        vo.setStageStatus(finishStatus);
        vo.setHighlight(BooleanEnum.Y.getCode());
        vo.setLog(log);
        vo.setChild(Lists.newArrayList());
    }
}
