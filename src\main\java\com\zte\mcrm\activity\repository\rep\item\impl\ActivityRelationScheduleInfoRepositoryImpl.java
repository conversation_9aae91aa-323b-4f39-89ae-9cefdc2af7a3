package com.zte.mcrm.activity.repository.rep.item.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleItemExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityScheduleItemAndPeopleVO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/17 10:36
 */
@Component
public class ActivityRelationScheduleInfoRepositoryImpl implements ActivityScheduleItemRepository {
    @Autowired
    private ActivityScheduleItemExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    public int insertSelective(ActivityScheduleItemDO record) {
        setDefaultValue(record);
        return extMapper.insertSelective(record);
    }

    @Override
    public int batchInsert(List<ActivityScheduleItemDO> scheduleItemList) {
        if (CollectionUtils.isEmpty(scheduleItemList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityScheduleItemDO record : scheduleItemList) {
            insertSelective(record);
        }
        return scheduleItemList.size();
    }

    @Override
    public Map<String, List<ActivityScheduleItemDO>> getRelationScheduleInfoIds(List<String> activityRowIds) {
        if (CollectionUtils.isEmpty(activityRowIds)) {
            return Collections.emptyMap();
        }
        return extMapper.queryAllScheduleInfoIds(activityRowIds)
                .stream().collect(Collectors.groupingBy(ActivityScheduleItemDO::getActivityRowId));
    }

    @Override
    public List<ActivityScheduleItemDO> getRelationScheduleInfoList(List<String> activityRowIds) {
        if (CollectionUtils.isEmpty(activityRowIds)) {
            return Lists.newArrayList();
        }
        return extMapper.queryAllScheduleInfoIds(activityRowIds);
    }

    @Override
    public List<ActivityScheduleItemDO> getScheduleItemByRowIds(List<String> scheduleItemRowIds) {
        if (CollectionUtils.isEmpty(scheduleItemRowIds)) {
            return new ArrayList<>();
        }

        return extMapper.getScheduleItemByIds(scheduleItemRowIds);
    }

    @Override
    public int batchUpdate(List<ActivityScheduleItemDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return NumberConstant.ZERO;
        }
        String empNo = HeadersProperties.getXEmpNo();
        Date updateDate = new Date();
        List<ActivityScheduleItemDO> updateRecords = records.stream().
                filter(record -> StringUtils.isNotBlank(record.getActivityRowId())).
                map(record -> {
                    record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy())
                            .orElse(empNo));
                    record.setLastUpdateDate(updateDate);
                    return record;
                }).collect(Collectors.toList());
        return CollectionUtils.isEmpty(updateRecords) ? NumberConstant.ZERO : extMapper.batchUpdate(updateRecords);
    }

    @Override
    public List<ActivityScheduleItemDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    /**
     * 设置默认值
     *
     * @param scheduleItemDO 实体类
     * <AUTHOR>
     */
    private void setDefaultValue(ActivityScheduleItemDO scheduleItemDO) {
        scheduleItemDO.setRowId(Optional.ofNullable(scheduleItemDO.getRowId())
                .orElse(keyIdService.getKeyId()));
        scheduleItemDO.setCreatedBy(Optional.ofNullable(scheduleItemDO.getCreatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        scheduleItemDO.setLastUpdatedBy(Optional.ofNullable(scheduleItemDO.getLastUpdatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        scheduleItemDO.setCreationDate(new Date());
        scheduleItemDO.setLastUpdateDate(new Date());
        scheduleItemDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    /**
     * 根据rowId，批量软删除日程安排数据
     * @param operator
     * @param rowIds
     * @return
     */
    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if ((CollectionUtils.isEmpty(rowIds))) {
            return NumberConstant.ZERO;
        }
        return extMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public ActivityScheduleItemDO selectByPrimaryKey(String rowId) {
        if(StringUtils.isBlank(rowId)){
            return null;
        }
        return extMapper.selectByPrimaryKey(rowId);
    }

    /**
     * 通过版本ID批量查询数据
     * @param orchestrationVersionIdList
     * @return
     */
    @Override
    public Map<String, List<ActivityScheduleItemDO>> queryAllScheduleInfoByVersionIdList(List<String> orchestrationVersionIdList) {
        return CollectionUtils.isEmpty(orchestrationVersionIdList) ? Collections.emptyMap() :
                extMapper.queryAllScheduleInfoByVersionIdList(orchestrationVersionIdList)
                        .stream().collect(Collectors.groupingBy(ActivityScheduleItemDO::getOrchestrationVersionRowId));
    }

    /**
     * 通过展会ID和人员查询所有日程
     * @param peopleNoList
     * @param exhibitionId
     * @return
     */
    @Override
    public List<ActivityScheduleItemAndPeopleVO> queryScheduleIdByExhibitionIdAndPeopleNoList(List<String> peopleNoList, String exhibitionId, String peopleType, List<String> activityStatusList) {
        return CollectionUtils.isEmpty(peopleNoList) ? Lists.newArrayList() :
                extMapper.queryScheduleIdByExhibitionIdAndPeopleNoList(peopleNoList, exhibitionId, peopleType, activityStatusList);

    }

    @Override
    public List<ActivityScheduleItemDO> getScheduleListByActivityIdListAndStatusListAndScheduleDate(List<String> activityIdList, List<String> scheduleStatusList, Date scheduleDate) {
        return CollectionUtils.isEmpty(activityIdList) ? Collections.emptyList() : extMapper.getScheduleListByActivityIdListAndStatusListAndScheduleDate(activityIdList, scheduleStatusList, scheduleDate);
    }
}
