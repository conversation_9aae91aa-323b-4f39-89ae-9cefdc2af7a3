package com.zte.mcrm.activity.service.ai;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptReqVO;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2024/10/31 13:52
 */
public interface MarketAgentService {

    /**
     * 获取展示数据
     * @param request
     * @return
     */
    List<IgptRespVO> getAnswerList(BizRequest<IgptReqVO> request);
}
