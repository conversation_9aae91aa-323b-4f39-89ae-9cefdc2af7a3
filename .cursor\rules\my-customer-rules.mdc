---
description: 
globs: 
alwaysApply: true
---
# ZTE-CRM客户拜访系统开发规范

# 重点要求
1、throw异常，提示语必须英文; log.xx()使用中文输出
2、禁止修改配置文件，如需要修改，需要争取我的意见
3、禁止硬编码
4、添加必要注释，类，函数必须添加注释。类注释按正常格式生成，作者使用：李龙0668001470
5、代码是给人看的，尽量简洁易懂，格式稍微紧凑点，减少空行
6、VO/BO/DO/DTO禁止使用get/set函数，必须使用 lombok注解:@Getter,@Setter，@ToString
7、VO/BO/DO/DTO应当跟随子模块包路径
8、ServiceData需要指定具体类
9、BizRequestUtil.createWithCurrentUserSecurity()来获取当前用户
10、controller只调用service函数，不写逻辑代码，参照CustomerRelationBizController写法
11、UT要求分支覆盖率100%，controller不需要生成UT,UT需要使用PowerMockRunner类
12、合理使用jdk8的lambda表达式减少嵌套精简代码，减少分支，提高可读性
13、代码不能含有中文硬编码问题! 除了log.xx()中可以使用中文，其他代码区域用中文必须提取出常量放置到常量类中或者枚举类中，常量类命名必须是xxxxConstant，
14、VO/BO/DO/DTO互转或者对象构建必须使用 xxxConverter转换类封装转换实现，且不能引入任何service
15、测试验证UT的时候禁止使用全局编译!只需要编译改变的文件
16、xxService函数的逻辑处理中含有对象构建需要在xxxConverter类中使用 组装函数组装，禁止当前service处理无关业务逻辑的
17、xxservice函数入参一般需要使用BizRequest<T>定义,如BizRequest<xxxDTO>
18、代码禁止硬编码中文!特别是返回字符串或者复制字符串!任何xxx.setxxx()， 禁止硬编码赋值，需要提取常量放置到对于xxxConstant类中，
19、代码函数应该按照高内聚低耦合原则，合理切分代码
20、原则上必须复用当前项目代码风格，包括但不限于代码分层，出入参转换传递，逻辑流程处理等
21、禁止重复造轮子!需要复用已有的组件、函数、常量、枚举
22、代码风格主要参照 com.zte.mcrm.activity包下的代码，其他代码比较老，参考意义不大
23、函数入参个数不能超过5个，如果超过需要提取对象进行包裹。
24、禁止UT使用 ArgumentMatchers类！



## controller特殊要求
参照 MagazineSubscriptionQueryController 的代码结构
1、入参使用 BizRequestUtil.createWithCurrentUser(param) 实例化service入参对象;

## service特殊要求
参照 MagazineSubscriptionQueryServiceImpl 的代码结构
1. 异常不需要捕捉，使用 BizRuntimeException 抛出异常
2. 抛出的错误信息需要使用中英文双语配置,配置到 messages_en_US.properties，messages_zh_CN.properties中，参考"magazine.subscription.get.contact.error";
3. service函数出入参使用固定对象包裹：
   - BizResult<T>为出参
   - BizRequest<T>为入参


## 📋 目录
- [项目架构规范](mdc:#项目架构规范)
- [包结构规范](mdc:#包结构规范)
- [分层架构规范](mdc:#分层架构规范)
- [数据访问层规范](mdc:#数据访问层规范)
- [注解使用规范](mdc:#注解使用规范)
- [编码规范](mdc:#编码规范)
- [API设计规范](mdc:#api设计规范)
- [异常处理规范](mdc:#异常处理规范)
- [配置管理规范](mdc:#配置管理规范)
- [测试规范](mdc:#测试规范)

## 🏗️ 项目架构规范

### 技术栈
- **框架**: Spring Boot 2.x + 中兴MSA框架 *******
- **数据访问**: MyBatis
- **文档**: Swagger 2
- **消息队列**: Kafka
- **缓存**: Redis
- **安全**: 中兴统一权限系统(UPP)
- **Java版本**: JDK 1.8

## 📁 包结构规范

### 标准包结构
```
com.zte.mcrm.activity/              # 活动业务模块（主要开发路径）
├── web/                           # Web层
│   └── controller/                # 控制器层
├── application/                   # 应用层
├── service/                       # 服务层
│   ├── I{Service}Service.java     # 服务接口
│   └── impl/
│       └── {Service}ServiceImpl.java   # 服务实现
├── repository/                    # 数据访问层
│   ├── model/                     # 数据模型层
│   │   ├── activity/              # 活动相关模型
│   │   ├── people/                # 人员相关模型
│   │   ├── relation/              # 关系相关模型
│   │   └── summary/               # 总结相关模型
│   ├── mapper/                    # MyBatis映射接口
│   │   └── activity/              # 活动相关Mapper（重点目录）
│   │       ├── {Entity}Mapper.java        # 基础Mapper接口
│   │       └── {Entity}ExtMapper.java     # 扩展Mapper接口
│   └── rep/                       # Repository层
├── integration/                   # 外部系统集成层
├── common/                        # 模块公共组件
│   ├── constant/                  # 常量定义
│   ├── enums/                     # 模块枚举
│   ├── util/                      # 模块工具类
│   ├── exception/                 # 模块异常
│   └── config/                    # 模块配置
└── thread/                        # 线程管理
```

### Mapper XML文件位置规范
> **重要**: XML映射文件的标准存放位置

```
src/main/resources/
└── com/zte/mcrm/activity/repository/mapper/activity/
    ├── {Entity}Mapper.xml         # 基础映射文件（对应基础Mapper接口）
    └── {Entity}ExtMapper.xml      # 扩展映射文件（对应扩展Mapper接口）
```

**示例**:
```
src/main/resources/com/zte/mcrm/activity/repository/mapper/activity/
├── ActivityApprovalInfoMapper.xml
├── ActivityApprovalInfoExtMapper.xml
├── ActivityInfoMapper.xml
├── ActivityInfoExtMapper.xml
├── ActivityStatusLifecycleMapper.xml
└── ActivityStatusLifecycleExtMapper.xml
```

### 包命名规范
- 全小写字母
- 使用点号分隔层次
- 包名要体现功能职责
- 避免使用数字和特殊字符

## 🔄 分层架构规范

### Controller层规范

#### 职责
- 处理HTTP请求和响应
- 参数校验和数据转换
- 调用Service层处理业务逻辑
- 不包含具体业务逻辑

#### 规范要求
```java
/**
 * 活动信息控制器
 * 负责活动信息相关API接口
 * 
 * <AUTHOR>
 */
@Api(tags = "活动信息管理")
@RestController
@RequestMapping("/activity/activityInfo")
public class ActivityInfoController {

    private static final Logger log = Logger.getLogger(ActivityInfoController.class);

    @Autowired
    private IActivityInfoService activityInfoService;

    @ApiOperation("根据主键获取活动详情")
    @GetMapping(value = "/{id}")
    @ResponseBody
    public ServiceData get(@PathVariable("id") @ApiParam(value = "活动ID", required = true) String id) 
        throws Exception {
        
        ServiceData ret = new ServiceData();
        ActivityInfoDO result = activityInfoService.get(id);
        ret.setBo(result);
        return ret;
    }
}
```

### Service层规范

#### 职责
- 业务逻辑处理
- 事务控制
- 数据组装和转换
- 调用Repository层进行数据操作

#### 规范要求
```java
/**
 * 活动信息服务接口
 */
public interface IActivityInfoService {
    /**
     * 根据ID获取活动信息
     * @param id 主键ID
     * @return 活动信息对象
     */
    ActivityInfoDO get(String id) throws Exception;
}

/**
 * 活动信息服务实现
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ActivityInfoServiceImpl implements IActivityInfoService {

    private static final Logger log = Logger.getLogger(ActivityInfoServiceImpl.class);

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Override
    public ActivityInfoDO get(String id) throws Exception {
        if (StringUtils.isBlank(id)) {
            throw new ValidationException("ID不能为空");
        }
        return activityInfoRepository.selectByPrimaryKey(id);
    }
}
```

### Repository层规范

#### 职责
- 数据访问和持久化
- 复杂查询逻辑封装
- 调用Mapper进行数据库操作

#### 规范要求
```java
/**
 * 活动信息数据仓储接口
 */
public interface ActivityInfoRepository {
    ActivityInfoDO selectByPrimaryKey(String id);
    int insertSelective(ActivityInfoDO record);
    int updateByPrimaryKeySelective(ActivityInfoDO record);
}

/**
 * 活动信息数据仓储实现
 */
@Service
public class ActivityInfoRepositoryImpl implements ActivityInfoRepository {
    
    @Autowired
    private ActivityInfoExtMapper activityInfoExtMapper;
    
    @Override
    public ActivityInfoDO selectByPrimaryKey(String id) {
        return activityInfoExtMapper.selectByPrimaryKey(id);
    }
}
```

## 🗄️ 数据访问层规范

### 数据表映射规范

> **核心规范**: 每个数据表必须拆分成基础Mapper和扩展Mapper，对应两个XML文件

#### 拆分原则
```
一个数据表 → 四个文件
├── {Entity}Mapper.java          # 基础Mapper接口（自动生成的标准CRUD）
├── {Entity}ExtMapper.java       # 扩展Mapper接口（业务自定义查询）
├── {Entity}Mapper.xml           # 基础映射文件（标准SQL映射）
└── {Entity}ExtMapper.xml        # 扩展映射文件（业务SQL映射）
```

#### 基础Mapper接口规范
> **说明**: 包含标准的CRUD操作，通常由代码生成器生成

```java
/**
 * 活动审批信息基础Mapper
 * 包含自动生成的标准CRUD操作
 * 
 * <AUTHOR> Generator
 */
public interface ActivityApprovalInfoMapper {
    
    /**
     * 全字段插入
     */
    int insert(ActivityApprovalInfoDO record);

    /**
     * 动态字段插入
     */
    int insertSelective(ActivityApprovalInfoDO record);

    /**
     * 根据主键查询
     */
    ActivityApprovalInfoDO selectByPrimaryKey(String rowId);

    /**
     * 根据主键更新全字段
     */
    int updateByPrimaryKey(ActivityApprovalInfoDO record);

    /**
     * 根据主键动态更新字段
     */
    int updateByPrimaryKeySelective(ActivityApprovalInfoDO record);
}
```

#### 扩展Mapper接口规范
> **说明**: 继承基础Mapper，添加业务相关的复杂查询

```java
/**
 * 活动审批信息扩展Mapper
 * 继承基础Mapper，包含业务相关的复杂查询
 * 
 * <AUTHOR>
 */
@Mapper
public interface ActivityApprovalInfoExtMapper extends ActivityApprovalInfoMapper {
    
    /**
     * 根据活动ID查询审批信息列表
     * @param activityRowId 活动ID
     * @return 审批信息列表
     */
    List<ActivityApprovalInfoDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);
    
    /**
     * 根据审批编号查询审批信息
     * @param approvalNo 审批编号
     * @return 审批信息列表
     */
    List<ActivityApprovalInfoDO> queryAllByApprovalNo(@Param("approvalNo") String approvalNo);
    
    /**
     * 根据活动ID选择性更新
     * @param record 更新记录
     * @return 更新行数
     */
    int updateByActivityRowIdSelective(ActivityApprovalInfoDO record);
    
    /**
     * 批量插入
     * @param list 记录列表
     * @return 插入行数
     */
    int insertByBatch(List<ActivityApprovalInfoDO> list);
}
```

#### XML映射文件规范

**基础Mapper映射文件**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 基础Mapper映射文件 -->
<mapper namespace="com.zte.mcrm.activity.repository.mapper.activity.ActivityApprovalInfoExtMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO">
        <result column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="activity_row_id" property="activityRowId" jdbcType="VARCHAR"/>
        <result column="instance_id" property="instanceId" jdbcType="VARCHAR"/>
        <result column="instance_status" property="instanceStatus" jdbcType="CHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="CHAR"/>
        <result column="approval_no" property="approvalNo" jdbcType="VARCHAR"/>
        <result column="approval_type" property="approvalType" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        row_id, activity_row_id, instance_id, instance_status, created_by, creation_date, 
        last_updated_by, last_update_date, enabled_flag, approval_no, approval_type
    </sql>
    
    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM activity_approval_info
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>
    
    <!-- 动态插入 -->
    <insert id="insertSelective" parameterType="com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO">
        INSERT INTO activity_approval_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rowId != null">row_id,</if>
            <if test="activityRowId != null">activity_row_id,</if>
            <if test="instanceId != null">instance_id,</if>
            <!-- 其他字段 -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rowId != null">#{rowId,jdbcType=VARCHAR},</if>
            <if test="activityRowId != null">#{activityRowId,jdbcType=VARCHAR},</if>
            <if test="instanceId != null">#{instanceId,jdbcType=VARCHAR},</if>
            <!-- 其他字段 -->
        </trim>
    </insert>
    
</mapper>
```

**扩展Mapper映射文件**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 扩展Mapper映射文件 -->
<mapper namespace="com.zte.mcrm.activity.repository.mapper.activity.ActivityApprovalInfoExtMapper">
    
    <!-- 根据活动ID查询审批信息列表 -->
    <select id="queryAllByActivityRowId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM activity_approval_info
        WHERE activity_row_id = #{activityRowId,jdbcType=VARCHAR}
        AND enabled_flag = 'Y'
        ORDER BY last_update_date DESC
    </select>
    
    <!-- 根据审批编号查询审批信息 -->
    <select id="queryAllByApprovalNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM activity_approval_info
        WHERE approval_no = #{approvalNo,jdbcType=VARCHAR}
        AND enabled_flag = 'Y'
        ORDER BY last_update_date DESC
    </select>
    
    <!-- 根据活动ID选择性更新 -->
    <update id="updateByActivityRowIdSelective" parameterType="com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO">
        UPDATE activity_approval_info
        <set>
            <if test="instanceId != null">instance_id = #{instanceId,jdbcType=VARCHAR},</if>
            <if test="instanceStatus != null">instance_status = #{instanceStatus,jdbcType=CHAR},</if>
            <if test="lastUpdatedBy != null">last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},</if>
            <if test="lastUpdateDate != null">last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE activity_row_id = #{activityRowId,jdbcType=VARCHAR}
    </update>
    
    <!-- 批量插入 -->
    <insert id="insertByBatch" parameterType="java.util.List">
        INSERT INTO activity_approval_info (
            row_id, activity_row_id, instance_id, instance_status, 
            created_by, creation_date, last_updated_by, last_update_date, enabled_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.rowId,jdbcType=VARCHAR},
                #{item.activityRowId,jdbcType=VARCHAR},
                #{item.instanceId,jdbcType=VARCHAR},
                #{item.instanceStatus,jdbcType=CHAR},
                #{item.createdBy,jdbcType=VARCHAR},
                #{item.creationDate,jdbcType=TIMESTAMP},
                #{item.lastUpdatedBy,jdbcType=VARCHAR},
                #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                #{item.enabledFlag,jdbcType=CHAR}
            )
        </foreach>
    </insert>
    
</mapper>
```

### Domain层规范

#### DO (Data Object)
```java
/**
 * 活动信息数据对象
 */
@Getter
@Setter
@ToString
public class ActivityInfoDO {
    
    /** 主键 */
    private String rowId;
    
    /** 活动标题 */
    private String activityTitle;
    
    /** 活动类型 */
    private String activityType;
    
    /** 活动状态 */
    private String activityStatus;

}
```

#### BO (Business Object)
```java
/**
 * 活动查询业务对象
 */
@Getter
@Setter
@ToString
public class ActivityQueryBO {
    
    @ApiModelProperty("查询开始时间")
    private Date startTime;
    
    @ApiModelProperty("查询结束时间")
    private Date endTime;
    
    @ApiModelProperty("活动状态")
    private String activityStatus;
    
   
}
```

#### VO (view Object)
```java
/**
 * 活动查询VO对象
 */
@Getter
@Setter
@ToString
public class ActivityQueryVO {

    @ApiModelProperty("活动编号")
    private Long activityNo;
    
    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动状态")
    private String activityStatus;
    
   
}
```

## 🏷️ 注解使用规范

### Spring相关注解

#### 组件注解
```java
@RestController     // REST控制器
@Service           // 服务层组件
@Repository        // 数据访问层组件(可选,已使用@Mapper)
@Component         // 通用组件
```

#### 依赖注入注解
```java
@Autowired         // 自动注入
@Qualifier         // 指定注入的Bean
@Value             // 注入配置值
@Resource          // JSR-250标准注入
```

#### 事务注解
```java
@Transactional(rollbackFor = Exception.class)  // 事务控制,遇到任何异常都回滚
```

### Web相关注解

#### 请求映射注解
```java
@RequestMapping("/activity")       // 类级别路径映射
@GetMapping("/list")              // GET请求映射 - 查询操作
@GetMapping("/{id}")              // GET请求映射 - 获取单个资源  
@PostMapping("/save")             // POST请求映射 - 保存操作
@PostMapping("/update")           // POST请求映射 - 更新操作
@PostMapping("/delete")           // POST请求映射 - 删除操作
```

#### 参数注解
```java
@PathVariable("id")               // 路径变量
@RequestParam("name")             // 请求参数
@RequestBody                      // 请求体
@RequestHeader                    // 请求头
@ResponseBody                     // 响应体
```

### Swagger文档注解

#### API文档注解
```java
@Api(tags = "模块名称")                    // 控制器描述
@ApiOperation("接口功能描述")               // 接口描述
@ApiParam(value = "参数描述", required = true)  // 参数描述
@ApiModel("模型描述")                      // 模型描述
@ApiModelProperty("属性描述")               // 属性描述
```

### 校验注解

#### JSR-303校验注解
```java
@Valid                           // 启用校验
@Validated({SaveGroup.class})    // 分组校验
@NotNull(message = "不能为空")     // 非空校验
@NotBlank(message = "不能为空字符") // 非空字符校验
@Length(max = 100, message = "长度不能超过100") // 长度校验
@Pattern(regexp = "正则表达式")    // 正则校验
@Range(min = 1, max = 100)       // 数值范围校验
```

### MyBatis注解

#### 映射注解
```java
@Mapper                          // Mapper接口标识（仅在ExtMapper上使用）
@Param("paramName")              // 参数名映射
```

## 📝 编码规范

### 命名规范

#### 类命名
```java
// 控制器类
{模块名}Controller
例: ActivityInfoController

// 服务接口
I{模块名}Service  
例: IActivityInfoService

// 服务实现
{模块名}ServiceImpl
例: ActivityInfoServiceImpl

// Repository接口
{模块名}Repository
例: ActivityInfoRepository

// Repository实现
{模块名}RepositoryImpl
例: ActivityInfoRepositoryImpl

// 基础Mapper接口
{模块名}Mapper
例: ActivityApprovalInfoMapper

// 扩展Mapper接口
{模块名}ExtMapper
例: ActivityApprovalInfoExtMapper

// DO对象
{模块名}DO
例: ActivityInfoDO

// BO对象
{模块名}BO
例: ActivityQueryBO
```

#### 方法命名
```java
// CRUD操作
selectByPrimaryKey(String id)    // 根据主键查询
queryAllByActivityRowId(String activityRowId)  // 根据条件查询列表
参考：PageRows<SelectExhibitionVO> selectableList(BizRequest<PageQuery<ExhibitionSelectParam>> request);        // 分页查询
insertSelective(Entity entity)  // 动态插入
updateByPrimaryKeySelective(Entity entity)  // 根据主键动态更新
deleteByPrimaryKey(String id)    // 根据主键删除
insertByBatch(List<Entity> list) // 批量插入
```

#### 变量命名
```java
// 常量: 全大写,下划线分隔
private static final String ACTIVITY_STATUS_DRAFT = "DRAFT";

// 静态变量: 小驼峰
private static final Logger log = Logger.getLogger(ActivityInfoController.class);

// 实例变量: 小驼峰
private IActivityInfoService activityInfoService;

// 局部变量: 小驼峰
ServiceData ret = new ServiceData();
```

### 注释规范

#### 类注释
```java
/**
 * 活动信息控制器
 * 负责处理活动信息的CRUD操作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ActivityInfoController {
}
```

#### 方法注释
```java
/**
 * 根据ID获取活动详情
 * 
 * @param id 活动ID,不能为空
 * @return 活动信息对象
 * @throws ValidationException 参数校验异常
 * @throws BusinessException 业务异常
 */
public ServiceData get(String id) throws Exception {
}
```

#### 复杂业务逻辑注释
```java
// 1. 数据标准化处理
dataStandardizing(activityInfoDO);

// 2. 保存活动主表数据
int count = activityInfoService.save(activityInfoDO);

// 3. 处理返回结果
if (count > 0) {
    ret.setBo(activityInfoDO.getRowId());
} else {
    // 保存失败,返回错误信息
    ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
}
```

## 🌐 API设计规范

### HTTP方法使用规范

#### 项目HTTP方法约定
> **说明**: 项目只使用GET和POST两种HTTP方法，简化前后端开发和理解成本

```java
GET    /resource/{id}       // 获取单个资源
GET    /resource/list       // 获取资源列表  
GET    /resource/getpage    // 分页查询
POST   /resource/save       // 创建资源
POST   /resource/update     // 更新资源
POST   /resource/delete     // 删除资源
```

#### URL设计
```java
// 资源路径设计
/activity/activityInfo                    // 资源根路径
/activity/activityInfo/{id}               // 特定资源
/activity/activityInfo/list               // 列表查询
/activity/activityInfo/getpage            // 分页查询
/activity/activityInfo/save               // 保存操作
/activity/activityInfo/update             // 更新操作
/activity/activityInfo/delete             // 删除操作
```

### 统一响应格式

#### ServiceData响应结构
```java
{
    "code": {
        "code": "0000",           // 响应码
        "msgId": "success"        // 消息ID
    },
    "bo": {                       // 业务数据
        // 具体业务对象
    }
}
```

#### 分页响应结构
```java
{
    "code": {
        "code": "0000",
        "msgId": "success"
    },
    "bo": {
        "rows": [],               // 当前页数据
        "total": 100,             // 总记录数
        "pageNo": 1,              // 当前页码
        "pageSize": 10            // 每页大小
    }
}
```

### 参数校验规范

#### 分组校验
```java
// 定义校验分组
public interface SaveGroup {}      // 保存时校验
public interface SubmitGroup {}    // 提交时校验

// 使用分组校验
@PostMapping("/save")
public ServiceData save(
    @Validated({SaveGroup.class, Default.class}) 
    @RequestBody ActivityInfoDO activityInfo,
    BindingResult result) throws Exception {
    
    // 检查校验结果
    if (result.hasErrors()) {
        throw new ValidationException(result);
    }
}
```

### 事务管理规范

#### 事务注解使用
```java
@Service
@Transactional(rollbackFor = Exception.class)  // 类级别事务
public class ActivityInfoServiceImpl implements IActivityInfoService {
    
    @Override
    @Transactional(readOnly = true)  // 只读事务
    public ActivityInfoDO get(String id) throws Exception {
        return activityInfoRepository.selectByPrimaryKey(id);
    }
    
    @Override
    public int save(ActivityInfoDO activityInfo) throws Exception {
        // 写操作使用默认事务设置
        return activityInfoRepository.insertSelective(activityInfo);
    }
}
```

## ⚠️ 异常处理规范

### 异常层次结构
```java
// 业务异常基类
public class BusinessException extends RuntimeException {
    private String code;
    private String msgId;
    
    // 构造方法
}

// 参数校验异常
public class ValidationException extends BusinessException {
    // 校验错误详情
}

// 数据访问异常  
public class DataAccessException extends BusinessException {
    // 数据访问错误
}
```

### 统一异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = Logger.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 校验异常处理
     */
    @ExceptionHandler(ValidationException.class)
    public ServiceData handleValidationException(ValidationException e) {
        log.error("参数校验异常", e);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode("1001", "参数校验失败"));
        ret.setBo(e.getErrors());
        return ret;
    }
    
    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException.class)
    public ServiceData handleBusinessException(BusinessException e) {
        log.error("业务异常", e);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(e.getCode(), e.getMsgId()));
        return ret;
    }
    
    /**
     * 系统异常处理
     */
    @ExceptionHandler(Exception.class)
    public ServiceData handleException(Exception e) {
        log.error("系统异常", e);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode("9999", "系统异常"));
        return ret;
    }
}
```

## ⚙️ 配置管理规范

> **🚨 重要警告**: 禁止修改配置文件，任何配置变更都需要请示

### 禁止修改的文件清单
```
❌ 禁止修改以下文件，如需修改必须请示：
├── pom.xml                    # Maven配置文件
├── application.yml            # 应用配置文件
├── application-*.properties   # 环境配置文件
├── bootstrap.yml              # 引导配置文件
├── logback-spring.xml         # 日志配置文件
└── src/main/java/*/config/    # 所有配置类
    ├── Swagger2Config.java
    ├── MybatisConfig.java
    ├── RedisConfig.java
    └── 其他所有Config类
```

### 配置类规范
> **注意**: 所有配置类的修改都需要请示，不要随意改动

```java
/**
 * Swagger配置类
 * 🚨 注意：配置类修改需要请示，禁止随意更改
 */
@Configuration
@EnableSwagger2
public class Swagger2Config {
    
    // 配置内容...
    // 如需修改，请先联系架构组
}
```

### 配置文件层次
```yaml
# 🚨 以下配置文件禁止修改，如需修改请先请示

# bootstrap.yml - 引导配置
spring:
  application:
    name: zte-crm-custinfo-custvisit
  cloud:
    config:
      enabled: true

# application.yml - 主配置
server:
  port: 8080
  servlet:
    context-path: /${spring.application.name}/

# application-{profile}.properties - 环境配置
# application-dev.properties
# application-test.properties  
# application-uat.properties
# application-prod.properties
```

## 🧪 测试规范

### 测试目录结构
> **重要**: 单元测试与业务代码必须在同一package下

```
src/test/java/com/zte/mcrm/activity/
├── web/controller/                           # 控制器测试
│   ├── ActivityInfoControllerTest.java
│   └── ActivityApprovalControllerTest.java
├── service/                                  # 服务层测试
│   ├── activity/
│   │   └── impl/
│   │       └── ActivityInfoServiceImplTest.java
│   ├── approval/
│   │   └── ActivityApprovalInfoServiceImplTest.java
│   └── activitylist/
│       └── impl/
│           └── ActivityInfoListQueryServiceImplTest.java
└── repository/                               # 数据访问测试
    └── rep/
        └── activity/
            └── impl/
                ├── ActivityInfoRepositoryImplTest.java
                └── ActivityApprovalInfoRepositoryImplTest.java
```

### 单元测试规范
```java
/**
 * 活动信息服务测试类
 * 注意：测试类必须与被测试类在同一package下
 */
@RunWith(PowerMockRunner.class)
public class ActivityInfoServiceImplTest {
    
    @InjectMocks
    private ActivityInfoServiceImpl activityInfoService;
    
    @Mock
    private ActivityInfoRepository activityInfoRepository;
    
    @Test
    public void testGet() throws Exception {
        // Given
        String id = "test-id";
        ActivityInfoDO expected = new ActivityInfoDO();
        expected.setRowId(id);
        
        when(activityInfoRepository.selectByPrimaryKey(id)).thenReturn(expected);
        
        // When
        ActivityInfoDO actual = activityInfoService.get(id);
        
        // Then
        assertThat(actual).isNotNull();
        assertThat(actual.getRowId()).isEqualTo(id);
    }
    
    @Test(expected = ValidationException.class)
    public void testGetWithNullId() throws Exception {
        // When & Then
        activityInfoService.get(null);
    }
}
```

### 集成测试规范
```java
/**
 * 活动信息控制器集成测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
public class ActivityInfoControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testGetActivity() {
        // Given
        String id = "test-id";
        
        // When
        ResponseEntity<ServiceData> response = restTemplate.getForEntity(
            "/activity/activityInfo/" + id, 
            ServiceData.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
    }
}
```

## 📊 日志规范

### 日志级别使用
```java
public class ActivityInfoServiceImpl {
    
    private static final Logger log = Logger.getLogger(ActivityInfoServiceImpl.class);
    
    public ActivityInfoDO get(String id) throws Exception {
        // DEBUG: 调试信息
        log.debug("开始获取活动信息, id=" + id);
        
        // INFO: 关键业务流程
        log.info("获取活动信息成功, id=" + id);
        
        // WARN: 警告信息
        log.warn("活动信息不存在, id=" + id);
        
        // ERROR: 错误信息
        log.error("获取活动信息失败, id=" + id, e);
    }
}
```

### 日志格式规范
```xml
<!-- 🚨 logback-spring.xml 禁止修改，如需修改请先请示 -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>
```

## 🔒 安全规范

### 权限控制
```java
/**
 * 权限检查工具使用
 */
@RestController
public class ActivityInfoController {
    
    @Autowired
    private AuthorityClientUtil authorityClientUtil;
    
    @PostMapping("/save")
    public ServiceData save(@RequestBody ActivityInfoDO activityInfo) throws Exception {
        // 权限检查
        AuthorityUserBO currentUser = authorityClientUtil.getCurrentUser();
        if (!hasPermission(currentUser, "ACTIVITY_CREATE")) {
            throw new BusinessException("无操作权限");
        }
        
        return activityInfoService.save(activityInfo);
    }
}
```

### 数据脱敏
```java
/**
 * 敏感数据处理
 */
public class DataMaskUtil {
    
    /**
     * 手机号脱敏
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone) || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
```

---

## 📋 检查清单

### 开发前检查
- [ ] 是否遵循包结构规范（com.zte.mcrm.activity）
- [ ] 是否正确拆分Mapper和ExtMapper
- [ ] XML文件是否存放在正确路径（com.zte.mcrm.activity.repository.mapper.activity）
- [ ] 是否使用正确的注解
- [ ] 是否定义了合适的校验分组
- [ ] 是否编写了Swagger文档

### 代码提交前检查  
- [ ] 代码是否符合命名规范
- [ ] 是否添加了必要的注释
- [ ] 是否处理了异常情况
- [ ] 是否编写了单元测试(与业务代码同package)
- [ ] 是否遵循了事务管理规范
- [ ] HTTP接口是否只使用GET和POST方法
- [ ] 是否确认没有修改配置文件和config类

### 上线前检查
- [ ] 是否完成了集成测试
- [ ] 是否更新了API文档
- [ ] 是否配置了正确的环境参数
- [ ] 是否进行了性能测试
- [ ] 所有配置类和配置文件修改是否已请示并获得批准

---




*本规范基于ZTE-CRM客户拜访系统activity模块制定，严格按照项目实际结构执行。如有疑问请联系架构组。* 