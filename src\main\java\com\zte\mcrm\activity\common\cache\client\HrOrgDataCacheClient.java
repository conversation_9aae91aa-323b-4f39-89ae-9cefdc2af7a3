package com.zte.mcrm.activity.common.cache.client;

import com.zte.mcrm.activity.common.cache.cache.CacheConfig;
import com.zte.mcrm.activity.common.cache.loader.CacheDataLoader;
import com.zte.mcrm.activity.common.cache.model.HrOrgDataModel;
import com.zte.mcrm.activity.common.enums.CompanyOrgSourceEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * 公司hr组织信息缓存使用客户端
 *
 * <AUTHOR>
 */
public class HrOrgDataCacheClient extends BaseCacheClient<HrOrgDataModel> {
    /**
     * @param loader 缓存数据加载器
     * @param config 缓存配置
     */
    public HrOrgDataCacheClient(CacheDataLoader<String, HrOrgDataModel> loader, CacheConfig config) {
        super(loader, config);
    }

    /**
     * 获取组织部门的名称
     *
     * @param orgNos
     * @return 《编码，组织部门名称》
     */
    public Map<String, String> fetchOrgName(List<String> orgNos) {
        if (CollectionUtils.isEmpty(orgNos)) {
            return Collections.emptyMap();
        }
        Map<String, HrOrgDataModel> map = fetchAllCache(new HashSet<>(orgNos));

        Map<String, String> res = new HashMap<>();
        map.forEach((no, model) -> res.put(no, model.getHrOrgName()));
        return res;
    }

    /**
     * 是否为某来源（子公司，股份等）的组织
     *
     * @param orgNo
     * @param orgSource
     * @return true-说明组织存在且是所属来源；false-说明不存在或不是所属来源的
     */
    public boolean belongSource(String orgNo, CompanyOrgSourceEnum orgSource) {
        Boolean flag = fetchAndTrans(orgNo, model -> model.belongSource(orgSource));
        return flag != null && flag;
    }

    /**
     * 判断子路径是否在多个父层目录下
     *
     * @param parentList
     * @param orgNo
     * @return
     */
    public boolean isFromSourceList(List<String> parentList, String orgNo) {
        if (CollectionUtils.isEmpty(parentList)) {
            return false;  // 如果 parentList 为空，返回 false
        }
        HrOrgDataModel model = fetchCache(orgNo);
        return model != null
                && StringUtils.isNotEmpty(model.getOrgIDPath())
                && parentList.stream().anyMatch(parentPath -> model.getOrgIDPath().contains(parentPath));
    }


}
