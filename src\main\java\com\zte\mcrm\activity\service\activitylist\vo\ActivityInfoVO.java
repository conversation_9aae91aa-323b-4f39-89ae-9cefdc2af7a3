package com.zte.mcrm.activity.service.activitylist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/16 14:09
 */
@Setter
@Getter
@ToString
public class ActivityInfoVO {

    /**
     * 活动id
     */
    private String rowId;

    /***
     * 展会名称
     */
    private String exhibitionName;

    /** 拓展活动申请单号 */
    private String activityRequestNo;

    /** 拓展活动议题 */
    private String activityTitle;

    /** 客户编码 */
    private String customerCode;

    /** 客户名称 */
    private String customerName;

    /** 客户联系人编码 */
    private List<String> contactNoList;

    /** 客户联系人名称*/
    private List<String> contactNameList;

    /** 客户联系人编码 */
    private String contactNo;

    /** 客户联系人名称*/
    private String contactName;

    /**
     * 客户联系人数量
     */
    private int contactNum;

    /** 提交人*/
    private String createdBy;

    /** 提交人*/
    private String createdByName;

    /** 我司参与人*/
    private String ztePeopleId;
    /** 我司参与人*/
    private String ztePeopleName;

    /**
     * 我司联系人数量
     */
    private int zteContactNum;

    /** 申请人员工编号 */
    private String applyPeopleNo;

    /** 申请人员工编号名字 */
    private String applyPeopleName;

    /** 申请人部门编码 */
    private String applyDepartmentNo;

    /** 拓展活动状态。枚举：ActivityStatusEnum */
    private String activityStatus;

    /** 拓展活动状态。枚举：ActivityStatusEnum */
    private String activityStatusName;

    /**
     * 组织者
     */
    private String organizerId;

    /**
     * 组织者名称
     */
    private String organizerName;

    /**
     * 活动是否被关注
     */
    private boolean activityNotice;


    /** 活动截止时间。显示按yyyy-MM-dd HH:mm格式 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;

    /** 活动起始时间。显示按yyyy-MM-dd HH:mm格式 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;

    /** 拓展活动类型。枚举：ActivityTypeEnum，快码：Activity_Type_Enum */
    private String activityType;

    /** 拓展活动类型。枚举：ActivityTypeEnum，快码：Activity_Type_Enum */
    private String activityTypeName;

    /** 交流方式。枚举：ActivityCommunicationWayEnum */
    private String communicationWay;

    /** 活动地点 */
    private String activityPlace;

    /** 老数据来源 */
    private String oldDataSource;

    /** 权限标识信息 */
    private ActivityOperatorFlagVO operatorFlagInfo;

    /**
     * 导出按钮是否可见
     */
    private boolean exportButton;
}
