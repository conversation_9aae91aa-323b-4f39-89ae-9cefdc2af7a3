package com.zte.mcrm.activity.repository.rep.plancto;
/* Started by AICoder, pid:f512cge8acf3e1e145050a42a0d0ed0ebbf1322c */

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanExeReportDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日16:34
 */
public interface CtoPlanExeReportRepository {

    /**
     * 插入数据
     *
     * @param list
     */
    int batchInsert(@Param("list") List<CtoPlanExeReportDO> list);

    /**
     * 动态更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CtoPlanExeReportDO record);

    /**
     * 根据CTO握手计划ID获取执行报表数据
     *
     * @param ctoPlanInfoId
     * @return
     */
    List<CtoPlanExeReportDO> getByCtoPlanInfoId(String ctoPlanInfoId);

    /**
     * 查询执行报表中待执行的数据
     * @param planInfoId
     * @return
     */
    List<CtoPlanExeReportDO> listExeReportWait(String planInfoId);

    /**
     * 修改执行报表中的数据
     * @param list
     * @return
     */
    int batchUpdate(List<CtoPlanExeReportDO> list);

    /**
     * 通过主键查询
     * @param ids
     * @return
     */
    List<CtoPlanExeReportDO> selectByPrimaries(List<String> ids);
}

/* Ended by AICoder, pid:f512cge8acf3e1e145050a42a0d0ed0ebbf1322c */