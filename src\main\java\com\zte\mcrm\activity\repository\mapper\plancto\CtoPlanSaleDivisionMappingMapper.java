package com.zte.mcrm.activity.repository.mapper.plancto;

import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;

import java.util.List;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-12
 */
public interface CtoPlanSaleDivisionMappingMapper {
    /* Started by AICoder, pid:g5397m704cbecad14a7109574010c30705d59f26 */
    int batchInsert(List<CtoPlanSaleDivisionMappingDO> list);
    int insertSelective(CtoPlanSaleDivisionMappingDO record);
    int batchUpdate(List<CtoPlanSaleDivisionMappingDO> list);
    int updateByPrimaryKeySelective(CtoPlanSaleDivisionMappingDO record);
    Iterable<CtoPlanSaleDivisionMappingDO> selectByPrimaries(List<String> primaries);
    /* Ended by AICoder, pid:g5397m704cbecad14a7109574010c30705d59f26 */
}
