package com.zte.mcrm.activity.application.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class SelectCtoActivityInfoResDTO {

    private String rowId;               // a.row_id
    private String activityRequestNo;     // a.activity_request_no
    private String activityTitle;     // a.activity_title
    private String activityType;         // a.activity_type
    private LocalDateTime activityStartTime;         // a.start_time
    private LocalDateTime activityEndTime;         // a.end_time
    private String activityStatus;       // a.activity_status
    private String marketCode;        // c.mkt_name
    private String marketName;        // c.mkt_name
    private String localCode;         // c.local_code
    private String localName;         // c.local_name
    private String belongBuId;        // c.belong_bu_id
    private String peopleCode;        // p.people_code
    private String peopleName;        // p.people_name


    private String orgDivision;  // 所属营销事业部

    // 握手指标唯一标识（活动-account客户）
    private String activityAccountKey;
    public String getActivityAccountKey() {
        return activityRequestNo + "_" + marketCode;
    }





}
