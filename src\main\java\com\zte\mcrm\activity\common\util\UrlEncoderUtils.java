package com.zte.mcrm.activity.common.util;

import java.util.BitSet;

/**
 * URL 编码识别工具类
 * <AUTHOR>
 * @date 2019/05/30
 */
public class UrlEncoderUtils {
    
    private UrlEncoderUtils() {
        throw new IllegalStateException("UrlEncoderUtils class");
    }

	private static BitSet dontNeedEncoding;

	static {
		dontNeedEncoding = new BitSet(256);
		int i;
		int a = 'a';
		int z = 'z';
		int bigA = 'A';
		int bigZ = 'Z';
		int zreo = '0';
		int nine = '9';
		for (i = a; i <= z; i++) {
			dontNeedEncoding.set(i);
		}
		for (i = bigA; i <= bigZ; i++) {
			dontNeedEncoding.set(i);
		}
		for (i = zreo; i <= nine; i++) {
			dontNeedEncoding.set(i);
		}
		dontNeedEncoding.set('+');
		dontNeedEncoding.set('-');
		dontNeedEncoding.set('_');
		dontNeedEncoding.set('.');
		dontNeedEncoding.set('*');
	}

	/**
	 * 判断str是否urlEncoder.encode过<br>
	 * 
	 * @param str
	 * @return
	 */
	public static boolean hasUrlEncoded(String str) {

		/*
		 * 支持JAVA的URLEncoder.encode出来的string做判断。 即: 将' '转成'+' <br>
		 * 0-9a-zA-Z保留 <br>
		 * '-'，'_'，'.'，'*'保留 <br>
		 * 其他字符转成%XX的格式，X是16进制的大写字符，范围是[0-9A-F]
		 */
		boolean needEncode = false;
		for (int i = 0; i < str.length(); i++) {
			char c = str.charAt(i);
			if (dontNeedEncoding.get((int) c)) {
				continue;
			}
			if (c == '%' && (i + 2) < str.length()) {
				// 判断是否符合urlEncode规范
				char c1 = str.charAt(++i);
				char c2 = str.charAt(++i);
				if (isDigit16Char(c1) && isDigit16Char(c2)) {
					continue;
				}
			}
			// 其他字符，肯定需要urlEncode
			needEncode = true;
			break;
		}

		return !needEncode;
	}

	/**
	 * 判断c是否是16进制的字符
	 * 
	 * @param c
	 * @return
	 */
	private static boolean isDigit16Char(char c) {
		return (c >= '0' && c <= '9') || (c >= 'A' && c <= 'F');
	}
}
