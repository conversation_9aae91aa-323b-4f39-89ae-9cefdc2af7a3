package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.config.MailConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ActivityResourceOperationTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.AttachmentSceneTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.forwardmessage.ForwardMessageComponent;
import com.zte.mcrm.activity.integration.forwardmessage.param.ZmailBodyParam;
import com.zte.mcrm.activity.integration.talk.AiGenerationActivityTalkService;
import com.zte.mcrm.activity.integration.talk.param.AiGenerationActivityTalkParam;
import com.zte.mcrm.activity.integration.talk.param.AiGenerationTalkAbstractParam;
import com.zte.mcrm.activity.integration.talk.response.AiGenerationActivityTalkResponseVO;
import com.zte.mcrm.activity.integration.talk.response.AiGenerationTalkAbstractResponseVO;
import com.zte.mcrm.activity.integration.talk.response.AiGenerationTalkAbstractResponseVO.AiGenerationTalkSummary;
import com.zte.mcrm.activity.integration.talk.response.TalkAbstractResponseVO;
import com.zte.mcrm.activity.integration.usercenter.UserCenterService;
import com.zte.mcrm.activity.integration.usercenter.dto.UserInfoDTO;
import com.zte.mcrm.activity.integration.zteKmCloudUdmCloudDisk.CloudDiskDownloadService;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationAttachmentDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemPeopleRepository;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationAttachmentRepository;
import com.zte.mcrm.activity.service.activity.ActivityResourceOperationLogService;
import com.zte.mcrm.activity.service.activity.ActivityTalkService;
import com.zte.mcrm.activity.service.activity.model.TalkFowardModel;
import com.zte.mcrm.activity.service.activity.param.TalkAbstractGenerationParam;
import com.zte.mcrm.activity.service.authority.ResourceOperationAuthService;
import com.zte.mcrm.activity.service.authority.model.OperationAuthModel;
import com.zte.mcrm.activity.service.authority.param.OperationAuthGrantModel;
import com.zte.mcrm.activity.service.authority.param.OperationAuthParam;
import com.zte.mcrm.activity.service.schedule.param.ScheduleOrchestrationMailParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityTalkMessageBodyParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityTalkParam;
import com.zte.mcrm.activity.web.controller.activity.vo.AiActivityTalkVO;
import com.zte.mcrm.expansion.business.ICustExpansionMoaService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.zte.mcrm.activity.common.constant.I18Constants.TRANSFER_FAILED_NO_AUTH;

@Service
public class ActivityTalkServiceImpl implements ActivityTalkService {

	private static final Logger logger = LoggerFactory.getLogger(ActivityTalkServiceImpl.class);
	
    @Autowired
    private AiGenerationActivityTalkService aiGenerationActivityTalkService;
    @Autowired
    private ResourceOperationAuthService resourceOperationAuthService;
    @Autowired
    private ForwardMessageComponent forwardMessageComponent;
    @Autowired
    private ActivityRelationAttachmentRepository attachmentRepository;
    @Autowired
    private ActivityInfoRepository activityInfoRepository;
    @Autowired
    private ActivityScheduleItemRepository scheduleItemRepository;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ActivityScheduleItemPeopleRepository scheduleItemPeopleRepository;
    @Autowired
    private ActivityResourceOperationLogService operationLogService;

    @Autowired
    private CloudDiskDownloadService cloudDiskDownloadService;

    @Autowired
    private ICustExpansionMoaService custExpansionMoaService;

    @Autowired
    private MailConfig mailConfig;

    @Override
    public AiActivityTalkVO getTalkByAiAssistant(ActivityTalkParam activityTalkParam) {
        AiActivityTalkVO aiActivityTalkVO = null;
        AiGenerationActivityTalkParam aiGenerationActivityTalkParam = new AiGenerationActivityTalkParam();
        aiGenerationActivityTalkParam.setId(activityTalkParam.getActivityRowId());
        aiGenerationActivityTalkParam.setSchId(activityTalkParam.getActivityScheduleItemRowId());
        AiGenerationActivityTalkResponseVO aiTalk = aiGenerationActivityTalkService.getTalkByAiAssistant(aiGenerationActivityTalkParam);
        if(aiTalk==null){
            return aiActivityTalkVO;
        }
        aiActivityTalkVO = new AiActivityTalkVO();
        aiActivityTalkVO.setFileName(aiTalk.getFileName());
        aiActivityTalkVO.setFileKey(aiTalk.getFileToken());
        return aiActivityTalkVO;

    }

    /**
     * 活动谈参转发到icenter，加入鉴权
     * @param request
     * @return boolean
     * <AUTHOR>
     * date: 2024/11/20 11:03
     */
    @Override
    public boolean forwardTalkToCenter(BizRequest<ActivityTalkMessageBodyParam> request) {
        ActivityTalkMessageBodyParam messageBodyParam = request.getParam();
        String scheduleId = messageBodyParam.getActivityScheduleItemRowId();
        ActivityInfoDO activity = activityInfoRepository.selectByPrimaryKey(messageBodyParam.getId());
        ActivityScheduleItemDO scheduleItemDO  = scheduleItemRepository.selectByPrimaryKey(scheduleId);
        List<ActivityRelationAttachmentDO> attachmentDOList = attachmentRepository
                .queryActivityAttachmentList(Lists.newArrayList(messageBodyParam.getActivityScheduleItemRowId()));
        List<ActivityScheduleItemPeopleDO> schedulePeopleInfoList = scheduleItemPeopleRepository
                .getRelationSchedulePeopleInfoList(Lists.newArrayList(scheduleId));

        TalkFowardModel forwardModel = new TalkFowardModel();

        forwardModel.setScheduleItem(scheduleItemDO);
        forwardModel.setSchedulePeopleInfoList(schedulePeopleInfoList);
        forwardModel.setAttachmentDOList(attachmentDOList);
        forwardModel.setActivity(activity);
        forwardModel.setTalkMessageBodyParam(messageBodyParam);
        // 从转发信息中抽除人员名称
        forwardModel.extractPeopleNo();
        // 查询人员
        List<UserInfoDTO> userInfoList = userCenterService.queryUserByIds(forwardModel.getAllEmpNo());
        List<OperationAuthGrantModel> authGrantModel = forwardModel.buildAuthGrantModel();
        // 组装邮件通知  日程名称/活动编号/议题/谈参名称/转发人/被转发人/转发时间
        ZmailBodyParam zmailBodyParam = forwardModel.buildMailBO(userInfoList);
        // 组装权限校验参数
        OperationAuthParam authParam = forwardModel.buildAuthParam();
        OperationAuthModel operationAuthModel = resourceOperationAuthService
                .getOperationAuthValueList(BizRequestUtil.copyRequest(request, e -> authParam)).getData();
        if (!operationAuthModel.checkAuth(scheduleId, ActivityResourceOperationTypeEnum.FORWARD)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, TRANSFER_FAILED_NO_AUTH);
        }

        // 转发Icenter
        forwardMessageComponent.forwardMessageToICenter(request.getParam());
        // 授日程查看权限
        resourceOperationAuthService.saveOperationAuthValue(BizRequestUtil.copyRequest(request, param -> authGrantModel));
        // 记录日志
        operationLogService.saveActivityResourceOperationLog(forwardModel.buildOperationLog(userInfoList));
        // 发送邮件
        return forwardMessageComponent.forwardMessageToZMail(MsaRpcRequestUtil.createWithBizReq(request, param -> zmailBodyParam));
    }

    @Override
    public int syncTalkAbstractByAI(BizRequest<String> request){
    	String attachmentRowId = request.getParam();
    	//通过id获取三个值
    	ActivityRelationAttachmentDO param = new ActivityRelationAttachmentDO();
    	param.setRowId(attachmentRowId);
    	ActivityRelationAttachmentDO queryByCondition = attachmentRepository.queryByCondition(param);
    	if(null ==queryByCondition){
    		logger.warn("===> syncTalkAbstractByAI 未查询到附件attachmentRowId={}",attachmentRowId);
    		return 0;
    	}
    	
    	//调用下面接口
    	String talkFileKey = queryByCondition.getFileToken();
    	String talkFileName = queryByCondition.getFileName();
    	BizRequest<TalkAbstractGenerationParam> talkAbstractGenerationRequest = BizRequestUtil.copyRequest(request, req -> {
    		TalkAbstractGenerationParam talkAbstractGenerationParam = new TalkAbstractGenerationParam();
    		talkAbstractGenerationParam.setTalkFileName(talkFileName);
    		talkAbstractGenerationParam.setTalkFileKey(talkFileKey);
            return talkAbstractGenerationParam;
        });
    	TalkAbstractResponseVO talkAbstractByAI = this.getTalkAbstractByAI(talkAbstractGenerationRequest);
    	
    	//更新到附件表
    	ActivityRelationAttachmentDO record = new ActivityRelationAttachmentDO();
    	record.setRowId(attachmentRowId);
        record.setAttachmentSceneType(AttachmentSceneTypeEnum.SCHEDULE_TALK.getCode());
    	record.setAbstractZh(StringUtils.substring(talkAbstractByAI.getAbstractZh(),NumberConstant.ZERO, NumberConstant.TWO_THOUSANDS));
    	record.setAbstractEn(StringUtils.substring(talkAbstractByAI.getAbstractEn(),NumberConstant.ZERO, NumberConstant.TWO_THOUSANDS));
    	record.setLastUpdateDate(new Date());
    	return attachmentRepository.updateByPrimaryKeySelectiveAndType(record);
    	
    }
    
    @Override
    public TalkAbstractResponseVO getTalkAbstractByAI(BizRequest<TalkAbstractGenerationParam> request){
        
    	String talkFileKey = request.getParam().getTalkFileKey();
    	String talkFileName = request.getParam().getTalkFileName();
    	String empNo = request.getEmpNo();

    	TalkAbstractResponseVO res = new TalkAbstractResponseVO();
    	
    	AiGenerationTalkAbstractParam param = new AiGenerationTalkAbstractParam();
        String downloadUrl = cloudDiskDownloadService.getDownloadTokenUrl(talkFileKey, talkFileName, StringUtils.defaultIfBlank(empNo, CharacterConstant.SYSTEM));
        param.setDocUrl(downloadUrl);
        String fileType = StringUtils.upperCase(StringUtils.substringAfterLast(talkFileName,CharacterConstant.DOT));
        param.setFileType(fileType);
        param.setLanguages(Lists.newArrayList("zh","en"));
        String token = request.getToken();

        AiGenerationTalkAbstractResponseVO aiTalkAbstact = aiGenerationActivityTalkService.getTalkAbstactByAiAssistant(param,empNo,token);
        
        AiGenerationTalkSummary talkSummary = Optional.ofNullable(aiTalkAbstact.getSummary()).orElse(aiTalkAbstact.new AiGenerationTalkSummary());
        res.setAbstractZh(talkSummary.getZh());
        res.setAbstractEn(talkSummary.getEn());
        
        return res;
    }

    /**
     * 根据日程id获取日程谈参摘要
     *
     * @param request
     * @return {@link TalkAbstractResponseVO}
     * <AUTHOR>
     * @date 2025/3/2 上午11:29
     */
    @Override
    public BizResult<TalkAbstractResponseVO> getScheduleTalkAbstract(BizRequest<String> request) {
        String scheduleId = request.getParam();
        ActivityRelationAttachmentDO attachmentDO
                = attachmentRepository.queryAttachmentBySceneOriginRowIdAndType(AttachmentSceneTypeEnum.SCHEDULE_TALK.getCode(), scheduleId);
        if (Objects.isNull(attachmentDO)) {
            return BizResult.buildSuccessRes(new TalkAbstractResponseVO());
        }
        TalkAbstractResponseVO abstractResponseVO = new TalkAbstractResponseVO();
        abstractResponseVO.setAbstractZh(attachmentDO.getAbstractZh());
        abstractResponseVO.setAbstractEn(attachmentDO.getAbstractEn());
        return BizResult.buildSuccessRes(abstractResponseVO);
    }

    /**
     * 生成日程谈参摘要
     *
     * @param request
     * @return {@link TalkAbstractResponseVO}
     * <AUTHOR>
     * @date 2025/3/2 上午11:29
     */
    @Override
    public BizResult<Void> generateScheduleTalkAbstract(BizRequest<String> request) {
        String attachmentId = request.getParam();
        try {
            syncTalkAbstractByAI(BizRequestUtil.copyRequest(request, p -> attachmentId));
        } catch (Exception e) {
            logger.error("生成日程谈参摘要失败,日程谈参附件id={}", attachmentId, e);
            // 生成失败发送邮件提醒
            sendGenerateFailedMail(attachmentId);
        }
        return BizResult.buildSuccessRes(null);
    }

    /**
     * 发送日程谈参摘要生成失败邮件
     *
     * @param attachmentId
     * <AUTHOR>
     * @date 2025/3/2 下午3:25
     */
    private void sendGenerateFailedMail(String attachmentId) {
        String serialNumber = ScheduleOrchestrationMailParam.MAIL_GENERATE_TALK_ABSTRACT_FAILED;
        String mailTo = mailConfig.getTalkAbstractFailedMailTo();
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("attachmentId", attachmentId);
        custExpansionMoaService.sendSysEmailBySerialNumbers(serialNumber, mailTo, paramMap);
    }
}
