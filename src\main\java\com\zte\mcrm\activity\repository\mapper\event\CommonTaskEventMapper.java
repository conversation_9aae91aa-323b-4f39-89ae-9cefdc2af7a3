package com.zte.mcrm.activity.repository.mapper.event;

import com.zte.mcrm.activity.repository.model.event.CommonTaskEventDO;

public interface CommonTaskEventMapper {
    /**
     * all field insert
     */
    int insert(CommonTaskEventDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(CommonTaskEventDO record);

    /**
     * query by primary key
     */
    CommonTaskEventDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(CommonTaskEventDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(CommonTaskEventDO record);
}