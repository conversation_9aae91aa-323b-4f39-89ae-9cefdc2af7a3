package com.zte.mcrm.activity.common.enums;

import lombok.Getter;

/**
 * 大会类型
 */
@Getter
public enum ConferenceTypeEnum {

    /**
     * 峰会
     */
    SUMMIT("summit", "峰会"),

    /**
     * workshop
     */
    WORKSHOP("workshop", "workshop"),

    /**
     * 发布会
     */
    PRESS_CONFERENCE("pressConference", "发布会"),

    /**
     * 自办活动
     */
    SELF_ORGANIZE_ACTIVITY("selfOrganizeActivity", "自办活动"),

    /**
     * 巡展
     */
    TOUR_EXHIBITION("tourExhibition", "巡展"),

    /**
     * 线上活动
     */
    ONLINE_ACTIVITY("onlineActivity", "线上活动"),
    ;

    /**
     * 要求的资源类型
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    /**
     * @param code
     * @param desc 描述
     */
    ConferenceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 是否当前枚举
     *
     * @param code
     * @return true-是当前枚举类型，false-不是
     */
    public boolean isMe(String code) {
        return this.code.equalsIgnoreCase(code);
    }

    /**
     * 获取对应枚举
     *
     * @param
     * @return 对应枚举
     */
    public static ConferenceTypeEnum getEnumByCode(String code) {
        for (ConferenceTypeEnum e : ConferenceTypeEnum.values()) {
            if (e.isMe(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 当前code是否在enums
     *
     * @param code
     * @param enums
     * @return
     */
    public static boolean in(String code, ConferenceTypeEnum... enums) {
        for (ConferenceTypeEnum e : enums) {
            if (e.isMe(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取对应枚举类
     *
     * @param typeCode
     * @return
     */
    public static String getDescByType(String typeCode) {
        ConferenceTypeEnum typeEnum = getEnumByCode(typeCode);
        return typeEnum == null ? null : typeEnum.getDesc();
    }
}
