package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;

import java.util.List;

public interface ActivitySummaryApMapper {
    /**
     * all field insert
     */
    int insert(ActivitySummaryApDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivitySummaryApDO record);

    /**
     * query by primary key
     */
    ActivitySummaryApDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivitySummaryApDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivitySummaryApDO record);


    /**
     * query by primary key
     */
    List<ActivitySummaryApDO> selectForEmptySummary(int limit);
}