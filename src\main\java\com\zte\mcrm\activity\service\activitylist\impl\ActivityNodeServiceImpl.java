package com.zte.mcrm.activity.service.activitylist.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.*;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtServiceImpl;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.repository.rep.activity.*;
import com.zte.mcrm.activity.repository.rep.evaluation.ActivityEvaluationInfoRepository;
import com.zte.mcrm.activity.repository.rep.notice.ActivityPendingNoticeRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationZtePeopleRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activitylist.ActivityNodeService;
import com.zte.mcrm.activity.service.activitylist.convert.ActivityFlowConvert;
import com.zte.mcrm.activity.service.activitylist.flow.ActivityFlowInfoDataSource;
import com.zte.mcrm.activity.service.activitylist.flow.node.ApprovalActivityFlowInfoServiceImpl;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;
import com.zte.mcrm.activity.service.approval.ActivityApprovalInfoService;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalInfoQueryVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessNodeQueryVO;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessQueryVO;
import com.zte.mcrm.activity.service.reservation.ActivityResourceReservationService;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationVo;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.common.util.ObjectUtils;
import com.zte.mcrm.common.util.StringExtUtils;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.constant.I18Constants.*;
import static com.zte.mcrm.activity.common.constant.NumberConstant.NEGATIVE_ONE;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.PARTICIPANTS;
import static com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum.*;

/**
 * <AUTHOR> 10333830
 * @date 2023-09-01 16:48
 **/
@Service
public class ActivityNodeServiceImpl implements ActivityNodeService {


    private static final Logger logger = LoggerFactory.getLogger(ActivityNodeServiceImpl.class);

    @Autowired
    private ActivityRelationCustPeopleRepository activityRelationCustPeopleRepository;

    @Autowired
    private ActivityApprovalInfoService activityApprovalInfoService;

    @Autowired
    private ActivityRelationZtePeopleRepository activityRelationZtePeopleRepository;

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private LocaleMessageSourceBean lmsb;

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private ActivityStatusLifecycleRepository statusLifecycleRepository;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    @Autowired
    private ActivityApprovalProcessRepository activityApprovalProcessRepository;
    @Autowired
    private ActivityApprovalProcessNodeRepository activityApprovalProcessNodeRepository;
    @Autowired
    private ActivityApprovalInfoRepository activityApprovalInfoRepository;

    @Autowired
    private ActivityResourceReservationService activityResourceReservationService;

    @Autowired
    private ActivityCommunicationDirectionRepository activityCommunicationDirectionRepository;

    @Autowired
    private ActivityPendingNoticeRepository activityPendingNoticeRepository;

    /**
     * 如果后续重构了这里的代码，可以将各个节点实现这里的接口，然后统一打包
     */
    @Autowired
    private ApprovalActivityFlowInfoServiceImpl approvalActivityFlowInfoServiceImpl;
    @Autowired
    private LookUpExtServiceImpl lookUpExtService;
    @Autowired
    private ActivityEvaluationInfoRepository evaluationInfoRepository;

    /**
     * 获取启动节点
     * @param statusToMap   状态Map
     * @param activityInfoDO    活动信息
     * @param peopleMap 人员信息
     * @return com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO
     * <AUTHOR>
     * date: 2023/9/1 10:14
     */
    @Override
    public ActivityFlowInfoVO getLaunchNode(Map<String, List<ActivityStatusLifecycleDO>> statusToMap,
                                            ActivityInfoDO activityInfoDO,
                                            Map<String, String> peopleMap) {
        String createdBy = activityInfoDO.getCreatedBy();
        String createdName = getTrigger(peopleMap, createdBy);
        //启动状态
        ActivityFlowInfoVO launchFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.LAUNCH, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
        if (ActivityStatusEnum.DRAFT.isMe(activityInfoDO.getActivityStatus())) {
            return launchFlowInfoVO;
        }
        //点亮启动状态节点
        ActivityFlowConvert.fillLightTriggerInfo(launchFlowInfoVO, createdName,
                activityInfoDO.getCreationDate(), ActivityFlowNodeEnum.FLOW_DONE, lmsb.getMessage(SUBMIT_ACTIVITY_LOG));
        //如果活动有过变更则添加变更记录
        List<ActivityStatusLifecycleDO> changeLogList = Optional.ofNullable(statusToMap.get(ActivityStatusEnum.CHANGE.getCode()))
                .orElse(new ArrayList<>()).stream().sorted(Comparator.comparing(ActivityStatusLifecycleDO::getCreationDate)).collect(Collectors.toList());

        for (ActivityStatusLifecycleDO changeLog : changeLogList) {
            String changeBy = getTrigger(peopleMap, changeLog.getCreatedBy());
            ActivityFlowInfoVO changeNode = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.LAUNCH, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
            //点亮启动状态变更子节点
            ActivityFlowConvert.fillLightTriggerInfo(changeNode, changeBy,
                    changeLog.getCreationDate(), ActivityFlowNodeEnum.FLOW_DONE, lmsb.getMessage(CHANGE_ACTIVITY_LOG));
            launchFlowInfoVO.getChild().add(changeNode);
        }
        return launchFlowInfoVO;
    }

    /**
     * 获取资源申请节点
     *
     * @param flowInfoVOS 节点列表
     * @param statusToMap 生命周期Map
     * @param peopleMap   人员信息
     * @param infoDO      活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 9:59
     */
    @Override
    public void getRequestNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap,
                               Map<String, String> peopleMap, ActivityInfoDO infoDO) {
        // 日常拜访去掉资源申请、草稿、当活动无资源预约时，全景图节点不显示资源预约
        ActivityResourceReservationVo reservationVo = activityResourceReservationService.queryReservationDetail(infoDO.getRowId());
        if(ActivityTypeEnum.DAILY_VISIT_ACTIVITY.isMe(infoDO.getActivityType())
                || ActivityStatusEnum.DRAFT.isMe(infoDO.getActivityStatus())
                || CollectionUtils.isEmpty(reservationVo.getReservationDetails())) {
            return;
        }
        //资源申请
        ActivityFlowInfoVO requestFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.REQUEST, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
        Date requestDate = infoDO.getCreationDate();
        requestDate = getRequestDate(statusToMap, requestDate);
        boolean reqDoing = reservationVo.getReservationDetails().stream().anyMatch(e-> ReserveStatusEnum.in(e.getReserveStatus(), ReserveStatusEnum.WAIT_DEAL));
        String createdName = getTrigger(peopleMap, infoDO.getCreatedBy());
        //点亮资源申请节点
        ActivityFlowConvert.fillLightTriggerInfo(requestFlowInfoVO, createdName, requestDate,
                reqDoing ? ActivityFlowNodeEnum.FLOW_DOING : ActivityFlowNodeEnum.FLOW_DONE, "");
        //过滤掉，无预约信息则不显示该节点
        if (CollectionUtils.isEmpty(reservationVo.getReservationDetails())) {
            return;
        }
        // 不展示预约信息
        if (getLatest(statusToMap, ActivityStatusEnum.REVOKED) != null) {
            reservationVo.setReservationDetails(Lists.newArrayList());
            reservationVo.setAcceptNum(ZERO);
            reservationVo.setRefuseNum(ZERO);
            reservationVo.setPendingResponseNum(ZERO);
        }
        requestFlowInfoVO.setReservationVo(reservationVo);
        flowInfoVOS.add(requestFlowInfoVO);
    }

    /**
     * 获取申请日期
     * @param statusToMap   状态Map
     * @param requestDate   资源申请日期
     * @return java.util.Date
     * <AUTHOR>
     * date: 2023/9/2 10:24
     */
    private static Date getRequestDate(Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Date requestDate) {
        //活动周期到达合规审批中
        ActivityStatusLifecycleDO latestApproval = getLatest(statusToMap, ActivityStatusEnum.COMPLIANCE_APPROVAL);
        //活动周期到达进行中
        ActivityStatusLifecycleDO latestProgress = getLatest(statusToMap, ActivityStatusEnum.PROGRESS);
        // 判断活动是否变更过
        boolean isChange2Approval = latestApproval != null && ActivityStatusEnum.CHANGE.isMe(latestApproval.getStatusFrom());
        boolean isChange2Progress = latestProgress != null && ActivityStatusEnum.CHANGE.isMe(latestProgress.getStatusFrom());
        if (isChange2Approval) {
            Date change2ApprovalTime = latestApproval.getCreationDate();
            requestDate = change2ApprovalTime;
            if (isChange2Progress) {
                Date change2ProgressTime = latestProgress.getCreationDate();
                requestDate = change2ApprovalTime.after(change2ProgressTime) ? change2ApprovalTime : change2ProgressTime;
            }
        } else if (isChange2Progress) {
            requestDate = latestProgress.getCreationDate();
        }
        return requestDate;
    }

    /**
     * 获取最新的生命周期记录
     * @param statusToMap   活动状态信息
     * @param statusEnum    活动状态枚举
     * @return com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO
     * <AUTHOR>
     * date: 2023/9/5 15:12
     */
    private static ActivityStatusLifecycleDO getLatest(Map<String, List<ActivityStatusLifecycleDO>> statusToMap,
                                                       ActivityStatusEnum statusEnum) {
        List<ActivityStatusLifecycleDO> list = statusToMap.get(statusEnum.getCode());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(ZERO);
    }

    /**
     * 获取审批节点
     *
     * @param flowInfoVOS 节点列表
     * @param statusToMap 生命周期Map
     * @param infoDO      活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 9:59
     */
    @Override
    public void getApprovalNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, ActivityInfoDO infoDO) {
        // 展会mvp版本上线后，数据结构就统一了，到时走统一打包方法（线上数据审批信息做下初始化）
        if (ActivityTypeEnum.in(infoDO.getActivityType(), JOIN_EXHIBITION, JOIN_CONFERENCE, VISITING_SAMPLE)) {
            getApprovalNodeForExhibition(flowInfoVOS, statusToMap, infoDO);
            return;
        }
        List<ActivityStatusLifecycleDO> approvalLifecycleList = this.buildApprovalList(statusToMap,Arrays.asList(ActivityStatusEnum.COMPLIANCE_APPROVAL.getCode(),ActivityStatusEnum.BUSINESS_APPROVAL.getCode()));
        //活动周期没到达过合规审批中, 不生成审批节点
        if (CollectionUtils.isEmpty(approvalLifecycleList) || ActivityStatusEnum.DRAFT.isMe(infoDO.getActivityStatus())) {
            return;
        }
        // 更新时间降序
        ActivityStatusLifecycleDO latestApproval = approvalLifecycleList.get(ZERO);
        ActivityStatusLifecycleDO changeLog = getLatest(statusToMap, ActivityStatusEnum.CHANGE);
        // 如果存在活动变更，且最新的合规审批节点不在变更后生成，则表明变更后不存在审批节点 --高层拜访可反复审批 走展会逻辑存在显示合规节点历史数据
        if (changeLog != null && latestApproval.getLastUpdateDate().before(changeLog.getLastUpdateDate())&& !ActivityTypeEnum.in(infoDO.getActivityType(),SENIOR_VISIT_EXPANSION)) {
            return;
        }
        String acRowId = infoDO.getRowId();
        //活动审批
        ActivityFlowInfoVO approvalFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.APPROVAL, BooleanEnum.Y, ActivityFlowNodeEnum.FLOW_DOING);
        // 通过活动状态判断该节点的完成状态
        String stageStatus = ActivityStatusEnum.in(infoDO.getActivityStatus(), ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.COMPLIANCE_APPROVAL)
                ? ActivityFlowNodeEnum.FLOW_DOING : ActivityFlowNodeEnum.FLOW_DONE;
        ActivityFlowConvert.fillLightTriggerInfo(approvalFlowInfoVO, null, latestApproval.getLastUpdateDate(), stageStatus, null);
        //查询审批流程信息
        fillApprovalInfo(acRowId, approvalFlowInfoVO, changeLog, statusToMap);
        //添加活动审批节点
        flowInfoVOS.add(approvalFlowInfoVO);
    }
    /* Started by AICoder, pid:lfae4z43d7q19f514b03099f30fc592021340703 */
    /**
     * 获取审批中的状态数据。
     *
     * 该方法从一个映射中获取与给定状态列表匹配的所有状态数据，并返回这些数据的列表。
     *
     * @param statusToMap 状态到状态生命周期数据的映射
     * @param statusList 需要查询的状态列表
     * @return 包含所有匹配状态的生命周期数据列表
     */
    private List<ActivityStatusLifecycleDO> buildApprovalList(
            Map<String, List<ActivityStatusLifecycleDO>> statusToMap,List<String> statusList) {
        // 使用流操作来减少代码复杂度和提高可读性
        return statusList.stream()
                .map(status -> statusToMap.getOrDefault(status, Collections.emptyList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:lfae4z43d7q19f514b03099f30fc592021340703 */
    /**
     * 展会活动审批节点
     * @param flowInfoVOS
     * @param statusToMap
     * @param infoDO
     */
    private void getApprovalNodeForExhibition(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, ActivityInfoDO infoDO) {

        ActivityFlowInfoDataSource data = new ActivityFlowInfoDataSource();
        data.setActivityInfo(infoDO);
        data.setActivityLifecycleList(statusToMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        List<ActivityApprovalInfoDO> approvalList = activityApprovalInfoRepository.queryAllByActivityRowId(infoDO.getRowId());
        if (CollectionUtils.isEmpty(approvalList)) {
            return;
        }

        data.setActivityApprovalList(approvalList);
        data.setActivityProcessList(activityApprovalProcessRepository.queryByActivityRowId(infoDO.getRowId()));
        data.setActivityProcessNodeList(activityApprovalProcessNodeRepository.queryAllByActivityRowId(infoDO.getRowId()));

        ActivityFlowInfoVO vo = approvalActivityFlowInfoServiceImpl.createActivityFlowInfo(data);
        if (vo != null) {
            flowInfoVOS.add(vo);
        }
    }

    /**
     * 补充审批信息
     *
     * @param acRowId            活动Id
     * @param approvalFlowInfoVO 审批节点
     * @param changeLog          变更记录
     * @param statusToMap        生命周期
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:25
     */
    private void fillApprovalInfo(String acRowId, ActivityFlowInfoVO approvalFlowInfoVO,
                                  ActivityStatusLifecycleDO changeLog, Map<String, List<ActivityStatusLifecycleDO>> statusToMap) {
        List<ActivityApprovalProcessNodeQueryVO> nodeList = Lists.newArrayList();
        Map<String, List<ActivityApprovalProcessNodeQueryVO>> processMap = Maps.newHashMap();
        ActivityApprovalInfoQueryVO approvalInfoQueryVO = activityApprovalInfoService.getApprovalProcessByActivityRowId(acRowId);
        ActivityStatusLifecycleDO cancelLog = getLatest(statusToMap, ActivityStatusEnum.REVOKED);
        if (ObjectUtils.isNotEmpty(approvalInfoQueryVO)) {
            List<ActivityApprovalProcessQueryVO> processQueryVOList = approvalInfoQueryVO.getProcessQueryVOList();
            nodeList.addAll(fillNodeList(processQueryVOList, changeLog, processMap, ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE));
            nodeList.addAll(fillNodeList(processQueryVOList, changeLog, processMap, ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE));
        }
        // 装填数据
        approvalFlowInfoVO.setApprovedNum(getApproveResultNum(nodeList, ApproveResultEnum.AGREE));
        approvalFlowInfoVO.setUnApprovedNum(getApproveResultNum(nodeList, ApproveResultEnum.PENDING_APPROVAL));
        approvalFlowInfoVO.setDisAgree(getApproveResultNum(nodeList, ApproveResultEnum.DISAGREE));
        approvalFlowInfoVO.setCanceledNum(getApproveResultNum(nodeList, ApproveResultEnum.CANCELED));
        approvalFlowInfoVO.setProcessMap(processMap);
        fillApproveTriggerTime(approvalFlowInfoVO, nodeList, cancelLog);
    }

    /**
     * 填充触发时间
     * @param approvalFlowInfoVO    审批节点
     * @param nodeList  审批信息列表
     * @param cancelLog 作废记录
     * @return void
     * <AUTHOR>
     * date: 2023/9/6 14:34
     */
    private void fillApproveTriggerTime(ActivityFlowInfoVO approvalFlowInfoVO,
                                   List<ActivityApprovalProcessNodeQueryVO> nodeList,
                                   ActivityStatusLifecycleDO cancelLog) {
        if (cancelLog != null) {
            approvalFlowInfoVO.setTriggerTime(cancelLog.getCreationDate());
            return;
        }
        if (CollectionUtils.isNotEmpty(nodeList)) {
            approvalFlowInfoVO.setTriggerTime(nodeList.get(ZERO).getCreationDate());
        }
    }

    /**
     * 获取对应审批结果数量
     * @param nodeList 审批节点列表
     * @param resultEnum    审批结果枚举
     * @return int
     * <AUTHOR>
     * date: 2023/9/5 12:48
     */
    private static int getApproveResultNum(List<ActivityApprovalProcessNodeQueryVO> nodeList, ApproveResultEnum resultEnum) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return ZERO;
        }
        return  (int) nodeList.stream().filter(e -> resultEnum.isMe(e.getApproveResult())).count();
    }

    /**
     * 填充节点列表
     * @param processQueryVOList    审批过程信息
     * @param changeLog 变更记录
     * @param processMap    审批信息Map
     * @param processTypeEnum   审批类型
     * @return void
     * <AUTHOR>
     * date: 2023/9/5 12:43
     */
    private List<ActivityApprovalProcessNodeQueryVO> fillNodeList(List<ActivityApprovalProcessQueryVO> processQueryVOList,
                                                                  ActivityStatusLifecycleDO changeLog,
                                                                  Map<String, List<ActivityApprovalProcessNodeQueryVO>> processMap,
                                                                  ProcessTypeEnum processTypeEnum) {
        List<ActivityApprovalProcessNodeQueryVO> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(processQueryVOList)) {
            return list;
        }
        // 只收集相关类型的审批信息
        for (ActivityApprovalProcessQueryVO processQueryVO : processQueryVOList) {
            if (processTypeEnum.isMe(processQueryVO.getProcessType())) {
                list.addAll(processQueryVO.getProcessNodeQueryVOList());
            }
        }
        list = changeLog == null ? list : list.stream()
                .filter(e -> changeLog.getCreationDate().before(e.getCreationDate()))
                .collect(Collectors.toList());
        processMap.put(processTypeEnum.getCode(), list);
        return list;
    }

    /**
     * 获取执行节点
     *
     * @param flowInfoVOS    节点列表
     * @param activityStatus 活动状态
     * @param statusToMap    生命周期Map
     * @param peopleMap      人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:02
     */
    @Override
    public void getExecuteNode(List<ActivityFlowInfoVO> flowInfoVOS, String activityStatus, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap) {
        //活动被取消了
        ActivityStatusLifecycleDO cancelLifecycle = getLatest(statusToMap, ActivityStatusEnum.REVOKED);
        //如果活动没有到进行中就被取消了，则不生成活动执行节点
        if (cancelLifecycle !=null && !ActivityStatusEnum.PROGRESS.isMe(cancelLifecycle.getStatusFrom())) {
            return;
        }
        //活动执行
        ActivityFlowInfoVO executeFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.EXECUTE, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
        String commitMeetingFeedBack = BooleanEnum.N.getCode();
        Date triggerTime = null;
        String trigger = null;
        String stageStatus = ActivityFlowNodeEnum.FLOW_UNDO;
        String ligtht = BooleanEnum.N.getCode();
        //活动状态在进行中、完成、已评价、已撤回、已作废
        if (ActivityStatusEnum.in(activityStatus, ActivityStatusEnum.PROGRESS, ActivityStatusEnum.REVOKED)) {
            //活动周期到达进行中
            stageStatus = ActivityFlowNodeEnum.FLOW_DOING;
            ligtht = BooleanEnum.Y.getCode();
            ActivityStatusLifecycleDO latestProgressLog = getLatest(statusToMap, ActivityStatusEnum.PROGRESS);
            if (latestProgressLog == null) {
                return;
            }
            trigger = getTrigger(peopleMap, latestProgressLog.getCreatedBy());
            triggerTime = latestProgressLog.getCreationDate();
        } else if (ActivityStatusEnum.in(activityStatus, ActivityStatusEnum.FINISH,
                ActivityStatusEnum.EVALUATED, ActivityStatusEnum.ABOLISH)) {
            stageStatus = ActivityFlowNodeEnum.FLOW_DONE;
            commitMeetingFeedBack = BooleanEnum.Y.getCode();
            ligtht = BooleanEnum.Y.getCode();
            ActivityStatusLifecycleDO latestfinishLog = getLatest(statusToMap, ActivityStatusEnum.FINISH);
            if (latestfinishLog == null) {
                return;
            }
            trigger = getTrigger(peopleMap, latestfinishLog.getCreatedBy());
            triggerTime = latestfinishLog.getCreationDate();
        }
        ActivityFlowConvert.fillLightTriggerInfo(executeFlowInfoVO, trigger, triggerTime, stageStatus, StringUtils.EMPTY);
        executeFlowInfoVO.setCommitMeetingFeedback(commitMeetingFeedBack);
        executeFlowInfoVO.setHighlight(ligtht);

        //添加活动执行节点
        flowInfoVOS.add(executeFlowInfoVO);
    }

    /**
     * 获取触发人不为NULL
     * @param peopleMap 人员信息Map
     * @param triggerNo 触发工号
     * @return java.lang.String
     * <AUTHOR>
     * date: 2024/1/4 19:31
     */
    private static String getTrigger(Map<String, String> peopleMap, String triggerNo) {
        String triggerName = peopleMap.get(triggerNo);
        return StringExtUtils.getTargetOrEmpty(triggerNo, StringExtUtils.getTargetOrEmpty(triggerName, triggerName+triggerNo));
    }

    /**
     * 获取取消节点
     *
     * @param flowInfoVOS 节点列表
     * @param statusToMap 生命周期Map
     * @param peopleMap   人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:03
     */
    @Override
    public void getCancelNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap) {
        //活动被取消了
        ActivityStatusLifecycleDO cancelLifecycle = getLatest(statusToMap, ActivityStatusEnum.REVOKED);
        if (cancelLifecycle == null) {
            return;
        }
        String cancelBy = getTrigger(peopleMap, cancelLifecycle.getCreatedBy());
        //活动作废
        ActivityFlowInfoVO cancelFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.CANCEL, BooleanEnum.Y,
                ActivityFlowNodeEnum.FLOW_DONE);
        ActivityFlowConvert.fillLightTriggerInfo(cancelFlowInfoVO, cancelBy, cancelLifecycle.getLastUpdateDate(),
                ActivityFlowNodeEnum.FLOW_DONE, CANCEL_ACTIVITY_LOG);
        flowInfoVOS.add(cancelFlowInfoVO);
    }

    /**
     * 获取活动完成节点
     *
     * @param flowInfoVOS 节点列表
     * @param statusToMap 生命周期
     * @param acRowId     活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:04
     */
    @Override
    public void getCompleteNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, String acRowId) {
        // 如果活动被取消了，不生成活动完成节点
        ActivityStatusLifecycleDO cancelLifecycle = getLatest(statusToMap, ActivityStatusEnum.REVOKED);
        if (cancelLifecycle != null) {
            return;
        }
        //活动周期到达已提交会议纪要
        ActivityStatusLifecycleDO finishLifecycle = getLatest(statusToMap, ActivityStatusEnum.FINISH);
        //活动完成节点生成
        ActivityFlowInfoVO completeFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.COMPLETE, BooleanEnum.N, ActivityFlowNodeEnum.FLOW_UNDO);
        if (finishLifecycle != null) {
            // 如未评价，显示会议纪要提交时间
            ActivityFlowConvert.fillLightTriggerInfo(completeFlowInfoVO, StringUtils.EMPTY,
                    finishLifecycle.getEnterTime(), ActivityFlowNodeEnum.FLOW_DOING, StringUtils.EMPTY);
            fillEvaluateInfo(statusToMap, acRowId, completeFlowInfoVO);
        }
        //添加活动完成节点
        flowInfoVOS.add(completeFlowInfoVO);
    }

    /**
     * 填充评价信息
     * @param statusToMap   生命周期
     * @param acRowId       活动Id
     * @param completeFlowInfoVO    活动完成节点
     * @return void
     * <AUTHOR>
     * date: 2023/9/3 10:35
     */
    private void fillEvaluateInfo(Map<String, List<ActivityStatusLifecycleDO>> statusToMap, String acRowId, ActivityFlowInfoVO completeFlowInfoVO) {
        ActivityStatusLifecycleDO evaluatedLifecycle = getLatest(statusToMap, ActivityStatusEnum.EVALUATED);
        //活动状态为已评价 显示最新评价完成时间
        if (evaluatedLifecycle != null) {
            completeFlowInfoVO.setTriggerTime(evaluatedLifecycle.getEnterTime());
            completeFlowInfoVO.setStageStatus(ActivityFlowNodeEnum.FLOW_DONE);
        }
        // 查询这个活动能够评价的人员
        List<ActivityRelationZtePeopleDO> ztePeopleDOList = activityRelationZtePeopleRepository.queryZtePeopleByActivityRowId(acRowId, NEGATIVE_ONE);
        List<String> canEvaluateList = ztePeopleDOList.stream()
                .filter(e -> ActivityPeopleTypeEnum.in(e.getPeopleType(), APPLICANT, LECTURER, ORGANIZER, PARTICIPANTS))
                .map(ActivityRelationZtePeopleDO::getPeopleCode).distinct().collect(Collectors.toList());
        //查询已评价单据
        List<ActivityEvaluationInfoDO> listEvaluation = evaluationInfoRepository.queryAllByActivityRowId(acRowId);
        List<String> evaluatedList = listEvaluation.stream().map(ActivityEvaluationInfoDO::getCreatedBy).distinct().collect(Collectors.toList());
        int evaluatedNum = evaluatedList.size();
        canEvaluateList.removeAll(evaluatedList);
        int unEvaluatedNum = canEvaluateList.size();
        completeFlowInfoVO.setUnEvaluatedNum(unEvaluatedNum);
        completeFlowInfoVO.setEvaluatedNum(evaluatedNum);
    }

    /**
     * 获取作废节点
     *
     * @param flowInfoVOS 节点列表
     * @param statusToMap 生命周期Map
     * @param peopleMap   人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:12
     */
    @Override
    public void getInvalidateNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap) {
        //活动被取消了
        ActivityStatusLifecycleDO invalidateLifecycle = getLatest(statusToMap, ActivityStatusEnum.ABOLISH);
        if (invalidateLifecycle == null) {
            return;
        }
        String cancelBy = getTrigger(peopleMap, invalidateLifecycle.getCreatedBy());
        //活动审批
        ActivityFlowInfoVO cancelFlowInfoVO = ActivityFlowConvert.enumToVo(ActivityFlowNodeEnum.ABOLISH, BooleanEnum.Y,
                ActivityFlowNodeEnum.FLOW_DONE);
        ActivityFlowConvert.fillLightTriggerInfo(cancelFlowInfoVO, cancelBy, invalidateLifecycle.getLastUpdateDate(),
                ActivityFlowNodeEnum.FLOW_DONE, invalidateLifecycle.getRemark());
        flowInfoVOS.add(cancelFlowInfoVO);
    }

}
