package com.zte.mcrm.activity.service.activity.util;

import com.google.common.collect.Sets;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.config.CustomerIntegrationUppConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.activity.ProcessTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.SamplePointApprovalNodeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.model.sample.SamplePointInfoDO;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityApprovalParam;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ActivityBaseInfoVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 活动审批工具辅助类
 *
 * <AUTHOR>
 */
public class ActivityApprovalUtil {

    /**
     * 展会审批级别判断
     *
     * @param exhibitionLevel 展会级别
     * @param approvalList    审批人入参列表
     * @return true-校验通过，false-校验失败
     */
    public static boolean checkExhibitionApproval(int exhibitionLevel, List<ActivityApprovalParam> approvalList) {
        if (CollectionUtils.isEmpty(approvalList)) {
            return false;
        }

        approvalList = approvalList.stream().filter(e -> !StringUtils.isBlank(e.getEmpNo())).collect(Collectors.toList());
        boolean hasLevel2 = approvalList.stream().anyMatch(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getApprovalType()) && e.getLevel() == NumberConstant.TWO);
        boolean hasLevel3 = approvalList.stream().anyMatch(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getApprovalType()) && e.getLevel() == NumberConstant.THREE);
        boolean hasLevel4 = approvalList.stream().anyMatch(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getApprovalType()) && e.getLevel() == NumberConstant.FOUR);

        // 判断展会审批级别，；3级，4、3级领导审批；4级，4级领导审批
        if (exhibitionLevel == NumberConstant.TWO) {
            // 如果是2级，则需要4、3、2级领导审批
            return hasLevel2 && hasLevel3 && hasLevel4;
        } else if (exhibitionLevel == NumberConstant.THREE) {
            // 如果是3级，则需要4、3级领导审批
            return !hasLevel2 && hasLevel3 && hasLevel4;
        } else if (exhibitionLevel == NumberConstant.FOUR) {
            // 如果是4级，则需要4级领导审批
            return !hasLevel2 && !hasLevel3 && hasLevel4;
        }

        return false;
    }

    /**
     * 样板点流程审批校验
     * 跨事业部（活动的业务归属部门即申请方部门、样板点维护的样板点管理部门即接收方部门的二层组织不同即为跨事业部）流程（a到i）
     * 非跨事业部流程（a、c、d、e、f；若申请方、接收方都归属国内营销三营或者都归属国内营销政企流程（a、c、e））；
     * @param baseInfoVO
     * @param samplePointInfo
     * @return
     */
    public static boolean checkSamplePointApproval(ActivityBaseInfoVO baseInfoVO, SamplePointInfoDO samplePointInfo, CustomerIntegrationUppConfig customerIntegrationUppConfig) {
        //样板点申请人部门（申请方部门）
        String applyFullDepartmentNo = baseInfoVO.getApplyFullDepartmentNo();
        //样板点管理员对应部门（接收方部门）
        String adminFullDepartmentNo = samplePointInfo.getAdminFullDepartmentNo();
        //提交申请单审批信息
        List<ActivityApprovalParam> approvalList = baseInfoVO.getApprovalList();
        Set<String> actualApprovalNode = approvalList.stream()
                .filter(e -> !StringUtils.isBlank(e.getEmpNo()))
                .map(ActivityApprovalParam::getApprovalNodeType)
                .collect(Collectors.toSet());
        //非跨事业部流程(二层部门)
        List<SamplePointApprovalNodeEnum> samplePointApprovalNodeList = getSamplePointApprovalNodes(applyFullDepartmentNo, adminFullDepartmentNo,customerIntegrationUppConfig);
        Set<String> expectedApprovalNode = samplePointApprovalNodeList.stream()
                .map(SamplePointApprovalNodeEnum::getCode).collect(Collectors.toSet());
        //判断预期审批节点跟时间实际审批节点是否有差异
        Sets.SetView<String> difference = Sets.difference(expectedApprovalNode, actualApprovalNode);
        if (difference.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 根据申请方部门和接收方部门获取审批角色对象
     * @param applyFullDepartmentNo
     * @param adminFullDepartmentNo
     * @return
     */
    public static List<SamplePointApprovalNodeEnum> getSamplePointApprovalNodes(String applyFullDepartmentNo, String adminFullDepartmentNo, CustomerIntegrationUppConfig config) {
        //申请方事业部编码
        String applyDivisionCode = getSecondLevelDepartmentCode(applyFullDepartmentNo);
        //样板点接收方事业部编码
        String receiverDivisionCode = getSecondLevelDepartmentCode(adminFullDepartmentNo);
        List<SamplePointApprovalNodeEnum> expectedApprovalNode;
        if (applyDivisionCode.equalsIgnoreCase(receiverDivisionCode)) {
            //若申请方、接收方都归属国内营销三营、国内营销政企、国内营销流程
            if (isInDomesticMarketing(applyDivisionCode,config)) {
                expectedApprovalNode = SamplePointApprovalNodeEnum.getMarketApproveRoleCodeList();
            } else {
                expectedApprovalNode = SamplePointApprovalNodeEnum.getApproveRoleCodeList();
            }
        } else {
            //跨事业部 所以节点都要审批
            expectedApprovalNode = Arrays.stream(SamplePointApprovalNodeEnum.values()).collect(Collectors.toList());
        }
        return expectedApprovalNode;
    }


    /**
     * 获取全路径部门对应二级部门编码
     * @param fullDepartmentNo
     * @return
     */
    private static String getSecondLevelDepartmentCode(String fullDepartmentNo) {
        if (StringUtils.isBlank(fullDepartmentNo)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "samplePoint.fullDepartment.can.not.be.null");
        }
        String[] departmentNoArray = fullDepartmentNo.split(CharacterConstant.SHORT_BAR_ZH);
        if (departmentNoArray.length > NumberConstant.ONE) {
            return departmentNoArray[NumberConstant.ONE];
        }
        return fullDepartmentNo;

    }

    /**
     * 判断部门是否是营销三营、国内营销政企、国内营销
     * @param departmentNo
     * @param customerIntegrationUppConfig
     * @return
     */
    private static boolean isInDomesticMarketing(String departmentNo,CustomerIntegrationUppConfig customerIntegrationUppConfig){
        if (null == customerIntegrationUppConfig) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "samplePoint.customerIntegrationUppConfig.not.empty");
        }
       return customerIntegrationUppConfig.getDivision3OrGovDeptCode().contains(departmentNo);
    }

}