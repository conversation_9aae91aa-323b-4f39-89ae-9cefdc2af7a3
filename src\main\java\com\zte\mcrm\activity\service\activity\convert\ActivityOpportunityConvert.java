package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationQueryParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityOpportunityRelationParam;
import com.zte.mcrm.customvisit.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 10307200
 * @since 2024-06-04 下午5:15
 **/
@Component
@Mapper(componentModel = "spring", imports = {DateUtils.class, StringUtils.class})
public interface ActivityOpportunityConvert {

    @Mapping(expression =  "java(StringUtils.isNotEmpty(param.getStartTime()) ? DateUtils.convertStringToDate(param.getStartTime(), DateUtils.YYYY_MM_DD_) : null)" , target = "dateStartTime")
    @Mapping(expression =  "java(StringUtils.isNotEmpty(param.getEndTime()) ? DateUtils.convertStringToDate(param.getEndTime(), DateUtils.YYYY_MM_DD_) : null)" , target = "dateEndTime")
    ActivityOpportunityRelationQueryParam convertToQueryParam(ActivityOpportunityRelationParam param);

}
