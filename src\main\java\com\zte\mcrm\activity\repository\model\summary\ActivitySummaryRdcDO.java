package com.zte.mcrm.activity.repository.model.summary;

import java.util.Date;

/**
 * table:activity_summary_rdc -- 
 */
public class ActivitySummaryRdcDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String activityRowId;

    /** 活动纪要row_id */
    private String activitySummaryRowId;

    /** 和rdc进行关联的id */
    private String rdcId;

    /** 和rdc进行关联的类型 */
    private String rdcType;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getActivitySummaryRowId() {
        return activitySummaryRowId;
    }

    public void setActivitySummaryRowId(String activitySummaryRowId) {
        this.activitySummaryRowId = activitySummaryRowId == null ? null : activitySummaryRowId.trim();
    }

    public String getRdcId() {
        return rdcId;
    }

    public void setRdcId(String rdcId) {
        this.rdcId = rdcId == null ? null : rdcId.trim();
    }

    public String getRdcType() {
        return rdcType;
    }

    public void setRdcType(String rdcType) {
        this.rdcType = rdcType == null ? null : rdcType.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}