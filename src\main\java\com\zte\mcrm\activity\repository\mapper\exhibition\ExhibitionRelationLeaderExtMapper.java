package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationLeaderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExhibitionRelationLeaderExtMapper extends ExhibitionRelationLeaderMapper {
    /**
     * 查询展会对应的领导信息
     *
     * @param exhibitionRowIds 展会RowId列表
     * @return
     */
    List<ExhibitionRelationLeaderDO> queryLeadersWithExhibitionRowId(@Param("exhibitionRowIds") List<String> exhibitionRowIds);
}

