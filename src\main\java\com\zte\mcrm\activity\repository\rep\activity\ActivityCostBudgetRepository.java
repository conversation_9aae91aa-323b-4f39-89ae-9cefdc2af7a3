package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityCostBudgetDO;

import java.util.List;
import java.util.Map;

/**
 * 样板点活动费用
 */
public interface ActivityCostBudgetRepository {

    /**
     * 新增样板点活动费用
     * @param activityCostBudgetList
     * @return
     */
    int insertSelective(List<ActivityCostBudgetDO> activityCostBudgetList);

    /**
     * 根据活动id批量查询活动相关样板点活动费用
     * @param activityRowId
     * @return
     */
    List<ActivityCostBudgetDO> queryCostBudgetByActivityRowIds(String activityRowId);

    /**
     * 根据rowId,批量软删除
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(String operator, List<String> rowIds);
    /**
     * 活动ids分组查询活动成本预算
     *
     * @param activityRowIdList
     * @return
     */
    Map<String, List<ActivityCostBudgetDO>> queryActivityCostBudgetByActivityRowIds(List<String> activityRowIdList);
}
