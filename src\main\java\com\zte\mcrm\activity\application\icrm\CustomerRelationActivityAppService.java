package com.zte.mcrm.activity.application.icrm;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.web.controller.icrm.param.CustomerRelationParam;
import com.zte.mcrm.activity.web.controller.icrm.vo.*;

import java.util.concurrent.ExecutionException;

/**
 * 客户关联活动业务数据服务
 *
 * <AUTHOR>
 */
public interface CustomerRelationActivityAppService {

    /**
     * 常用客户
     * @param request
     * @return
     */
    LowCodePageRow<LatestVisitCustomerVO> latestVisitCustomer(BizRequest<CustomerRelationParam> request);

    /**
     * 客户所有业务
     *
     * @return
     */
    LowCodePageRow<CustomerRelationAllVO> allForCustomer(BizRequest<CustomerRelationParam> request);
    /**
     * 客户活动列表
     *
     * @return
     */
    LowCodePageRow<CustomerRelationActivityVO> activityListForCustomer(BizRequest<CustomerRelationParam> request);


    /**
     * 客户活动AP列表
     *
     * @return
     */
    LowCodePageRow<CustomerRelationActivityApVO> apListForCustomer(BizRequest<CustomerRelationParam> request);


    /**
     * 客户活动RDC列表
     *
     * @return
     */
    LowCodePageRow<CustomerRelationActivityRdcVO> rdcListForCustomer(BizRequest<CustomerRelationParam> request);

}
