package com.zte.mcrm.activity.common.export;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.List;

/**
 * Excel导入辅助类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelImportHelper {

    /**
     * Excel文件数据提取（根据easypoi进行第一个sheet的数据提取）
     *
     * @param in
     * @param dataType
     * @param <T>
     * @return null-表示文件解析失败
     */
    public static <T> List<T> parseSimpleData(InputStream in, Class<T> dataType) {
        List<T> list = null;
        try {
            list = ExcelImportUtil.importExcel(in, dataType, new ImportParams());
        } catch (Exception e) {
            log.error("Excel解析失败：", e);
        }
        return list;
    }

}
