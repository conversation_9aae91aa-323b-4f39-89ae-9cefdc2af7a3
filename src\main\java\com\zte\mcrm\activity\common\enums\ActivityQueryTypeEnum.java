package com.zte.mcrm.activity.common.enums;

import lombok.Getter;

/**
 * @author: 汤踊10285568
 * @date: 2023/5/18 16:36
 */
@Getter
public enum ActivityQueryTypeEnum {
    /**
     * 查看活动基础信息
     */
    BASE_INFO("baseInfo", "查看活动基础信息"),
    /**
     * 查询活动反馈信息
     */
    SUMMARY_INFO("summaryInfo", "查询活动反馈信息"),
    /**
     * 查询活动评价信息
     */
    EVALUATION_INFO("evaluationInfo", "查询活动评价信息");

    private String code;
    private String desc;

    ActivityQueryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean isMe(String code) {
        return this.code.equalsIgnoreCase(code);
    }
}
