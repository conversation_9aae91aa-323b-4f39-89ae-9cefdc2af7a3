package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.DateTimePeriod;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.ObjectUtil;
import com.zte.mcrm.activity.common.util.ServiceDataUtils;
import com.zte.mcrm.activity.integration.accountinfo.AccountInfoCaller;
import com.zte.mcrm.activity.integration.accountinfo.CustomerInfoQueryService;
import com.zte.mcrm.activity.integration.accountinfo.dto.AccountInfoDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerBaseInfoDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.ContactDetailParam;
import com.zte.mcrm.activity.integration.accountinfo.vo.CustomerPersonBaseDTO;
import com.zte.mcrm.activity.integration.accountinfo.vo.CustomerPersonDeptDTO;
import com.zte.mcrm.activity.integration.accountinfo.vo.CustomerPersonDetailDTO;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationContactDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityEsSearchRepository;
import com.zte.mcrm.activity.repository.rep.people.ActivityRelationCustPeopleRepository;
import com.zte.mcrm.activity.service.activity.ActivityCountService;
import com.zte.mcrm.activity.service.activity.convert.HomePageQueryResponseConvert;
import com.zte.mcrm.activity.service.activitylist.param.ActivityInfoQuery;
import com.zte.mcrm.activity.service.dict.DictService;
import com.zte.mcrm.activity.web.controller.activity.param.ContactVisitsDetailsParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ContactVisitsDetailsVO;
import com.zte.mcrm.cust.constants.Constants;
import com.zte.mcrm.isearch.enums.QueryDataTypeEnum;
import com.zte.mcrm.isearch.enums.UserPortraitLevelEnum;
import com.zte.mcrm.isearch.model.dto.activity.AppActivityViewNumDTO;
import com.zte.mcrm.isearch.model.dto.portrait.AppUserPortraitRequestDTO;
import com.zte.mcrm.isearch.model.dto.portrait.UserPortraitDetailDTO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryRequestVO;
import com.zte.mcrm.isearch.model.vo.HomePageQueryResponseVO;
import com.zte.mcrm.isearch.service.AppUserPortraitService;
import com.zte.mcrm.isearch.service.impl.CustomerPortraitServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.comparator.BooleanComparator;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.mcrm.activity.common.constant.DictConstant.ISEARCH_FILTER_OLD_ACTIVITY_TYPE;

/**
 * 活动信息统计
 *
 * <AUTHOR>
 * @date 2024/2/2 下午3:03
 */
@Slf4j
@Service
public class ActivityCountServiceImpl implements ActivityCountService {

    @Autowired
    @Lazy
    private AppUserPortraitService appUserPortraitService;

    @Autowired
    private ActivityEsSearchRepository activityEsSearchRepository;

    @Autowired
    private DictService dictService;

    @Autowired
    private AccountInfoCaller accountInfoCaller;

    @Autowired
    private ActivityRelationCustPeopleRepository custPeopleRepository;

    @Autowired
    private CustomerInfoQueryService customerInfoQueryService;

    /**
     * 统计融合活动信息
     * 与 {@link CustomerPortraitServiceImpl#homePageQuery(HomePageQueryRequestVO)} 保持一致
     *
     * @param bizRequest
     * @return {@link HomePageQueryResponseVO}
     * <AUTHOR>
     * @date 2024/2/2 下午3:03
     */
    @Override
    public ServiceData<HomePageQueryResponseVO> queryIntegrationActivityCountInfo(BizRequest<HomePageQueryRequestVO> bizRequest) {
        HomePageQueryRequestVO requestVO = bizRequest.getParam();
        HomePageQueryResponseVO responseVO = new HomePageQueryResponseVO();
        responseVO.setActivityInfo(new AppActivityViewNumDTO());
        responseVO.setAttenderInfo(Lists.newArrayList());
        responseVO.setSchemeInfo(Lists.newArrayList());
        ServiceData<HomePageQueryResponseVO> serviceData = ServiceDataUtils.success(responseVO);

        // 入参处理(转换)
        requestVO.setActivityStatus(com.zte.mcrm.isearch.enums.ActivityStatusEnum.getOldCodeByCode(requestVO.getActivityStatus()));
        // 查询客户编码
        List<String> customerList = this.fetchCustomerCodeList(requestVO);
        if (CollectionUtils.isEmpty(customerList)) {
            return serviceData;
        }
        List<String> excludeDataSourceList = this.fetchExcludeDataSourceList();
        // 查询活动信息
        ActivityInfoQuery esQuery = new ActivityInfoQuery();
        esQuery.setCustomerCodeList(customerList);
        esQuery.setIsAp(requestVO.getApType());
        esQuery.setStartTime(requestVO.getStartTime());
        esQuery.setEndTime(requestVO.getEndTime());
        esQuery.setActivityStatus(ActivityStatusEnum.customerAppCountActivityStatus().stream()
                .map(ActivityStatusEnum::getCode).collect(Collectors.toList()));
        esQuery.setExcludeDataSource(excludeDataSourceList);
        int pageNo = 0;
        final int pageSize = 500;
        // 最多查询50页，防止因参数异常查询全部
        final int maxPageNo = 50;
        while (pageNo < maxPageNo) {
            pageNo++;
            esQuery.setPage(pageNo, pageSize);
            PageRows<ActivityInfoDO> pageRows = activityEsSearchRepository.searchPage(esQuery);
            if (CollectionUtils.isNotEmpty(pageRows.getRows())) {
                HomePageQueryResponseVO partResponse = countActivityInfo(requestVO, pageRows);
                responseVO = HomePageQueryResponseConvert.merge(responseVO, partResponse);
            }
            if (CollectionUtils.isEmpty(pageRows.getRows()) || pageRows.getRows().size() < pageSize) {
                break;
            }
        }
        return ServiceDataUtils.success(responseVO);
    }

    /**
     * 获取需要过滤掉的数据源
     *
     * @return {@link List< String>}
     * <AUTHOR>
     * @date 2024/2/5 上午11:12
     */
    private List<String> fetchExcludeDataSourceList() {
        List<DictLanguageDTO> filterOldActivityConfig = dictService.exactQueryListByType(ISEARCH_FILTER_OLD_ACTIVITY_TYPE);
        return Optional.ofNullable(filterOldActivityConfig).orElse(Collections.emptyList()).stream()
                .map(DictLanguageDTO::getDictValue)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 统计活动信息，暂时只统计活动数量，后续补充参与人统计、交流方向统计
     *
     * @param requestVO
     * @param pageRows
     * @return {@link HomePageQueryResponseVO}
     * <AUTHOR>
     * @date 2024/2/5 上午10:45
     */
    private HomePageQueryResponseVO countActivityInfo(HomePageQueryRequestVO requestVO, PageRows<ActivityInfoDO> pageRows) {
        HomePageQueryResponseVO responseVO = new HomePageQueryResponseVO();
        // 统计活动数据
        if (StringUtils.isBlank(requestVO.getDataType()) || QueryDataTypeEnum.STATUS_NUM.isMe(requestVO.getDataType())) {
            responseVO.setActivityInfo(countActivity(pageRows.getRows()));
        }
        return responseVO;
    }

    /**
     * 统计活动信息
     *
     * @param activityList
     * @return {@link AppActivityViewNumDTO}
     * <AUTHOR>
     * @date 2024/2/4 下午3:49
     */
    private AppActivityViewNumDTO countActivity(List<ActivityInfoDO> activityList) {
        // 已完成
        int endNum = 0;
        // 进行中
        int ingNum = 0;
        // 计划完成
        int planNum = 0;
        Date now = new Date();
        for (ActivityInfoDO activityInfoDO : activityList) {
            if (isCompleted(activityInfoDO)) {
                endNum++;
            } else if (isInProgress(activityInfoDO, now)) {
                ingNum++;
            } else if (isPlanCompletion(activityInfoDO, now)) {
                planNum++;
            }
        }
        AppActivityViewNumDTO activityViewNumDTO = new AppActivityViewNumDTO();
        activityViewNumDTO.setEndNum(endNum);
        activityViewNumDTO.setIngNum(ingNum);
        activityViewNumDTO.setPlanNum(planNum);
        return activityViewNumDTO;
    }

    /**
     * 已完成
     *
     * @param activityInfoDO
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/2/4 下午7:42
     */
    private boolean isCompleted(ActivityInfoDO activityInfoDO) {
        return ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), ActivityStatusEnum.FINISH, ActivityStatusEnum.EVALUATED);
    }

    /**
     * 进行中
     *
     * @param activityInfoDO
     * @param referenceTime
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/2/4 下午7:43
     */
    private boolean isInProgress(ActivityInfoDO activityInfoDO, Date referenceTime) {
        return Objects.nonNull(activityInfoDO.getEndTime())
                && Objects.nonNull(referenceTime)
                && activityInfoDO.getEndTime().compareTo(referenceTime) < 0
                && ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), ActivityStatusEnum.COMPLIANCE_APPROVAL,
                ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.PROGRESS, ActivityStatusEnum.CHANGE);
    }

    /**
     * 计划完成
     *
     * @param activityInfoDO
     * @param referenceTime
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2024/2/4 下午7:43
     */
    private boolean isPlanCompletion(ActivityInfoDO activityInfoDO, Date referenceTime) {
        return Objects.nonNull(activityInfoDO.getEndTime())
                && Objects.nonNull(referenceTime)
                && activityInfoDO.getEndTime().compareTo(referenceTime) >= 0
                && ActivityStatusEnum.in(activityInfoDO.getActivityStatus(), ActivityStatusEnum.COMPLIANCE_APPROVAL,
                ActivityStatusEnum.BUSINESS_APPROVAL, ActivityStatusEnum.PROGRESS, ActivityStatusEnum.CHANGE);
    }

    /**
     * 获取客户编码
     *
     * @param requestVO
     * @return {@link List<String>}
     * <AUTHOR>
     * @date 2024/2/2 下午3:57
     */
    private List<String> fetchCustomerCodeList(HomePageQueryRequestVO requestVO) {
        AppUserPortraitRequestDTO paramDto = new AppUserPortraitRequestDTO();
        paramDto.setCustLevel(requestVO.getCustLevel());
        paramDto.setCustCode(requestVO.getCustCode());
        List<UserPortraitDetailDTO> customerList = appUserPortraitService.getEntityByParams(paramDto, UserPortraitLevelEnum.ENTITY.getValue());
        return Optional.ofNullable(customerList).orElse(Collections.emptyList()).stream()
                .map(UserPortraitDetailDTO::getCustCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<ContactVisitsDetailsVO> recentContactVisitsDetails(ContactVisitsDetailsParam param) {
        DateTimePeriod dateTimePeriod = new DateTimePeriod(param.getStartTime(), param.getEndTime());

        // 1. 查出登录人作为A/B角色关联的客户联系人
        List<AccountInfoDTO> accountInfoDTOList = accountInfoCaller.queryABAccountContacts(param.getManagerType());
        // 根据入参的联系人姓名和联系人编号过滤
        accountInfoDTOList = accountInfoDTOList.stream()
                .filter(dto -> ObjectUtil.filterByProperty(dto, param.getContactPersonName(), AccountInfoDTO::getName))
                .filter(dto -> ObjectUtil.filterByProperty(dto, param.getContactNo(), AccountInfoDTO::getContactNo))
                .filter(dto -> ObjectUtil.filterByProperty(dto, param.getPersonLevel(), AccountInfoDTO::getPersonLevel))
                .collect(Collectors.toList());
        List<String> contactNos = accountInfoDTOList.stream().map(AccountInfoDTO::getContactNo).collect(Collectors.toList());

        // 2. 根据联系人编号，查询全部联系人拜访情况
        List<ActivityRelationContactDO> activityRelationCustPeopleDOList = custPeopleRepository.queryContactVisits(contactNos);
        // 联系人编号-联系人拜访情况Map
        Map<String, List<ActivityRelationContactDO>> contactVisitsMap = activityRelationCustPeopleDOList.stream().collect(Collectors.groupingBy(ActivityRelationContactDO::getContactNo));

        // 3. 查客户基本信息
        List<String> customerCodeList = accountInfoDTOList.stream().map(AccountInfoDTO::getCustomerCode).collect(Collectors.toList());
        // 客户编码-客户基本信息Map
        Map<String, OutCustomerBaseInfoDTO> customerBaseInfoMap = customerInfoQueryService.queryBaseInfoByBatchCode(
                MsaRpcRequestUtil.createWithCurrentUserSecurity(new HashSet<>(customerCodeList))).getBo();

        // 4. 查联系人所在客户部门和岗位信息
        ContactDetailParam contactDetailParam = new ContactDetailParam();
        contactDetailParam.setCustomerCodes(customerCodeList);
        contactDetailParam.setContactDetailTypes(Collections.singletonList(Constants.DEPT));
        List<CustomerPersonDetailDTO> contactDetailVOList = accountInfoCaller.queryContactDetailList(contactDetailParam);
        // 联系人编号-联系人详细信息Map
        Map<String, CustomerPersonDetailDTO> contactDetailsMap = contactDetailVOList.stream().collect(Collectors.toMap(
                CustomerPersonDetailDTO::getContactNo, Function.identity(), (v1, v2) -> v2));


        // 5. 汇总生成最近拜访明细列表
        List<ContactVisitsDetailsVO> contactVisitsDetailsVOList = generateContactVisitsDetailsVOList(accountInfoDTOList,
                customerBaseInfoMap, contactDetailsMap, contactVisitsMap, dateTimePeriod);
        // 过滤是否拜访过
        contactVisitsDetailsVOList = contactVisitsDetailsVOList.stream()
                .filter(vo -> ObjectUtil.filterByProperty(vo, param.getVisited(), ContactVisitsDetailsVO::visited))
                .collect(Collectors.toList());

        // 6. 将最近拜访列表按 在时间区间 和 不在时间区间 分成2个列表，分别排序（按最近拜访日期降序，为null的排后边），整合返回
        // 即按 时间区间内是否有拜访（无在前，有在后）、最近拜访日期（倒序） 两个属性排序
        Comparator<ContactVisitsDetailsVO> inDateComparator = (o1, o2) -> new BooleanComparator(false).compare(o1.visited(), o2.visited());
        return contactVisitsDetailsVOList.stream().sorted(inDateComparator
                .thenComparing(ContactVisitsDetailsVO::getLastVisitData, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    /**
     * 汇总生成最近拜访明细列表
     *
     * @param accountInfoDTOList  登录人作为A/B角色关联的客户联系人信息列表
     * @param customerBaseInfoMap 客户编码-客户基本信息Map
     * @param contactDetailsMap   联系人编号-联系人详细信息Map
     * @param contactVisitsMap    联系人编号-联系人拜访情况Map
     * @param dateTimePeriod      时间区间模型
     * @return contactVisitsDetailsVOList 联系人最近拜访明细列表
     */
    private List<ContactVisitsDetailsVO> generateContactVisitsDetailsVOList(List<AccountInfoDTO> accountInfoDTOList,
                                                                            Map<String, OutCustomerBaseInfoDTO> customerBaseInfoMap,
                                                                            Map<String, CustomerPersonDetailDTO> contactDetailsMap,
                                                                            Map<String, List<ActivityRelationContactDO>> contactVisitsMap,
                                                                            DateTimePeriod dateTimePeriod) {
        List<ContactVisitsDetailsVO> contactVisitsDetailsVOList = new ArrayList<>();
        for (AccountInfoDTO dto : accountInfoDTOList) {
            Date now = new Date();
            ContactVisitsDetailsVO vo = new ContactVisitsDetailsVO();
            vo.setContactPersonName(dto.getName());
            vo.setContactPersonNameEn(dto.getNameEn());
            vo.setContactRowId(dto.getContactRowId());
            vo.setContactNo(dto.getContactNo());
            vo.setCustomerCode(dto.getCustomerCode());
            vo.setPersonLevel(dto.getPersonLevel());
            vo.setManagerType(dto.getManagerType());

            vo.setCustName(customerBaseInfoMap.getOrDefault(dto.getCustomerCode(), new OutCustomerBaseInfoDTO()).getCustomerName());
            vo.setCustNameEn(customerBaseInfoMap.getOrDefault(dto.getCustomerCode(), new OutCustomerBaseInfoDTO()).getCustomerEnglishName());

            CustomerPersonDetailDTO detailDTO = contactDetailsMap.getOrDefault(dto.getContactNo(), new CustomerPersonDetailDTO());
            CustomerPersonDeptDTO deptDTO = Optional.ofNullable(detailDTO.getDeptInfo()).orElse(new CustomerPersonDeptDTO());
            CustomerPersonBaseDTO baseDTO = Optional.ofNullable(detailDTO.getBaseInfo()).orElse(new CustomerPersonBaseDTO());
            vo.setDept(deptDTO.getName());
            vo.setJob(baseDTO.getJob());

            // 计算 上次拜访日期、距上次拜访（天）、拜访次数
            List<ActivityRelationContactDO> activityRelationContactDOList = contactVisitsMap
                    .getOrDefault(dto.getContactNo(), Collections.emptyList());
            // 过滤掉截止时间大于当前时间的拜访活动
            activityRelationContactDOList = activityRelationContactDOList.stream()
                    .filter(activity -> {
                        Date endTime = activity.getEndTime();
                        return endTime == null || endTime.compareTo(now) <= 0;
                    })
                    .collect(Collectors.toList());
            // 修复空指针问题，迁移单据存在无活动时间情况
            ActivityRelationContactDO nearestActivity = activityRelationContactDOList.stream()
                    .filter(item -> Objects.nonNull(item.getEndTime()))
                    .max(Comparator.comparing(ActivityRelationContactDO::getEndTime))
                    .orElse(new ActivityRelationContactDO());

            vo.setLastVisitData(nearestActivity.getEndTime());
            if (nearestActivity.getEndTime() != null) {
                Long daysUntilEnd = ChronoUnit.DAYS.between(
                        nearestActivity.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        LocalDate.now());
                vo.setDaySinceLastVisit(daysUntilEnd);
            }

            Long visitNum = activityRelationContactDOList.stream()
                    .filter(activity -> dateTimePeriod.in(activity.getEndTime()))
                    .count();
            vo.setVisitNum(visitNum);

            contactVisitsDetailsVOList.add(vo);
        }
        return contactVisitsDetailsVOList;
    }

}
