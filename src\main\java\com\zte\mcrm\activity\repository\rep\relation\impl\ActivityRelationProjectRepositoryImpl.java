package com.zte.mcrm.activity.repository.rep.relation.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.relation.ActivityRelationProjectExtMapper;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;
import com.zte.mcrm.activity.repository.rep.relation.ActivityRelationProjectRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动关联的项目信息
 *
 * <AUTHOR>
 */
@Component
public class ActivityRelationProjectRepositoryImpl implements ActivityRelationProjectRepository {
    @Resource
    private ActivityRelationProjectExtMapper activityRelationProjectExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    /**
     * 项目批量插入
     *
     * @param recordList 列表
     * @return 返回结果
     */
    @Override
    public int batchInsert(List<ActivityRelationProjectDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityRelationProjectDO projectDO : recordList) {
            setDefaultValue(projectDO);
        }
        activityRelationProjectExtMapper.batchInsert(recordList);
        return recordList.size();
    }

    /**
     * 插入单条数据
     *
     * @param projectDO
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    @Override
    public int insertSelective(ActivityRelationProjectDO projectDO) {
        if (null == projectDO) {
            return NumberConstant.ZERO;
        }
        setDefaultValue(projectDO);
        return activityRelationProjectExtMapper.insertSelective(projectDO);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityRelationProjectDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return activityRelationProjectExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityRelationProjectDO> queryAllProjectForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityRelationProjectExtMapper.queryAllProjectForActivity(activityRowId);
    }

    @Override
    public Map<String, List<ActivityRelationProjectDO>> queryAllByActivityRowId(List<String> activityRowIds) {
        List<ActivityRelationProjectDO> list = CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyList()
                : activityRelationProjectExtMapper.queryAllByActivityRowId(activityRowIds);

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() : list.stream().collect(Collectors.groupingBy(ActivityRelationProjectDO::getActivityRowId));
    }

    /**
     * 设置默认值
     *
     * @param projectDO
     */
    private void setDefaultValue(ActivityRelationProjectDO projectDO) {
        projectDO.setRowId(Optional.ofNullable(projectDO.getRowId()).orElse(keyIdService.getKeyId()));
        projectDO.setCreatedBy(Optional.ofNullable(projectDO.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
        projectDO.setLastUpdatedBy(Optional.ofNullable(projectDO.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
        projectDO.setCreationDate(new Date());
        projectDO.setLastUpdateDate(new Date());
        projectDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return activityRelationProjectExtMapper.softDeleteByActivityIds(operator, activityIds);
    }
    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }

        return activityRelationProjectExtMapper.deleteByRowIds(operator, rowIds);
    }
}
