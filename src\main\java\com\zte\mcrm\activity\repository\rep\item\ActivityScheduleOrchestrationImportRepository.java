package com.zte.mcrm.activity.repository.rep.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationImportDO;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationVersionDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ActivityScheduleOrchestrationImportRepository {

    /**
     * 动态插入数据
     */
    int insertSelective(ActivityScheduleOrchestrationImportDO record);

    /**
     * 动态更新数据
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationImportDO record);

    /**
     * 根据主键查询信息
     * @param rowId
     * @return
     */
    ActivityScheduleOrchestrationImportDO selectByPrimaryKey(String rowId);
}
