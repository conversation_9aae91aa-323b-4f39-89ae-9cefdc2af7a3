package com.zte.mcrm.activity.application.activity.impl;

import com.zte.mcrm.activity.application.activity.ActivityAnalysisService;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.accountinfo.CustomerInfoQueryService;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerBaseInfoDTO;
import com.zte.mcrm.common.util.DateUtil;
import com.zte.mcrm.dataservice.dto.*;
import com.zte.mcrm.dataservice.enums.ActivityDivisionEnum;
import com.zte.mcrm.dataservice.model.ActivityAnalysisOfficePersonQuery;
import com.zte.mcrm.dataservice.model.ActivityVisitAccountQuery;
import com.zte.mcrm.dataservice.model.ActivityWeeklySummaryQuery;
import com.zte.mcrm.dataservice.service.ActivityDataResultsService;
import com.zte.mcrm.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/11/08 14:18
 */
@Service
public class ActivityAnalysisServiceImpl implements ActivityAnalysisService {
    @Autowired
    private ActivityDataResultsService activityDataResultsService;

    @Autowired
    private CustomerInfoQueryService customerInfoQueryService;

    @Override
    public List<ActivityAnalysisOfficePersonDTO> queryActivityOfficePersonDate(BizRequest<ActivityAnalysisOfficePersonQuery> bizRequest){
        return activityDataResultsService.queryActivityOfficePersonDate(bizRequest);
    }

    @Override
    public List<ActivityVisitAccountDTO> listActivityVisitAccount(BizRequest<ActivityVisitAccountQuery> visitAccountQuery) {
        List<ActivityVisitAccountDTO> visitAccountDTOS = activityDataResultsService.listActivityVisitAccount(visitAccountQuery);
        // 3. 查客户基本信息
        Set<String> customerCodeSet = visitAccountDTOS.stream().map(ActivityVisitAccountDTO::getCustomerCode).collect(Collectors.toSet());
        // 客户编码-客户基本信息Map
        Map<String, OutCustomerBaseInfoDTO> customerBaseInfoMap = customerInfoQueryService.queryBaseInfoByBatchCode(MsaRpcRequestUtil.createWithCurrentUser(customerCodeSet)).getBo();
        visitAccountDTOS.forEach(e -> {
            OutCustomerBaseInfoDTO customerBase = customerBaseInfoMap.getOrDefault(e.getCustomerCode(), new OutCustomerBaseInfoDTO());
            e.setCustomerName(customerBase.getCustomerName());
            e.setEndTime(DateUtil.formatStr(e.getEndTime(), DateUtil.PATTERN_YMD));
        });
        return visitAccountDTOS;
    }

    /**
     * 查询活动周总结数据
     *
     * @param query 查询参数
     * @return 活动周总结
     */
    @Override
    public ActivityWeeklySummaryDTO activityWeeklySummary(BizRequest<ActivityWeeklySummaryQuery> query) {
        ActivityWeeklySummaryDTO activityWeeklySummaryDTO = new ActivityWeeklySummaryDTO();
        List<ManagerSummaryDTO> managerSummaryDTOList = activityDataResultsService.activityWeeklySummary(query);
        if (CollectionUtils.isEmpty(managerSummaryDTOList)) {
            return activityWeeklySummaryDTO;
        }
        List<ManagerSummary> managerSummaryList = managerSummaryDTOList.stream()
                .map(dto -> {
                    ManagerSummary summary = new ManagerSummary();
                    summary.setLeaderEmpNo(dto.getPeopleCode());
                    summary.setLeaderEmpName(dto.getPeopleName());
                    summary.setActivityTotal(dto.getActivityCount());
                    String positionName = Stream.of(
                                    dto.getPeopleL2OrgCnName(),
                                    dto.getPeopleL3OrgCnName(),
                                    dto.getPeopleL4OrgCnName()
                            )
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(CharacterConstant.SLASH));
                    summary.setPositionName(positionName);
                    return summary;
                })
                .collect(Collectors.toList());
        // 汇总活动总数
        int total = managerSummaryList.stream()
                .mapToInt(ManagerSummary::getActivityTotal)
                .sum();
        activityWeeklySummaryDTO.setTotal(total);
        managerSummaryList.sort(Comparator.comparing(ManagerSummary::getActivityTotal).reversed());
        activityWeeklySummaryDTO.setManagerSummary(managerSummaryList);
        return activityWeeklySummaryDTO;
    }

    /**
     * 查询管理干部参与活动情况汇总
     *
     * @param query 查询参数
     * @return 管理干部参与活动情况汇总
     */
    @Override
    public List<ActivityDivisionSummaryDTO> listActivityVisitManager(BizRequest<ActivityWeeklySummaryQuery> query) {
        List<ActivityDivisionSummaryDTO> activityDivisionSummaries = activityDataResultsService.listActivityVisitManager(query);
        Map<String, Integer> divisionOrderMap =
                Stream.of(ActivityDivisionEnum.values())
                        .collect(Collectors.toMap(
                                ActivityDivisionEnum::getCode,
                                ActivityDivisionEnum::getOrder
                        ));
        activityDivisionSummaries.sort(Comparator.comparingInt(
                dto -> divisionOrderMap.getOrDefault(dto.getL2OrgShortName(), Integer.MAX_VALUE)
        ));
        return activityDivisionSummaries;
    }
}
