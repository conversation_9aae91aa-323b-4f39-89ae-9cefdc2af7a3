package com.zte.mcrm.activity.service.activitylist.flow;

import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ApproveNodeStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ProcessTypeEnum;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.dto.PersonInfoDTO;
import com.zte.mcrm.activity.repository.model.activity.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动流程信息数据源 （放开get方法仅仅是对大家用户习惯）
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ActivityFlowInfoDataSource {
    /**
     * 活动信息
     */
    private ActivityInfoDO activityInfo;
    /**
     * 活动状态流程信息
     */
    private List<ActivityStatusLifecycleDO> activityLifecycleList;

    /**
     * 活动审批信息
     */
    private List<ActivityApprovalInfoDO> activityApprovalList;
    /**
     * 活动审批阶段信息
     */
    private List<ActivityApprovalProcessDO> activityProcessList;
    /**
     * 活动审批各阶段审批节点信息
     */
    private List<ActivityApprovalProcessNodeDO> activityProcessNodeList;
    /**
     * 员工信息
     */
    private Map<String, PersonInfoDTO> empMap;

    /**
     * 获取第一次审批
     *
     * @return
     */
    public ActivityApprovalInfoDO fetchFirstApproval() {
        // 目前是通过排序来决定，审批信息差一个业务类型
        return activityApprovalList == null ? null : activityApprovalList.stream().min(Comparator.comparing(ActivityApprovalInfoDO::getCreationDate)).orElse(null);
    }

    /**
     * 获取最近一次变更审批
     *
     * @return
     */
    public ActivityApprovalInfoDO fetchChangeApproval() {
        // 目前是通过排序来决定，审批信息差一个业务类型
        return activityApprovalList == null ? null : activityApprovalList.stream().max(Comparator.comparing(ActivityApprovalInfoDO::getCreationDate)).orElse(null);
    }

    /**
     * 获取第一次审批阶段信息
     *
     * @return
     */
    public List<ActivityApprovalProcessDO> fetchFirstApprovalProcess() {
        ActivityApprovalInfoDO approvalInfo = fetchFirstApproval();
        if (approvalInfo == null) {
            return null;
        }
        List<ActivityApprovalProcessDO> activityProcessTempList = activityProcessList == null ? Collections.emptyList() : activityProcessList;

        List<ActivityApprovalProcessDO> list = new ArrayList<>();
        // 为了兼容老的数据逻辑（老的审批阶段中没有保持对应审批信息ID）
        activityProcessTempList.stream()
                .filter(e -> StringUtils.isBlank(e.getActivityApprovalInfoRowId()) || StringUtils.equals(approvalInfo.getRowId(), e.getActivityApprovalInfoRowId()))
                .filter(e -> ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.isMe(e.getProcessType()))
                .min(Comparator.comparing(ActivityApprovalProcessDO::getCreationDate)).ifPresent(list::add);

        activityProcessTempList.stream()
                .filter(e -> StringUtils.isBlank(e.getActivityApprovalInfoRowId()) || StringUtils.equals(approvalInfo.getRowId(), e.getActivityApprovalInfoRowId()))
                .filter(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getProcessType())).
                min(Comparator.comparing(ActivityApprovalProcessDO::getCreationDate)).ifPresent(list::add);

        return list;
    }

    /**
     * 获取最近一次变更审批阶段信息
     *
     * @return
     */
    public List<ActivityApprovalProcessDO> fetchChangeApprovalProcess() {
        ActivityApprovalInfoDO approvalInfo = fetchChangeApproval();
        if (approvalInfo == null) {
            return null;
        }
        List<ActivityApprovalProcessDO> activityProcessTempList = activityProcessList == null ? Collections.emptyList() : activityProcessList;

        List<ActivityApprovalProcessDO> list = new ArrayList<>();
        // 为了兼容老的数据逻辑（老的审批阶段中没有保持对应审批信息ID）
        activityProcessTempList.stream()
                .filter(e -> StringUtils.isBlank(e.getActivityApprovalInfoRowId()) || StringUtils.equals(approvalInfo.getRowId(), e.getActivityApprovalInfoRowId()))
                .filter(e -> ProcessTypeEnum.COMPLIANCE_AUDITOR_NODE_CODE.isMe(e.getProcessType()))
                .max(Comparator.comparing(ActivityApprovalProcessDO::getCreationDate)).ifPresent(list::add);

        activityProcessTempList.stream()
                .filter(e -> StringUtils.isBlank(e.getActivityApprovalInfoRowId()) || StringUtils.equals(approvalInfo.getRowId(), e.getActivityApprovalInfoRowId()))
                .filter(e -> ProcessTypeEnum.LEADER_AUDITOR_NODE_CODE.isMe(e.getProcessType())).
                max(Comparator.comparing(ActivityApprovalProcessDO::getCreationDate)).ifPresent(list::add);


        return list;
    }

    /**
     * 获取对应审批阶段节点信息
     *
     * @param approvalProcessRowId
     * @return 返回已经被激活过的节点
     */
    public List<ActivityApprovalProcessNodeDO> fetchApprovalProcessNode(String... approvalProcessRowId) {
        return activityProcessNodeList == null ? null :
                activityProcessNodeList.stream().
                        filter(e -> StringUtils.equalsAny(e.getApprovalProcessRowId(), approvalProcessRowId)
                                && !ApproveNodeStatusEnum.in(e.getNodeStatus(), ApproveNodeStatusEnum.DEFAULT))
                        .collect(Collectors.toList());
    }

    /**
     * 获取员工信息
     *
     * @param empNo
     * @return
     */
    public String fetchEmpName(String empNo) {
        PersonInfoDTO person = empMap == null ? null : empMap.get(empNo);

        return person == null ? null : person.getEmpName();
    }

    /**
     * 获取当前数据最后一条数据
     *
     * @param statusEnums 状态限制（statusTo）
     * @return
     */
    public ActivityStatusLifecycleDO fetchLastLifecycle(ActivityStatusEnum... statusEnums) {

        return CollectionUtils.isEmpty(activityLifecycleList) || statusEnums.length == 0 ? null :
                activityLifecycleList.stream()
                        .filter(e -> ActivityStatusEnum.in(e.getStatusTo(), statusEnums))
                        .max(Comparator.comparing(ActivityStatusLifecycleDO::getCreationDate))
                        .orElse(null);
    }
}
