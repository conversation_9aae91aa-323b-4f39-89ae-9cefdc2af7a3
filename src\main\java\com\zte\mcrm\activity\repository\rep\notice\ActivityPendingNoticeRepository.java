package com.zte.mcrm.activity.repository.rep.notice;

import com.zte.mcrm.activity.common.enums.activity.PendingBizTypeEnum;
import com.zte.mcrm.activity.common.enums.activity.PendingNoticeStatusEnum;
import com.zte.mcrm.activity.repository.model.notice.ActivityPendingNoticeDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public interface ActivityPendingNoticeRepository {
    /**
     * dynamic field insert
     */
    int insertSelective(List<ActivityPendingNoticeDO> record);

    /**
     * query by primary key
     */
    ActivityPendingNoticeDO selectByPrimaryKey(String rowId);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityPendingNoticeDO record);

    /**
     * @return 获取已过期待办信息（最多返回500条）
     */
    List<ActivityPendingNoticeDO> fetchExpiredNotice();

    /**
     * 查询活动所有待办信息
     *
     * @param activityRowId 活动rowId
     * @param bizTypeEnum 业务类型，可空
     * @param statusEnum 待办信息状态，可空
     * @return
     */
    List<ActivityPendingNoticeDO> queryAllPending(String activityRowId, PendingBizTypeEnum bizTypeEnum, PendingNoticeStatusEnum statusEnum);

    /**
     * 按照条件列表查询
     *
     * @param record    查询条件
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/6/2 11:08
     */
    List<ActivityPendingNoticeDO> getList(ActivityPendingNoticeDO record);

    /**
     * 删除指定活动下的所有待办
     * @param operator  操作者
     * @param activityRowId 活动Id
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 16:17
     */
    int deleteByActivityId(String operator, String activityRowId);

    /**
     * 根据待办类型更新数据
     *
     * @param activityRowId
     * @param bizType
     */
    void updateByActivityRowIdAndBizType(String activityRowId, String bizType,String status);

    /**
     * 更新指定活动状态
     * @param activityRowId 活动主键
     * @param beforeStatus  旧状态
     * @param afterStatus   新状态
     * @return int
     * <AUTHOR>
     * date: 2023/8/29 19:23
     */
    int updateTargetStatusByActivityRowId(String activityRowId, String beforeStatus, String afterStatus);

    /**
     * 批量添加待办信息（如果没有主键，自动生成）
     *
     * @param list
     */
    int batchInsert(List<ActivityPendingNoticeDO> list);

    /**
     * 查询活动下个人的待办事项
     *
     * @return List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/5/24 14:23
     */
    List<ActivityPendingNoticeDO> getMyNoticeByActivityRowId(String activityRowId);

    /**
     * 根据业务id获取待办事项
     *
     * @param businessId
     * @param status
     * @return
     */
    void updateStatusByBusinessId(String businessId, String status);

    /**
     * 根据业务id和状态获取待办集合
     *
     * @param businessId
     * @param status
     * @return
     */
    List<ActivityPendingNoticeDO> queryByBusinessIdAndStatus(String businessId, String status);

    /**
     * 批量更新
     *
     * @param ids
     * @param update
     * @return {@link int}
     * <AUTHOR>
     * @date 2023/8/30 下午10:11
     */
    int batchUpdate(List<String> ids, ActivityPendingNoticeDO update);

    /**
     * 根据活动Id查询所有待办信息
     * @param activityRowIds    活动Id列表
     * @return java.util.Map<java.lang.String,java.util.List<ActivityPendingNoticeDO>>
     * <AUTHOR>
     * date: 2023/9/4 7:19
     */
    Map<String, List<ActivityPendingNoticeDO>> queryAllPendingByActivityRowId(Set<String> activityRowIds);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityPendingNoticeDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityPendingNoticeDO> queryAllActivityWithNotEnable(String activityRowId);

    /**
     * 根据业务类型和状态批量查询待办，性能不好，慎用
     *
     * @param pendingBizType
     * @param pendingStatus
     * @param size
     * @return {@link List<ActivityPendingNoticeDO>}
     * <AUTHOR>
     * @date 2024/7/27 上午10:32
     */
    List<ActivityPendingNoticeDO> selectListBatch(String pendingBizType, String pendingStatus, int size);
}
