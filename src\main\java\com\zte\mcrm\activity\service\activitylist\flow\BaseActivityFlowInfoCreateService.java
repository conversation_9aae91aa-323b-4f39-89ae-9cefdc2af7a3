package com.zte.mcrm.activity.service.activitylist.flow;

import com.zte.mcrm.activity.common.enums.activity.ActivityFlowNodeEnum;

/**
 * 基础活动流程节点信息服务
 *
 * <AUTHOR>
 */
public abstract class BaseActivityFlowInfoCreateService implements ActivityFlowInfoCreateService {

    private final ActivityFlowNodeEnum flowNode;

    /**
     * @param flowNodeEnum
     */
    public BaseActivityFlowInfoCreateService(ActivityFlowNodeEnum flowNodeEnum) {
        this.flowNode = flowNodeEnum;
    }

    @Override
    public ActivityFlowNodeEnum getFlowNode() {
        return flowNode;
    }


}
