package com.zte.mcrm.activity.repository.model.relation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * table:activity_relation_solution -- 
 */
@Getter
@Setter
@ToString
public class ActivityRelationSolutionDO {
    /** 主键 */
    private String rowId;

    /** 活动row_id */
    private String activityRowId;

    /** 方案提出者-活动关联人员#row_id */
    private String relationPeopleRowId;

    /** 关联方案id。t_cust_activity_comm_plan#id */
    private String planId;

    /** 方案名称-中文 */
    private String solutionNameCn;

    /** 方案名称-英文 */
    private String solutionNameEn;

    /** 方案附加链接 */
    private String solutionUrl;

    /** 方案顺序 */
    private Integer solutionOrder;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 关联的ID */
    private String releatedId;

}