package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemPeopleDO;

public interface ActivityScheduleItemPeopleMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleItemPeopleDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleItemPeopleDO record);

    /**
     * query by primary key
     */
    ActivityScheduleItemPeopleDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleItemPeopleDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleItemPeopleDO record);
}