package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleOrchestrationDetailExtMapper extends ActivityScheduleOrchestrationDetailMapper {
    /**
     * 批量逻辑删除数据
     * @param rowIdList 主键ID
     * @param operEmpNo 操作人
     * @return
     */
    int batchDelete(@Param("rowIdList")List<String> rowIdList, @Param("operEmpNo") String operEmpNo);

    /**
     * 批量插入数据
     * @param list
     * @return
     */
    int batchInsert(@Param("list")List<ActivityScheduleOrchestrationDetailDO> list);

    List<ActivityScheduleOrchestrationDetailDO> queryActivityScheduleOrchestrationDetailsByOrchestrationRowIds(@Param("orchestrationRowIds") List<String> orchestrationRowIds);

    List<ActivityScheduleOrchestrationDetailDO> queryActivityScheduleOrchestrationDetailsByScheduleItemRowIds(@Param("scheduleItemRowIds") List<String> scheduleItemRowIds);

    List<ActivityScheduleOrchestrationDetailDO> queryEmpNoTransList(DataTransParam searchParam);
}