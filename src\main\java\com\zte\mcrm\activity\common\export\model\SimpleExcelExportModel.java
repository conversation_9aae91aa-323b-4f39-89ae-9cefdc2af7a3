package com.zte.mcrm.activity.common.export.model;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 简单excel数据导出模型（使用easyPOI）
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
public class SimpleExcelExportModel {
    /**
     * true-如果对应sheet数据为空，则对应sheet页不会生成；false-即便数据为空也会生成对应sheet页。
     */
    private final boolean ignoreEmptySheet;
    /**
     * sheet页名称
     */
    private final List<String> sheetNameList = new ArrayList<>(4);
    /**
     * 每个sheet页中数据表格title
     */
    private final Map<String, Class<?>> sheetDataTitle = new HashMap<>(8);
    /**
     * sheet数据
     */
    private final Map<String, List<?>> sheetData = new HashMap<>(8);

    /**
     * 导出选择信息
     */
    @Getter
    @Setter
    private List<ExcelExportParamSetModel> exportParamSetModelList;

    @Getter
    @Setter
    private List<String> modifiyLineList;

    public SimpleExcelExportModel() {
        this(false);
    }

    /**
     * @param ignoreEmptySheet true-如果对应sheet数据为空，则对应sheet页不会生成；false-即便数据为空也会生成对应sheet页。
     */
    public SimpleExcelExportModel(boolean ignoreEmptySheet) {
        this.ignoreEmptySheet = ignoreEmptySheet;
    }

    /**
     * 添加数据（如果sheetName是重复的，将会覆盖之前添加的数据）
     *
     * @param sheetName sheet名称
     * @param data      数据
     * @param c         需要提供一个无参构造器，且数据不要有任何初始化，为了处理数据data为空，sheet不会生成
     */
    public <T> void addSheetData(String sheetName, List<? super T> data, Class<T> c) {
        if (StringUtils.isBlank(sheetName)) {
            return;
        }

        sheetName = sheetName.trim();
        if (!sheetNameList.contains(sheetName)) {
            sheetNameList.add(sheetName);
        }

        sheetDataTitle.put(sheetName, c);
        sheetData.put(sheetName, data == null ? Lists.newArrayList() : data.stream().filter(Objects::nonNull).collect(Collectors.toList()));
    }

    /**
     * 转为导出Excel标准参数
     *
     * @return 使用easyPOI导出方法需要的参数
     */
    public List<Map<String, Object>> toExportExcelParam() {
        List<Map<String, Object>> list = new ArrayList<>();

        for (String sheetName : sheetNameList) {
            Class<?> c = sheetDataTitle.get(sheetName);
            List<?> dataList = sheetData.get(sheetName);

            if (ignoreEmptySheet && CollectionUtils.isEmpty(dataList)) {
                continue;
            }

            // 如果数据为空，sheet页则不会生成。为了能生成sheet页，在此加入一个仅仅new的对象。（如果在构造其中有初始化值）
            if (CollectionUtils.isEmpty(dataList)) {
                // 备注：给添加null，对应sheet也不会生成，故只能new一个对象使其能生成
                dataList = Lists.newArrayList(newInstance(c));
            }

            ExportParams p = new ExportParams();
            p.setSheetName(sheetName);

            Map<String, Object> sheetMap = new HashMap<>(4);
            sheetMap.put("title", p);
            sheetMap.put("entity", c);
            sheetMap.put("data", dataList);

            list.add(sheetMap);
        }

        return list;
    }

    /**
     * @param c
     * @return
     */
    private Object newInstance(Class<?> c) {
        // 如果对象本身有初始化的值，这里就暂时不处理了，各对象自己按规范编码即可
        return BeanUtils.instantiateClass(c);
    }
}
