package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitySummaryExtMapper extends ActivitySummaryMapper {

    /**
     * 获取一个活动下的所有会议纪要
     *
     * @param activityRowId 活动Id
     * @return List<ActivitySummaryDO>
     * <AUTHOR>
     * date: 2023/5/30 22:23
     */
    @Deprecated
    List<ActivitySummaryDO> queryAllSummaryForActivity(String activityRowId);

    /**
     * 查询活动会议纪要信息
     *
     * @param activityIds
     * @return
     */
    List<ActivitySummaryDO> queryAllByActivityRowId(@Param("activityIds") List<String> activityIds);

    /**
     * 批量删除
     * @param operator  操作者
     * @param activityIds   活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:52
     */
    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    List<ActivitySummaryDO> queryEmpNoTransList(DataTransParam searchParam);
}