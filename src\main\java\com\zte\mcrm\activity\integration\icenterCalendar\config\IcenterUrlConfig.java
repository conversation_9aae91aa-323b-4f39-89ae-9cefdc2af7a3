package com.zte.mcrm.activity.integration.icenterCalendar.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ICenter日程没有注册msb，所以配置对应的请求地址由这个配置统一管理
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "zte-icenter-calendar-biz")
@Getter
@Setter
@Component
public class IcenterUrlConfig {
    /**
     * 日程占用接口地址 post
     */
    private String takeUpUrl;
    /**
     * 日程创建的接口地址 post
     */
    private String createSchedule;
    /**
     * 编辑日程信息接口地址 put
     */
    private String editSchedule;
    /**
     * 删除日程的接口地址 delete
     */
    private String deleteSchedule;
    /**
     * 日程参与人状态查询接口地址 Get
     */
    private String inviteStatus;

    /**
     * 授权日程 post
     */
    private String accreditSchedule;

}
