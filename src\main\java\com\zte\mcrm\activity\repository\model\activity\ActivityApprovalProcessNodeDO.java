package com.zte.mcrm.activity.repository.model.activity;

import java.util.Date;

/**
 * table:activity_approval_process_node -- 
 */
public class ActivityApprovalProcessNodeDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 活动审批流程信息row_id */
    private String approvalProcessRowId;

    /** 审批人 */
    private String approveBy;

    /** 审批人姓名 */
    private String approverName;

    /** 审批时间 */
    private Date approveTime;

    /** 审批taskID */
    private String approvalFlowNo;

    /** 审批结果Y-同意，N-拒绝，T-转交。枚举：ApproveResultEnum */
    private String approveResult;

    /** 如果是转交过来的，则该有值，转交来源id（本表的row_id） */
    private String transFrom;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 节点状态。ApproveNodeStatusEnum */
    private String nodeStatus;

    /** 节点类型，见：ApproveNodeTypeEnum */
    private String nodeType;

    /** 审批意见备注 */
    private String remark;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getApprovalProcessRowId() {
        return approvalProcessRowId;
    }

    public void setApprovalProcessRowId(String approvalProcessRowId) {
        this.approvalProcessRowId = approvalProcessRowId == null ? null : approvalProcessRowId.trim();
    }

    public String getApproveBy() {
        return approveBy;
    }

    public void setApproveBy(String approveBy) {
        this.approveBy = approveBy == null ? null : approveBy.trim();
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName == null ? null : approverName.trim();
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getApprovalFlowNo() {
        return approvalFlowNo;
    }

    public void setApprovalFlowNo(String approvalFlowNo) {
        this.approvalFlowNo = approvalFlowNo == null ? null : approvalFlowNo.trim();
    }

    public String getApproveResult() {
        return approveResult;
    }

    public void setApproveResult(String approveResult) {
        this.approveResult = approveResult == null ? null : approveResult.trim();
    }

    public String getTransFrom() {
        return transFrom;
    }

    public void setTransFrom(String transFrom) {
        this.transFrom = transFrom == null ? null : transFrom.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getNodeStatus() {
        return nodeStatus;
    }

    public void setNodeStatus(String nodeStatus) {
        this.nodeStatus = nodeStatus == null ? null : nodeStatus.trim();
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType == null ? null : nodeType.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}