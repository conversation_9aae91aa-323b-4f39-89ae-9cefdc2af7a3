package com.zte.mcrm.activity.repository.rep.plancto.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.application.model.CtoPlanDetailDTO;
import com.zte.mcrm.activity.application.model.dto.SelectCtoActivityParamDTO;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.plancto.CtoPlanOrgFinishExtMapper;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import com.zte.mcrm.activity.repository.model.plancto.CtoReportApIndicatorDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanOrgFinishRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CtoPlanOrgFinishRepositoryImpl implements CtoPlanOrgFinishRepository {
    @Autowired
    private CtoPlanOrgFinishExtMapper ctoPlanOrgFinishExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<CtoPlanOrgFinishDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        for (CtoPlanOrgFinishDO cto : list) {
            if (StringUtils.isBlank(cto.getRowId())) {
                cto.setRowId(keyIdService.getKeyId());
            }
            cto.setEnabledFlag(BooleanEnum.Y.getCode());
            cto.setCreationDate(new Date());
            cto.setLastUpdateDate(new Date());
        }

        return ctoPlanOrgFinishExtMapper.batchInsert(list);
    }

    @Override
    public int updateByPrimaryKeySelective(CtoPlanOrgFinishDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        return ctoPlanOrgFinishExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchUpdate(List<CtoPlanOrgFinishDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        Date now = new Date();
        list.forEach(item -> item.setLastUpdateDate(now));
        return ctoPlanOrgFinishExtMapper.batchUpdate(list);
    }

    @Override
    public PageRows<CtoPlanOrgFinishDO> pageOrgFinish(PageQuery<String> pageQuery) {
        String planInfoId = pageQuery.getParam();
        if (StringUtils.isBlank(planInfoId)) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        PageInfo<CtoPlanOrgFinishDO> pageInfo = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), pageQuery.withCount())
                .doSelectPageInfo(() -> ctoPlanOrgFinishExtMapper.listOrgFinishSorted(pageQuery.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }

    @Override
    public List<CtoPlanOrgFinishDO> listOrgFinishByPlanInfoId(String planInfoId) {
        if (StringUtils.isBlank(planInfoId)) {
            return Collections.emptyList();
        }
        return ctoPlanOrgFinishExtMapper.listOrgFinish(planInfoId);
    }

    @Override
    public List<String> listByUndoProcess() {
        return ctoPlanOrgFinishExtMapper.listByUndoProcess();
    }

    @Override
    public List<CtoPlanOrgFinishDO> selectByPlanId(String planId) {
        return ctoPlanOrgFinishExtMapper.selectByPlanId(planId);
    }



    @Override
    public Integer countFinishByAccountAndParticipant(SelectCtoActivityParamDTO countParam) {
        return ctoPlanOrgFinishExtMapper.countFinishByAccountAndParticipant(countParam);
    }

    @Override
    public List<CtoReportApIndicatorDO> listOrgApIndicatorByPlanInfoId(String ctoPlanInfoId) {
        return ctoPlanOrgFinishExtMapper.listOrgApIndicatorByPlanInfoId(ctoPlanInfoId);
    }

    @Override
    public List<CtoReportApIndicatorDO> listProductApIndicatorByPlanInfoId(String ctoPlanInfoId) {
        return ctoPlanOrgFinishExtMapper.listProductApIndicatorByPlanInfoId(ctoPlanInfoId);
    }

    @Override
    public CtoPlanDetailDTO queryOrgDetail(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return new CtoPlanDetailDTO();
        }
        return ctoPlanOrgFinishExtMapper.queryOrgDetail(rowId);
    }
}
