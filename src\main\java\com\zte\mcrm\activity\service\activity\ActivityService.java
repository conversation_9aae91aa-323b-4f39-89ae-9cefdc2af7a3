package com.zte.mcrm.activity.service.activity;


import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.integration.forwardmessage.param.MessageBodyParam;
import com.zte.mcrm.activity.service.activity.model.ScheduleItemModel;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityConflictCheckParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityInvalidateParam;
import com.zte.mcrm.activity.web.controller.activity.param.ConvenientInfoParam;
import com.zte.mcrm.activity.web.controller.activity.vo.*;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ActivityBaseInfoVO;
import com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoVO;
import com.zte.mcrm.activity.web.controller.reception.vo.CustomerReceptionVO;
import com.zte.mcrm.activity.web.controller.reservation.param.ReserveScheduleInitiationParam;

import java.util.List;

/**
 * 查询活动明细信息
 *
 * @author: 汤踊10285568
 * @date: 2023/5/18 17:30
 */
public interface ActivityService {

    /**
     * 查询活动信息
     *
     * @param activityRowId 查询条件
     * @return 查询结果
     * @author: 汤踊10285568
     * @date: 2023/5/18 17:30
     */
    ActivityDetailInfoVO getActivityInfo(String activityRowId);


    /**
     * 提交活动信息
     *
     * @param baseInfoVO 基本信息对象
     * @return String 活动Id
     * @author: 汤踊10285568
     * @date: 2023/5/23 17:30
     */
    BizResult<String> submitActivityBaseInfo(ActivityBaseInfoVO baseInfoVO);

    /**
     * 保存活动信息
     *
     * @param baseInfoVO 基本信息对象
     * @return String 活动Id
     * @author: 汤踊10285568
     * @date: 2023/5/23 17:30
     */
    String saveActivityBaseInfo(ActivityBaseInfoVO baseInfoVO);

    int deleteActivity(BizRequest<List<String>> activityIds);


    /**
     * 查询活动评价信息
     *
     * @param activityRowId 活动主键Id
     * @return List<EvaluationInfoVO>   活动评价信息列表
     * <AUTHOR>
     * date: 2023/5/26 10:40
     */
    EvaluationDetailsVO getEvaluationInfo(String activityRowId);
    /**
     * 通过活动主键Id查询会议纪要详情
     *
     * @param activityRowId 活动Id主键
     * @return SummaryInfoVO    会议纪要详情
     * <AUTHOR>
     * date: 2023/5/26 19:13
     */
    SummaryInfoVO getSummaryInfo(String activityRowId);

    /**
     * 通过活动id获取初始化客户接待信息
     *
     * @param activityRowId
     * @return
     */
    CustomerReceptionVO getActivityInfoForInitReception(String activityRowId);

    /**
     * 编辑、赋值活动
     *
     * @param activityRowId 活动Id
     * @return ActivityDetailInfoVO
     * <AUTHOR>
     * date: 2023/6/15 11:13
     */
    ActivityDetailInfoVO getActivityDetailDraftInfo(String activityRowId);

    /**
     * 获取活动基本信息-APP端使用（中兴参与人绑定预约状态）
     *
     * @param activityRowId 活动Id
     * @return ActivityDetailInfoVO
     * <AUTHOR>
     * date: 2023/6/21 9:17
     */
    ActivityDetailInfoVO getActivityDetailInfoToApp(String activityRowId);

    /**
     * 转发到icenter，加入鉴权
     *
     * @param messageBodyParam
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/17 下午8:02
     */
    boolean forwardMessageToICenter(MessageBodyParam messageBodyParam);

    /**
     * 作废活动
     * @param param 作废活动入参
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/10 13:56
     */
    String invalidate(BizRequest<ActivityInvalidateParam> param);

    /**
     * 活动变更接口
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/29 21:46
     */
    String changeActivity(BizRequest<ActivityBaseInfoVO> request);

    /**
     * 预约日程资源,并发送工作通知
     *
     * @param bizRequest
     * @return
     */
    Boolean scheduleReservationAndWorkNotice(BizRequest<ReserveScheduleInitiationParam> bizRequest);

    /**
     * 统计所有客户联系人的访问信息
     *
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @return CountContactVisitVO
     */
    CountContactVisitVO countAllContactVisits(String startTime, String endTime);

    /**
     * 查询当前来访活动是否绑定客户接待信息
     * @param activityRowId 活动id
     * @return 关联信息
     */
    ActivityRelationReceptionVO getRelationReceptionInfo(String activityRowId);

    /**
     * 获取默认便捷信息
     *
     * @param pram
     * @return com.zte.mcrm.activity.web.controller.activitylist.vo.ConvenientInfoPramVO
     * @date 2024/5/7
     */
    ConvenientInfoVO getConvenientInfo(ConvenientInfoParam pram);

    /**
     * 同步一线活动日程
     *
     * @param scheduleRequest 同步活动日程对象
     */
    boolean syncUpScheduleItem(BizRequest<ScheduleItemModel> scheduleRequest);

    /**
     * 冲突人员
     * @param param
     * @return
     */
    List<ActivityConflictCheckVO> checkExhibitonConflict(BizRequest<ActivityConflictCheckParam> param);

    /**
     * 获取活动基本信息-活动参与人信息
     * @param activityRowId
     * @return
     */
    RelationPeopleInfoVO getActivityDetailRelationPeopleInfoToApp(String activityRowId);
}