package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.repository.model.resource.ActivityResourceFeeDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-16 16:57
 **/
public interface ActivityResourceFeeRepository {
    /**
     * description 批量添加活动相关费用信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午3:55
     */
    int insertSelective(List<ActivityResourceFeeDO> recordList);

    /**
     * description 根据主键更新活动相关费用信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午4:02
     */
    int updateByPrimaryKeySelective(ActivityResourceFeeDO record);

    /**
     * description 根据活动id批量查询活动相关费用信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/16 下午3:57
     */
    Map<String, List<ActivityResourceFeeDO>> queryActivityResourceFeesByActivityRowIds(List<String> activityRowIds);

    /**
     * 根据rowId,批量软删除
     *
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批量更新数据
     * @param records
     * @return
     */
    void batchUpdate(List<ActivityResourceFeeDO> records);
}
