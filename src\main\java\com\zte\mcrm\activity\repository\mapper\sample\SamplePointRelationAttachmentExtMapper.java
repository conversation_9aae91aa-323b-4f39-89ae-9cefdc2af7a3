package com.zte.mcrm.activity.repository.mapper.sample;

import com.zte.mcrm.activity.repository.model.sample.SamplePointRelationAttachmentDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface SamplePointRelationAttachmentExtMapper extends SamplePointRelationAttachmentMapper {

    /**
     * 根据样板点id列表获取关联附件
     *
     * @param samplePointRowIdList 样板点id列表
     * @return List<SamplePointRelationAttachmentDO>
     * <AUTHOR>
     * @date 2024/2/4
     */
    List<SamplePointRelationAttachmentDO> getAttachmentBySamplePointRowIds(@Param("samplePointRowIdList") List<String> samplePointRowIdList);
    /**
     * 删除样板点关联的附件信息
     * @param samplePointRowId
     * @return
     */
    int deleteBySamplePointRowId(String samplePointRowId);
}