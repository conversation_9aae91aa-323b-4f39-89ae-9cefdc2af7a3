package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityStatusLifecycleExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityStatusLifecycleRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class ActivityStatusLifecycleRepositoryImpl implements ActivityStatusLifecycleRepository {
    @Resource
    private ActivityStatusLifecycleExtMapper activityStatusLifecycleExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<ActivityStatusLifecycleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityStatusLifecycleDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            record.setCreatedBy(Optional.ofNullable(record.getCreatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setLastUpdatedBy(Optional.ofNullable(record.getLastUpdatedBy()).orElse(HeadersProperties.getXEmpNo()));
            record.setCreationDate(new Date());
            record.setLastUpdateDate(new Date());
            record.setEnabledFlag(BooleanEnum.Y.getCode());
            activityStatusLifecycleExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int insertSelectiveWithoutDefaultValue(List<ActivityStatusLifecycleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }
        for (ActivityStatusLifecycleDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
            record.setEnabledFlag(BooleanEnum.Y.getCode());
            activityStatusLifecycleExtMapper.insertSelective(record);
        }
        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityStatusLifecycleDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityStatusLifecycleExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityStatusLifecycleDO> queryStatusForActivity(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList() : activityStatusLifecycleExtMapper.queryStatusForActivity(activityRowId);
    }

    @Override
    public int deleteByActivityIds(String operator, List<String> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return NumberConstant.ZERO;
        }

        return activityStatusLifecycleExtMapper.softDeleteByActivityIds(operator, activityIds);
    }
}
