package com.zte.mcrm.activity.repository.rep.banner;

import com.zte.mcrm.activity.common.enums.banner.BannerBusinessTypeEnum;
import com.zte.mcrm.activity.repository.model.banner.BannerInfoDO;
import com.zte.mcrm.activity.repository.model.banner.BannerInfoNewDO;

import java.util.List;
import java.util.Map;

public interface BannerInfoRepository {

    /**
     * 添加banner信息
     *
     * @param record
     * @return
     */
    int insertSelective(BannerInfoNewDO record);

    /**
     * 更新banner信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BannerInfoNewDO record);

    /**
     * 根据业务类型获取需要展示的banner列表
     *
     * @param bizType
     * @return
     */
    List<BannerInfoNewDO> fetchProcessBannerList(BannerBusinessTypeEnum bizType);

    /**
     * 根据ID查询
     * @param bannerId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2024/02/01
     */
    BannerInfoDO get(String bannerId);

    /**
     * 查询列表
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/02/01
     */
    List<BannerInfoDO> getList(BannerInfoDO entity);

    /**
     * 删除(软删除)
     * @param bannerId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    int delete(String bannerId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    String insert(BannerInfoDO entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    int update(BannerInfoDO entity);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2024/02/01
     */
    long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/02/01
     */
    List<BannerInfoDO> getPage(Map<String, Object> map);
}
