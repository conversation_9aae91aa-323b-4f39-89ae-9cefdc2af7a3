package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionDirectorDO;
import java.util.List;

public interface ExhibitionDirectorMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionDirectorDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionDirectorDO record);

    /**
     * query by primary key
     */
    ExhibitionDirectorDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionDirectorDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionDirectorDO record);
}