package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.resource.ActivityResourceCarExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceCarDO;
import com.zte.mcrm.activity.repository.rep.resource.ActivityResourceCarRepository;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-16 16:29
 **/
@Component
public class ActivityResourceCarRepositoryImpl implements ActivityResourceCarRepository {
    @Autowired
    private IKeyIdService keyIdService;
    @Resource
    private ActivityResourceCarExtMapper activityResourceCarExtMapper;
    @Override
    public int insertSelective(List<ActivityResourceCarDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityResourceCarDO record : recordList) {
            //能走到插入数据，一定没有rowId，否则就是走更新了。
            record.setRowId(keyIdService.getKeyId());
            setDefaultValue(record);
            activityResourceCarExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    private void setDefaultValue(ActivityResourceCarDO resourceCarDO) {
        resourceCarDO.setCreatedBy(Optional.ofNullable(resourceCarDO.getCreatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceCarDO.setLastUpdatedBy(Optional.ofNullable(resourceCarDO.getLastUpdatedBy())
                .orElse(HeadersProperties.getXEmpNo()));
        resourceCarDO.setCreationDate(new Date());
        resourceCarDO.setLastUpdateDate(new Date());
        resourceCarDO.setEnabledFlag(BooleanEnum.Y.getCode());
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityResourceCarDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return activityResourceCarExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, List<ActivityResourceCarDO>> queryActivityResourceCarsByActivityRowIds(List<String> activityRowIds) {
        return org.springframework.util.CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityResourceCarExtMapper.queryActivityResourceCarsByActivityRowIds(activityRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityResourceCarDO::getActivityRowId));
    }

    /**
     * 批量删除
     * @param operator
     * @param rowIds
     * @return
     */
    @Override
    public int deleteByRowIds(String operator, List<String> rowIds) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return NumberConstant.ZERO;
        }
        return activityResourceCarExtMapper.deleteByRowIds(operator, rowIds);
    }

    @Override
    public void batchUpdate(List<ActivityResourceCarDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (ActivityResourceCarDO record : records) {
            updateByPrimaryKeys(record);
        }
    }

    private int updateByPrimaryKeys(ActivityResourceCarDO record){
        if (StringUtils.isBlank(record.getRowId())){
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return activityResourceCarExtMapper.updateByPrimaryKeySelective(record);
    }
}
