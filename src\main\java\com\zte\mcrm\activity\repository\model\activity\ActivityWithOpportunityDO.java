package com.zte.mcrm.activity.repository.model.activity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ActivityWithOpportunityDO {

    /**
     * 活动Id
     */
    private String activityRowId;

    /**
     * 拓展活动申请单号
     */
    private String activityRequestNo;

    /**
     * 拓展活动类型
     */
    private String activityType;

    /**
     * 拓展活动议题
     */
    private String activityTitle;

    /**
     * 商机编号
     */
    private String opportunityId;

    /**
     * 绑定标识（1绑定，2解绑）
     */
    private Integer bindFlag;

    /**
     * 绑定人
     */
    private String bindBy;

    /**
     * 绑定时间
     */
    private Date bindDate;

    /**
     * 绑定时活动状态
     */
    private String activityBindStatus;

    /**
     * 解绑人
     */
    private String unbindBy;

    /**
     * 解绑时间
     */
    private Date unbindDate;

    /**
     * 解绑时活动状态
     */
    private String activityUnBindStatus;

    /**
     * 拓展活动状态。枚举：ActivityStatusEnum
     */
    private String activityStatus;

    /**
     * 活动起始时间。显示按yyyy-MM-dd HH:mm格式
     */
    private Date startTime;

    /**
     * 活动截止时间。显示按yyyy-MM-dd HH:mm格式
     */
    private Date endTime;

    /**
     * 活动地点
     */
    private String activityPlace;

    /**
     * 申请人员工编号
     */
    private String applyPeopleNo;

    /**
     * 申请部门编码
     */
    private String applyDepartmentNo;

    /**
     * 交流城市
     */
    private String cityCode;

    /**
     * 交流国家
     */
    private String countryCode;

    /**
     * 活动交流城市名称
     */
    private String cityCodeName;

    /**
     * 活动交流城市所在国家
     */
    private String countryCodeName;

}
