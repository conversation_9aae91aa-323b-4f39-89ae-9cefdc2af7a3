/* Started by AICoder, pid:t5397n704cuecad14a7109574010c35705d69f26 */
package com.zte.mcrm.activity.repository.model.plancto;

import lombok.Data;
import java.util.Date;

@Data
public class CtoPlanSaleDivisionMappingDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * 营销事业部。见枚举：SaleDivisionEnum
     */
    private String salesDivision;

    /**
     * 组织来源。见枚举：CompanyOrgSourceEnum
     */
    private String orgSource;

    /**
     * 组织编码
     */
    private String orgNo;

    /**
     * 组织编码全路径
     */
    private String orgNoFullPath;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;
}

/* Ended by AICoder, pid:t5397n704cuecad14a7109574010c35705d69f26 */