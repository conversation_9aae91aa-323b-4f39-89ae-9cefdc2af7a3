package com.zte.mcrm.activity.integration.authorityclient;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.CommonRoleEntity;
import com.zte.itp.authorityclient.entity.input.RoleDataEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.authorityclient.param.UppRoleUserSearchParam;
import com.zte.mcrm.activity.service.authority.dto.UppRoleUserDTO;
import com.zte.mcrm.authority.AuthorityClientUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * UPP 接口封装
 *
 * <AUTHOR>
 * @date 2023/5/23 下午7:13
 */
@Service
public class VisitUppService {

    @Value("${upp.auth.visit.moduleId:0}")
    private String moduleId;

    @Autowired
    private AuthorityClientUtil authorityClientUtil;
    /**
     * 获取具有指定约束、指定角色、指定约束值的用户
     *
     * @param request
     * @return {@link List <UppRoleUserDTO>}
     * <AUTHOR>
     * @date 2023/3/23 下午2:55
     */
    public List<UppRoleUserDTO> fetchRoleDataUer(MsaRpcRequest<RoleDataEntity> request) {
        RoleDataEntity param = request.getBody();
        param.setEmpidui(request.getHeader(RequestHeaderConstant.X_EMP_NO));
        param.setToken(request.getHeader(RequestHeaderConstant.X_AUTH_VALUE));
        param.setModuleId(moduleId);

        ServiceData<LinkedHashMap<String, List<Object>>> roleDataUser = AuthorityClient.getRoleDataUser(param);
        List<UppRoleUserDTO> result = Lists.newArrayList();
        if (Objects.isNull(roleDataUser) || MapUtils.isEmpty(roleDataUser.getBo())) {
            return result;
        }
        roleDataUser.getBo().forEach((key, value) -> {
            if (CollectionUtils.isNotEmpty(value)) {
                List<UppRoleUserDTO> roleUserDTOList = JSON.parseArray(JSON.toJSONString(value), UppRoleUserDTO.class);
                result.addAll(roleUserDTOList);
            }
        });
        return result;
    }

    /**
     * 当前用户是否在角色列表中
     *
     */
    public boolean hasRoleAuthority(MsaRpcRequest<String> request){
        UppRoleUserSearchParam uppRoleUserSearchParam = new UppRoleUserSearchParam();
        uppRoleUserSearchParam.setLangId(request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID));
        uppRoleUserSearchParam.setRoleCodeList(Splitter.on(CharacterConstant.COMMA).omitEmptyStrings().trimResults().splitToList(request.getBody()));
        MsaRpcRequest<UppRoleUserSearchParam> msaRpcRequest = MsaRpcRequestUtil.createWithCurrentUser(uppRoleUserSearchParam);

        Map<String, List<UserVO>> serviceData = fetchUsersByRoleCode(msaRpcRequest);
        return serviceData.values().stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).anyMatch(userVO -> userVO.getEmpidui().equals(request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)));
    }

    /**
     * 查询用户拥有的角色列表
     */

    public List<String> queryUserRoleAuthority(MsaRpcRequest request) {
        List<RoleVO> roleList = authorityClientUtil.getRoleListModuleId(request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO), moduleId);
        List<String> roleStrList = new ArrayList<>();
        if(org.apache.commons.collections.CollectionUtils.isEmpty(roleList)){
            return roleStrList;
        }
        //过滤出角色编码集合
        for (RoleVO roleVO : roleList) {
            if (null != roleVO ) {
                roleStrList.add(roleVO.getRoleCode());
            }
        }
        return roleStrList;
    }

    /**
     * 获取角色用户集合
     *
     * @param request
     * @return {@link Map<String, List<UserVO>>}
     * <AUTHOR>
     * @date 2023/8/26 下午3:58
     */
    public Map<String, List<UserVO>> fetchUsersByRoleCode(MsaRpcRequest<UppRoleUserSearchParam> request) {
        UppRoleUserSearchParam param = request.getBody();
        List<String> roleCodeList = param.getRoleCodeList();
        if (CollectionUtils.isEmpty(roleCodeList)) {
            return Maps.newHashMap();
        }
        CommonRoleEntity entity = new CommonRoleEntity();
        entity.setEmpidui(request.getHeader(RequestHeaderConstant.X_EMP_NO));
        entity.setToken(request.getHeader(RequestHeaderConstant.X_AUTH_VALUE));
        entity.setModuleId(moduleId);
        ServiceData<Map<String, List<UserVO>>> serviceData = AuthorityClient.queryUsersByRoleCode(entity, roleCodeList, param.getLangId());
        if (Objects.nonNull(serviceData) && MapUtils.isNotEmpty(serviceData.getBo())) {
            return serviceData.getBo();
        }
        return Maps.newHashMap();
    }


}
