package com.zte.mcrm.activity.service.activity.model;

import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/1/14 上午9:58
 */
@Data
public class ActivityScheduleItemPeopleModel {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 活动日程事项RowId */
    private String activityScheduleItemRowId;

    /** 人员类型ScheduleItemPeopleTypeEnum。我司参与人，我司现场接口人，非我司现场接口人，客户参与人 */
    private String peopleType;

    /** 人员编号（员工编号、客户参与人编号） */
    private String peopleNo;

    /** 人员名称 */
    private String peopleName;

    /** 人员标签。见：PeopleRoleLabelEnum */
    private String peopleLabel;

    /** 电话 */
    private String phoneNum;

    /** 岗位名称。因为管理联系人岗位最大256长度 */
    private String position;

    /** 对应客户编码，如果是客户参与人这个是有值的 */
    private String customerCode;

    public <T> ActivityScheduleItemPeopleModel convert(T data) {
        BeanUtils.copyProperties(data, this);
        return this;
    }
}
