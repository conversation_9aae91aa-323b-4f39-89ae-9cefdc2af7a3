package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleItemOriginVersionExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginVersionDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemOriginVersionRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ActivityScheduleItemOriginVersionRepositoryImpl implements ActivityScheduleItemOriginVersionRepository {
    @Autowired
    private ActivityScheduleItemOriginVersionExtMapper activityScheduleItemOriginVersionExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<ActivityScheduleItemOriginVersionDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityScheduleItemOriginVersionDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
        }

        return activityScheduleItemOriginVersionExtMapper.batchInsert(recordList);
    }

    @Override
    public Map<String, ActivityScheduleItemOriginVersionDO> getRelationScheduleItemOriginInfos(List<String> activityRowIds) {

        return CollectionUtils.isEmpty(activityRowIds) ? Collections.emptyMap() :
                activityScheduleItemOriginVersionExtMapper.getByActivityRowIds(activityRowIds)
                        .stream()
                        .collect(Collectors.toMap(
                                        ActivityScheduleItemOriginVersionDO::getActivityRowId,
                                        Function.identity(),
                                        (v1, v2) -> v1.getVersionNum() - v2.getVersionNum() > 0 ? v1 : v2));
    }

    @Override
    public Map<String, ActivityScheduleItemOriginVersionDO> getRelationScheduleItemOriginInfos(List<String> activityRowIds, Integer backVersionLevel) {
        if (CollectionUtils.isEmpty(activityRowIds) || Objects.isNull(backVersionLevel) || backVersionLevel < 0) {
            return Collections.emptyMap();
        }

        List<ActivityScheduleItemOriginVersionDO> activityScheduleItemOriginVersions = activityScheduleItemOriginVersionExtMapper.getByActivityRowIds(activityRowIds);
        Map<String, List<ActivityScheduleItemOriginVersionDO>> activityGroup = activityScheduleItemOriginVersions.stream().collect(Collectors.groupingBy(ActivityScheduleItemOriginVersionDO::getActivityRowId));

        //存储指定版本的日程
        Map<String, ActivityScheduleItemOriginVersionDO> returnMap = new HashMap<>(8);
        for (Map.Entry<String, List<ActivityScheduleItemOriginVersionDO>> activityEntry : activityGroup.entrySet()) {
            List<ActivityScheduleItemOriginVersionDO> originVersionDOS = activityEntry.getValue();
            if (originVersionDOS.size() < backVersionLevel + 1) {
                continue;
            }
            //按版本号从大到小排序
            Collections.sort(originVersionDOS, Comparator.comparing(ActivityScheduleItemOriginVersionDO::getVersionNum).reversed());
            ActivityScheduleItemOriginVersionDO activityScheduleItemOriginVersionDO = originVersionDOS.get(backVersionLevel);
            returnMap.put(activityEntry.getKey(),activityScheduleItemOriginVersionDO);
        }
        return returnMap;
    }
}
