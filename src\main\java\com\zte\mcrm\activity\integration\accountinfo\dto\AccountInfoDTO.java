package com.zte.mcrm.activity.integration.accountinfo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * zte-crm-account-info 客户管理服务端代理
 *
 * <AUTHOR>
 * @date 2024-04-18 15:45:00
 */
@Data
public class AccountInfoDTO {
    /**
     * 联系人编号
     */
    private String contactNo;
    /** 联系人rowId */
    private String contactRowId;

    /**
     * 联系人所属客户编码
     */
    private String customerCode;

    /**
     * 姓名
     */
    private String name;
    /**
     * 英文名
     */
    private String nameEn;

    /**
     * 联系人级别：SVIP、VIP、A、B
     */
    private String personLevel;

    /**
     * 性别：1-男，0-女
     */
    private Integer sex;

    /**
     * 客户经理类型(A/B)
     */
    private String managerType;

}
