package com.zte.mcrm.activity.integration.ai.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 会议纪要AI生成总结
 *
 * <AUTHOR>
 * @date 2025/3/4 下午5:02
 */
@Getter
@Setter
@ToString
public class SummaryAbstractDTO {

    /**
     * 人员信息
     */
    private SummaryPeopleInfoDTO peopleOrder;

    /**
     * 遗留问题
     */
    private SummaryContentDTO questionSummary;

    /**
     * 会议概要
     */
    private SummaryContentDTO meetingSummary;

    /**
     * 人员信息
     */
    @Getter
    @Setter
    @ToString
    public static class SummaryPeopleInfoDTO {

        /**
         * 中文
         */
        private Map<String, List<PeopleInfoDTO>> zh;

        /**
         * 英文
         */
        private Map<String, List<PeopleInfoDTO>> en;
    }

    /**
     * 决议
     */
    @Getter
    @Setter
    @ToString
    public static class SummaryContentDTO {

        /**
         * 中文
         */
        private String zh;

        /**
         * 英文
         */
        private String en;
    }
}
