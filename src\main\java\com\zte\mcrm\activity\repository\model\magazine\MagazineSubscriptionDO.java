package com.zte.mcrm.activity.repository.model.magazine;

import com.zte.mcrm.activity.repository.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 杂志订阅申请数据对象
 * 对应数据库表：cust_magazine_subscription
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class MagazineSubscriptionDO extends BaseEntity {
    
    /** 主键ID */
    private String rowId;
    
    /** 申请单号 */
    private String applicationNo;
    
    /** 杂志编码 */
    private String magazineCode;
    
    /** 杂志名称 */
    private String magazineName;
    
    /** 杂志类型：PAPER-纸质，ELECTRONIC-电子 */
    private String magazineType;
    
    /** 杂志数量 */
    private Integer magazineQuantity;
    
    /** 客户编码 */
    private String customerCode;
    
    /** 客户名称 */
    private String customerName;
    
    /** 联系人编号 */
    private String contactPersonNo;
    
    /** 联系人姓名 */
    private String contactPerson;
    
    /** 联系邮箱 */
    private String contactEmail;
    
    /** 联系电话 */
    private String contactPhone;
    
    /** 收货地址 */
    private String deliveryAddress;
    
    /** 派发方式：EMAIL-邮件发送，POST-邮寄纸质 */
    private String deliveryMethod;
    
    /** 订阅原因 */
    private String subscriptionReason;
    
    /** 申请状态 */
    private String applicationStatus;
    
    /** 流程实例ID */
    private String flowInstanceId;
    
    /** 申请人工号 */
    private String applyEmpNo;
    
    /** 申请部门编号 */
    private String applyDeptNo;
    
    /** 申请时间 */
    private Date applyDate;
    
    /** 审批人工号 */
    private String approveEmpNo;
    
    /** 审批时间 */
    private Date approveDate;
    
    /** 审批意见 */
    private String approveOpinion;
} 