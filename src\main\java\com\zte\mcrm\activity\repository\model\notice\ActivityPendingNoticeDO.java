package com.zte.mcrm.activity.repository.model.notice;

import java.util.Date;

/**
 * table:activity_pending_notice -- 
 */
public class ActivityPendingNoticeDO {
    /** 主键 */
    private String rowId;

    /** 拓展活动id */
    private String activityRowId;

    /** 待办标题 */
    private String pendingTitle;

    /** 待办业务类型。如：专家预约待办，领导预约待办，枚举：PendingBizTypeEnum */
    private String pendingBizType;

    /** 待办通知状态（1-待处理,2-完成,3-已删除保持与待办服务统一，其他状态可自定义），见枚举：PendingNoticeStatusEnum */
    private String pendingStatus;

    /** 待办处理最后截止时间（为活动结束时间）。如未处理，过了截止时间会自动取消 */
    private Date replyLastTime;

    /** 待办通知人员工编码 */
    private String noticerNo;

    /** 备注信息 */
    private String remark;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    /** 关联业务ID */
    private String businessId;

    /** 发送给待办中心的内容json */
    private String sendPendingJson;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public String getPendingTitle() {
        return pendingTitle;
    }

    public void setPendingTitle(String pendingTitle) {
        this.pendingTitle = pendingTitle == null ? null : pendingTitle.trim();
    }

    public String getPendingBizType() {
        return pendingBizType;
    }

    public void setPendingBizType(String pendingBizType) {
        this.pendingBizType = pendingBizType == null ? null : pendingBizType.trim();
    }

    public String getPendingStatus() {
        return pendingStatus;
    }

    public void setPendingStatus(String pendingStatus) {
        this.pendingStatus = pendingStatus == null ? null : pendingStatus.trim();
    }

    public Date getReplyLastTime() {
        return replyLastTime;
    }

    public void setReplyLastTime(Date replyLastTime) {
        this.replyLastTime = replyLastTime;
    }

    public String getNoticerNo() {
        return noticerNo;
    }

    public void setNoticerNo(String noticerNo) {
        this.noticerNo = noticerNo == null ? null : noticerNo.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId == null ? null : businessId.trim();
    }

    public String getSendPendingJson() {
        return sendPendingJson;
    }

    public void setSendPendingJson(String sendPendingJson) {
        this.sendPendingJson = sendPendingJson == null ? null : sendPendingJson.trim();
    }
}