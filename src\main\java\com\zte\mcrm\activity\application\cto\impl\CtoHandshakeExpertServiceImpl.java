package com.zte.mcrm.activity.application.cto.impl;

import com.zte.mcrm.activity.application.cto.CtoHandshakeExpertService;
import com.zte.mcrm.activity.common.model.*;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.service.resource.ResourceCtoPersonService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoHandshakeExpertVO;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceCtoPersonParam;
import com.zte.mcrm.activity.web.controller.resource.vo.ResourceCtoPersonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zte.mcrm.custcomm.common.constant.RiskConst.LANGUAGE_ZH_CN;

/**
 * <AUTHOR>
 * @description：握手专家服务实现类
 * @date ：2024/12/06 10:33
 */
@Slf4j
@Service
public class CtoHandshakeExpertServiceImpl implements CtoHandshakeExpertService {
    private static final String VERSION_1 = "v1";
    @Autowired
    private ResourceCtoPersonService resourceCtoPersonService;
    @Override
    public LowCodePageRow<CtoHandshakeExpertVO> queryHandshakeExpertList(BizRequest<PageQuery<ResourceCtoPersonParam>> param) {
        PageQuery<ResourceCtoPersonParam> resourceCtoPersonParam = param.getParam();
        PageRows<ResourceCtoPersonVO> resourceCtoPerson = resourceCtoPersonService.getCtoPersonPage(param);
        if(Objects.isNull(resourceCtoPerson) ||CollectionUtils.isEmpty(resourceCtoPerson.getRows())){
            return PageRowsUtil.buildEmptyLowCodePageRow(resourceCtoPersonParam.getPageNo(), resourceCtoPersonParam.getPageSize());
        }
        long total = resourceCtoPerson.getTotal();
        List<CtoHandshakeExpertVO> expertList = new ArrayList<>(resourceCtoPerson.getRows().size());
        for (ResourceCtoPersonVO resourceCtoPersonDO : resourceCtoPerson.getRows()) {
            CtoHandshakeExpertVO ctoHandshakeExpertVO = new CtoHandshakeExpertVO();
            BeanUtils.copyProperties(resourceCtoPersonDO, ctoHandshakeExpertVO);
            expertList.add(ctoHandshakeExpertVO);
        }
        return PageRowsUtil.buildLowCodePageRow(resourceCtoPersonParam.getPageNo(), resourceCtoPersonParam.getPageSize(), (int) total, expertList);
    }
}
