package com.zte.mcrm.activity.integration.ap;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.ap.dto.ApTaskDTO;
import com.zte.mcrm.activity.integration.ap.param.ApQueryParam;
import com.zte.mcrm.adapter.KmJpsAppAdapter;
import com.zte.mcrm.adapter.vo.KmJpsApVO;
import com.zte.mcrm.common.util.CollectionExtUtils;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import com.zte.mcrm.custcomm.ui.model.QueryApTaskBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/19 15:21
 */
@Component
public class ApSystemService {

    @Autowired
    private KmJpsAppAdapter kmJpsAppAdapter;
    @Value("${icenter.expansionapp.syscode}")
    private String activityApSysCode;

    public ApTaskDTO getApInfoList(MsaRpcRequest<ApQueryParam> request){
        //调用AP接口查询已关联的AP信息
        KmJpsApVO kmJpsApVO = new KmJpsApVO();
        ApQueryParam apQueryParam = request.getBody();
        BeanUtils.copyProperties(apQueryParam, kmJpsApVO);
        QueryApTaskBO queryApTaskBO = kmJpsAppAdapter.outerTenantQuery(kmJpsApVO);
        ApTaskDTO apTaskDTO = new ApTaskDTO();
        BeanUtils.copyProperties(queryApTaskBO, apTaskDTO);
        return apTaskDTO;
    }

    /**
     * 根据AP event ID（对于拓展活动的AP，对应拓展活动ID）获取AP任务信息
     * @param request
     * @return
     */
    public List<ApTaskVO> fetchApTaskInfo(MsaRpcRequest<Set<String>> request) {
        if (CollectionUtils.isEmpty(request.getBody())) {
            return Collections.emptyList();
        }
        //调用AP接口查询已关联的AP信息
        KmJpsApVO query = new KmJpsApVO();
        query.setCurrentPage(NumberConstant.ONE);
        query.setPageSize(NumberConstant.TWO_HUNDRED);
        query.setSystemCode(activityApSysCode);
        query.setTypeId("customer_communication_AP");
        query.setEventIds(request.getBody().stream().toArray(String[]::new));

        PageRows<ApTaskVO> page = pageApList(query);

        List<ApTaskVO> taskList = new ArrayList<>((int) page.getTotal());
        taskList.addAll(page.getRows());

        int size = page.getRows().size();
        // 如果第一页查询不完，则遍历查询后面的页数据
        if (size >= NumberConstant.TWO_HUNDRED) {
            List<KmJpsApVO> taskParamList = new ArrayList<>((int) page.getTotalPage());
            for (int i = 1; i < page.getTotalPage(); i++) {
                KmJpsApVO param = new KmJpsApVO();
                BeanUtils.copyProperties(query, param);
                param.setCurrentPage(i + 1);
                taskParamList.add(param);
            }

            // 并行任务处理
            List<PageRows<ApTaskVO>> resList = ThreadManager.doTaskParallel(taskParamList, this::pageApList);
            for (PageRows<ApTaskVO> res : resList) {
                taskList.addAll(res.getRows());
            }
        }

        return taskList;
    }

    /**
     * 分页查询AP
     *
     * @param query
     * @return
     */
    public PageRows<ApTaskVO> pageApList(KmJpsApVO query) {
        QueryApTaskBO res = kmJpsAppAdapter.outerTenantQuery(query);
        res = Optional.ofNullable(res).orElse(new QueryApTaskBO());
        return PageRowsUtil.buildPageRow(query.getCurrentPage(), query.getPageSize(),
                Optional.ofNullable(res.getTotal()).orElse(NumberConstant.ZERO),
                CollectionExtUtils.getListOrDefaultEmpty(res.getDataList()));
    }
}
