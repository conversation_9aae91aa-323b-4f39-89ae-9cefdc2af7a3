package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalProcessDO;

import java.util.List;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/11 13:55
 */
public interface ActivityApprovalProcessRepository {

    /**
     * all field insert
     */
    int insert(ActivityApprovalProcessDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityApprovalProcessDO record);

    /**
     * query by primary key
     */
    ActivityApprovalProcessDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityApprovalProcessDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityApprovalProcessDO record);

    /**
     * 根据活动id获取审批信息
     *
     * @param activityRowId
     * @return
     */
    List<ActivityApprovalProcessDO> getApprovalProcessListByActivityRowId(String activityRowId);

    /**
     * 新增审批信息
     *
     * @param activityApprovalProcessDOList
     * @return
     */
    int insertByBatch(List<ActivityApprovalProcessDO> activityApprovalProcessDOList);

    /**
     * 根据主键删除记录（逻辑删除）
     *
     * @param approveRowId
     * @return
     */
    int deleteByRowId(String approveRowId);


    /**
     * 获取审批节点
     *
     * @param activityRowId
     * @param processType
     * @return
     */
    List<ActivityApprovalProcessDO> queryByActivityRowIdAndProcessType(String activityRowId, String processType);


    /**
     * 根据审批单号查询审批节点（单号对应审批中心taskId）
     *
     * @param approvalFlowNo
     * @return
     */
    ActivityApprovalProcessDO queryByApprovalFlowNo(String approvalFlowNo);

    /**
     * 通过活动id获取审批节点
     *
     * @param activityRowId
     * @return
     */
    List<ActivityApprovalProcessDO> queryByActivityRowId(String activityRowId);

    /**
     * 通过活动Id更新
     * @param processDO 更新记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/4 13:53
     */
    int updateByActivityRowIdSelective(ActivityApprovalProcessDO processDO);

    /**
     * 通过主键ID列表删除节点
     * @param rowIds
     * @return
     */
    int deleteByRowIds(List<String> rowIds);
}
