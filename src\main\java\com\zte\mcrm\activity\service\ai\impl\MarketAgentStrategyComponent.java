package com.zte.mcrm.activity.service.ai.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.service.ai.MarketAgentStrategy;
import com.zte.mcrm.activity.web.controller.ai.agentvo.IgptRespVO;
import com.zte.mcrm.activity.web.controller.ai.vo.AiApplicationRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class MarketAgentStrategyComponent {
    @Autowired
    private ApplicationContext context;

    public List<IgptRespVO> processBusinessStrategy(AiApplicationRespVO respVO) {
        Map<String, MarketAgentStrategy> serviceMap = context.getBeansOfType(MarketAgentStrategy.class);

        for (MarketAgentStrategy service : serviceMap.values()) {
            if (service.support(respVO.getApiType())) {
                return service.processBusiness(respVO);
            }
        }
        return Lists.newArrayList();
    }
}
