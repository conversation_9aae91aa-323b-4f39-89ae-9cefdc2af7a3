package com.zte.mcrm.activity.repository.rep.magazine.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.magazine.MagazineSubscriptionExtMapper;
import com.zte.mcrm.activity.repository.model.magazine.MagazineSubscriptionDO;
import com.zte.mcrm.activity.repository.rep.magazine.MagazineSubscriptionRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 杂志订阅Repository实现类
 * 负责杂志订阅数据的访问和持久化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class MagazineSubscriptionRepositoryImpl implements MagazineSubscriptionRepository {
    
    @Autowired
    private MagazineSubscriptionExtMapper extMapper;
    
    @Autowired
    private IKeyIdService keyIdService;
    
    @Override
    public MagazineSubscriptionDO selectByPrimaryKey(String rowId) {
        if (StringUtils.isBlank(rowId)) {
            return null;
        }
        return extMapper.selectByPrimaryKey(rowId);
    }
    
    @Override
    public PageInfo<String> selectDistinctContactPersonNoWithPage(String contactPersonNo, String magazineName, 
                                                                 int pageNo, int pageSize) {
        // 使用PageHelper进行分页
        PageHelper.startPage(pageNo, pageSize);
        List<String> contactPersonNos = extMapper.selectDistinctContactPersonNoByConditionWithPage(
            contactPersonNo, magazineName);
        return new PageInfo<>(contactPersonNos);
    }
    
    @Override
    public List<MagazineSubscriptionDO> selectByContactPersonNos(List<String> contactPersonNos) {
        if (CollectionUtils.isEmpty(contactPersonNos)) {
            return Collections.emptyList();
        }
        return extMapper.selectByContactPersonNos(contactPersonNos);
    }
    
    @Override
    public int insertSelective(MagazineSubscriptionDO record) {
        if (record == null) {
            return 0;
        }
        
        // 设置主键
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        
        // 设置创建信息
        Date now = new Date();
        record.setCreationDate(now);
        record.setLastUpdateDate(now);
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        
        // 设置创建人
        String currentUser = BizRequestUtil.createWithCurrentUser().getEmpNo();
        if (StringUtils.isBlank(record.getCreatedBy())) {
            record.setCreatedBy(currentUser);
        }
        if (StringUtils.isBlank(record.getLastUpdatedBy())) {
            record.setLastUpdatedBy(currentUser);
        }
        
        return extMapper.insertSelective(record);
    }
    
    @Override
    public int updateByPrimaryKeySelective(MagazineSubscriptionDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return 0;
        }
        
        // 设置更新信息
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
        
        return extMapper.updateByPrimaryKeySelective(record);
    }
} 