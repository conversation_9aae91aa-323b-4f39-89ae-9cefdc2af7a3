package com.zte.mcrm.activity.integration.icenterCalendar.dto;

import com.zte.mcrm.activity.common.model.DateTimePeriod;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * 用户日程占用数据
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ScheduleTakeUpDto {

    /**
     * 员工编号
     */
    private String empNo;
    /**
     * 员工占用时间列表
     */
    private Map<String, DateTimePeriod> takeUpMap = new HashMap<>();
    /**
     * 条件限制范围
     */
    private DateTimePeriod conditionTime;

    /**
     * 添加占用时间区间
     *
     * @param outSystemId 对应日程关联ID
     * @param startTime 开始时间
     * @param endTime 截止时间
     */
    public void addTakeUpTime(String outSystemId, Date startTime, Date endTime) {

        if (startTime != null && endTime != null) {
            takeUpMap.put(outSystemId, new DateTimePeriod(startTime, endTime));
        }
    }

    /**
     * 移除对应日程占用时间
     *
     * @param outSystemId
     */
    public void removeTakeUpTime(String outSystemId) {
        takeUpMap.remove(outSystemId);
    }

    public List<DateTimePeriod> getTakeUpList() {
        return new ArrayList<>(takeUpMap.values());
    }
}
