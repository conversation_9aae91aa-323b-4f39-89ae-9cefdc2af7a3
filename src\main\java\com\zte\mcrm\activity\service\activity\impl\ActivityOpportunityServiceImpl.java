package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.constant.LookupConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.OpportunityConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityFlowNodeEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum;
import com.zte.mcrm.activity.common.enums.activity.ActivityTypeEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.common.util.ValidationUtils;
import com.zte.mcrm.activity.integration.lookupapi.impl.LookUpExtService;
import com.zte.mcrm.activity.integration.opportunity.OpportunityCaller;
import com.zte.mcrm.activity.integration.opportunity.dto.OpportunityDTO;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmOrgInfoSearchService;
import com.zte.mcrm.activity.integration.zteHrmUsercenterPginfo.HrmUserCenterSearchService;
import com.zte.mcrm.activity.repository.model.activity.*;
import com.zte.mcrm.activity.repository.model.relation.ActivityCustomerInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityInfoRepository;
import com.zte.mcrm.activity.repository.rep.activity.ActivityOpportunityRepository;
import com.zte.mcrm.activity.repository.rep.relation.ActivityCustomerInfoRepository;
import com.zte.mcrm.activity.service.activity.ActivityOpportunityService;
import com.zte.mcrm.activity.service.activity.convert.ActivityOpportunityConvert;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.*;
import com.zte.mcrm.activity.web.controller.activity.vo.*;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.customvisit.util.DateUtils;
import com.zte.mcrm.keyid.service.IKeyIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.EMPTY_STR;
import static com.zte.mcrm.activity.common.constant.CharacterConstant.WAVE;
import static com.zte.mcrm.activity.common.constant.I18Constants.ACTIVITY_ID_EMPTY;
import static com.zte.mcrm.activity.common.constant.I18Constants.BIND_AND_UNBIND_AT_THE_SAME_TIME;
import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.*;
import static com.zte.mcrm.activity.common.enums.activity.ActivityStatusEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityOpportunityServiceImpl implements ActivityOpportunityService {

    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private LocaleMessageSourceBean localeMessage;

    @Autowired
    private LookUpExtService lookUpExtService;

    @Autowired
    private HrmOrgInfoSearchService orgInfoSearchService;

    @Autowired
    private HrmUserCenterSearchService hrmUserCenterSearchService;

    @Autowired
    private ActivityOpportunityConvert activityOpportunityConvert;

    @Autowired
    private ActivityOpportunityRepository activityOpportunityRepository;

    @Autowired
    private ActivityCustomerInfoRepository activityCustomerInfoRepository;

    @Autowired
    private ActivityInfoRepository activityInfoRepository;

    @Autowired
    private OpportunityCaller opportunityCaller;

    @Override
    public PageRows<ActivityOpportunityVO> queryActivityForOpportunity(BizRequest<PageQuery<ActivityOpportunityQueryParam>> request) {
        PageQuery<ActivityOpportunityQueryParam> pageQuery = request.getParam();
        ValidationUtils.validateMessage(pageQuery.getParam());
        PageRowsUtil.setDefaultPageQueryParams(pageQuery);

        PageRows<ActivityWithOpportunityDO> pageRows = activityOpportunityRepository.queryActivityOpportunityInfo(pageQuery);
        // 过滤出全部的组织编码批量查询
        Set<String> activityRowIdSet = new HashSet<>();
        Set<String> applyPeopleNoSet = new HashSet<>();
        Set<String> applyDepartmentNoSet = new HashSet<>();
        List<ActivityWithOpportunityDO> activityWithOpportunityDOList = pageRows.getRows().stream().peek(item -> {
            activityRowIdSet.add(item.getActivityRowId());
            applyPeopleNoSet.add(item.getApplyPeopleNo());
            applyDepartmentNoSet.add(item.getApplyDepartmentNo());
        }).collect(Collectors.toList());

        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);
        Map<String, OrgInfoVO> orgInfoByOrgIds = orgInfoSearchService.getOrgInfoByOrgIds(Lists.newArrayList(applyDepartmentNoSet));
        Map<String, String> applyPersonNameMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(applyPeopleNoSet)).getBo();
        Map<String, ActivityCustomerInfoDO> activityMainCustomerInfoMap = activityCustomerInfoRepository.getActivityCustomerListByActivityRowIdSet(activityRowIdSet)
                .stream().filter(item -> BooleanEnum.Y.isMe(item.getMainCust())).collect(Collectors.toMap(ActivityCustomerInfoDO::getActivityRowId, i -> i, (u, v) -> u));
        List<ActivityOpportunityVO> activityOpportunityVOList = Lists.newArrayList();
        for (ActivityWithOpportunityDO activityWithOpportunityDO : activityWithOpportunityDOList) {
            ActivityOpportunityVO activityOpportunityVO = new ActivityOpportunityVO();
            BeanUtils.copyProperties(activityWithOpportunityDO, activityOpportunityVO);
            activityOpportunityVO.setActivityTypeName(ActivityTypeEnum.getDescByType(activityOpportunityVO.getActivityType()));
            activityOpportunityVO.setActivityStatusName(statusMap.get(activityOpportunityVO.getActivityStatus()));
            activityOpportunityVO.setActivityStartEndTime(DateUtils.getDateFormatDay(activityOpportunityVO.getStartTime()) +
                    WAVE + DateUtils.getDateFormatDay(activityOpportunityVO.getEndTime()));
            String applyDepartmentNo = activityWithOpportunityDO.getApplyDepartmentNo();
            if (orgInfoByOrgIds.containsKey(applyDepartmentNo)) {
                OrgInfoVO orgInfoVO = orgInfoByOrgIds.get(applyDepartmentNo);
                if (orgInfoVO != null) {
                    activityOpportunityVO.setApplyDepartmentName(orgInfoVO.getHrOrgName());
                }
            }

            if (activityMainCustomerInfoMap.containsKey(activityWithOpportunityDO.getActivityRowId())) {
                ActivityCustomerInfoDO mainCustomer = activityMainCustomerInfoMap.get(activityWithOpportunityDO.getActivityRowId());
                activityOpportunityVO.setSanctionedPatryCode(mainCustomer.getSanctionedPatryCode());
                activityOpportunityVO.setCustomerCode(mainCustomer.getCustomerCode());
                activityOpportunityVO.setCustomerName(mainCustomer.getCustomerName());
            }

            String applyPeopleName = EMPTY_STR;
            String empNo = activityOpportunityVO.getApplyPeopleNo();
            if (applyPersonNameMap.containsKey(empNo)) {
                applyPeopleName = applyPersonNameMap.get(empNo);
            }
            activityOpportunityVO.setApplyPeopleDesc(applyPeopleName + empNo);
            activityOpportunityVOList.add(activityOpportunityVO);
        }
        return PageRowsUtil.buildPageRow(pageRows.getCurrent(), pageRows.getPageSize(), pageRows.getTotal(), activityOpportunityVOList);
    }

    @Override
    public PageRows<ActivityOpportunityRelationVO> queryActivityOpportunityRelation(BizRequest<PageQuery<ActivityOpportunityRelationParam>> request) {
        PageQuery<ActivityOpportunityRelationParam> pageQuery = request.getParam();
        PageRowsUtil.setDefaultPageQueryParams(pageQuery);

        ActivityOpportunityRelationParam param = pageQuery.getParam();
        if (!this.checkActivityOpportunityRelationQueryParam(param)) {
            return PageRowsUtil.buildEmptyPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        }

        ActivityOpportunityRelationQueryParam queryParam = activityOpportunityConvert.convertToQueryParam(param);
        queryParam.setEmpNo(request.getEmpNo());
        queryParam.setPeopleTypeList(Lists.newArrayList(CREATE_BY.getCode(), APPLICANT.getCode(), LECTURER.getCode(), PARTICIPANTS.getCode(), ORGANIZER.getCode(), SITE_PEOPLE.getCode()));
        Set<String> filterIds = Optional.ofNullable(activityOpportunityRepository.getMapByOpportunityIds(Lists.newArrayList(param.getOpportunityCode()))
                .get(param.getOpportunityCode())).orElse(new HashSet<>());
        queryParam.setFilterActivityRowIdList(Lists.newArrayList(filterIds));

        PageRows<ActivityOpportunityRelationDO> pageRows = activityOpportunityRepository.queryActivityOpportunityRelation(pageQuery.getPageNo(), pageQuery.getPageSize(), queryParam);
        Set<String> applyPeopleNoSet = new HashSet<>();
        List<ActivityOpportunityRelationDO> activityOpportunityRelationDOList = pageRows.getRows().stream().peek(item -> {
            applyPeopleNoSet.add(item.getApplyPeopleNo());
        }).collect(Collectors.toList());

        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);
        Map<String, String> applyPersonNameMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(applyPeopleNoSet)).getBo();
        List<ActivityOpportunityRelationVO> activityOpportunityRelationVOList = Lists.newArrayList();
        for (ActivityOpportunityRelationDO activityWithOpportunityDO : activityOpportunityRelationDOList) {
            ActivityOpportunityRelationVO relationVO = new ActivityOpportunityRelationVO();
            BeanUtils.copyProperties(activityWithOpportunityDO, relationVO);
            relationVO.setActivityTypeName(ActivityTypeEnum.getDescByType(relationVO.getActivityType()));
            relationVO.setActivityStatusName(statusMap.get(relationVO.getActivityStatus()));

            String applyPeopleName = EMPTY_STR;
            String empNo = relationVO.getApplyPeopleNo();
            if (applyPersonNameMap.containsKey(empNo)) {
                applyPeopleName = applyPersonNameMap.get(empNo);
            }
            relationVO.setApplyPeopleDesc(applyPeopleName + empNo);
            relationVO.setSubmitTime(DateUtils.convertDateToString(activityWithOpportunityDO.getSubmitTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            activityOpportunityRelationVOList.add(relationVO);
        }
        return PageRowsUtil.buildPageRow(pageRows.getCurrent(), pageRows.getPageSize(), pageRows.getTotal(), activityOpportunityRelationVOList);
    }

    /**
     * 添加或更新绑定关系
     *
     * @param request
     * @return
     */
    @Override
    public int changeBind2Opportunity(BizRequest<ChangeBind2OpportunityParam> request) {
        ChangeBind2OpportunityParam inputParam = request.getParam();
        ValidationUtils.validateMessage(inputParam);
        List<String> bindActivityRowIds = Optional.ofNullable(inputParam.getBindActivityRowIds()).orElse(new ArrayList<>());
        List<String> unbindActivityRowIds = Optional.ofNullable(inputParam.getUnbindActivityRowIds()).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(bindActivityRowIds) && CollectionUtils.isEmpty(unbindActivityRowIds)) {
            return 0;
        }
        // 不允许即绑定 又 解绑的操作
        if (CollectionUtils.isNotEmpty(bindActivityRowIds) && CollectionUtils.isNotEmpty(unbindActivityRowIds)) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, localeMessage.getMessage(BIND_AND_UNBIND_AT_THE_SAME_TIME));
        }

        // 1. 查询活动列表信息
        List<String> allActivityRowIds = Stream.concat(bindActivityRowIds.stream(), unbindActivityRowIds.stream()).distinct().collect(Collectors.toList());
        Map<String, ActivityInfoDO> activityInfoMap = Optional.ofNullable(activityInfoRepository.selectByIds(allActivityRowIds))
                .orElse(new ArrayList<>()).stream().collect(Collectors.toMap(ActivityInfoDO::getRowId, i -> i, (u, v) -> u));

        // 2. 查询当前商机编号已绑定的活动id列表
        String opportunityId = inputParam.getOpportunityCode();
        Set<String> oppIdAlreadyRelationAcIdSet = Optional.ofNullable(activityOpportunityRepository.getMapByOpportunityIds(Lists.newArrayList(opportunityId))
                .get(opportunityId)).orElse(new HashSet<>());

        List<ActivityOpportunityInfoDO> needBindActOppList = new ArrayList<>();
        List<ActivityOpportunityInfoDO> needUnBindActOppList = new ArrayList<>();
        // 只有已经绑定该商机的活动id才需要解绑
        unbindActivityRowIds.forEach(item -> {
            if (oppIdAlreadyRelationAcIdSet.contains(item) && activityInfoMap.containsKey(item)) {
                ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
                activityOpportunityInfoDO.setOpportunityId(opportunityId);
                activityOpportunityInfoDO.setActivityRowId(item);
                activityOpportunityInfoDO.setBindFlag(NumberConstant.TWO);
                activityOpportunityInfoDO.setUnbindBy(request.getEmpNo());
                activityOpportunityInfoDO.setUnbindDate(new Date());
                activityOpportunityInfoDO.setActivityUnbindStatus(activityInfoMap.get(item).getActivityStatus());
                needUnBindActOppList.add(activityOpportunityInfoDO);
            }
        });
        // 本次绑定的活动，如果之前已经绑定过该商机，直接跳过 如果活动不存在 直接跳过
        bindActivityRowIds.forEach(item -> {
            if (oppIdAlreadyRelationAcIdSet.contains(item) || !activityInfoMap.containsKey(item)) {
                return;
            }

            ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
            activityOpportunityInfoDO.setOpportunityId(opportunityId);
            activityOpportunityInfoDO.setActivityRowId(item);
            activityOpportunityInfoDO.setBindFlag(NumberConstant.ONE);
            activityOpportunityInfoDO.setActivityBindStatus(activityInfoMap.get(item).getActivityStatus());
            needBindActOppList.add(activityOpportunityInfoDO);
        });

        int updateNum = activityOpportunityRepository.batchUpdateByActIdAndOppId(needUnBindActOppList);
        int addNum = activityOpportunityRepository.insertByBatch(needBindActOppList);

        return CollectionUtils.isNotEmpty(unbindActivityRowIds) ? updateNum : addNum;
    }

    @Override
    public int changeBind2Activity(BizRequest<ChangeBind2ActivityParam> request) {
        ChangeBind2ActivityParam inputParam = request.getParam();
        ValidationUtils.validateMessage(inputParam);
        List<String> bindOpportunityCodes = Optional.ofNullable(inputParam.getBindOpportunityCodes()).orElse(new ArrayList<>());
        List<String> unbindOpportunityCodes = Optional.ofNullable(inputParam.getUnbindOpportunityCodes()).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(bindOpportunityCodes) && CollectionUtils.isEmpty(unbindOpportunityCodes)) {
            return 0;
        }
        // 不允许即绑定 又 解绑的操作
        if (CollectionUtils.isNotEmpty(bindOpportunityCodes) && CollectionUtils.isNotEmpty(unbindOpportunityCodes)) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, localeMessage.getMessage(BIND_AND_UNBIND_AT_THE_SAME_TIME));
        }

        // 1. 查询活动列表信息
        String activityRowId = inputParam.getActivityRowId();
        List<ActivityInfoDO> activityInfos = activityInfoRepository.selectByIds(Lists.newArrayList(activityRowId));
        if (CollectionUtils.isEmpty(activityInfos)) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, localeMessage.getMessage(ACTIVITY_ID_EMPTY));
        }
        ActivityInfoDO activityInfo = activityInfos.get(0);
        // 2. 查询当前活动id已绑定的商机编号列表
        List<ActivityOpportunityInfoDO> activityOpportunityRepositoryList =
                activityOpportunityRepository.getList(Lists.newArrayList(activityRowId), new ArrayList<>());
        Set<String> acIdAlreadyRelationOppIdSet = Optional.ofNullable(activityOpportunityRepositoryList).orElse(new ArrayList<>())
                .stream().map(ActivityOpportunityInfoDO::getOpportunityId).collect(Collectors.toSet());
        List<ActivityOpportunityInfoDO> needBindActOppList = new ArrayList<>();
        List<ActivityOpportunityInfoDO> needUnBindActOppList = new ArrayList<>();
        // 只有已经绑定该商机的活动id才需要解绑
        unbindOpportunityCodes.forEach(e -> {
            if (acIdAlreadyRelationOppIdSet.contains(e)) {
                ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
                activityOpportunityInfoDO.setOpportunityId(e);
                activityOpportunityInfoDO.setActivityRowId(activityRowId);
                activityOpportunityInfoDO.setBindFlag(NumberConstant.TWO);
                activityOpportunityInfoDO.setUnbindBy(request.getEmpNo());
                activityOpportunityInfoDO.setUnbindDate(new Date());
                activityOpportunityInfoDO.setActivityUnbindStatus(activityInfo.getActivityStatus());
                needUnBindActOppList.add(activityOpportunityInfoDO);
            }
        });
        // 本次绑定的商机，如果之前已经绑定过该活动则不处理
        bindOpportunityCodes.forEach(e -> {
            if (StringUtils.isBlank(e) || acIdAlreadyRelationOppIdSet.contains(e)) {
                return;
            }
            ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
            activityOpportunityInfoDO.setOpportunityId(e);
            activityOpportunityInfoDO.setActivityRowId(activityRowId);
            activityOpportunityInfoDO.setBindFlag(NumberConstant.ONE);
            activityOpportunityInfoDO.setActivityBindStatus(activityInfo.getActivityStatus());
            needBindActOppList.add(activityOpportunityInfoDO);
        });

        int updateNum = activityOpportunityRepository.batchUpdateByActIdAndOppId(needUnBindActOppList);
        int addNum = activityOpportunityRepository.insertByBatch(needBindActOppList);

        return CollectionUtils.isNotEmpty(unbindOpportunityCodes) ? updateNum : addNum;
    }

    boolean checkActivityOpportunityRelationQueryParam(ActivityOpportunityRelationParam param) {
        if (StringUtils.isBlank(param.getOpportunityCode())) {
            return false;
        }
        // 时间约束不能只传一侧
        if (StringUtils.isNotBlank(param.getStartTime()) && StringUtils.isBlank(param.getEndTime())) {
            return false;
        }
        if (StringUtils.isNotBlank(param.getEndTime()) && StringUtils.isBlank(param.getStartTime())) {
            return false;
        }

        return true;
    }

    /**
     * 通过商机编码或拓展活动id查询绑定解绑历史记录
     *
     * @param request
     * @return
     */
    @Override
    public List<ActivityOpportunityRecordVO> queryRecord(BizRequest<ActivityOpportunityQueryRecordParam> request) {
        ActivityOpportunityQueryRecordParam param = request.getParam();
        if (StringUtils.isBlank(param.getOpportunityCode()) && StringUtils.isBlank(param.getActivityRowId())) {
            return Collections.emptyList();
        }

        Set<String> operatorSet = new HashSet<>();
        List<ActivityWithOpportunityDO> activityWithOpportunityDOList = activityOpportunityRepository.queryRecord(param);
        Set<String> optCodeSet = activityWithOpportunityDOList.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getBindBy())) {
                operatorSet.add(item.getBindBy());
            }
            if (StringUtils.isNotBlank(item.getUnbindBy())) {
                operatorSet.add(item.getUnbindBy());
            }
        }).map(ActivityWithOpportunityDO::getOpportunityId).collect(Collectors.toSet());
        Map<String, String> statusMap = lookUpExtService.getLookUpMapByType(LookupConstant.LOOKUP_TYPE_ACTIVITY_STATUS);
        Map<String, String> operatorMap = hrmUserCenterSearchService.fetchPersonName(MsaRpcRequestUtil.createWithCurrentUser(operatorSet)).getBo();
        Map<String, OpportunityDTO> opportunityMap = Optional.ofNullable(opportunityCaller.queryOpportunityList(Lists.newArrayList(optCodeSet))).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(OpportunityDTO::getOptyCode, i -> i, (u, v) -> u));
        List<ActivityOpportunityRecordVO> recordVOList = Lists.newArrayList();
        for (ActivityWithOpportunityDO record : activityWithOpportunityDOList) {
            OpportunityDTO opportunityDTO = opportunityMap.getOrDefault(record.getOpportunityId(), new OpportunityDTO());
            ActivityOpportunityRecordVO bindRecordVO = new ActivityOpportunityRecordVO();
            BeanUtils.copyProperties(record, bindRecordVO);
            bindRecordVO.setOperateType(OpportunityConstant.BIND);
            bindRecordVO.setOperator(record.getBindBy());
            if (operatorMap.containsKey(record.getBindBy())) {
                bindRecordVO.setOperatorDesc(Optional.ofNullable(operatorMap.get(record.getBindBy()))
                        .orElse(StringUtils.EMPTY) + record.getBindBy());
            }
            bindRecordVO.setOperationTime(record.getBindDate());
            bindRecordVO.setActivityStatusAtOperation(record.getActivityBindStatus());
            bindRecordVO.setActivityStatusAtOperationName(statusMap.get(record.getActivityBindStatus()));
            bindRecordVO.setOptyId(opportunityDTO.getOptyId());
            bindRecordVO.setOptyCode(opportunityDTO.getOptyCode());
            bindRecordVO.setOptyName(opportunityDTO.getOptyName());
            recordVOList.add(bindRecordVO);
            // 如果绑定标识为1 则标识只有绑定记录 否则 应该生成绑定&解绑两条记录
            if (NumberConstant.ONE == record.getBindFlag()) {
                continue;
            }

            ActivityOpportunityRecordVO unbindRecordVO = new ActivityOpportunityRecordVO();
            BeanUtils.copyProperties(record, unbindRecordVO);
            unbindRecordVO.setOperateType(OpportunityConstant.UNBIND);
            unbindRecordVO.setOperator(record.getUnbindBy());
            if (operatorMap.containsKey(record.getUnbindBy())) {
                unbindRecordVO.setOperatorDesc(Optional.ofNullable(operatorMap.get(record.getUnbindBy()))
                        .orElse(StringUtils.EMPTY) + record.getUnbindBy());
            }
            unbindRecordVO.setOperationTime(record.getUnbindDate());
            unbindRecordVO.setActivityStatusAtOperation(record.getActivityUnBindStatus());
            unbindRecordVO.setActivityStatusAtOperationName(statusMap.get(record.getActivityUnBindStatus()));
            unbindRecordVO.setOptyId(opportunityDTO.getOptyId());
            unbindRecordVO.setOptyCode(opportunityDTO.getOptyCode());
            unbindRecordVO.setOptyName(opportunityDTO.getOptyName());
            recordVOList.add(unbindRecordVO);
        }
        recordVOList = recordVOList.stream()
                .sorted(Comparator.comparing(ActivityOpportunityRecordVO::getOperationTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        return recordVOList;
    }

    /**
     * 通过拓展活动id查询绑定的商机列表
     *
     * @param request
     * @return
     */
    @Override
    public List<OpportunityVO> queryBindOpportunityList(BizRequest<String> request) {
        String activityRowId = request.getParam();
        if (StringUtils.isEmpty(activityRowId)) {
            return Collections.emptyList();
        }
        ActivityOpportunityQueryRecordParam param = new ActivityOpportunityQueryRecordParam();
        param.setActivityRowId(activityRowId);
        List<ActivityWithOpportunityDO> activityWithOpportunityDOList = activityOpportunityRepository.queryRecord(param);
        // 从商机绑定解绑记录中取出状态为绑定的商机id列表
        Set<String> opportunityCodeSet = activityWithOpportunityDOList.stream().filter(o -> NumberConstant.ONE == o.getBindFlag())
                .map(ActivityWithOpportunityDO::getOpportunityId)
                .collect(Collectors.toSet());

        List<OpportunityDTO> opportunityDTOList = opportunityCaller.queryOpportunityList(Lists.newArrayList(opportunityCodeSet));
        List<OpportunityVO> opportunityVOList = Lists.newArrayList();
        for (OpportunityDTO dto : opportunityDTOList) {
            OpportunityVO vo = new OpportunityVO();
            BeanUtils.copyProperties(dto, vo);
            opportunityVOList.add(vo);
        }

        return opportunityVOList;
    }

    @Override
    public Map<String, List<ActivityOpportunityRecordVO>> getActivityStatusRecordMap(String activityRowId) {
        if (StringUtils.isEmpty(activityRowId)) {
            return new HashMap<>();
        }

        ActivityOpportunityQueryRecordParam param = new ActivityOpportunityQueryRecordParam();
        param.setActivityRowId(activityRowId);
        List<ActivityOpportunityRecordVO> activityWithOpportunityVOList = this.queryRecord(BizRequestUtil.createWithCurrentUserSecurity(param));
        if (CollectionUtils.isEmpty(activityWithOpportunityVOList)) {
            return new HashMap<>();
        }

        Map<String, List<ActivityOpportunityRecordVO>> activityStatusRecordMap = new HashMap<>();
        for (ActivityOpportunityRecordVO entity : activityWithOpportunityVOList) {
            String activityStatusAtOperation = entity.getActivityStatusAtOperation();
            if (StringUtils.isBlank(activityStatusAtOperation)) {
                continue;
            }

            String flowNode = this.getFlowNodeByActivityStatus(activityStatusAtOperation);
            List<ActivityOpportunityRecordVO> recordByFlowNodeGroupList =
                    Optional.ofNullable(activityStatusRecordMap.get(flowNode)).orElse(new ArrayList<>());
            recordByFlowNodeGroupList.add(entity);
            activityStatusRecordMap.putIfAbsent(flowNode, recordByFlowNodeGroupList);
        }
        return activityStatusRecordMap;
    }

    @Override
    public Integer batchUpdateRecord(BizRequest<List<ActivityOpportunityUpdateVO>> request) {
        List<ActivityOpportunityUpdateVO> activityOpportunityUpdateVOList = request.getParam();
        ValidationUtils.validateListMessage(activityOpportunityUpdateVOList);
        if (CollectionUtils.isEmpty(activityOpportunityUpdateVOList)) {
            return 0;
        }

        List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList = new ArrayList<>();
        activityOpportunityUpdateVOList.forEach(item -> {
            ActivityOpportunityInfoDO activityOpportunityInfoDO = new ActivityOpportunityInfoDO();
            BeanUtils.copyProperties(item, activityOpportunityInfoDO);
            activityOpportunityInfoDOList.add(activityOpportunityInfoDO);
        });

        return activityOpportunityRepository.batchUpdateByPrimaryKey(activityOpportunityInfoDOList);
    }

    String getFlowNodeByActivityStatus(String activityStatus) {
        if (ActivityStatusEnum.in(activityStatus, DRAFT)) {
            return ActivityFlowNodeEnum.LAUNCH.getCode();
        }

        if (ActivityStatusEnum.in(activityStatus, PROGRESS)) {
            return ActivityFlowNodeEnum.EXECUTE.getCode();
        }

        if (ActivityStatusEnum.in(activityStatus, FINISH, EVALUATED)) {
            return ActivityFlowNodeEnum.COMPLETE.getCode();
        }

        return StringUtils.EMPTY;
    }

}
