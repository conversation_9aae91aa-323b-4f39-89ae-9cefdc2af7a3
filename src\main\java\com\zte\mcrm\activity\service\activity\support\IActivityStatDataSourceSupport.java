package com.zte.mcrm.activity.service.activity.support;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.service.activity.param.ActivityStatDataSource;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityStatParam;

public interface IActivityStatDataSourceSupport {

    /**
     * 根据活动类型id查询信息
     * @param bizRequest
     * @return
     */
    ActivityStatDataSource getActivityStatDataSourceByOriginRowId(BizRequest<PageQuery<ActivityStatParam>> bizRequest);

    /**
     * 当前资源id的统计信息
     * @param bizRequest
     * @return
     */
    ActivityStatDataSource getVisitingSamplePointStatDataSource(BizRequest<ActivityStatParam> bizRequest);
}
