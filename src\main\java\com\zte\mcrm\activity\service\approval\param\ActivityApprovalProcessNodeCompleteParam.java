package com.zte.mcrm.activity.service.approval.param;

import com.zte.mcrm.expansion.access.vo.ApprovalTaskCompletedInfoVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 审批完成入参
 */
@Data
public class ActivityApprovalProcessNodeCompleteParam {

    /**
     * 拓展活动id
     */
    @NotNull
    private String activityRowId;


    /**
     * 审批单号-对应审批中心 taskId
     */
    @NotNull
    private String approvalFlowNo;

    /**
     * 审批人
     */
    @NotNull
    private String approveBy;


    /**
     * 审批时间
     */
    @NotNull
    private Date approveTime;

    /**
     * 审批结果Y-同意，N-拒绝，T-转交。枚举：ApproveResultEnum
     */
    private String approveResult;

    /**
     * 审批意见
     */
    private String opinion;

    public void buildCompleteOf(ApprovalTaskCompletedInfoVO param) {
        this.activityRowId = param.getBusinessId();
        this.approvalFlowNo = param.getTaskId();
        this.approveBy = param.getApprover();
        this.approveTime=param.getApprovalDate();
        this.approveResult = param.getResult();
        this.opinion = param.getOpinion();
    }
}
