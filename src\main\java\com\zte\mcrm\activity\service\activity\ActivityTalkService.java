package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.integration.talk.response.TalkAbstractResponseVO;
import com.zte.mcrm.activity.service.activity.param.TalkAbstractGenerationParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityTalkMessageBodyParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityTalkParam;
import com.zte.mcrm.activity.web.controller.activity.vo.AiActivityTalkVO;
import org.springframework.stereotype.Service;

@Service
public interface ActivityTalkService {

    AiActivityTalkVO getTalkByAiAssistant(ActivityTalkParam activityTalkParam);

    /**
     * 活动谈参转发到icenter，加入鉴权
     *
     * @param request  消息提参数
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2023/8/17 下午8:02
     */
    boolean forwardTalkToCenter(BizRequest<ActivityTalkMessageBodyParam> request);
    
    /**
     * 通过AI获取谈参摘要
     * @param request
     * @return
     */
    TalkAbstractResponseVO getTalkAbstractByAI(BizRequest<TalkAbstractGenerationParam> request);
    
    /**
     * 同步谈参附件摘要
     * @param request
     * @return
     */
    int syncTalkAbstractByAI(BizRequest<String> request);

    /**
     * 根据日程id获取日程谈参摘要
     *
     * @param request
     * @return {@link TalkAbstractResponseVO}
     * <AUTHOR>
     * @date 2025/3/2 上午11:29
     */
    BizResult<TalkAbstractResponseVO> getScheduleTalkAbstract(BizRequest<String> request);

    /**
     * 生成日程谈参摘要
     *
     * @param request
     * @return {@link TalkAbstractResponseVO}
     * <AUTHOR>
     * @date 2025/3/2 上午11:29
     */
    BizResult<Void> generateScheduleTalkAbstract(BizRequest<String> request);
}
