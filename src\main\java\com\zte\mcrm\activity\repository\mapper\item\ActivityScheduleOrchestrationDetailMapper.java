package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleOrchestrationDetailDO;

public interface ActivityScheduleOrchestrationDetailMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleOrchestrationDetailDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleOrchestrationDetailDO record);

    /**
     * query by primary key
     */
    ActivityScheduleOrchestrationDetailDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleOrchestrationDetailDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleOrchestrationDetailDO record);
}