package com.zte.mcrm.activity.repository.model.plancto;

/**
 * <AUTHOR>
 * @date 2024年12月09日15:42
 */
/* Started by AICoder, pid:09816u555165cbf14ff609ac9180ac15c015fff1 */
import lombok.Data;

import java.util.Date;

@Data
public class CtoPlanExeReportDO {
    /**
     * 主键
     */
    private String rowId;

    /**
     * CTO拓展计划ID
     */
    private String ctoPlanInfoId;

    /**
     * 年
     */
    private String ctoPlanYear;

    /**
     * 月
     */
    private String ctoPlanMonth;

    /**
     * 统计范围起始
     */
    private Date scopeStart;

    /**
     * 统计范围截止
     */
    private Date scopeEnd;

    /**
     * 专家教会汇总规划详表
     */
    private String reportSavant;

    /**
     * 核心领导交流汇总规划详表
     */
    private String reportLeader;

    /**
     * 拓展完成数量
     */
    private String reportActivity;

    /**
     * 名单盘活表
     */
    private String reportDetail;

    /**
     * 是否已推送
     */
    private String hasPush;

    /**
     * 执行状态。BooleanEnum
     */
    private String exeStatus;

    /**
     * 待执行时间
     */
    private Date exeWaitTime;

    /**
     * 报表推送接收人
     */
    private String receiver;

    /**
     * 执行完毕时间
     */
    private Date exeFinishTime;

    /**
     * 云文档报表key
     */
    private String fileKey;

    /**
     * 记录创建人
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date creationDate;

    /**
     * 记录最近修改人
     */
    private String lastUpdatedBy;

    /**
     * 记录最近修改时间
     */
    private Date lastUpdateDate;

    /**
     * 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum
     */
    private String enabledFlag;
}

/* Ended by AICoder, pid:09816u555165cbf14ff609ac9180ac15c015fff1 */
