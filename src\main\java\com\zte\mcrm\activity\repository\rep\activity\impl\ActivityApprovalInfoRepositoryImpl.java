package com.zte.mcrm.activity.repository.rep.activity.impl;

import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.activity.ActivityApprovalInfoExtMapper;
import com.zte.mcrm.activity.repository.model.activity.ActivityApprovalInfoDO;
import com.zte.mcrm.activity.repository.rep.activity.ActivityApprovalInfoRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActivityApprovalInfoRepository.java
 * @Description
 * @createTime 2023年05月13日 14:11:00
 */
@Service
public class ActivityApprovalInfoRepositoryImpl implements ActivityApprovalInfoRepository {
    @Autowired
    private ActivityApprovalInfoExtMapper extMapper;

    @Autowired
    private IKeyIdService keyIdService;
    @Override
    public int insertSelective(ActivityApprovalInfoDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }



    @Override
    public int updateByPrimaryKeySelective(ActivityApprovalInfoDO record) {
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据活动Id更新
     * @param record    记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/4 14:25
     */
    @Override
    public int updateByActivityRowIdSelective(ActivityApprovalInfoDO record) {
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(BizRequestUtil.createWithCurrentUser().getEmpNo());
        return extMapper.updateByActivityRowIdSelective(record);
    }

    @Override
    public List<ActivityApprovalInfoDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    @Override
    public ActivityApprovalInfoDO queryByActivityRowId(String activityRowId) {
        List<ActivityApprovalInfoDO> activityApprovalInfoList = queryAllByActivityRowId(activityRowId);
        return CollectionUtils.isEmpty(activityApprovalInfoList)?null:activityApprovalInfoList.iterator().next();
    }

    /**
     * 根据审批信息BusinessId获取当前数据

     * @param approvalNo
     * @return
     *
     */
    @Override
    public ActivityApprovalInfoDO queryByApprovalNo(String approvalNo) {
        List<ActivityApprovalInfoDO> resultList = StringUtils.isBlank(approvalNo) ? Collections.emptyList()
                : extMapper.queryAllByApprovalNo(approvalNo);
        return CollectionUtils.isEmpty(resultList) ? null : resultList.iterator().next();
    }
}
