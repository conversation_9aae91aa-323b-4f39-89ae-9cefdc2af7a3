package com.zte.mcrm.activity.service.approval.vo;

import com.zte.mcrm.activity.repository.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * 审批节点对象
 *
 * <AUTHOR> 10317843
 * @date 2023/05/22
 */
@Getter
@Setter
@ToString
public class ActivityApprovalProcessQueryVO extends BaseEntity {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String rowId;

    /**
     * 拓展活动id
     */
    @ApiModelProperty("拓展活动id")
    private String activityRowId;

    /**
     * 审批过程类型。业务审批、合规审批。枚举：ProcessTypeEnum
     */
    @ApiModelProperty("审批过程类型。业务审批、合规审批。枚举：ProcessTypeEnum")
    private String processType;

    /**
     * 审批过程状态，枚举：ProcessStatusEnum
     */
    @ApiModelProperty("审批过程状态，枚举：ProcessStatusEnum ")
    private String processStatus;

    /**
     * 审批备注
     */
    @ApiModelProperty("审批备注")
    private String remark;

    @ApiModelProperty("审批节点详情数据")
    private List<ActivityApprovalProcessNodeQueryVO> processNodeQueryVOList;

}