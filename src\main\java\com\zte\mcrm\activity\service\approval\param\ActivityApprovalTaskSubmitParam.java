package com.zte.mcrm.activity.service.approval.param;

import com.zte.iss.approval.sdk.bean.AttachedFileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 活动审批节点提交对象
 * @createTime 2023年05月16日 21:03:00
 */
@Data
public class ActivityApprovalTaskSubmitParam {

    @NotNull
    @ApiModelProperty("任务id")
    private String taskId;

    @NotNull
    @ApiModelProperty("审批是否同意:Y/N")
    private String result;

    @ApiModelProperty("审批意见")
    @NotNull
    private String opinion;

    @ApiModelProperty("审批附件")
    private List<AttachedFileDTO> attachedFiles;
}