package com.zte.mcrm.activity.repository.model.reception;

import java.util.Date;

/**
 * table:t_cust_reception_lcm_customer -- 
 */
public class CustReceptionLcmCustomerDO {
    /** 主键 */
    private String rowId;

    /** t_cust_expansion_header#id */
    private String headerId;

    /** 关联对应客户t_cust_expansion_custom#id */
    private String expansionCustomId;

    /** 一行客户信息-客户和联系人结对ID */
    private String pairRowId;

    /** 客户编码 */
    private String customerCode;

    /** 客户名称 */
    private String customerName;

    /** 国家名称 */
    private String countryName;

    /** 国家地区编码 */
    private String countryCode;

    /** 国家二位码 */
    private String countryBitCode;

    /** 城市名称 */
    private String cityName;

    /** 街道 */
    private String street;

    /** LCM扫描单号 */
    private String lcmScanNo;

    /** LCM扫描状态.枚举：LcmScanStatusEnum */
    private String lcmScanStatus;

    /** 描受限制结果 */
    private String sanctionedParty;

    /** 受限制主体结果来源，枚举：SanctionedSourceEnum。SS,LCM */
    private String sanctionedSource;

    /** 记录创建人 */
    private String createdBy;

    /** 记录创建时间 */
    private Date creationDate;

    /** 记录最近修改人 */
    private String lastUpdatedBy;

    /** 记录最近修改时间 */
    private Date lastUpdateDate;

    /** 记录有效标识。Y-有效，N-无效。枚举：BooleanEnum */
    private String enabledFlag;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getHeaderId() {
        return headerId;
    }

    public void setHeaderId(String headerId) {
        this.headerId = headerId == null ? null : headerId.trim();
    }

    public String getExpansionCustomId() {
        return expansionCustomId;
    }

    public void setExpansionCustomId(String expansionCustomId) {
        this.expansionCustomId = expansionCustomId == null ? null : expansionCustomId.trim();
    }

    public String getPairRowId() {
        return pairRowId;
    }

    public void setPairRowId(String pairRowId) {
        this.pairRowId = pairRowId == null ? null : pairRowId.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName == null ? null : countryName.trim();
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode == null ? null : countryCode.trim();
    }

    public String getCountryBitCode() {
        return countryBitCode;
    }

    public void setCountryBitCode(String countryBitCode) {
        this.countryBitCode = countryBitCode == null ? null : countryBitCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street == null ? null : street.trim();
    }

    public String getLcmScanNo() {
        return lcmScanNo;
    }

    public void setLcmScanNo(String lcmScanNo) {
        this.lcmScanNo = lcmScanNo == null ? null : lcmScanNo.trim();
    }

    public String getLcmScanStatus() {
        return lcmScanStatus;
    }

    public void setLcmScanStatus(String lcmScanStatus) {
        this.lcmScanStatus = lcmScanStatus == null ? null : lcmScanStatus.trim();
    }

    public String getSanctionedParty() {
        return sanctionedParty;
    }

    public void setSanctionedParty(String sanctionedParty) {
        this.sanctionedParty = sanctionedParty == null ? null : sanctionedParty.trim();
    }

    public String getSanctionedSource() {
        return sanctionedSource;
    }

    public void setSanctionedSource(String sanctionedSource) {
        this.sanctionedSource = sanctionedSource == null ? null : sanctionedSource.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }
}