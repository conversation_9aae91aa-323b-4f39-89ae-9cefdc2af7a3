package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.controller.activity.vo.CustPeopleInfoVO;
import com.zte.mcrm.activity.web.controller.activity.vo.CustUnitInfoVO;
import com.zte.mcrm.activity.web.controller.baseinfo.param.CheckInternalParam;

import java.util.List;

/**
 * 客户活动客户-相关API
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
public interface ActivityCustomerService {

    /**
     * 判断活动是否可操作接口
     * @param request   请求
     * @return java.lang.String
     * <AUTHOR>
     * date: 2023/8/29 21:03
     */
    String operatorCheck(BizRequest<String> request);

    /**
     * 通过活动Id判断活动是否可操作, 含有冻结/合并的客户不可进行操作
     * @param activityRowId 活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/8/30 10:48
     */
    String operatorCheckByActivityRowId(String activityRowId);

    /**
     * 操作校验并更新客户信息
     * @param listCust  客户列表
     * @param acType  活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    List<CustUnitInfoVO> operatorCheckAndFillCustomerInfo(List<CustUnitInfoVO> listCust, String acType);

    /**
     * 操作校验并更新客户信息
     * @param listCust  客户列表
     * @param listCustPeopleInfo 客户联系人列表
     * @param acType 活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    void operatorCheckAndFillCustomerInfo(List<CustUnitInfoVO> listCust, List<CustPeopleInfoVO> listCustPeopleInfo, String acType);

    /**
     * 更新客户信息
     * @param listCust  客户列表
     * @param acType  活动类型
     * @return java.util.List<CustUnitInfoVO>
     * <AUTHOR>
     * date: 2023/9/18 14:41
     */
    List<CustUnitInfoVO> fillCustomerInfo(List<CustUnitInfoVO> listCust, String acType);

    /**
     * 校验客户是否为公司/子公司
     * @param request   请求
     * @return java.lang.Boolean
     * <AUTHOR>
     * date: 2023/10/9 11:09
     */
    Boolean checkInternal(BizRequest<CheckInternalParam> request);

}
