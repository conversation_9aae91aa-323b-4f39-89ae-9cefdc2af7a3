package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 字符工具
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {


    /**
     * 拼接字符串
     * <pre>
     *  如果列表中都是空或空白字符[null, ""]，则返回默认值allNoneDefVal
     *  如果列表中存在不是空或空白字符[null, "", "a"]，则空白符用noneReplaceStr(如：N)替换，并将列表中用分割符splitStr(如：/)拼接。 如返回 N/N/a
     * </pre>
     *
     * @param list           字符列表
     * @param splitStr       字符拼接分割符
     * @param noneReplaceStr 空字符替换符号
     * @param allNoneDefVal  如果字符列表都空，返回默认字符
     * @return
     */
    public static String append(List<String> list, String splitStr, String noneReplaceStr, String allNoneDefVal) {
        if (CollectionUtils.isEmpty(list)) {
            return allNoneDefVal;
        }

        boolean none = true;
        for (String s : list) {
            if (isNotBlank(s)) {
                none = false;
                break;
            }
        }

        if (none) {
            return allNoneDefVal;
        }

        StringBuilder sb = new StringBuilder();
        int size = list.size();
        for (int i = 0; i < size; i++) {
            String str = list.get(i);

            sb.append(isNotBlank(str) ? str : noneReplaceStr);

            if (i != (size - 1)) {
                sb.append(splitStr);
            }
        }

        return sb.toString();
    }

    /**
     * 对字符串进行trim操作并设置（比如：对某些属性取前后空格）
     *
     * @param get 数据提供者
     * @param set 数据消费者（消费进行trim后的字符数据）
     */
    public static void trim(Supplier<String> get, Consumer<String> set) {
        String filed = get.get();
        if (filed != null) {
            filed = filed.trim();
        }
        set.accept(filed);
    }

    /**
     * 自适应字符串长度（如果字符串长度超过length，则自动缩减长度为length）
     *
     * @param str 字符串
     * @param length 调整后最大长度（如果字符串本身小于改长度，则不调整）
     * @return 返回字符串本身，或者调整长度后的字符串
     */
    public static String adjustStrLength(String str, int length) {
        if (str == null) {
            return null;
        }
        if(str.length() > length) {
            str = str.substring(NumberConstant.ZERO, length);
        }

        return str;
    }

    /**
     * 字符串都是由数字组成
     *
     * @param str
     * @return 字符串为空返回false，包含任何非0-9的返回false。全部都是0-9字符的返回true
     */
    public static boolean isAllDigitString(String str) {
        boolean flag = isNotBlank(str);
        if (isNotBlank(str)) {
            for (char c : str.toCharArray()) {
                if (!(c >= '0' && c <= '9')) {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 如果字符串str为空，则返回默认字符串defVal
     *
     * @param str    源字符串
     * @param defVal 默认字符串
     * @return
     */
    public static String getDefValIfBlank(String str, String defVal) {
        return isNotBlank(str) ? str : defVal;
    }

    /**
     * 对于数字型字符串，去掉前面的0
     *
     * @param str    源字符串
     * @return
     */
    public static String removeLeadingZero(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        if (isNumeric(str)) {
            return str.replaceFirst("^0+", "");
        } else {
            return str;
        }
    }
}
