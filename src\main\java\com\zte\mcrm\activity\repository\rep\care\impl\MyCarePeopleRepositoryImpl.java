package com.zte.mcrm.activity.repository.rep.care.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.care.MyCarePeopleExtMapper;
import com.zte.mcrm.activity.repository.model.care.MyCarePeopleDO;
import com.zte.mcrm.activity.repository.rep.care.MyCarePeopleRepository;
import com.zte.mcrm.activity.web.controller.resource.param.MyCarePeopleParam;
import com.zte.mcrm.common.util.ObjectUtils;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 我关注的人
 *
 * <AUTHOR>
 */
@Component
public class MyCarePeopleRepositoryImpl implements MyCarePeopleRepository {
    @Resource
    private MyCarePeopleExtMapper myCarePeopleExtMapper;
    @Autowired
    private IKeyIdService keyIdService;


    @Override
    public int insertSelective(List<MyCarePeopleDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return NumberConstant.ZERO;
        }

        for (MyCarePeopleDO record : recordList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }

            myCarePeopleExtMapper.insertSelective(record);
        }

        return recordList.size();
    }

    @Override
    public int updateByPrimaryKeySelective(MyCarePeopleDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }

        record.setLastUpdateDate(new Date());
        return myCarePeopleExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<MyCarePeopleDO> queryAllMyCarePeople(MyCarePeopleParam param) {
        return ObjectUtils.isEmpty(param) ? Collections.emptyList() : myCarePeopleExtMapper.queryAllMyCarePeople(param);
    }
}
