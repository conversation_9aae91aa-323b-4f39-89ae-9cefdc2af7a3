package com.zte.mcrm.activity.integration.accountinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.model.MsaRpcResponse;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.MsaRpcResponseUtil;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.integration.accountinfo.dto.LatestVisitCustomerDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerBaseInfoDTO;
import com.zte.mcrm.activity.integration.accountinfo.dto.OutCustomerDetailDTO;
import com.zte.mcrm.activity.integration.accountinfo.param.CustomerDetailQueryParamDTO;
import com.zte.mcrm.common.enums.ServiceAliasEnum;
import com.zte.mcrm.isearch.enums.RequestTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户信息查询服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomerInfoQueryService {


    /**
     * 最近浏览的客户信息
     * @param request
     *
     * @return
     */
    public MsaRpcResponse<PageRows<LatestVisitCustomerDTO>> latestVisitCustomer(MsaRpcRequest<Integer> request) {
        if (request.getBody() == null) {
            return MsaRpcResponseUtil.successRes(PageRowsUtil.buildPageRow(NumberConstant.ONE, NumberConstant.ONE, NumberConstant.ZERO, Collections.emptyList()));
        }
        String url = "/customer/manage/latestVisitCustomer?showSize=" + request.getBody();

        try {
            String json = MicroServiceRestUtil.invokeService(ServiceAliasEnum.ACCOUNT.getNormalizedName(), ServiceAliasEnum.ACCOUNT.getNormalizedVersion(),
                    RequestTypeEnum.GET.getValue(), url, null, request.fetchHeaderMap());
            return new MsaRpcResponse<>(JSON.parseObject(json, new TypeReference<ServiceData<PageRows<LatestVisitCustomerDTO>>>() {
            }));
        } catch (Exception e) {
            log.error("调用" + ServiceAliasEnum.ACCOUNT.getNormalizedName() + url + "异常", e);
            return new MsaRpcResponse<>(MsaRpcResponse.OUT_SERVERERROR_CODE, "out system error", "",
                    PageRowsUtil.buildPageRow(NumberConstant.ONE, request.getBody(), NumberConstant.ZERO, Collections.emptyList()));
        }
    }

    /**
     * 根据【客户编码】精确查询（基本信息）
     *
     * @param request
     * @return
     */
    public MsaRpcResponse<Map<String, OutCustomerBaseInfoDTO>> queryBaseInfoByBatchCode(MsaRpcRequest<Set<String>> request) {
        return queryBaseInfoByBatch(request, NumberConstant.TWO);
    }

    /**
     * 根据【客户名称】精确查询（基本信息）
     *
     * @param request
     * @return
     */
    public MsaRpcResponse<Map<String, OutCustomerBaseInfoDTO>> queryBaseInfoByBatchName(MsaRpcRequest<Set<String>> request) {
        return queryBaseInfoByBatch(request, NumberConstant.ONE);
    }

    /**
     * 精确查询客户（基本信息）
     *
     * @param request
     * @param type    1-客户名称，2-客户编码
     * @return
     */
    MsaRpcResponse<Map<String, OutCustomerBaseInfoDTO>> queryBaseInfoByBatch(MsaRpcRequest<Set<String>> request, int type) {
        if (CollectionUtils.isEmpty(request.getBody())) {
            return MsaRpcResponseUtil.successRes(Collections.emptyMap());
        }

        List<String> conditionList = new ArrayList<>(request.getBody());
        Map<String, OutCustomerDetailDTO> resMap = new HashMap<>(conditionList.size());
        List<Future<Map<String, OutCustomerDetailDTO>>> dataList = new ArrayList<>(conditionList.size());

        for (int i = 0; i < conditionList.size(); i = i + NumberConstant.HUNDRED) {
            int end = i + NumberConstant.HUNDRED;
            if (end > conditionList.size()) {
                end = conditionList.size();
            }

            List<String> batch = conditionList.subList(i, end);
            CustomerDetailQueryParamDTO param = new CustomerDetailQueryParamDTO();
            if (type == NumberConstant.ONE) {
                param.setNameList(batch);
            } else {
                param.setCustomerCodeList(batch);
            }

            dataList.add(ThreadManager.submitToParallel(() -> queryCustomerDetail(MsaRpcRequestUtil.trans(request, names -> param)).getBo()));
        }
        for (Future<Map<String, OutCustomerDetailDTO>> f : dataList) {
            try {
                resMap.putAll(f.get());
            } catch (Exception e) {
                log.error("查询客户信息时发生多线程异常", e);
                // ignore 正常来说接口不会报错，即便报错，这里数据查不到，查不到由业务决定如何操作
            }
        }

        return MsaRpcResponseUtil.successRes(resMap.values().stream().collect(Collectors.toMap(
                OutCustomerDetailDTO::getCustomerCode, OutCustomerDetailDTO::getBaseInfo, (v1, v2) -> v2)));
    }


    /**
     * 查询客户详情信息（每次最大100）
     *
     * @param request
     * @return
     */
    MsaRpcResponse<Map<String, OutCustomerDetailDTO>> queryCustomerDetail(MsaRpcRequest<CustomerDetailQueryParamDTO> request) {
        String url = "/api/customer/batchDetail";

        try {
            String json = MicroServiceRestUtil.invokeService(ServiceAliasEnum.ACCOUNT.getNormalizedName(), ServiceAliasEnum.ACCOUNT.getNormalizedVersion(),
                    RequestTypeEnum.POST.getValue(), url, JSON.toJSONString(request.getBody()), request.fetchHeaderMap());
            log.info("批量查询客户详情请求结果：" + json);

            ServiceData<List<OutCustomerDetailDTO>> res = JSON.parseObject(json, new TypeReference<ServiceData<List<OutCustomerDetailDTO>>>() {
            });

            MsaRpcResponse<Map<String, OutCustomerDetailDTO>> response = new MsaRpcResponse<>(res, list ->
                    list.stream().collect(Collectors.toMap(OutCustomerDetailDTO::getCustomerCode, Function.identity())));

            if (response.getBo() == null) {
                response.setBo(Collections.emptyMap());
            }
            return response;
        } catch (Exception e) {
            log.error("调用" + ServiceAliasEnum.ACCOUNT.getNormalizedName() + url + "异常", e);
            return new MsaRpcResponse<>(MsaRpcResponse.OUT_SERVERERROR_CODE, "out system error", "", Collections.emptyMap());
        }
    }

}
