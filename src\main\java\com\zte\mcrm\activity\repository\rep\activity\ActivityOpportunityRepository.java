package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationQueryParam;
import com.zte.mcrm.activity.repository.model.activity.ActivityWithOpportunityDO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityOpportunityQueryParam;
import com.zte.mcrm.activity.web.controller.activity.param.ActivityOpportunityQueryRecordParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ActivityOpportunityRepository {

    /***
     * <p>
     * 根据条件查询商机关联的活动列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/4 下午7:56
     * @param param 查询入参
     * @return java.util.List<com.zte.mcrm.activity.repository.model.activity.ActivityWithOpportunityDO>
     */
    List<ActivityWithOpportunityDO> getActivityWithOpportunityList(ActivityOpportunityQueryParam param);

    /***
     * <p>
     * 分页查询商机关联的活动列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/4 下午7:57
     * @param pageQuery 分页查询入参
     * @return com.zte.mcrm.activity.common.model.PageRows<com.zte.mcrm.activity.repository.model.activity.ActivityWithOpportunityDO>
     */
    PageRows<ActivityWithOpportunityDO> queryActivityOpportunityInfo(PageQuery<ActivityOpportunityQueryParam> pageQuery);

    /***
     * <p>
     * 分页查询当前登录人关联的活动列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/4 下午7:58
     * @param pageNo 当前页
     * @param pageSize 页大小
     * @param queryParam 查询入参
     * @return com.zte.mcrm.activity.common.model.PageRows<com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityRelationDO>
     */
    PageRows<ActivityOpportunityRelationDO> queryActivityOpportunityRelation(int pageNo, int pageSize, ActivityOpportunityRelationQueryParam queryParam);

    /***
     * <p>
     * 根据活动id列表查询活动id关联的商机id集合 Map
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/6 上午9:25
     * @param activityRowIds 活动id列表
     * @return java.util.Map<java.lang.String,java.util.Set<java.lang.String>>
     */
    Map<String, Set<String>> getMapByActivityRowIds(List<String> activityRowIds);

    /***
     * <p>
     * 根据商机编号列表获取关联的活动id集合 Map
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/6 上午9:25
     * @param opportunityIds 商机编号列表
     * @return java.util.Map<java.lang.String,java.util.Set<java.lang.String>>
     */
    Map<String, Set<String>> getMapByOpportunityIds(List<String> opportunityIds);

    /***
     * <p>
     * 根据商机id集合 和 活动id集合 获取绑定的记录列表
     *
     * </p>
     * <AUTHOR>
     * @since 2024/6/6 上午9:27
     * @param activityRowIds 活动id列表
     * @param opportunityIds 商机id列表
     * @return java.util.List<com.zte.mcrm.activity.repository.model.activity.ActivityOpportunityInfoDO>
     */
    List<ActivityOpportunityInfoDO> getList(List<String> activityRowIds, List<String> opportunityIds);

    /**
     * 批量插入绑定关系
     *
     * @param activityOpportunityInfoDOList
     * @return
     */
    int insertByBatch(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList);

    /**
     * 批量更新绑定关系
     *
     * @param activityOpportunityInfoDOList
     * @return
     */
    int batchUpdateByActIdAndOppId(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList);

    /**
     * 通过商机编码或拓展活动id查询绑定解绑历史记录
     *
     * @param param
     * @return
     */
    List<ActivityWithOpportunityDO> queryRecord(ActivityOpportunityQueryRecordParam param);

    /**
     * 通过主键修改字段值
     *
     * @param activityOpportunityInfoDOList 主表列表
     * @return 修改成功数量
     */
    int batchUpdateByPrimaryKey(List<ActivityOpportunityInfoDO> activityOpportunityInfoDOList);
}
