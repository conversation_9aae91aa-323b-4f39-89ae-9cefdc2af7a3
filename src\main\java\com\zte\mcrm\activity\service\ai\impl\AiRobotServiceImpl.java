package com.zte.mcrm.activity.service.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.integration.ai.AiRobotStudioService;
import com.zte.mcrm.activity.integration.ai.IcenterService;
import com.zte.mcrm.activity.service.ai.AiRobotService;
import com.zte.mcrm.activity.web.controller.ai.vo.IcenterRobotVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataQueryVO;
import com.zte.mcrm.activity.web.controller.ai.vo.MarketDataRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: 汤踊10285568
 * @date: 2024/7/5 9:33
 */
@Service
@Slf4j
public class AiRobotServiceImpl implements AiRobotService {

    @Autowired
    private AiRobotStudioService studioService;
    @Autowired
    private IcenterService icenterService;

    /**
     * 调用studio接口，回答问题
     *
     * @param req
     * @author: 汤踊10285568
     * @date: 2024/7/5 9:31
     */
    @Override
    public String aiRobotForIcenter(IcenterRobotVO req) {
        if (Objects.isNull(req.getData())) {
            return StringUtils.EMPTY;
        }
        log.info("接收icenter群机器人消息:{}", JSONObject.toJSONString(req));
        String studioResult = studioService.getAiRobotResult(req.getData());
        if(StringUtils.isNotBlank(studioResult)){
            icenterService.sendICenterMsg(req.getData(), studioResult);
        }
        return studioResult;
    }

    @Override
    public String getMarketDataInfo(MarketDataQueryVO queryVO) {
        if (StringUtils.isBlank(queryVO.getQuestion())) {
            return null;
        }

        return studioService.getMarketDataInfo(queryVO);
    }
}
