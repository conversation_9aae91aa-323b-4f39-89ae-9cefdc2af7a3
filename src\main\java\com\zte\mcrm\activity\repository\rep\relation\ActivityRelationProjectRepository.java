package com.zte.mcrm.activity.repository.rep.relation;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.relation.ActivityRelationProjectDO;

import java.util.List;
import java.util.Map;

/**
 * 活动关联的项目信息
 *
 * <AUTHOR>
 */
public interface ActivityRelationProjectRepository {

    /**
     * 批量插入数据
     *
     * @param recordList
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityRelationProjectDO> recordList);


    /**
     * 插入单条数据
     *
     * @param projectDO
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int insertSelective(ActivityRelationProjectDO projectDO);


    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityRelationProjectDO record);

    /**
     * 查询活动相关的项目信息
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivityRelationProjectDO> queryAllProjectForActivity(String activityRowId);

    /**
     * 查询活动相关的项目信息
     * @param activityRowIds
     * @return
     */
    Map<String, List<ActivityRelationProjectDO>> queryAllByActivityRowId(List<String> activityRowIds);

    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);
}
