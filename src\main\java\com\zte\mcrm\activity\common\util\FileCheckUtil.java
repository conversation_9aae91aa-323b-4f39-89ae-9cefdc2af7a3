package com.zte.mcrm.activity.common.util;

import com.google.common.base.Joiner;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.common.config.FileConfig;
import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.FileConstant;
import com.zte.mcrm.activity.common.constant.I18nConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.FileTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

/**
 * 文件校验
 *
 * <AUTHOR>
 * @date 2023/12/7 下午4:17
 */
public class FileCheckUtil {

    private FileCheckUtil() {
    }

    /**
     * 附件上传校验
     *
     * @param file 附件
     * <AUTHOR>
     * @date 2023/12/7 下午5:01
     */
    public static void uploadFileCheck(MultipartFile file) {
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_CAN_NOT_EMPTY);
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)
                || fileName.length() > FileConstant.FILE_NAME_MAX_LENGTH) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_NAME_LENGTH_ERROR, NumberConstant.ONE, FileConstant.FILE_NAME_MAX_LENGTH);
        }
        List<String> uploadLimitTypeList = FileConfig.getUploadLimitTypeList();
        String fileType = FilenameUtils.getExtension(fileName);
        if (uploadLimitTypeList.stream().noneMatch(type -> StringUtils.equalsIgnoreCase(fileType, type))) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_TYPE_LIMIT, Joiner.on(CharacterConstant.COMMA).join(uploadLimitTypeList));
        }
    }

    /**
     * 是否属于文件类型
     *
     * @param file      文件
     * @param fileTypes 所属文件类型列表（如：txt、xls等文件）
     * @return
     */
    public static boolean belongFileType(MultipartFile file, String... fileTypes) {
        if (file.isEmpty() || StringUtils.isBlank(file.getOriginalFilename())) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, I18nConstant.FILE_CAN_NOT_EMPTY);
        }
        String fileType = FileTypeEnum.fetchFileType(file.getOriginalFilename());

        return StringUtils.equalsAnyIgnoreCase(fileType, fileTypes);

    }

}
