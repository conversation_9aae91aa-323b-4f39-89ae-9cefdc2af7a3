package com.zte.mcrm.activity.repository.rep.banner.impl;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.banner.BannerBusinessTypeEnum;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.mapper.banner.BannerInfoMapper;
import com.zte.mcrm.activity.repository.mapper.banner.BannerInfoNewExtMapper;
import com.zte.mcrm.activity.repository.model.banner.BannerInfoDO;
import com.zte.mcrm.activity.repository.model.banner.BannerInfoNewDO;
import com.zte.mcrm.activity.repository.rep.banner.BannerInfoRepository;
import com.zte.mcrm.activity.service.banner.util.BannerBusinessUtil;
import com.zte.mcrm.keyid.service.IKeyIdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BannerInfoRepositoryImpl implements BannerInfoRepository {
    @Autowired
    private IKeyIdService iKeyIdService;
    @Autowired
    private BannerInfoMapper bannerInfoMapper;
    @Autowired
    private BannerInfoNewExtMapper bannerInfoNewExtMapper;


    @Override
    public int insertSelective(BannerInfoNewDO record) {
        if (StringUtils.isBlank(record.getBannerId())) {
            record.setBannerId(iKeyIdService.getKeyId());
        }

        record.setEnabledFlag(BooleanEnum.Y.getCode());
        Date now = new Date();
        record.setCreationDate(now);
        record.setLastUpdateDate(now);
        return bannerInfoNewExtMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(BannerInfoNewDO record) {
        if (StringUtils.isBlank(record.getBannerId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        return bannerInfoNewExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<BannerInfoNewDO> fetchProcessBannerList(BannerBusinessTypeEnum bizType) {
        if (bizType == null) {
            return Collections.emptyList();
        }

        return bannerInfoNewExtMapper.fetchProcessBannerList(bizType.getCode(), new Date());
    }

    @Override
    public BannerInfoDO get(String bannerId) {
        return bannerInfoMapper.get(bannerId);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation= Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public int delete(String bannerId){
        BannerInfoDO entity = new BannerInfoDO();
        entity.setBannerId(bannerId);
        entity.setEnabledFlag(CharacterConstant.N);
        return this.update(entity);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public String insert(BannerInfoDO entity){
        String emp = BannerBusinessUtil.getEmpNoOrSystem();
        Date now = new Date();
        entity.setCreatedBy(emp);
        entity.setLastUpdatedBy(emp);
        entity.setCreationDate(now);
        entity.setLastUpdateDate(now);
        if (StringUtils.isBlank(entity.getBannerId())) {
            entity.setBannerId(iKeyIdService.getKeyId());
        }
        int insertCount = bannerInfoMapper.insert(entity);
        log.info("===insert banner info, insert count = {}", insertCount);
        return entity.getBannerId();
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public int update(BannerInfoDO entity){
        entity.setLastUpdatedBy(BannerBusinessUtil.getEmpNoOrSystem());
        entity.setLastUpdateDate(new Date());
        return bannerInfoMapper.update(entity);
    }

    @Override
    public List<BannerInfoDO> getList(BannerInfoDO entity){
        return bannerInfoMapper.getList(entity);
    }

    @Override
    public long getCount(Map<String, Object> map){
        return bannerInfoMapper.getCount(map);
    }

    @Override
    public List<BannerInfoDO> getPage(Map<String, Object> map){
        return bannerInfoMapper.getPage(map);
    }
}
