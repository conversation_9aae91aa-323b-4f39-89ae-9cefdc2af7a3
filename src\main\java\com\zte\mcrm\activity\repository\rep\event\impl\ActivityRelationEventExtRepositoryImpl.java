package com.zte.mcrm.activity.repository.rep.event.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.util.BizRequestUtil;
import com.zte.mcrm.activity.repository.mapper.event.ActivityRelationEventExtMapper;
import com.zte.mcrm.activity.repository.model.event.ActivityRelationEventDO;
import com.zte.mcrm.activity.repository.rep.event.ActivityRelationEventExtRepository;
import com.zte.mcrm.activity.web.controller.event.vo.ActivityRelationEventQuery;
import com.zte.mcrm.adapter.common.HeadersProperties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.zte.mcrm.activity.common.constant.NumberConstant.ZERO;


/**
 * 客户活动关联事件 服务类
 * <AUTHOR>
 * @date 2023/06/29
 */
@Service
public class ActivityRelationEventExtRepositoryImpl implements ActivityRelationEventExtRepository {

    @Autowired
    private ActivityRelationEventExtMapper activityRelationEventExtDao ;

    /**
     * 批量更新
     *
     * @param updateList 待更新列表
     * @return: int
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    @Override
    public int batchUpdateByPrimaryKey(List<ActivityRelationEventDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        String updateBy = BizRequestUtil.createWithCurrentUser().getEmpNo();
        for (ActivityRelationEventDO entity : updateList) {
            entity.setLastUpdatedBy(updateBy);
            entity.setLastUpdateDate(new Date());
        }
        return activityRelationEventExtDao.batchUpdateByPrimaryKey(updateList);
    }

    /**
     * 按条件查询
     *
     * @param query 按条件查询
     * @return: List<ActivityRelationEventDO>
     * @author: 唐佳乐10333830
     * @date: 2023/5/21 13:44
     */
    @Override
    public List<ActivityRelationEventDO> getListByQuery(ActivityRelationEventQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        return activityRelationEventExtDao.getListByQuery(query);
    }


    /**
     * 根据活动Id批次删除
     *
     * @param operator          用户工号
     * @param activityRowIds 活动Id列表
     * @return int
     * <AUTHOR>
     * date: 2023/8/30 15:47
     */
    @Override
    public int deleteByActivityIds(String operator, List<String> activityRowIds) {
        return CollectionUtils.isEmpty(activityRowIds) ? 0 : activityRelationEventExtDao.softDeleteByActivityIds(operator, activityRowIds);
    }

    /**
     * 动态更新
     * @param record
     * @return
     * <AUTHOR>
     * @date 2024/2/23
     */
    @Override
    public int updateByPrimaryKeySelective(ActivityRelationEventDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return ZERO;
        }
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        record.setLastUpdateDate(new Date());
        return activityRelationEventExtDao.updateByPrimaryKeySelective(record);
    }
}
