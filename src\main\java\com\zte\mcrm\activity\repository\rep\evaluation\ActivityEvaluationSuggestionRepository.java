package com.zte.mcrm.activity.repository.rep.evaluation;

import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationSuggestionDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 活动评估建议
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityEvaluationSuggestionRepository {

    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityEvaluationSuggestionDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityEvaluationSuggestionDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityEvaluationSuggestionDO> queryAllByActivityRowId(String activityRowId);
}