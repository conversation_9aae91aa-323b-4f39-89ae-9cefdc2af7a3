package com.zte.mcrm.activity.common.thread;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
public class TaskExecutorThreadFactory implements ThreadFactory {
    private static final ThreadGroup THREAD_GROUP = new ThreadGroup("TaskExecutor");

    private final AtomicLong threadNumber = new AtomicLong(1L);
    private final String namePrefix;


    public TaskExecutorThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
    }

    public Thread newThread(Runnable runnable) {
        Thread thread = new Thread(THREAD_GROUP, runnable, THREAD_GROUP.getName() + "-" + this.namePrefix + "-" + this.threadNumber.getAndIncrement());
        thread.setPriority(5);

        return thread;
    }

}
