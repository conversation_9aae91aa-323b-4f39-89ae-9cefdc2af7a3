package com.zte.mcrm.activity.repository.rep.resource.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.common.util.PageRowsUtil;
import com.zte.mcrm.activity.repository.mapper.resource.ResourceCtoPersonExtMapper;
import com.zte.mcrm.activity.repository.model.resource.ResourceCtoPersonDO;
import com.zte.mcrm.activity.repository.rep.resource.ResourceCtoPersonRepository;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceCtoPersonParam;
import com.zte.mcrm.adapter.EmployeeAdapter;
import com.zte.mcrm.adapter.bo.EmployeeBO;
import com.zte.mcrm.adapter.common.HeadersProperties;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-11
 */
@Component
public class ResourceCtoPersonRepositoryImpl implements ResourceCtoPersonRepository {
    @Autowired
    private ResourceCtoPersonExtMapper resourceCtoPersonExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Autowired
    private EmployeeAdapter employeeAdapter;

    @Override
    public int batchInsert(List<ResourceCtoPersonDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberConstant.ZERO;
        }
        Date now = new Date();
        for (ResourceCtoPersonDO cto : list) {
            if (StringUtils.isBlank(cto.getRowId())) {
                cto.setRowId(keyIdService.getKeyId());
            }
            cto.setCreationDate(now);
            cto.setLastUpdateDate(now);
        }

        return resourceCtoPersonExtMapper.batchInsert(list);
    }
    /**
     * 查询有效的数据
     * @return
     */
    @Override
    public List<ResourceCtoPersonDO> listValidResourceCtoPerson() {
        ResourceCtoPersonParam resourceCtoPersonParam = new ResourceCtoPersonParam();
        resourceCtoPersonParam.setEnabledStatus(BooleanEnum.Y.getCode());
        return resourceCtoPersonExtMapper.getCtoPersonListByProductDirection(resourceCtoPersonParam);
    }


    @Override
    public int updateByPrimaryKeySelective(ResourceCtoPersonDO record) {
        if (record == null || StringUtils.isBlank(record.getRowId())) {
            return NumberConstant.ZERO;
        }
        record.setLastUpdateDate(new Date());
        record.setLastUpdatedBy(HeadersProperties.getXEmpNo());
        return resourceCtoPersonExtMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateEnableStatusByPrimaryKeySelective(List<ResourceCtoPersonDO> ctoPersonDOS) {
        if (CollectionUtils.isEmpty(ctoPersonDOS)) {
            return NumberConstant.ZERO;
        }
        return resourceCtoPersonExtMapper.batchUpdate(ctoPersonDOS);
    }


    /**
     * 根据专家ID批量查询
     *
     * @param employeeNos 专家员工编号
     * @return {@link List< ResourceCtoPersonDO>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:04
     */
    @Override
    public List<ResourceCtoPersonDO> selectByEmployeeNos(List<String> employeeNos) {
        if (CollectionUtils.isEmpty(employeeNos)) {
            return Collections.emptyList();
        }
        ResourceCtoPersonParam ctoPersonParam = new ResourceCtoPersonParam();
        ctoPersonParam.setEmployeeNoList(employeeNos);
        return resourceCtoPersonExtMapper.getCtoPersonListByProductDirection(ctoPersonParam);
    }

    @Override
    public PageRows<ResourceCtoPersonDO> getCtoPersonPageByProducts(PageQuery<ResourceCtoPersonParam> param) {
        if (!param.validatePage()) {
            return PageRowsUtil.buildEmptyPage(param.getPageNo(), param.getPageSize());
        }

        PageInfo<ResourceCtoPersonDO> pageInfo = PageHelper.startPage(param.getPageNo(), param.getPageSize(), param.withCount())
                .doSelectPageInfo(() -> resourceCtoPersonExtMapper.getCtoPersonListByProductDirection(param.getParam()));
        return PageRowsUtil.buildPageRow(pageInfo);
    }

    @Override
    public List<ResourceCtoPersonDO> getCtoPersonListByProductDirection(ResourceCtoPersonParam ctoPersonParam) {
        return resourceCtoPersonExtMapper.getCtoPersonListByProductDirection(ctoPersonParam);
    }
}
