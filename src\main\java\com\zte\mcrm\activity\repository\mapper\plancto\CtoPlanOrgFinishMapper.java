package com.zte.mcrm.activity.repository.mapper.plancto;
/* Started by AICoder, pid:i8f6b15a3bg963f14c4f08f9200a0b266b735ff5 */
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanOrgFinishDO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CtoPlanOrgFinishMapper {

    int batchInsert(@Param("list") List<CtoPlanOrgFinishDO> list);

    int insertSelective(CtoPlanOrgFinishDO record);

    int batchUpdate(@Param("list") List<CtoPlanOrgFinishDO> list);

    int updateByPrimaryKeySelective(CtoPlanOrgFinishDO record);

    List<CtoPlanOrgFinishDO> selectByPrimaries(@Param("list") List<String> primaries);


}
/* Ended by AICoder, pid:i8f6b15a3bg963f14c4f08f9200a0b266b735ff5 */