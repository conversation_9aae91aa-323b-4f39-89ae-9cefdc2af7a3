package com.zte.mcrm.activity.service.activity.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.cache.loader.HrOrgDataLoader;
import com.zte.mcrm.activity.common.cache.model.HrOrgDataModel;
import com.zte.mcrm.activity.common.config.InOneConfig;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.enums.CompanyOrgSourceEnum;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.dicapi.dto.DictLanguageDTO;
import com.zte.mcrm.activity.integration.usercenter.dto.EmployeeInfoDTO;
import com.zte.mcrm.activity.service.activity.ActivityTenantInfoService;
import com.zte.mcrm.activity.service.dict.impl.DictServiceImpl;
import com.zte.mcrm.activity.web.controller.activity.vo.TenantInfoVO;
import com.zte.mcrm.adapter.UserCenterPgAdapter;
import com.zte.mcrm.adapter.common.HeadersProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.zte.mcrm.activity.common.constant.DictConstant.*;
import static com.zte.mcrm.activity.common.constant.DictConstant.JOINT_STOCK_CORPORATE_NO;

/**
 * <AUTHOR>
 * @date ：2025/3/21 10:46
 * @description ：适配租户及法人实现类
 */
@Log4j2
@Service
public class ActivityTenantInfoServiceImpl implements ActivityTenantInfoService {

    @Autowired
    private InOneConfig inOneConfig;

    @Autowired
    HrOrgDataLoader hrOrgDataLoader;

    @Autowired
    private DictServiceImpl dictService;

    @Autowired
    private UserCenterPgAdapter userCenterPgAdapter;

    /**
     * 通过部门编码适配法人及租户信息
     *
     * @param orgNo 部门/组织编码
     * @return 法人及租户信息
     */
    @Override
    public TenantInfoVO fetchTenantByOrgNo(String orgNo) {
        TenantInfoVO tenantInfoVO = new TenantInfoVO();
        Map<String, String> headerMap = MsaRpcRequestUtil.createWithCurrentUser().fetchHeaderMap();
        headerMap.put(RequestHeaderConstant.IN_ONE_APPCODE, inOneConfig.getCallerAppCode());
        List<HrOrgDataModel> orgList = hrOrgDataLoader.queryOrgInfo(headerMap, CompanyOrgSourceEnum.ZTE_AND_SUB, Lists.newArrayList(orgNo));
        String tenantId = orgList.stream()
                .findFirst()
                .map(HrOrgDataModel::getTenantId)
                .orElse(JOINT_STOCK_TENANT);
        String companyId = orgList.stream()
                .findFirst()
                .map(HrOrgDataModel::getCompanyId)
                .orElse(JOINT_STOCK_COMPANY_ID);
        DictLanguageDTO dictByTypeAndKey = dictService.getDictByTypeAndKey(ORG_TO_CORPORATE, companyId);
        String corporateNo = Optional.ofNullable(dictByTypeAndKey).map(DictLanguageDTO::getDictValue).orElse(JOINT_STOCK_CORPORATE_NO);
        tenantInfoVO.setTenantId(tenantId);
        tenantInfoVO.setCorporateNo(corporateNo);
        return tenantInfoVO;
    }

    /**
     * 通过员工短工号适配法人及租户信息
     *
     * @return 法人及租户信息
     */
    @Override
    public TenantInfoVO fetchTenantByEmpNo() {
        String empNo = HeadersProperties.getXEmpNo();
        TenantInfoVO tenantInfoVO = new TenantInfoVO();
        // 查询人事中心获取人员基本信息
        Map<String, EmployeeInfoDTO> empInfoDetailMap = userCenterPgAdapter.getEmpInfoDetailsByManagerId(Lists.newArrayList(empNo));
        EmployeeInfoDTO employeeInfoDTO = empInfoDetailMap.getOrDefault(empNo, new EmployeeInfoDTO());
        String companyId = Optional.ofNullable(employeeInfoDTO.getCompanyID()).orElse(JOINT_STOCK_COMPANY_ID);
        // 查询数据字典获取公司对应的法人编码和租户id
        DictLanguageDTO corporateDict = dictService.getDictByTypeAndKey(ORG_TO_CORPORATE, companyId);
        String corporateNo = Optional.ofNullable(corporateDict).map(DictLanguageDTO::getDictValue).orElse(JOINT_STOCK_CORPORATE_NO);
        DictLanguageDTO tenantDict = dictService.getDictByTypeAndKey(ORG_TO_TENANT, companyId);
        String tenantId = Optional.ofNullable(tenantDict).map(DictLanguageDTO::getDictValue).orElse(JOINT_STOCK_TENANT);
        tenantInfoVO.setTenantId(tenantId);
        tenantInfoVO.setCorporateNo(corporateNo);
        return tenantInfoVO;
    }
}
