package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryRdcDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitySummaryRdcExtMapper extends ActivitySummaryRdcMapper {
    /**
     * 查询活动相关原始需求信息
     * @param activityIds
     * @return
     */
    List<ActivitySummaryRdcDO> queryAllRdcByActivityRowId(@Param("activityIds") List<String> activityIds);


}