package com.zte.mcrm.activity.repository.rep.activity;

import com.zte.mcrm.activity.repository.model.activity.ActivityCommunicationDirectionDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 客户交流方向信息
 * @createTime 2023年05月13日 14:11:00
 */
public interface ActivityCommunicationDirectionRepository {


    /**
     * 插入单条数据
     *
     * @param record
     * @return
     */
    int insertSelective(ActivityCommunicationDirectionDO record);


    /**
     * 更新选定字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ActivityCommunicationDirectionDO record);

    /**
     * 根据活动id获取当前数据集合
     *
     * @param activityRowId
     * @return
     */
    List<ActivityCommunicationDirectionDO> queryAllByActivityRowId(String activityRowId);

    /**
     * 根据活动id批量获取当前数据集合
     * @param activityRowIds
     * @return
     */
    List<ActivityCommunicationDirectionDO> queryAllByActivityRowIds(List<String> activityRowIds);

    /**
     * 查询活动相关交流方向
     * @param activityRowIds
     * @return
     */
    Map<String, List<ActivityCommunicationDirectionDO>> queryAllByActivityRowId(List<String> activityRowIds);


    /**
     * 批量插入
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<ActivityCommunicationDirectionDO> recordList);

    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 添加活动关联交流方向信息（如果没有主键，自动生成）
     *
     * @param list
     */
    int batchInsert(List<ActivityCommunicationDirectionDO> list);
    /**
     * 添加活动关联交流方向信息（如果没有主键，自动生成）
     *
     * @param list
     */
    void batchUpdateCommunication(List<ActivityCommunicationDirectionDO> list);

    /**
     * 查询所有包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityCommunicationDirectionDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityCommunicationDirectionDO> queryAllActivityWithNotEnable(String activityRowId);

}