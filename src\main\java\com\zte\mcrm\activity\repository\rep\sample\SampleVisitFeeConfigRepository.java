package com.zte.mcrm.activity.repository.rep.sample;

import com.zte.mcrm.activity.repository.model.sample.SampleVisitFeeConfigDO;

import java.util.List;
import java.util.Map;


/**
 * 样板参观费用配置 服务接口类
 * <AUTHOR>
 * @date 2024/01/30  
 */
public interface SampleVisitFeeConfigRepository {


    /**
     * 查询列表
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/01/30
     */
	List<SampleVisitFeeConfigDO> queryFeeConfigAll();

    /**
     * 获取符合条件的实体列表
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/01/30
     */
    List<SampleVisitFeeConfigDO> queryFeeConfigSelectiveList(Map<String,Object> map);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2024/01/30
     */
	int insert(SampleVisitFeeConfigDO entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2024/01/30
     */
	int update(SampleVisitFeeConfigDO entity);

    /**
     *
     * @param rowIds
     * @return
     */
    int softDeleteByBatch(List<String> rowIds, String empNo);
}