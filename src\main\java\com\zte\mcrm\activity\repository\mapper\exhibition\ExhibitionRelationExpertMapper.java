package com.zte.mcrm.activity.repository.mapper.exhibition;

import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionRelationExpertDO;
import java.util.List;

public interface ExhibitionRelationExpertMapper {
    /**
     * all field insert
     */
    int insert(ExhibitionRelationExpertDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ExhibitionRelationExpertDO record);

    /**
     * query by primary key
     */
    ExhibitionRelationExpertDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ExhibitionRelationExpertDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ExhibitionRelationExpertDO record);
}