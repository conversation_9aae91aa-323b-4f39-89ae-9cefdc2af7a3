package com.zte.mcrm.activity.repository.rep.resource;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.resource.ResourceCtoPersonDO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.resource.param.ResourceCtoPersonParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-11
 */
public interface ResourceCtoPersonRepository {
    /**
     * 插入数据
     *
     * @param list
     */
    int batchInsert(@Param("list") List<ResourceCtoPersonDO> list);

    /**
     * 动态更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ResourceCtoPersonDO record);

    /**
     * 查询有效的数据
     * @return
     */
    List<ResourceCtoPersonDO> listValidResourceCtoPerson();


    /**
     * 修改产品线专家启用状态
     * @param ctoPersonDOS 专家表id 启用状态 Y-启用；N-不启用；
     * @return
     */
    int updateEnableStatusByPrimaryKeySelective(List<ResourceCtoPersonDO> ctoPersonDOS);

    /**
     * 根据专家ID批量查询
     *
     * @param employeeNos 专家员工编号
     * @return {@link List< ResourceCtoPersonDO>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:04
     */
    List<ResourceCtoPersonDO> selectByEmployeeNos(@Param("employeeNos") List<String> employeeNos);

    PageRows<ResourceCtoPersonDO> getCtoPersonPageByProducts(PageQuery<ResourceCtoPersonParam> pageQuery);

    List<ResourceCtoPersonDO> getCtoPersonListByProductDirection(ResourceCtoPersonParam ctoPersonParam);
}
