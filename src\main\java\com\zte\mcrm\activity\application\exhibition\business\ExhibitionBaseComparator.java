package com.zte.mcrm.activity.application.exhibition.business;

import java.util.Comparator;

import org.apache.commons.lang3.StringUtils;

import com.zte.mcrm.adapter.dto.ExhibitionAnalysisDrillDownDTO;

/**
 * 展会分析 -mto比较器
 * <AUTHOR>
 *
 */
public class ExhibitionBaseComparator implements Comparator<ExhibitionAnalysisDrillDownDTO> {

    @Override
    public int compare(ExhibitionAnalysisDrillDownDTO o1, ExhibitionAnalysisDrillDownDTO o2) {
    	int compare = Integer.compare(o1.getHighLevelCnt(), o2.getHighLevelCnt());
    	if(compare != 0){
    		return compare*(-1);
    	}
    	compare = Integer.compare(o1.getDeepAcCnt(), o2.getDeepAcCnt());
    	if(compare != 0){
    		return compare*(-1);
    	}
    	
        return StringUtils.compare(o1.getIndexDimName(),o2.getIndexDimName());
    }
}
