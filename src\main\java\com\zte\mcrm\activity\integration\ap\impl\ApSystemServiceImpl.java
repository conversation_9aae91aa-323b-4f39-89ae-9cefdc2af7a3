package com.zte.mcrm.activity.integration.ap.impl;

import com.google.common.collect.Lists;
import com.zte.mcrm.activity.common.model.MsaRpcRequest;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.integration.ap.ApSystemService;
import com.zte.mcrm.activity.integration.ap.IApSystemService;
import com.zte.mcrm.activity.integration.ap.dto.ApTaskDTO;
import com.zte.mcrm.activity.integration.ap.param.ApQueryParam;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryApExtMapper;
import com.zte.mcrm.activity.repository.mapper.summary.ActivitySummaryExtMapper;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryApDO;
import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryDO;
import com.zte.mcrm.custcomm.access.dao.CustCommunicateApIssueDao;
import com.zte.mcrm.custcomm.access.vo.ApTaskVO;
import com.zte.mcrm.keyid.service.IKeyIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.zte.mcrm.activity.integration.ap.constant.ApSystemConstant;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/6/26 15:03
 * @description ：老活动AP信息同步service
 */

@Service
@Slf4j
public class ApSystemServiceImpl implements IApSystemService {

    @Autowired
    private CustCommunicateApIssueDao custCommunicateApIssueDao;

    @Autowired
    private ApSystemService apSystemService;

    @Autowired
    private ActivitySummaryExtMapper activitySummaryExtMapper;

    @Autowired
    private ActivitySummaryApExtMapper activitySummaryApExtMapper;

    @Autowired
    private IKeyIdService iKeyIdService;


    /**
     * 交流活动sysCode
     */
    @Value("${activity.ap.sysCode}")
    private String activityApSysCode;

    /**
     * 交流活动typeId
     */
    @Value("${activity.ap.typeId}")
    private String activityApTypeId;

    /**
     * 拓展活动sysCode
     */
    @Value("${expansion.ap.sysCode}")
    private String expansionApSysCode;

    /**
     * 拓展活动typeId
     */
    @Value("${expansion.ap.typeId}")
    private String expansionApTypeId;


    @Override
    public int initOldAp() {
        int total = 0;
        total += handlerActivity();
        total += handlerExpansion();
        return total;
    }


    private int handlerActivity() {
        //先获取总数
        int total = custCommunicateApIssueDao.totalActivityCount();
        if (total <= 0) {
            return 0;
        }
        log.warn("同步老的交流活动的Ap信息开始");
        int page = total % ApSystemConstant.PAGE_SIZE == 0 ? total / ApSystemConstant.PAGE_SIZE : total / ApSystemConstant.PAGE_SIZE + 1;
        int result = 0;
        for (int i = 1; i <= page; i++) {
            List<String> headerIds = custCommunicateApIssueDao.getActivityHeaderIdByType((i - 1) * ApSystemConstant.PAGE_SIZE, ApSystemConstant.PAGE_SIZE);
            result = handlerDate(result, headerIds, ApSystemConstant.ACTIVITY_NAME);
        }
        log.warn("同步老的交流活动AP信息总的成功条数：{}", result);
        return result;
    }


    private int handlerExpansion() {
        //先获取总数
        int total = custCommunicateApIssueDao.totalExpansionCount();
        if (total <= 0) {
            return 0;
        }
        log.warn("同步老的拓展活动的Ap信息开始");
        int page = total % ApSystemConstant.PAGE_SIZE == 0 ? total / ApSystemConstant.PAGE_SIZE : total / ApSystemConstant.PAGE_SIZE + 1;
        int result = 0;
        for (int i = 1; i <= page; i++) {
            List<String> headerIds = custCommunicateApIssueDao.getExpansionHeaderIdByType((i - 1) * ApSystemConstant.PAGE_SIZE, ApSystemConstant.PAGE_SIZE);
            result = handlerDate(result, headerIds, ApSystemConstant.EXPANSION_NAME);
        }
        log.warn("同步老的拓展活动AP信息总的成功条数：{}", result);
        return result;
    }

    /**
     * 公共方法
     *
     * @param result    条数
     * @param headerIds 处理的活动ID
     * @param typeName  活动类型名称
     * @return result
     */
    private int handlerDate(int result, List<String> headerIds, String typeName) {
        //现根据headerId去找activity_summary  roW_ID
        List<ActivitySummaryDO> activitySummaryDOS = activitySummaryExtMapper.queryAllByActivityRowId(headerIds);
        if (CollectionUtils.isEmpty(activitySummaryDOS)) {
            return result;
        }
        //只取匹配到活动纪要的id
        List<String> ids = activitySummaryDOS.stream().map(ActivitySummaryDO::getActivityRowId).collect(Collectors.toList());
        Map<String, String> summaryMap;
        for (List<String> headerId : Lists.partition(ids, ApSystemConstant.BATCH_MAX)) {
            summaryMap = activitySummaryDOS.stream().filter(x -> headerId.contains(x.getActivityRowId()))
                    .collect(Collectors.toMap(ActivitySummaryDO::getActivityRowId,
                            ActivitySummaryDO::getRowId));
            //组装参数去查指定headerId的ap信息
            MsaRpcRequest<ApQueryParam> apQueryParam = buildParam(true, headerId);
            try {
                ApTaskDTO apInfoList = apSystemService.getApInfoList(apQueryParam);
                if (null == apInfoList || CollectionUtils.isEmpty(apInfoList.getDataList())) {
                    continue;
                }
                //组装ap信息  activity_row_id + ap_no
                List<ActivitySummaryApDO> dos = assemblyApMessage(headerId, summaryMap, apInfoList.getDataList());
                //还要过滤在activity_summary_ap中存在的数据  activity_row_id + ap_no
                List<ActivitySummaryApDO> existDos = custCommunicateApIssueDao.getExistHeaderIds(dos);
                if (CollectionUtils.isNotEmpty(existDos)) {
                    /* Started by AICoder, pid:49de06a3fde647519ba73db71fe2c200 */
                    // 创建一个Set来存储已存在的activityRowId和apNo的组合，以便快速查找
                    Set<String> existingCombinations = existDos.stream()
                            .map(existDo -> existDo.getActivityRowId() + "_" + existDo.getApNo())
                            .collect(Collectors.toSet());
                     // 过滤dos列表，移除与已存在记录匹配的项
                    dos = dos.stream()
                            .filter(currentDo -> !existingCombinations.contains(currentDo.getActivityRowId() + "_" + currentDo.getApNo()))
                            .collect(Collectors.toList());
                    /* Ended by AICoder, pid:49de06a3fde647519ba73db71fe2c200 */
                }
                if (CollectionUtils.isEmpty(dos)) {
                    continue;
                }
                int insert = activitySummaryApExtMapper.batchInsert(dos);
                log.warn("同步老的{}AP信息成功条数：{}", typeName, insert);
                result += insert;
            } catch (Exception e) {
                log.warn("同步老的{}AP信息失败,失败的headerIds为{}", typeName, headerId.toString());
            }
        }
        return result;
    }


    /**
     * 组装查询AP信息的参数
     *
     * @param type     区分是拓展活动还是交流活动
     * @param eventIds 头IDs
     * @return 查询参数
     */
    private MsaRpcRequest<ApQueryParam> buildParam(Boolean type, List<String> eventIds) {
        ApQueryParam apQueryParam = new ApQueryParam();
        //true为拓展活动
        if (type) {
            apQueryParam.setSystemCode(expansionApSysCode);
            apQueryParam.setTypeId(expansionApTypeId);
        } else {
            //交流活动
            apQueryParam.setSystemCode(activityApSysCode);
            apQueryParam.setTypeId(activityApTypeId);
        }
        apQueryParam.setEventIds(eventIds.toArray(new String[0]));
        //分批处理
        return MsaRpcRequestUtil.createWithCurrentUser(apQueryParam);
    }


    /**
     * 组装AP数据信息
     *
     * @param headerIds  头ID
     * @param summaryMap headerId去找activity_summary  roW_ID
     * @param dataList   ap信息
     */
    /* Started by AICoder, pid:75366aa990434d70950c3096b3db6c50 */
    private List<ActivitySummaryApDO> assemblyApMessage(List<String> headerIds,
                                                        Map<String, String> summaryMap,
                                                        List<ApTaskVO> dataList) {
        Map<String, List<ApTaskVO>> collect = dataList.stream()
                .collect(Collectors.groupingBy(ApTaskVO::getEventId));
        Date date = new Date();
        return headerIds.stream()
                .flatMap(headerId -> {
                    String rowId = summaryMap.get(headerId);
                    List<ApTaskVO> vos = Optional.ofNullable(collect.get(headerId)).orElse(Collections.emptyList());
                    return vos.stream()
                            .map(vo -> buildActivitySummaryApDO(rowId, vo, date));
                })
                .collect(Collectors.toList());
    }

    private ActivitySummaryApDO buildActivitySummaryApDO(String rowId, ApTaskVO vo, Date date) {
        ActivitySummaryApDO activitySummaryApDO = new ActivitySummaryApDO();
        BeanUtils.copyProperties(vo, activitySummaryApDO);
        activitySummaryApDO.setRowId(iKeyIdService.getKeyId());
        activitySummaryApDO.setActivityRowId(vo.getEventId());
        activitySummaryApDO.setActivitySummaryRowId(rowId);
        activitySummaryApDO.setApLightStatus(String.valueOf(vo.getLightStatus()));
        activitySummaryApDO.setApResponsible(vo.getResponsiblePersionNo());
        activitySummaryApDO.setApType(vo.getFlowTemplateId());
        activitySummaryApDO.setCreationDate(date);
        activitySummaryApDO.setLastUpdateDate(date);
        activitySummaryApDO.setLastUpdatedBy("");
        activitySummaryApDO.setCreatedBy("");
        return activitySummaryApDO;
    }
    /* Ended by AICoder, pid:75366aa990434d70950c3096b3db6c50 */

}
