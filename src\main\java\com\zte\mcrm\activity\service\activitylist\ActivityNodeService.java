package com.zte.mcrm.activity.service.activitylist;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.activity.ActivityStatusLifecycleDO;
import com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 10333830
 * @date 2023-09-01 16:47
 **/
public interface ActivityNodeService {


    /**
     * 获取启动节点
     * @param statusToMap   状态Map
     * @param activityInfoDO    活动信息
     * @param peopleMap 人员信息
     * @return com.zte.mcrm.activity.service.activitylist.vo.ActivityFlowInfoVO
     * <AUTHOR>
     * date: 2023/9/1 10:14
     */
    ActivityFlowInfoVO getLaunchNode(Map<String, List<ActivityStatusLifecycleDO>> statusToMap,
                                     ActivityInfoDO activityInfoDO,
                                     Map<String, String> peopleMap);

    /**
     * 获取资源申请节点
     * @param flowInfoVOS   节点列表
     * @param statusToMap   生命周期Map
     * @param peopleMap     人员信息
     * @param infoDO        活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 9:59
     */
    void getRequestNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap,
                        Map<String, String> peopleMap, ActivityInfoDO infoDO);

    /**
     * 获取审批节点
     * @param flowInfoVOS   节点列表
     * @param statusToMap   生命周期Map
     * @param infoDO        活动信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 9:59
     */
    void getApprovalNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, ActivityInfoDO infoDO);

    /**
     * 获取执行节点
     * @param flowInfoVOS   节点列表
     * @param activityStatus    活动状态
     * @param statusToMap   生命周期Map
     * @param peopleMap     人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:02
     */
    void getExecuteNode(List<ActivityFlowInfoVO> flowInfoVOS, String activityStatus, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap);

    /**
     * 获取取消节点
     * @param flowInfoVOS   节点列表
     * @param statusToMap   生命周期Map
     * @param peopleMap     人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:03
     */
    void getCancelNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap);

    /**
     * 获取活动完成节点
     * @param flowInfoVOS   节点列表
     * @param statusToMap   生命周期
     * @param acRowId       活动Id
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:04
     */
    void getCompleteNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, String acRowId);

    /**
     * 获取作废节点
     * @param flowInfoVOS   节点列表
     * @param statusToMap   生命周期Map
     * @param peopleMap     人员信息
     * @return void
     * <AUTHOR>
     * date: 2023/9/2 10:12
     */
    void getInvalidateNode(List<ActivityFlowInfoVO> flowInfoVOS, Map<String, List<ActivityStatusLifecycleDO>> statusToMap, Map<String, String> peopleMap);
}
