package com.zte.mcrm.activity.repository.mapper.resource;

import com.zte.mcrm.activity.service.resource.model.ExpertQuery;
import com.zte.mcrm.activity.repository.model.resource.ResourceBizExpertDO;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ExpertSearchParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ResourceBizExpertExtMapper extends ResourceBizExpertMapper {

    /**
     * 根据专家ID批量查询
     *
     * @param employeeNos 专家员工编号
     * @return {@link List< ResourceBizExpertDO>}
     * <AUTHOR>
     * @date 2023/5/18 下午10:04
     */
    List<ResourceBizExpertDO> selectByEmployeeNos(@Param("employeeNos") List<String> employeeNos);

    List<ResourceBizExpertDO> selectByPrimaryKeys(List<String> rowIds);

    List<ResourceBizExpertDO> getList(Map<String, Object> map);

    int batchUpdateByPrimaryKeySelective(Map<String, Object> map);

    /**
     * 根据交流方向统计专家数
     * @return key:direction,num
     */
    List<HashMap<String, Object>> summaryExpertWithDirection();

    /**
     * 分页查询
     *
     * @param param
     * @return {@link List< ResourceBizExpertDO>}
     * <AUTHOR>
     * @date 2023/5/19 下午5:47
     */
    List<ResourceBizExpertDO> selectParticipantsPage(ExpertSearchParam param);

    List<ResourceBizExpertDO> getExpertPageByConditions(ExpertQuery param);

    /**
     * 获取非领导身份的专家工号，用于数据迁移
     * @return
     */
    List<String> getNonLeaderExpertCodes();

}