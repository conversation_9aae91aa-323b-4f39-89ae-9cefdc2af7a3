package com.zte.mcrm.activity.service.activity;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.integration.igpt.dto.PushParamDTO;
import com.zte.mcrm.activity.service.summary.dto.SummaryPushContentDTO;

/**
 * <AUTHOR>
 * @date 2025/3/5 16:52
 */
public interface ActivityToIGPTService {
     /**
      * 推送展会纪要信息-iGPT
      *
      * @param info
      * @return
      */
     ServiceData sendIGPT(SummaryPushContentDTO info);


     /**
      * 推送领导展会会议纪要
      *
      * @param param
      * @return
      */
     ServiceData pushData(PushParamDTO param);

}
