package com.zte.mcrm.activity.application.project.impl;

import com.google.common.collect.Maps;
import com.zte.mcrm.activity.application.project.ActivityProjectSearchAppService;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.convert.ActivityProjectConvert;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityProjectSearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityProjectVO;
import com.zte.mcrm.adapter.RdcAdapter;
import com.zte.mcrm.adapter.vo.RdcReturnVO;
import com.zte.mcrm.custcomm.business.ICustCommService;
import com.zte.mcrm.custcomm.ui.model.ActivityProjectRequestBO;
import com.zte.mcrm.isearch.model.dto.deal.ActivityProjectResultDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 客户活动项目查询
 *
 * <AUTHOR>
 * @date 2023/5/28 下午2:44
 */
@Service
public class ActivityProjectSearchAppServiceImpl implements ActivityProjectSearchAppService {

    @Autowired
    @Lazy
    private ICustCommService custCommService;

    @Autowired
    private RdcAdapter rdcAdapter;

    /**
     * 查询项目
     *
     * @param pageQuery
     * @return {@link List <  ActivityProjectVO >}
     * <AUTHOR>
     * @date 2023/5/28 下午2:58
     */
    @Override
    public List<ActivityProjectVO> listProject(PageQuery<ActivityProjectSearchParam> pageQuery) {
        ActivityProjectSearchParam param = pageQuery.getParam();
        if (!pageQuery.validatePage()) {
            return Collections.emptyList();
        }
        return fetchSalesProject(param);
    }

    /**
     * 获取营销项目
     *
     * @param param
     * @return {@link List< ActivityProjectVO>}
     * <AUTHOR>
     * @date 2023/5/28 下午3:22
     */
    private List<ActivityProjectVO> fetchSalesProject(ActivityProjectSearchParam param) {
        if (CollectionUtils.isEmpty(param.getCustNos())) {
            return Collections.emptyList();
        }
        ActivityProjectRequestBO searchParam = new ActivityProjectRequestBO();
        searchParam.setCustNos(param.getCustNos());
        searchParam.setKeyWord(param.getProjectName());
        List<ActivityProjectResultDTO> projectList = custCommService.getProjectByCustNoWithoutDefault(searchParam);
        return ActivityProjectConvert.convert2ActivityProjectVO(projectList);
    }

    /**
     * 获取RDC项目
     *
     * @param pageQuery
     * @return {@link List< ActivityProjectVO>}
     * <AUTHOR>
     * @date 2023/5/28 下午3:23
     */
    private List<ActivityProjectVO> fetchRdcProject(PageQuery<ActivityProjectSearchParam> pageQuery) {
        ActivityProjectSearchParam param = pageQuery.getParam();
        if (StringUtils.isBlank(param.getDeptCode())) {
            return Collections.emptyList();
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectName", StringUtils.isBlank(param.getProjectName()) ? null : param.getProjectName());
        map.put("deptCode", param.getDeptCode());
        map.put("pageNum", pageQuery.getPageNo());
        map.put("pageSize", pageQuery.getPageSize());

        RdcReturnVO rdcReturnVO = rdcAdapter.getRdcProject(map);
        return ActivityProjectConvert.convert2ActivityProjectVO(rdcReturnVO);
    }
}
