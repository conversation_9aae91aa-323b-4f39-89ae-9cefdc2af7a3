package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.BizResult;
import com.zte.mcrm.activity.web.controller.activity.param.CustomerLatestActivityParam;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityInfoVO;

/**
 * 活动历史信息查询
 *
 * <AUTHOR>
 */
public interface ActivityHistoryQueryService {

    /**
     * 查询最近某客户参加的活动信息()
     *
     * @param request
     * @return
     */
    BizResult<ActivityInfoVO> custLatestActivity(BizRequest<CustomerLatestActivityParam> request);


}
