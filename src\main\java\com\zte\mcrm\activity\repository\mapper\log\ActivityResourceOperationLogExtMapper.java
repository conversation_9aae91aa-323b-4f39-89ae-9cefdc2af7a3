package com.zte.mcrm.activity.repository.mapper.log;

import com.zte.mcrm.activity.repository.model.log.ActivityResourceOperationLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/* Started by AICoder, pid:oe9bbs2b24qe1931444e087fd0ec931436711330 */
@Mapper
public interface ActivityResourceOperationLogExtMapper extends ActivityResourceOperationLogMapper {
    // 可以在这里添加自定义的查询方法等

    /**
     * 根据业务id批量查询操作日志
     *
     * @param bizRelatedIdList  业务id
     * @param bizType           业务类型
     * @return {@link List< ActivityResourceOperationLogDO>}
     * <AUTHOR>
     * @date 2024/11/13 下午5:20
     */
    List<ActivityResourceOperationLogDO> selectByBizRelatedIdList(@Param("bizRelatedIdList") List<String> bizRelatedIdList,
                                                                  @Param("bizType") String bizType);

    /**
     * 根据业务对象id查询操作日志
     *
     * @param bizRelatedId 业务对象id
     * @return 活动资源操作日志列表
     */
    List<ActivityResourceOperationLogDO> selectByBizRelatedId(@Param("bizRelatedId") String bizRelatedId);
}

/* Ended by AICoder, pid:oe9bbs2b24qe1931444e087fd0ec931436711330 */