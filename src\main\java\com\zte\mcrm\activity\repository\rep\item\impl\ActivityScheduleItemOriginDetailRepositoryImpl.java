package com.zte.mcrm.activity.repository.rep.item.impl;

import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.repository.mapper.item.ActivityScheduleItemOriginDetailExtMapper;
import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginDetailDO;
import com.zte.mcrm.activity.repository.rep.item.ActivityScheduleItemOriginDetailRepository;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ActivityScheduleItemOriginDetailRepositoryImpl implements ActivityScheduleItemOriginDetailRepository {

    @Autowired
    private ActivityScheduleItemOriginDetailExtMapper activityScheduleItemOriginDetailExtMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int batchInsert(List<ActivityScheduleItemOriginDetailDO> scheduleItemList) {
        if (CollectionUtils.isEmpty(scheduleItemList)) {
            return NumberConstant.ZERO;
        }

        for (ActivityScheduleItemOriginDetailDO record : scheduleItemList) {
            if (StringUtils.isBlank(record.getRowId())) {
                record.setRowId(keyIdService.getKeyId());
            }
        }

        return activityScheduleItemOriginDetailExtMapper.batchInsert(scheduleItemList);
    }

    @Override
    public Map<String, List<ActivityScheduleItemOriginDetailDO>> getRelationScheduleItemOriginDetails(List<String> scheduleItemOriginVersionRowIds) {
        return CollectionUtils.isEmpty(scheduleItemOriginVersionRowIds) ? Collections.emptyMap() :
                activityScheduleItemOriginDetailExtMapper.getRelationScheduleItemOriginDetails(scheduleItemOriginVersionRowIds)
                        .stream().collect(Collectors.groupingBy(ActivityScheduleItemOriginDetailDO::getScheduleItemOriginRowId));
    }
}
