package com.zte.mcrm.activity.service.activitylist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.activity.service.approval.vo.ActivityApprovalProcessNodeQueryVO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityOpportunityRecordVO;
import com.zte.mcrm.activity.web.controller.reservation.vo.ActivityResourceReservationVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: 王伟江6724000185
 * @Date: 2023/5/24 17:07
 */
@Setter
@Getter
@ToString
public class ActivityFlowInfoVO {

    @ApiModelProperty(name = "节点编码：launch 活动启动，request 资源申请，approval 活动审批，execute 活动执行，complete 活动完成")
    private String nodeCode;

    @ApiModelProperty(name = "节点名称")
    private String nodeName;

    @ApiModelProperty(name = "节点名称En")
    private String nodeNameEn;

    @ApiModelProperty(name = "高亮状态：Y高亮，N置灰")
    private String highlight;

    @ApiModelProperty(name = "阶段状态：done-过去，doing-进行中，2-undo")
    private String stageStatus;

    @ApiModelProperty(name = "触发人")
    private String trigger;

    @ApiModelProperty(name = "触发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date triggerTime;

    @ApiModelProperty(name = "资源预约申请数据")
    private ActivityResourceReservationVo reservationVo;

    @ApiModelProperty(name = "待审批人数")
    private Integer unApprovedNum;

    @ApiModelProperty(name = "已审批人数")
    private Integer approvedNum;

    @ApiModelProperty(name = "已拒绝人数")
    private Integer disAgree;

    @ApiModelProperty(name = "已取消人数")
    private Integer canceledNum;

    @ApiModelProperty(name = "活动审批数据：complianceAuditorNodeCode 合规经理审批节点，leaderAuditorNodeCode 责任部门领导审批节点")
    private Map<String, List<ActivityApprovalProcessNodeQueryVO>> processMap;

    @ApiModelProperty(name = "是否提交会议纪要：Y是，N否")
    private String commitMeetingFeedback;

    @ApiModelProperty(name = "待评价人数")
    private Integer unEvaluatedNum;

    @ApiModelProperty(name = "已评价人数")
    private Integer evaluatedNum;

    @ApiModelProperty(name = "节点记录")
    private String log;

    @ApiModelProperty(name = "子节点")
    private List<ActivityFlowInfoVO> child;

    @ApiModelProperty(name = "活动关联商机日志记录")
    private List<ActivityOpportunityRecordVO> activityBindOpportunityRecords;

}
