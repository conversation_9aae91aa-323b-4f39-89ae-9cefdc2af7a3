package com.zte.mcrm.activity.repository.rep.exhibition;

import com.zte.mcrm.activity.common.model.PageRows;
import com.zte.mcrm.activity.repository.model.exhibition.ExhibitionInfoDO;
import com.zte.mcrm.activity.repository.rep.exhibition.param.ExhibitionInfoQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ExhibitionInfoRepository
 * @projectName zte-crm-custinfo-custvisit
 * @description: 展会信息业务层接口
 * @date 2023/9/614:25
 */
public interface ExhibitionInfoRepository {

    /**
     * 活动展会信息
     * @param rowId 展会ID（主键）
     * @return
     */
    ExhibitionInfoDO selectByPrimaryKey(String rowId);

    List<ExhibitionInfoDO> listByRowIds(List<String> exhibitionRowIds);

    /**
     * 批量新增任务信息
     *
     * @param recordList 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int insertSelective(List<ExhibitionInfoDO> recordList);

    /**
     * 更新任务信息
     *
     * @param record 任务信息
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int updateByPrimaryKeySelective(ExhibitionInfoDO record);

    /**
     * description 查询展会列表（权限控制）
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/19 下午4:16
     */
    PageRows<ExhibitionInfoDO> queryExhibitionInfoList(ExhibitionInfoQuery  exhibitionInfoQuery);

    /**
     * 查询展会，不需要权限
     *
     * @param query
     * @return {@link PageRows<ExhibitionInfoDO>}
     * <AUTHOR>
     * @date 2023/12/28 上午10:16
     */
    PageRows<ExhibitionInfoDO> searchListNoAuth(ExhibitionInfoQuery query);

    /**
     * description 查询展会数量（权限控制）
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/19 下午4:19
     */
    int getExhibitionInfoCount(ExhibitionInfoQuery  exhibitionInfoQuery);

    /**
     * 获取符合条件的展会数量
     * @param query 展会查询入参
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    int getCountExhibitionInfo(ExhibitionInfoQuery query);

    /**
     * 获取主键ID集合展会数据
     * @param rowIds 展会主键ID
     * @return
     * <AUTHOR>
     * @date 2023/9/6
     */
    Map<String,ExhibitionInfoDO> queryExhibitionInfoByRowId(List<String> rowIds);
}
