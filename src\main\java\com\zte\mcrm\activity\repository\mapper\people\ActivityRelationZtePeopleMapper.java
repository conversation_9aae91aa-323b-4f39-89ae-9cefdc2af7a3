package com.zte.mcrm.activity.repository.mapper.people;

import com.zte.mcrm.activity.repository.model.activity.ActivityInfoDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.temp.service.model.DataTransParam;

import java.util.List;

public interface ActivityRelationZtePeopleMapper {
    /**
     * all field insert
     */
    int insert(ActivityRelationZtePeopleDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityRelationZtePeopleDO record);

    /**
     * query by primary key
     */
    ActivityRelationZtePeopleDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityRelationZtePeopleDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityRelationZtePeopleDO record);

    /**
     * 新工号切换
     */
    List<ActivityRelationZtePeopleDO> queryEmpNoTransList(DataTransParam searchParam);
}