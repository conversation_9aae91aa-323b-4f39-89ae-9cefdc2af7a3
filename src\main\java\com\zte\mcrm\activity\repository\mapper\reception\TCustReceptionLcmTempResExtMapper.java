package com.zte.mcrm.activity.repository.mapper.reception;

import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResDO;
import com.zte.mcrm.activity.repository.model.reception.TCustReceptionLcmTempResWithOldPairRowIdDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface TCustReceptionLcmTempResExtMapper extends TCustReceptionLcmTempResMapper {

    /**
     * 批量插入
     * @param list  新增列表
     * @return int
     * <AUTHOR>
     * date: 2024/1/8 11:28
     */
    int batchInsert(@Param("list")List<TCustReceptionLcmTempResDO> list);

    /**
     * 根据客户接待拓展活动ID获取对应客户LCM扫描暂存信息
     *
     * @param list  头Id列表
     * @return
     */
    List<TCustReceptionLcmTempResDO> getByHeaderIds(@Param("list")List<String> list);

    /**
     * 批量更新
     *
     * @param updateList 待更新列表
     * @return int
     * <AUTHOR>
     * date: 2023/7/1 10:59
     */
    int batchUpdateByPrimaryKey(@Param("updateList")List<TCustReceptionLcmTempResDO> updateList);

    /**
     * 根据旧结对Id批量更新
     *
     * @param list 待更新列表
     * @return int
     * <AUTHOR>
     * date: 2023/7/1 10:59
     */
    int batchUpdateByPairRowId(@Param("updateList")List<TCustReceptionLcmTempResWithOldPairRowIdDO> list);
}