package com.zte.mcrm.activity.repository.model.item;

import java.util.Date;

/**
 * table:activity_schedule_item -- 
 */
public class ActivityScheduleItemDO {
    /** 主键 */
    private String rowId;

    /** 活动RowId */
    private String activityRowId;

    /** 日程安排日期 */
    private Date scheduleDate;

    /** 日程排时间。形如：10:10~10:40 */
    private String scheduleTime;

    /** 日程安排类型。见：ScheduleItemTypeEnum */
    private String scheduleItemType;

    /** 日程安排名称 */
    private String scheduleItemName;

    /** 会见地点类型。见：ScheduleItemPlaceTypeEnum。room,other */
    private String placeType;

    /** 会见地点名称 */
    private String placeName;

    /** 会见地点预计人数 */
    private String placeCapacityNum;

    /** 资源编排处理状态。见：ResourceOrchestrationDealStatusEnum */
    private String dealStatus;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    private Date creationDate;

    /** 最后修改人 */
    private String lastUpdatedBy;

    /** 最后修改时间 */
    private Date lastUpdateDate;

    /** 逻辑删除标识。BooleanEnum */
    private String enabledFlag;

    /** 资源编排处理意见 */
    private String dealNote;

    /** 资源编排处理备注 */
    private String remark;

    /** 是否发生过变更（第一次新增不是变更）。Y-是，N-否。见BooleanEnum */
    private String hasChanged;

    /** 所属资源编排版本。activity_schedule_orchestration_version#row_id */
    private String orchestrationVersionRowId;

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getActivityRowId() {
        return activityRowId;
    }

    public void setActivityRowId(String activityRowId) {
        this.activityRowId = activityRowId == null ? null : activityRowId.trim();
    }

    public Date getScheduleDate() {
        return scheduleDate;
    }

    public void setScheduleDate(Date scheduleDate) {
        this.scheduleDate = scheduleDate;
    }

    public String getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(String scheduleTime) {
        this.scheduleTime = scheduleTime == null ? null : scheduleTime.trim();
    }

    public String getScheduleItemType() {
        return scheduleItemType;
    }

    public void setScheduleItemType(String scheduleItemType) {
        this.scheduleItemType = scheduleItemType == null ? null : scheduleItemType.trim();
    }

    public String getScheduleItemName() {
        return scheduleItemName;
    }

    public void setScheduleItemName(String scheduleItemName) {
        this.scheduleItemName = scheduleItemName == null ? null : scheduleItemName.trim();
    }

    public String getPlaceType() {
        return placeType;
    }

    public void setPlaceType(String placeType) {
        this.placeType = placeType == null ? null : placeType.trim();
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName == null ? null : placeName.trim();
    }

    public String getPlaceCapacityNum() {
        return placeCapacityNum;
    }

    public void setPlaceCapacityNum(String placeCapacityNum) {
        this.placeCapacityNum = placeCapacityNum == null ? null : placeCapacityNum.trim();
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus == null ? null : dealStatus.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag == null ? null : enabledFlag.trim();
    }

    public String getDealNote() {
        return dealNote;
    }

    public void setDealNote(String dealNote) {
        this.dealNote = dealNote == null ? null : dealNote.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getHasChanged() {
        return hasChanged;
    }

    public void setHasChanged(String hasChanged) {
        this.hasChanged = hasChanged == null ? null : hasChanged.trim();
    }

    public String getOrchestrationVersionRowId() {
        return orchestrationVersionRowId;
    }

    public void setOrchestrationVersionRowId(String orchestrationVersionRowId) {
        this.orchestrationVersionRowId = orchestrationVersionRowId == null ? null : orchestrationVersionRowId.trim();
    }
}