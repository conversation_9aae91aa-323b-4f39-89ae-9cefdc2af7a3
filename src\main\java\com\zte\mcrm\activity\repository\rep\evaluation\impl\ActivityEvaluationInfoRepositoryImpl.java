package com.zte.mcrm.activity.repository.rep.evaluation.impl;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.repository.mapper.evaluation.ActivityEvaluationInfoExtMapper;
import com.zte.mcrm.activity.repository.model.evaluation.ActivityEvaluationInfoDO;
import com.zte.mcrm.activity.repository.rep.evaluation.ActivityEvaluationInfoRepository;
import com.zte.mcrm.activity.service.resource.vo.ActivityScoreCountVO;
import com.zte.mcrm.keyid.service.IKeyIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ActivityEvaluationInfoRepositoryImpl implements ActivityEvaluationInfoRepository {
    @Autowired
    private ActivityEvaluationInfoExtMapper extMapper;
    @Autowired
    private IKeyIdService keyIdService;

    @Override
    public int insertSelective(ActivityEvaluationInfoDO record) {
        if (StringUtils.isBlank(record.getRowId())) {
            record.setRowId(keyIdService.getKeyId());
        }
        record.setCreationDate(new Date());
        record.setLastUpdateDate(new Date());
        record.setEnabledFlag(BooleanEnum.Y.getCode());
        return extMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ActivityEvaluationInfoDO record) {
        record.setLastUpdateDate(new Date());
        return extMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<ActivityEvaluationInfoDO> queryAllByActivityRowId(String activityRowId) {
        return StringUtils.isBlank(activityRowId) ? Collections.emptyList()
                : extMapper.queryAllByActivityRowId(activityRowId);
    }

    /**
     * 查询活动参与人评价分数及总次数
     *
     * @param codeList           参与人编号
     * @param activityStatusList 活动状态
     * @return {@link List<   ActivityScoreCountVO  >}
     * <AUTHOR>
     * @date 2023/5/22 下午3:14
     */
    @Override
    public List<ActivityScoreCountVO> selectParticipantsScoreCount(List<String> codeList,
                                                                   List<String> activityStatusList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        return extMapper.selectParticipantsScoreCount(codeList, activityStatusList);
    }
}