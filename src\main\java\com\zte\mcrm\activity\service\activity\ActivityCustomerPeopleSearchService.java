package com.zte.mcrm.activity.service.activity;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import com.zte.mcrm.activity.web.controller.baseinfo.vo.ActivityCustomerPeopleVO;
import com.zte.mcrm.adapter.vo.ContactVO;

import java.util.List;

/**
 * 客户活动客户查询
 *
 * <AUTHOR>
 * @date 2023/5/17 下午3:23
 */
public interface ActivityCustomerPeopleSearchService {

    /**
     * 查询活动创建中用户最近使用的客户参与人
     *
     * @param request
     * @return {@link List<ActivityCustomerPeopleVO>}
     * <AUTHOR>
     * @date 2023/5/17 下午2:59
     */
    List<ContactVO> searchRecentlyCustomerPeoples(BizRequest<PageQuery<ActivityRecentlySearchParam>> request);

    /**
     * 根据编号批量查询联系人
     *
     * @param conPerNums
     * @return {@link List< ContactVO>}
     * <AUTHOR>
     * @date 2023/5/28 下午2:14
     */
    List<ContactVO> fetchContactPerson(List<String> conPerNums);
}
