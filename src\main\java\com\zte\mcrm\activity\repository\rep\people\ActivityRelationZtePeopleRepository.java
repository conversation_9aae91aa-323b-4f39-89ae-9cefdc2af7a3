package com.zte.mcrm.activity.repository.rep.people;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;
import com.zte.mcrm.activity.service.activitylist.param.ActivityRelationZtePeopleQuery;
import com.zte.mcrm.activity.service.model.people.ActivityRelationZtePeople;
import com.zte.mcrm.activity.service.resource.vo.ActivityTimesCountVO;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 活动我司人员
 *
 * <AUTHOR>
 */
public interface ActivityRelationZtePeopleRepository {

    /**
     * 添加活动关联我司人员（如果没有主键，自动生成）
     *
     * @param recordList
     */
    int insertSelective(List<ActivityRelationZtePeopleDO> recordList);

    /**
     * 按主键动态更新
     *
     * @param record
     */
    int updateByPrimaryKeySelective(ActivityRelationZtePeopleDO record);

    /**
     * 查询活动关联的我司人员
     *
     * @param activityRowId 活动RowId
     * @return
     */
    List<ActivityRelationZtePeopleDO> queryAllZtePeopleForActivity(String activityRowId);

    /**
     *
     * @param activityRelationZtePeopleQuery
     * @return
     */
    List<ActivityRelationZtePeopleDO> queryInfoList(ActivityRelationZtePeopleQuery activityRelationZtePeopleQuery);

    /**
     * 查找用户最近创建的活动中使用的中兴参与人
     *
     * @param pageQuery
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:57
     */
    List<ActivityRelationZtePeopleDO> selectRecentlyZtePeopleByUser(PageQuery<ActivityRecentlySearchParam> pageQuery);

    /**
     * 根据工号统计活动参与数量
     *
     * @param codeList              工号
     * @param activityStatusList    活动状态
     * @return {@link List< ActivityTimesCountVO >}
     * <AUTHOR>
     * @date 2023/5/22 下午2:29
     */
    List<ActivityTimesCountVO> selectCountByPeopleCode(List<String> codeList, List<String> activityStatusList);

    /**
     * 批量更新
     *
     * @param updateList    更新列表
     * @return int
     * <AUTHOR>
     * date: 2023/5/21 13:44
     */
    int batchUpdateByPrimaryKey(List<ActivityRelationZtePeopleDO> updateList);

    /**
     * 批量更新(数据修复不刷新更新人与更新时间)
     * @param updateList    更新列表
     * @return int
     * <AUTHOR>
     */
    int batchUpdateByPrimaryKeyWithoutUpdateInfo(List<ActivityRelationZtePeopleDO> updateList);

    /**
     * 列表查询
     *
     * @param query    查询实体
     * @return List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/5/23 10:35
     */
    List<ActivityRelationZtePeopleDO> queryZtePeople(ActivityRelationZtePeopleQuery query);

    /**
     * 查询指定的参与人
     *
     * @param activityRowId 活动主键
     * @param authCount 权限约束值最小值
     * @return List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/5/23 10:35
     */
    List<ActivityRelationZtePeopleDO> queryZtePeopleByActivityRowId(String activityRowId, Integer authCount);

    /**
     * 根据活动id获取当前数据集合
     * @param activityRowIds
     * @return
     */
    Map<String,List<ActivityRelationZtePeopleDO>> getZtePeopleListByActivityRowIds(Set<String> activityRowIds);

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(List<ActivityRelationZtePeopleDO> list);

    /**
     * 批量插入数据
     *
     * @param list 列表
     * @return 返回结果
     * <AUTHOR>
     */
    int batchInsertWithoutDefault(List<ActivityRelationZtePeopleDO> list);

    /**
     * 根据活动id和活动角色查询参与人数据
     *
     * @param activityRowId
     * @param codes
     * @return {@link List}<{@link ActivityRelationZtePeople}>
     */
    List<ActivityRelationZtePeopleDO> queryByActivityRowIdAndPeopleType(String activityRowId, List<String> codes);

    /**
     * 批量获取参与人
     * @param activityRowIds
     * @param codes
     * @return
     */
    Map<String, List<ActivityRelationZtePeopleDO>> queryByActivityRowIdsAndPeopleType(List<String> activityRowIds, List<String> codes);

    int deleteByActivityIds(String operator, List<String> activityIds);

    int deleteByRowIds(String operator, List<String> rowIds);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 查询所有-包含无效数据
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationZtePeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationZtePeopleDO> queryAllActivityWithNotEnable(String activityRowId);

    /**
     * 查询没有名字的我司联系人
     *
     * @param activityRowId
     * @param limit
     * @return {@link List< ActivityRelationZtePeopleDO>}
     * <AUTHOR>
     * @date 2024/1/3 下午11:45
     */
    List<ActivityRelationZtePeopleDO> selectNoNameList(String activityRowId, String rowId, int limit);

    /**
     * 根据迁移活动主键和专家编码,获取需要打专家标签的记录id(每次查询1000条)
     * @param activityRowIds
     * @param expertCodes
     * @return
     */
    List<String> getUpdateSaventLabelRowIds(List<String> activityRowIds, List<String> expertCodes);

    /**
     * 根据主键id打赏专家标签
     *
     * @param rowIds
     * @return
     */
    int updateSaventLabel(List<String> rowIds);
}
