package com.zte.mcrm.activity.service.activity.convert;

import static com.zte.mcrm.activity.common.constant.ActivityConstant.*;
import static com.zte.mcrm.activity.common.enums.activity.CustBelongDepartmentTypeEnum.*;

/**
 * <AUTHOR>
 * @title: ActivityCheckConvert
 * @projectName zte-crm-custinfo-custvisit
 * @description: TODO
 * @date 2024/5/15 15:42
 */
public class ActivityCheckConvert {

    /**
     * 三营判断
     * @param dictValue
     * @param secondLevelOrg
     * @param parentCommunication
     * @return
     * <AUTHOR>
     * @date 2024/5/15
     */
    public static boolean salesJudgment(String dictValue,String secondLevelOrg,String parentCommunication){
        boolean parentFlag = false;
        if (ACTIVITY_COMMUNICATION_OPERATOR.equals(parentCommunication) || ACTIVITY_COMMUNICATION_NEW_BUSINESS.equals(parentCommunication)){
            parentFlag = true;
        }
        return dictValue.contains(secondLevelOrg) && parentFlag;
    }

    /**
     * 政企判断
     * @param dictValue
     * @param secondLevelOrg
     * @param parentCommunication
     * @return
     * <AUTHOR>
     * @date 2024/5/15
     */
    public static boolean govJudgment(String dictValue,String secondLevelOrg,String parentCommunication){
        boolean parentFlag = false;
        if (ACTIVITY_COMMUNICATION_GOVERNMENT_ENTERPRISE.equals(parentCommunication)){
            parentFlag = true;
        }
        return dictValue.contains(secondLevelOrg) && parentFlag;
    }
}
