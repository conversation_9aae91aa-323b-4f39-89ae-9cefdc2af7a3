package com.zte.mcrm.activity.repository.rep.banner;

import com.zte.mcrm.activity.repository.model.banner.BannerConfigDO;
import com.zte.mcrm.activity.repository.model.banner.BannerConfigNewDO;

import java.util.List;
import java.util.Map;

public interface BannerConfigRepository {
    /**
     * 添加banner配置信息
     *
     * @param recordList
     * @return
     */
    int insertSelective(List<BannerConfigNewDO> recordList);

    /**
     * 更新banner配置信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(BannerConfigNewDO record);

    /**
     * 根据banner id获取对应配置
     * @param bannerIds
     * @return
     */
    Map<String, List<BannerConfigNewDO>> fetchBannerConfigByBannerIds(List<String> bannerIds);

    /**
     * 根据ID查询
     * @param bannerConfigId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2024/02/01
     */
    BannerConfigDO get(String bannerConfigId);

    /**
     * 查询列表
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/02/01
     */
    List<BannerConfigDO> getList(BannerConfigDO entity);

    /**
     * 删除(软删除)
     * @param bannerConfigId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    int delete(String bannerConfigId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    String insert(BannerConfigDO entity);

    /**
     *
     * @param bannerId
     * @param list
     * @return
     */
    int insertByBatch(String bannerId, List<BannerConfigDO> list);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2024/02/01
     */
    int update(BannerConfigDO entity);

    /**
     * 根据banner id软删除
     * @param bannerId
     * @return
     * <AUTHOR>
     * @date 2024/02/01
     */
    int deteleBatchByBannerId(String bannerId);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2024/02/01
     */
    long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2024/02/01
     */
    List<BannerConfigDO> getPage(Map<String, Object> map);

    /**
     * 根据banner id获取对应配置
     *
     * @param bannerIds  bannerID集合
     * @return Map<String, List<BannerConfigNewDO>>
     */
    Map<String, List<BannerConfigNewDO>> matchBannerConfigByBannerIds(List<String> bannerIds);
}
