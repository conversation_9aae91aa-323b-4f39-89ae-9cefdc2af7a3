package com.zte.mcrm.activity.application.cto.impl;

import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.activity.application.cto.CtoPlanSaleDivisionMappingService;
import com.zte.mcrm.activity.common.cache.client.HrOrgDataCacheClient;
import com.zte.mcrm.activity.common.cache.model.HrOrgDataModel;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.enums.BooleanEnum;
import com.zte.mcrm.activity.common.enums.MappingTypeEnum;
import com.zte.mcrm.activity.common.exception.BizRuntimeException;
import com.zte.mcrm.activity.common.model.*;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.activity.repository.model.plancto.CtoPlanSaleDivisionMappingDO;
import com.zte.mcrm.activity.repository.rep.plancto.CtoPlanSaleDivisionMappingRepository;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanSaleDivisionMappingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 类的描述
 * @author: 罗振6005002932
 * @Date: 2024-12-12
 */
@Slf4j
@Service
public class CtoPlanSaleDivisionMappingServiceImpl implements CtoPlanSaleDivisionMappingService {
    @Autowired
    CtoPlanSaleDivisionMappingRepository ctoPlanSaleDivisionMappingRepository;
    @Autowired
    private LocaleMessageSourceBean locale;
    @Autowired
    private HrOrgDataCacheClient hrOrgDataCacheClient;

    /* Started by AICoder, pid:c240b64fddsc15c14bfd09cba074e165b6a42f22 */

    /**
     * 事业部与业务部门映射管理（插删改）
     * @param req
     * @return
     */
    @Override
    public int saleDivisionMappingOrgNoLists(BizRequest<List<CtoPlanSaleDivisionMappingVO>> req) {
        List<CtoPlanSaleDivisionMappingVO> ctoPlanSaleDivisionMappingVOList = req.getParam();
        if (CollectionUtils.isEmpty(ctoPlanSaleDivisionMappingVOList)) {
            throw new BizRuntimeException(RetCode.BUSINESSERROR_CODE, "info.center.param.empty");
        }
        // 提取所有组织编号
        Set<String> orgNoSet = ctoPlanSaleDivisionMappingVOList.stream()
                .map(CtoPlanSaleDivisionMappingVO::getOrgNo)
                .collect(Collectors.toSet());
        // 获取缓存数据
        Map<String, HrOrgDataModel> hrOrgDataModelMap = hrOrgDataCacheClient.fetchAllCache(orgNoSet);
        this.updateCtoPlanSaleDivisionMappingVOList(hrOrgDataModelMap, ctoPlanSaleDivisionMappingVOList);
        // 检查组织编号全路径和来源是否为空
        if (ctoPlanSaleDivisionMappingVOList.stream().anyMatch(vo ->
                StringUtils.isEmpty(vo.getOrgNoFullPath()) || StringUtils.isEmpty(vo.getOrgSource()))) {
            return NumberConstant.ZERO;
        }
        // 按照标识分类
        Map<String, List<CtoPlanSaleDivisionMappingVO>> ctoMappingVOListGroupByFlag = ctoPlanSaleDivisionMappingVOList.stream()
                .collect(Collectors.groupingBy(CtoPlanSaleDivisionMappingVO::getFlag));

        // 处理每个分组
        ctoMappingVOListGroupByFlag.forEach((flag, voList) -> {
            List<CtoPlanSaleDivisionMappingDO> doList = voList.stream()
                    .map(vo -> {
                        CtoPlanSaleDivisionMappingDO doObj = new CtoPlanSaleDivisionMappingDO();
                        BeanUtils.copyProperties(vo, doObj);
                        return doObj;
                    })
                    .collect(Collectors.toList());

            MappingTypeEnum typeEnum = MappingTypeEnum.fromCode(flag);
            if (typeEnum != null && StringUtils.isNotEmpty(typeEnum.getCode())) {
                Date now = new Date();
                switch (typeEnum) {
                    case INSERT:
                        doList.forEach(doObj -> {
                            doObj.setEnabledFlag(BooleanEnum.Y.getCode());
                            doObj.setCreationDate(now);
                            doObj.setCreatedBy(req.getEmpNo());
                            doObj.setLastUpdateDate(now);
                            doObj.setLastUpdatedBy(req.getEmpNo());
                        });
                        ctoPlanSaleDivisionMappingRepository.batchInsert(doList);
                        break;
                    case UPDATE:
                        doList.forEach(doObj -> {
                            doObj.setEnabledFlag(BooleanEnum.Y.getCode());
                            doObj.setLastUpdateDate(now);
                            doObj.setLastUpdatedBy(req.getEmpNo());
                        });
                        ctoPlanSaleDivisionMappingRepository.batchUpdate(doList);
                        break;
                    case DELETE:
                        doList.forEach(doObj -> {
                            doObj.setEnabledFlag(BooleanEnum.N.getCode());
                            doObj.setLastUpdateDate(now);
                            doObj.setLastUpdatedBy(req.getEmpNo());
                        });
                        ctoPlanSaleDivisionMappingRepository.batchUpdate(doList);
                        break;
                    default:
                        // 忽略未知类型
                        break;
                }
            }
        });

        return NumberConstant.ONE;
    }
    /* Ended by AICoder, pid:c240b64fddsc15c14bfd09cba074e165b6a42f22 */


    /* Started by AICoder, pid:915f5e9fedk957c147260ab140f631163726adf9 */
    public void updateCtoPlanSaleDivisionMappingVOList(Map<String, HrOrgDataModel> hrOrgDataModelMap,
            List<CtoPlanSaleDivisionMappingVO> ctoPlanSaleDivisionMappingVOList) {
        // 使用局部变量来减少重复查找，提高性能
        for (CtoPlanSaleDivisionMappingVO vo : ctoPlanSaleDivisionMappingVOList) {
            String orgNo = vo.getOrgNo();
            HrOrgDataModel model = hrOrgDataModelMap.get(orgNo);

            // 只有当model不为null时才进行更新操作
            if (model != null) {
                vo.setOrgNoFullPath(model.getOrgIDPath());
                vo.setOrgSource(model.getType());
            }
        }
    }

    /* Ended by AICoder, pid:915f5e9fedk957c147260ab140f631163726adf9 */

}
