package com.zte.mcrm.activity.application.cto;

import com.zte.mcrm.activity.common.model.BizRequest;
import com.zte.mcrm.activity.common.model.LowCodePageRow;
import com.zte.mcrm.activity.web.PageQuery;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanDataParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanExportParam;
import com.zte.mcrm.activity.web.controller.cto.parm.CtoPlanOrgDetailParam;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanOrgDetailVO;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanOrgFinishVO;
import com.zte.mcrm.activity.web.controller.cto.vo.CtoPlanProductFinishVO;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日19:17
 */
public interface CtoPlanActivityDataService {

    /**
     * 活动数据-查询事业部维度
     * @param req
     * @return
     */
    LowCodePageRow<CtoPlanOrgFinishVO> listOrgFinish(BizRequest<PageQuery<String>> req);

    /**
     * 活动数据-查询产品维度
     * @param req
     * @return
     */
    LowCodePageRow<CtoPlanProductFinishVO> listProductFinish(BizRequest<PageQuery<String>> req);

    /**
     * CTO握手发布
     * @param req
     * @return
     */
    Boolean publishPlan(BizRequest<CtoPlanDataParam> req);

    /**
     * 生成活动报表数据
     *
     * @param req 入参
     * @return 生成的文件
     */
    ByteArrayOutputStream generatePlanDetailFileData(BizRequest<CtoPlanExportParam> req);

    /**
     * 更新执行报表数据
     *
     * @param req 入参
     * @return 操作是否成功
     */
    boolean updateCtoPlanExeReport(BizRequest<CtoPlanExportParam> req);

    /**
     * 查询CTO握手-数据看板-规划详情下钻
     * @param req
     * @return
     */
    LowCodePageRow<CtoPlanOrgDetailVO> queryOrgDetail(BizRequest<PageQuery<CtoPlanOrgDetailParam>> req);

    /**
     * 查询CTO握手-数据看板-重点客户下钻
     * @param req
     * @return
     */
    LowCodePageRow<CtoPlanOrgDetailVO> queryKeyAccount(BizRequest<PageQuery<CtoPlanOrgDetailParam>> req);
}
