package com.zte.mcrm.activity.common.config;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 文件相关配置
 *
 * <AUTHOR>
 * @date 2023/12/11 下午8:32
 */
@Configuration
public class FileConfig {

    /**
     * 支持上传的文件类型
     */
    private static String uploadLimitFileType;

    @Value("${upload.limitFileType:jpeg,png,jpg,gif,bmp,txt,csv,rtf,MP4,RMVB,AVI,WMV,FLV,MKV,WebM,MOV,MP3,WAV,FLAC,OGG,AAC,WMA,xlsx,xls,doc,docx,ppt,pptx,wps,et,ods,7z,rar,zip,pdf,odt,dps}")
    public void setUploadLimitFileType(String uploadLimitFileType) {
        loadUploadLimitFileType(uploadLimitFileType);
    }

    public static void loadUploadLimitFileType(String uploadLimitFileType) {
        FileConfig.uploadLimitFileType = uploadLimitFileType;
    }

    /**
     * 获取可上传附件类型集合
     *
     * @return {@link List<String>}
     * <AUTHOR>
     * @date 2023/12/11 下午7:41
     */
    public static List<String> getUploadLimitTypeList() {
        if (StringUtils.isBlank(uploadLimitFileType)) {
            return Collections.emptyList();
        }
        return Arrays.asList(uploadLimitFileType.split(CharacterConstant.COMMA));
    }
}
