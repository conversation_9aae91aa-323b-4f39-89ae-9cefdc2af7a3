package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.constant.CharacterConstant;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.adapter.vo.OrgInfoVO;
import com.zte.mcrm.common.util.StringExtUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;

import static com.zte.mcrm.activity.common.constant.CharacterConstant.SLASH;
import static com.zte.mcrm.activity.common.constant.NumberConstant.ONE;

/**
 * <AUTHOR> 10344346
 * @date 2023-10-28 9:46
 **/
public class OrgUtil {
    /**
     * description 将完整的组织名切割成除去中兴通讯股份有限公司的组织名
     * 例如：输入"开发二团队/数字技术产品部/系统产品_产研平台与直属/中兴通讯股份有限公司"
     * 输出"开发二团队/数字技术产品部/系统产品_产研平台与直属"
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/28 上午9:49
     */
    public static String getOrgNameUnion(String orgFullName){
        if (StringUtils.isBlank(orgFullName) || !orgFullName.contains(SLASH)){
            return orgFullName;
        }
        return orgFullName.substring(0,orgFullName.lastIndexOf(SLASH));
    }

    /**
     * description 获取一层组织名
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/28 上午10:03
     */
    public static String getOrg1Name(String orgFullName){
        return getOrgTargetLevelNameOrCode(orgFullName, SLASH, ONE, true);
    }

    /**
     * description 获取二层组织名
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/28 上午10:03
     */
    public static String getOrg2Name(String orgFullName){
        return getOrgTargetLevelNameOrCode(orgFullName, SLASH, NumberConstant.TWO, true);
    }

    /**
     * 根据分隔符，获取目标层级名称或编码，名称倒序取值，编码正序取值
     * Name：l5/l4/l3/l2/l1  Code: l1-l2-l3-l4-l5
     * @param orgFull   部门全路径
     * @param splitStr  分隔符
     * @param level     层级
     * @return java.lang.String
     * <AUTHOR>
     * date: 2024/9/13 9:36
     */
    static String getOrgTargetLevelNameOrCode(String orgFull, String splitStr, int level, boolean isDesc) {
        if (StringUtils.isBlank(orgFull)){
            return Strings.EMPTY;
        }
        String[] orgArr = orgFull.split(splitStr);
        if (orgArr.length<level){
            return Strings.EMPTY;
        }
        if (isDesc) {
            return orgArr[orgArr.length - level];
        } else {
            return orgArr[level - ONE];
        }
    }

    /**
     * description 获取三层组织名
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/28 上午10:03
     */
    public static String getOrg3Name(String orgFullName){
        return getOrgTargetLevelNameOrCode(orgFullName, SLASH, NumberConstant.THREE, true);

    }

    /**
     * description 获取四层组织名或者四层/五层组织
     *
     * @param
     * @return
     * <AUTHOR>
     * @createDate 2023/10/28 上午10:03
     */
    public static String getOrg4Name(String orgFullName){
        String level4 = getOnlyOrg4Name(orgFullName);
        String level5 = getOrg5Name(orgFullName);
        // 如果五层不为空 则拼接  /level5，否则为空串
        level5 = StringExtUtils.getTargetOrEmpty(level5, SLASH + level5);
        // 如果4层不为空 则拼接 level4/level5 否则为空串
        return StringExtUtils.getTargetOrEmpty(level4, level4 + level5);
    }

    /**
     * description 获取四层组织名
     *
     */
    public static String getOnlyOrg4Name(String orgFullName){
        return getOrgTargetLevelNameOrCode(orgFullName, SLASH, NumberConstant.FOUR, true);
    }

    /**
     * description 获取五层组织名
     *
     */
    public static String getOrg5Name(String orgFullName){
        return getOrgTargetLevelNameOrCode(orgFullName, SLASH, NumberConstant.FIVE, true);
    }

    /**
     * 拼接组织信息
     * @param orgList
     * @param orgInfoMap
     * @param split
     * @return
     */
    public static String fetchOrgNameWithSplit(List<String> orgList, Map<String, OrgInfoVO> orgInfoMap, String split) {
        if (CollectionUtils.isEmpty(orgList)) {
            return StringUtils.EMPTY;
        }

        StringBuilder sb = new StringBuilder();
        split = String.valueOf(split);

        for (String code : orgList) {
            OrgInfoVO orgInfoVO = orgInfoMap.get(code);
            if (null != orgInfoVO) {
                sb.append(orgInfoVO.getHrOrgName());
            } else {
                sb.append(code);
            }
            sb.append(split);
        }

        return sb.substring(NumberConstant.ZERO, sb.length() - split.length());
    }

    /**
     * description 获取四层组织编码
     *
     */
    public static String getLevel4OrgId(String orgFullId){
        return getOrgTargetLevelNameOrCode(orgFullId, CharacterConstant.SHORT_BAR_ZH, NumberConstant.FOUR, false);
    }

    /**
     * description 获取二层组织编码
     *
     */
    public static String getLevel2OrgId(String orgFullId){
        return getOrgTargetLevelNameOrCode(orgFullId, CharacterConstant.SHORT_BAR_ZH, NumberConstant.TWO, false);
    }

    /**
     * description 获取全路径组织编码的层级
     *
     */
    public static String getLevelsByDeptFullPath(String orgFullId){
        if (StringUtils.isBlank(orgFullId)){
            return String.valueOf(NumberConstant.ZERO);
        }
        String[] orgIdArr = orgFullId.split(CharacterConstant.SHORT_BAR_ZH);
        return String.valueOf(orgIdArr.length);
    }

    /**
     * description 获取最后一层的组织编码
     *
     */
    public static String getLastOrgId(String orgFullId){
        if (StringUtils.isBlank(orgFullId)){
            return Strings.EMPTY;
        }
        String[] orgIdArr = orgFullId.split(CharacterConstant.SHORT_BAR_ZH);
        return orgIdArr[orgIdArr.length - ONE];
    }
}
