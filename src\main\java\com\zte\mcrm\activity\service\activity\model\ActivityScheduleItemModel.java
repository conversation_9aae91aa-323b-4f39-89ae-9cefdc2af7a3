package com.zte.mcrm.activity.service.activity.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/1/14 上午9:54
 */
@Data
public class ActivityScheduleItemModel {
    /** 主键 */
    private String rowId;
    /** 活动RowId */
    private String activityRowId;

    /** 日程安排日期 */
    private Date scheduleDate;

    /** 日程排时间。形如：10:10~10:40 */
    private String scheduleTime;

    /** 日程安排类型。见：ScheduleItemTypeEnum */
    private String scheduleItemType;

    /** 日程安排名称 */
    private String scheduleItemName;

    /** 会见地点类型。见：ScheduleItemPlaceTypeEnum。room,other */
    private String placeType;

    /** 会见地点名称 */
    private String placeName;

    /** 会见地点预计人数 */
    private String placeCapacityNum;

    /** 资源编排处理状态。见：ResourceOrchestrationDealStatusEnum */
    private String dealStatus;
    /** 资源编排处理意见 */
    private String dealNote;

    /** 资源编排处理备注 */
    private String remark;

    /** 是否发生过变更（第一次新增不是变更）。Y-是，N-否。见BooleanEnum */
    private String hasChanged;

    /** 所属资源编排版本。activity_schedule_orchestration_version#row_id */
    private String orchestrationVersionRowId;

    /**
     * 日程人员信息
     */
    private List<ActivityScheduleItemPeopleModel> scheduleItemPeopleModels;
}
