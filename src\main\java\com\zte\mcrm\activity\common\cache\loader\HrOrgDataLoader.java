package com.zte.mcrm.activity.common.cache.loader;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.json.JsonSanitizer;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.activity.common.cache.model.HrOrgDataModel;
import com.zte.mcrm.activity.common.config.InOneConfig;
import com.zte.mcrm.activity.common.constant.NumberConstant;
import com.zte.mcrm.activity.common.constant.RequestHeaderConstant;
import com.zte.mcrm.activity.common.enums.CompanyOrgSourceEnum;
import com.zte.mcrm.activity.common.thread.ThreadManager;
import com.zte.mcrm.activity.common.util.MsaRpcRequestUtil;
import com.zte.mcrm.activity.common.util.StringUtils;
import com.zte.mcrm.common.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公司hr组织信息加载器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class HrOrgDataLoader implements CacheDataLoader<String, HrOrgDataModel> {
    @Autowired
    InOneConfig inOneConfig;

    @Override
    public HrOrgDataModel load(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return loadAll(Collections.singleton(key)).get(key);
    }

    @Override
    public Map<String, HrOrgDataModel> loadAll(Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }

        Map<String, String> headerMap = MsaRpcRequestUtil.createWithCurrentUser().fetchHeaderMap();
        headerMap.put(RequestHeaderConstant.IN_ONE_APPCODE, inOneConfig.getCallerAppCode());

        // 将批量数据按100为一批次拆分，然后并行执行，最后返回结果
        List<List<HrOrgDataModel>> result = ThreadManager.doTaskSplitBatch(new ArrayList<>(keys), p -> p, NumberConstant.HUNDRED, p -> queryOrgInfo(headerMap, p));

        return result.stream().flatMap(Collection::stream).collect(Collectors.toMap(HrOrgDataModel::getHrOrgID, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 查询组织信息（子公司、股份等）
     *
     * @param headerMap
     * @param orgNos
     * @return
     */
    List<HrOrgDataModel> queryOrgInfo(Map<String, String> headerMap, List<String> orgNos) {
        List<HrOrgDataModel> zteList = queryOrgInfo(headerMap, CompanyOrgSourceEnum.ZTE, orgNos);
        Set<String> zteNos = zteList.stream().map(HrOrgDataModel::getHrOrgID).collect(Collectors.toSet());
        // 不使用removeAll
        List<String> subOrgNos = new ArrayList<>();
        for (String orgNo : orgNos) {
            if (!zteNos.contains(orgNo)) {
                subOrgNos.add(orgNo);
            }
        }

        List<HrOrgDataModel> subList = queryOrgInfo(headerMap, CompanyOrgSourceEnum.SUB_COMPANY, subOrgNos);

        List<HrOrgDataModel> list = new ArrayList<>(orgNos.size());
        list.addAll(zteList);
        list.addAll(subList);

        return list;
    }

    /**
     * 查询组织信息
     *
     * @param headerMap
     * @param orgType   T0001：股份组织     T0002：子公司      T0003：合作方公司
     * @param orgNos    部门组织编号
     * @return
     */
    public List<HrOrgDataModel> queryOrgInfo(Map<String, String> headerMap, CompanyOrgSourceEnum orgType, List<String> orgNos) {
        if (CollectionUtils.isEmpty(orgNos)) {
            return Collections.emptyList();
        }
        // CompanyOrgSourceEnum： T0001：股份组织     T0002：子公司      T0003：合作方公司
        Map<String, Object> param = new HashMap<>();
        param.put("idType", orgType.getCode());
        param.put("ver", "v3");
        param.put("msname", "zte-crm-custinfo-custvisit");
        param.put("ids", orgNos);

        ServiceData<Map<String, HrOrgDataModel>> serviceData;

        String url = inOneConfig.getInOneHost() + "/ihol/usercenter/orginfo/usercenter/getOrgInfo";
        try {
            String result = HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(param), headerMap);
            result = JsonSanitizer.sanitize(result);
            serviceData = JSON.parseObject(result, new TypeReference<ServiceData<Map<String, HrOrgDataModel>>>() {
            });
        } catch (Exception e) {
            log.error("查询组织信息报错：url={}", url, e);
            // ignore，以前的查询对报错也不错处理
            serviceData = new ServiceData<>();
        }

        return MapUtils.isEmpty(serviceData.getBo()) ? Collections.emptyList() :
                serviceData.getBo().values().stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}