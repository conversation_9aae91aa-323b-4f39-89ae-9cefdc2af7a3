package com.zte.mcrm.activity.repository.mapper.people;

import com.zte.mcrm.activity.repository.model.people.ActivityRelationContactDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.web.controller.baseinfo.param.ActivityRecentlySearchParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ActivityRelationCustPeopleExtMapper extends ActivityRelationCustPeopleMapper {
    /**
     * 查询非P开头的联系人（临时，这里应该存客户编号而不是ID）
     * @param index
     * @param size
     * @return
     */
    List<ActivityRelationCustPeopleDO> queryErrContractNos(@Param("index")int index, @Param("size")int size);

    List<ActivityRelationCustPeopleDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);

    /**
     * 根据活动id查询客户参与人员
     * @param activityRowIdList
     * @return
     */
    List<ActivityRelationCustPeopleDO> queryAllCustPeopleForActivity(@Param("activityRowIdList") List<String> activityRowIdList);

    /**
     * 客户联系人
     * @param activityRowIds
     * @return
     */
    List<ActivityRelationCustPeopleDO> getCustPeopleListByActivityRowIds(@Param("activityRowIds") Set<String> activityRowIds);

    /**
     * 查找用户最近创建的活动中使用的客户联系人
     *
     * @param param  查询参数
     * @return {@link List< ActivityRelationCustPeopleDO>}
     * <AUTHOR>
     * @date 2023/5/17 下午3:56
     */
    List<ActivityRelationCustPeopleDO> selectRecentlyCustomerPeopleByUser(ActivityRecentlySearchParam param);

    /**
     * 批量插入
     *
     * @param list 项目列表
     * @return 返回结果
     * <AUTHOR>
     * @date: 2023/5/23 19:14
     */
    int batchInsert(@Param("list")List<ActivityRelationCustPeopleDO> list);

    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    int deleteByRowIds(@Param("operator") String operator, @Param("rowIds") List<String> rowIds);

    /**
     * 查询所有-包含无效数据
     * 增加 enabled_flag = 'Y'，推送ES不需要无效客户联系人
     * @param activityRowId 活动Id
     * @return java.util.List<ActivityRelationCustPeopleDO>
     * <AUTHOR>
     * date: 2023/12/14 8:56
     */
    List<ActivityRelationCustPeopleDO> queryAllActivityWithNotEnable(@Param("activityRowId")String activityRowId);

    /**
     * 获取客户联系人访问记录
     *
     * @param contactNos 客户联系人编码
     * @param startTime  开始实践
     * @param endTime    结束时间
     * @return List<ActivityRelationCustPeopleDO>
     */
    List<ActivityRelationContactDO> queryAllContactVisits(@Param("contactNos") List<String> contactNos,
                                                          @Param("startTime") String startTime,
                                                          @Param("endTime") String endTime);
}