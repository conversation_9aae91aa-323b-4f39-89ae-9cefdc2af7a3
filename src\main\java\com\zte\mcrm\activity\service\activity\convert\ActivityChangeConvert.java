package com.zte.mcrm.activity.service.activity.convert;

import com.zte.mcrm.activity.service.model.activity.ActivityBO;

import java.util.ArrayList;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zte.mcrm.activity.common.enums.activity.ActivityPeopleTypeEnum.SITE_PEOPLE;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationCustPeopleDO;
import com.zte.mcrm.activity.repository.model.people.ActivityRelationZtePeopleDO;

/**
 * <AUTHOR>
 * @title: ActivityChangeConvert
 * @projectName zte-crm-custinfo-custvisit
 * @date 2023/11/2915:54
 */
public class ActivityChangeConvert {

    /**
     * 变更判定新旧现场接口人，用于后续操作
     * @param newActivityBO
     * @param oldActivityBO
     * @return
     * <AUTHOR>
     * @date 2023/11/29
     */
    public static boolean isZteSitePeopleChange(ActivityBO newActivityBO, ActivityBO oldActivityBO) {
        Set<String> newZteSitePeopleSet = Optional.ofNullable(newActivityBO.getListZtePeopleInfo()).orElse(new ArrayList<>())
                .stream().filter(e -> SITE_PEOPLE.isMe(e.getPeopleType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toSet());
        Set<String> oldZteSitePeopleSet = Optional.ofNullable(oldActivityBO.getListZtePeopleInfo()).orElse(new ArrayList<>())
                .stream().filter(e -> SITE_PEOPLE.isMe(e.getPeopleType()))
                .map(ActivityRelationZtePeopleDO::getPeopleCode).collect(Collectors.toSet());
        return newZteSitePeopleSet.size() > oldZteSitePeopleSet.size();
    }


    /**
     * 变更判定新旧客户参与人，用于后续操作
     * @param newActivityBO
     * @param oldActivityBO
     * @return
     * <AUTHOR>
     * @date 2023/11/29
     */
    public static boolean isCustomerPeopleNotChange(ActivityBO newActivityBO, ActivityBO oldActivityBO) {
        Set<String> newCustomerPeopleSet = Optional.ofNullable(newActivityBO.getListCustPeopleInfo()).orElse(new ArrayList<>())
                .stream().map(ActivityRelationCustPeopleDO::getContactNo).collect(Collectors.toSet());
        Set<String> oldCustomerPeopleSet = Optional.ofNullable(oldActivityBO.getListCustPeopleInfo()).orElse(new ArrayList<>())
                .stream().map(ActivityRelationCustPeopleDO::getContactNo).collect(Collectors.toSet());
        return newCustomerPeopleSet.size() == oldCustomerPeopleSet.size() && newCustomerPeopleSet.containsAll(oldCustomerPeopleSet);
    }
}
