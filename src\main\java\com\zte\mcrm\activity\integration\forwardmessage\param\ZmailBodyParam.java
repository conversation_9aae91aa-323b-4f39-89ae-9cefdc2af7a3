package com.zte.mcrm.activity.integration.forwardmessage.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * @Author: 陈连成10307838
 * @Date: 2023/5/18 14:21
 */
@Setter
@Getter
@ToString
public class ZmailBodyParam {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 发件人
     */
    private String sendEmpNo;

    /**
     * 收件人
     */
    private String receiveEmpNo;

    /**
     * 抄送人
     */
    private String ccEmpNo;

    /**
     * 链接地址
     */
    private String linkUrl;

    /**
     * 模板数据
     */
    private Map<String, Object> templateParamMap;

}
