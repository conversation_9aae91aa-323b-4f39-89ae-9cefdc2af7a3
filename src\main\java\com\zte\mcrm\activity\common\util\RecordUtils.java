package com.zte.mcrm.activity.common.util;

import com.zte.mcrm.activity.common.annotation.NotTainted;

import java.text.Normalizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 正则匹配文件名工具类
 * <AUTHOR>
 * @date 2021/09/02
 */
public class RecordUtils {

    @NotTainted
    private String value;

    /**正则匹配值*/
    private static final String REGULAR = "\\.\\.\\/|\\.\\.\\\\";

    /**
     * 获取修正后的值
     *  <AUTHOR>
     *  @date 2021/09/02
     * @return
     */
    public String getCleanManipulation() {
        value = Normalizer.normalize(value, Normalizer.Form.NFKC);
        Pattern pattern = Pattern.compile(REGULAR);
        Matcher matcher = pattern.matcher(value);
        if (matcher.find())
        {
            throw new IllegalStateException();
        }
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
