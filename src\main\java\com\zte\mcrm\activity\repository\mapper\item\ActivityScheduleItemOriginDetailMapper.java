package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemOriginDetailDO;

public interface ActivityScheduleItemOriginDetailMapper {
    /**
     * all field insert
     */
    int insert(ActivityScheduleItemOriginDetailDO record);

    /**
     * dynamic field insert
     */
    int insertSelective(ActivityScheduleItemOriginDetailDO record);

    /**
     * query by primary key
     */
    ActivityScheduleItemOriginDetailDO selectByPrimaryKey(String rowId);

    /**
     * update all field by primary key
     */
    int updateByPrimaryKey(ActivityScheduleItemOriginDetailDO record);

    /**
     * dynamic update field by primary key
     */
    int updateByPrimaryKeySelective(ActivityScheduleItemOriginDetailDO record);
}