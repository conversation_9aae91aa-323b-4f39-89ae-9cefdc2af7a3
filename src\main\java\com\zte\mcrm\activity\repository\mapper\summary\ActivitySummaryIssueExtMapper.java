package com.zte.mcrm.activity.repository.mapper.summary;

import com.zte.mcrm.activity.repository.model.summary.ActivitySummaryIssueDO;
import com.zte.mcrm.temp.service.model.DataTransParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActivitySummaryIssueExtMapper extends ActivitySummaryIssueMapper {

    /**
     * 查询活动关联的所有会议纪要遗留问题
     *
     * @param activityRowId 活动RowId
     * @return
     */
    @Deprecated
    List<ActivitySummaryIssueDO> queryAllSummaryIssueForActivity(String activityRowId);

    /**
     * 查询活动关联的会议纪要遗留问题
     *
     * @param activityIds
     * @return
     */
    List<ActivitySummaryIssueDO> queryAllByActivityRowId(@Param("activityIds") List<String> activityIds);

    /**
     * 批次删除
     * @param rowIdList
     * @return
     */
    int deleteBatch(List<String> rowIdList);

    /**
     * 按活动id维度删除
     * @param operator
     * @param activityIds
     * @return
     */
    int softDeleteByActivityIds(@Param("operator") String operator, @Param("activityIds") List<String> activityIds);

    /**
     * 通过活动Id更新
     * @param record    更新记录
     * @return int
     * <AUTHOR>
     * date: 2023/9/4 13:49
     */
    int updateByActivityId(ActivitySummaryIssueDO record);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(@Param("list")List<ActivitySummaryIssueDO> list);

    List<ActivitySummaryIssueDO> queryEmpNoTransList(DataTransParam searchParam);
}