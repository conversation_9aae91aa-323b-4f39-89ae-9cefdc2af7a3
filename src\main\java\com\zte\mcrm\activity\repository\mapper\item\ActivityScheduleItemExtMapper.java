package com.zte.mcrm.activity.repository.mapper.item;

import com.zte.mcrm.activity.repository.model.item.ActivityScheduleItemDO;
import com.zte.mcrm.activity.repository.model.resource.ActivityResourceHotelDO;
import com.zte.mcrm.activity.web.controller.activity.vo.ActivityScheduleItemAndPeopleVO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.Date;
import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface ActivityScheduleItemExtMapper extends ActivityScheduleItemMapper {
    /**
     * 批量查询数据
     * @param activityRowIds
     * @return
     */
    List<ActivityScheduleItemDO> queryAllScheduleInfoIds(@Param("activityRowIds") List<String> activityRowIds);

    List<ActivityScheduleItemDO> getScheduleItemByIds(List<String> itemRowIds);

    /**
     * 批量更新数据
     * @param records
     * @return
     */
    int batchUpdate(@Param("records") List<ActivityScheduleItemDO> records);

    /**
     * 根据活动id查询日程安排信息
     * @param activityRowId
     * @return
     */
    List<ActivityScheduleItemDO> queryAllByActivityRowId(@Param("activityRowId") String activityRowId);

    /**
     * 根据rowId，批量软删除日程安排数据
     *
     * @param operator
     * @param rowIds
     * @return
     */
    int deleteByRowIds(@Param("operator") String operator, @org.apache.ibatis.annotations.Param("rowIds") List<String> rowIds);

    /**
     * 通过版本ID批量查询数据
     * @param orchestrationVersionIdList
     * @return
     */
    List<ActivityScheduleItemDO> queryAllScheduleInfoByVersionIdList(@Param("orchestrationVersionIdList") List<String> orchestrationVersionIdList);


    /**
     * 通过展会ID和人员查询所有日程
     * @param peopleNoList
     * @param exhibitionId
     * @param peopleType
     * @return
     */
    List<ActivityScheduleItemAndPeopleVO> queryScheduleIdByExhibitionIdAndPeopleNoList(@Param("peopleNoList") List<String> peopleNoList, @Param("exhibitionId") String exhibitionId
            , @Param("peopleType") String peopleType, @Param("activityStatusList") List<String> activityStatusList);
    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<ActivityScheduleItemDO> records);

    /**
     * 通过活动id列表、状态列表、日程时间查询当前日程
     * @param activityIdList
     * @param statusList
     * @param scheduleDate
     * @return
     */
    List<ActivityScheduleItemDO> getScheduleListByActivityIdListAndStatusListAndScheduleDate(
            @Param("activityIdList")  List<String> activityIdList,
            @Param("statusList") List<String> statusList,
            @Param("scheduleDate") Date scheduleDate
    );
}